# Flood Prediction System - Spring Boot Backend

This is the backend service for the Flood Prediction System, built with Spring Boot and MongoDB.

## Features

- **Real-time Community Alerts**: Users can post, update, and resolve flood-related alerts
- **User Authentication**: Secure JWT-based authentication system
- **WebSocket Support**: Real-time updates for alerts and comments
- **Geospatial Queries**: Find alerts near specific locations
- **Weather Data Integration**: Automatically fetches weather data for alert locations
- **Alert Verification System**: Community-based verification of alerts
- **RESTful API**: Well-documented API endpoints for all operations

## Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- MongoDB 4.4 or higher

## Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/flood-prediction-system.git
cd flood-prediction-system/backend
```

### 2. Configure MongoDB

Make sure MongoDB is running on your system. The default configuration expects MongoDB to be running on localhost:27017 with a database named `floodprediction`.

You can modify the MongoDB connection settings in `src/main/resources/application.properties`.

### 3. Build and run the application

```bash
mvn clean install
mvn spring-boot:run
```

The application will start on port 8080 by default.

## API Documentation

Once the application is running, you can access the Swagger UI documentation at:

```
http://localhost:8080/api/swagger-ui.html
```

## Authentication

The API uses JWT for authentication. To access protected endpoints:

1. Register a new user at `/api/auth/signup`
2. Login at `/api/auth/login` to get a JWT token
3. Include the token in the Authorization header for subsequent requests:
   ```
   Authorization: Bearer your_jwt_token
   ```

## WebSocket Endpoints

Connect to the WebSocket at `/api/ws` to receive real-time updates.

Subscribe to the following topics:

- `/topic/alerts` - All new alerts
- `/topic/alerts/{id}` - Updates for a specific alert
- `/topic/alerts/{id}/comments` - New comments on a specific alert
- `/topic/alerts/{id}/updates` - New updates on a specific alert
- `/topic/alerts/city/{city}` - Alerts for a specific city

## Project Structure

- `config/` - Configuration classes
- `controller/` - REST and WebSocket controllers
- `dto/` - Data Transfer Objects
- `exception/` - Custom exceptions and error handling
- `model/` - Domain models
- `repository/` - MongoDB repositories
- `security/` - JWT authentication
- `service/` - Business logic

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
