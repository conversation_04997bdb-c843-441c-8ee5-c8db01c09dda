# Server Configuration
server.port=8080
server.servlet.context-path=/api

# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=floodprediction
spring.data.mongodb.auto-index-creation=true

# JWT Configuration
jwt.secret=floodPredictionSecretKey2023ForSecureTokenGenerationAndValidation
jwt.expiration=86400000

# Logging Configuration
logging.level.org.springframework.data.mongodb.core.MongoTemplate=DEBUG
logging.level.com.floodprediction.backend=DEBUG

# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:5003
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=Authorization,Content-Type,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
cors.exposed-headers=Authorization
cors.allow-credentials=true
cors.max-age=3600

# WebSocket Configuration
websocket.endpoint=/ws
websocket.allowed-origins=http://localhost:3000,http://localhost:5003

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
