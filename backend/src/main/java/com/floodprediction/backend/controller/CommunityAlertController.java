package com.floodprediction.backend.controller;

import com.floodprediction.backend.dto.AlertCommentDto;
import com.floodprediction.backend.dto.AlertUpdateDto;
import com.floodprediction.backend.dto.CommunityAlertDto;
import com.floodprediction.backend.model.AlertSeverity;
import com.floodprediction.backend.model.AlertType;
import com.floodprediction.backend.model.CommunityAlert;
import com.floodprediction.backend.service.CommunityAlertService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/alerts")
@RequiredArgsConstructor
@Tag(name = "Community Alerts", description = "API for managing community flood alerts")
public class CommunityAlertController {

    private final CommunityAlertService alertService;

    @PostMapping
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Create a new alert", description = "Creates a new community alert with the provided information")
    public ResponseEntity<CommunityAlert> createAlert(
            @Valid @RequestBody CommunityAlertDto alertDto,
            Authentication authentication) {
        CommunityAlert alert = alertService.createAlert(alertDto, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @GetMapping
    @Operation(summary = "Get all alerts", description = "Retrieves all community alerts with pagination")
    public ResponseEntity<Page<CommunityAlert>> getAllAlerts(Pageable pageable) {
        Page<CommunityAlert> alerts = alertService.getAlertsPaginated(pageable);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get alert by ID", description = "Retrieves a specific community alert by its ID")
    public ResponseEntity<CommunityAlert> getAlertById(@PathVariable String id) {
        CommunityAlert alert = alertService.getAlertById(id);
        return ResponseEntity.ok(alert);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Update an alert", description = "Updates an existing community alert with new information")
    public ResponseEntity<CommunityAlert> updateAlert(
            @PathVariable String id,
            @Valid @RequestBody CommunityAlertDto alertDto,
            Authentication authentication) {
        CommunityAlert alert = alertService.updateAlert(id, alertDto, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @PostMapping("/{id}/updates")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Add update to alert", description = "Adds a new update to an existing community alert")
    public ResponseEntity<CommunityAlert> addUpdate(
            @PathVariable String id,
            @Valid @RequestBody AlertUpdateDto updateDto,
            Authentication authentication) {
        CommunityAlert alert = alertService.addUpdate(id, updateDto, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @PostMapping("/{id}/comments")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Add comment to alert", description = "Adds a new comment to an existing community alert")
    public ResponseEntity<CommunityAlert> addComment(
            @PathVariable String id,
            @Valid @RequestBody AlertCommentDto commentDto,
            Authentication authentication) {
        CommunityAlert alert = alertService.addComment(id, commentDto, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @PostMapping("/{id}/verify")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "Verify an alert", description = "Marks an alert as verified by the current user")
    public ResponseEntity<CommunityAlert> verifyAlert(
            @PathVariable String id,
            Authentication authentication) {
        CommunityAlert alert = alertService.verifyAlert(id, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @PostMapping("/{id}/resolve")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR')")
    @Operation(summary = "Resolve an alert", description = "Marks an alert as resolved")
    public ResponseEntity<CommunityAlert> resolveAlert(
            @PathVariable String id,
            Authentication authentication) {
        CommunityAlert alert = alertService.resolveAlert(id, authentication.getName());
        return ResponseEntity.ok(alert);
    }

    @GetMapping("/location")
    @Operation(summary = "Get alerts by location", description = "Retrieves alerts near a specific location")
    public ResponseEntity<List<CommunityAlert>> getAlertsByLocation(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "10") double radiusInKm) {
        List<CommunityAlert> alerts = alertService.getAlertsByLocation(latitude, longitude, radiusInKm);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/city/{city}")
    @Operation(summary = "Get active alerts by city", description = "Retrieves active alerts for a specific city")
    public ResponseEntity<List<CommunityAlert>> getActiveAlertsByCity(@PathVariable String city) {
        List<CommunityAlert> alerts = alertService.getActiveAlertsByCity(city);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/high-severity")
    @Operation(summary = "Get high severity alerts", description = "Retrieves all active high severity alerts")
    public ResponseEntity<List<CommunityAlert>> getHighSeverityActiveAlerts() {
        List<CommunityAlert> alerts = alertService.getHighSeverityActiveAlerts();
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/filter")
    @Operation(summary = "Filter alerts", description = "Retrieves alerts filtered by type and severity")
    public ResponseEntity<List<CommunityAlert>> getFilteredAlerts(
            @RequestParam AlertType alertType,
            @RequestParam AlertSeverity severity) {
        List<CommunityAlert> alerts = alertService.getActiveAlertsByTypeAndSeverity(alertType, severity);
        return ResponseEntity.ok(alerts);
    }
}
