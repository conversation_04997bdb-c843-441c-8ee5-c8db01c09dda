package com.floodprediction.backend.controller;

import com.floodprediction.backend.dto.AlertCommentDto;
import com.floodprediction.backend.dto.AlertUpdateDto;
import com.floodprediction.backend.model.CommunityAlert;
import com.floodprediction.backend.service.CommunityAlertService;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.security.Principal;

@Controller
@RequiredArgsConstructor
public class AlertWebSocketController {

    private final CommunityAlertService alertService;

    @MessageMapping("/alerts/{id}/comment")
    @SendTo("/topic/alerts/{id}/comments")
    public CommunityAlert.AlertComment addComment(
            @DestinationVariable String id,
            @Payload AlertCommentDto commentDto,
            SimpMessageHeaderAccessor headerAccessor) {
        
        Principal principal = headerAccessor.getUser();
        if (principal == null) {
            throw new IllegalStateException("User not authenticated");
        }
        
        CommunityAlert alert = alertService.addComment(id, commentDto, principal.getName());
        return alert.getComments().get(alert.getComments().size() - 1);
    }

    @MessageMapping("/alerts/{id}/update")
    @SendTo("/topic/alerts/{id}/updates")
    public CommunityAlert.AlertUpdate addUpdate(
            @DestinationVariable String id,
            @Payload AlertUpdateDto updateDto,
            SimpMessageHeaderAccessor headerAccessor) {
        
        Principal principal = headerAccessor.getUser();
        if (principal == null) {
            throw new IllegalStateException("User not authenticated");
        }
        
        CommunityAlert alert = alertService.addUpdate(id, updateDto, principal.getName());
        return alert.getUpdates().get(alert.getUpdates().size() - 1);
    }

    @MessageMapping("/alerts/{id}/verify")
    @SendTo("/topic/alerts/{id}")
    public CommunityAlert verifyAlert(
            @DestinationVariable String id,
            SimpMessageHeaderAccessor headerAccessor) {
        
        Principal principal = headerAccessor.getUser();
        if (principal == null) {
            throw new IllegalStateException("User not authenticated");
        }
        
        return alertService.verifyAlert(id, principal.getName());
    }
}
