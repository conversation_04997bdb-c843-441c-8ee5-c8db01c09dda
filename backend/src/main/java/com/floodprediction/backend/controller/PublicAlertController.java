package com.floodprediction.backend.controller;

import com.floodprediction.backend.model.AlertSeverity;
import com.floodprediction.backend.model.AlertType;
import com.floodprediction.backend.model.CommunityAlert;
import com.floodprediction.backend.service.CommunityAlertService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/public/alerts")
@RequiredArgsConstructor
@Tag(name = "Public Alerts", description = "Public API for accessing community flood alerts without authentication")
public class PublicAlertController {

    private final CommunityAlertService alertService;

    @GetMapping("/high-severity")
    @Operation(summary = "Get high severity alerts", description = "Retrieves all active high severity alerts")
    public ResponseEntity<List<CommunityAlert>> getHighSeverityActiveAlerts() {
        List<CommunityAlert> alerts = alertService.getHighSeverityActiveAlerts();
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/city/{city}")
    @Operation(summary = "Get active alerts by city", description = "Retrieves active alerts for a specific city")
    public ResponseEntity<List<CommunityAlert>> getActiveAlertsByCity(@PathVariable String city) {
        List<CommunityAlert> alerts = alertService.getActiveAlertsByCity(city);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/location")
    @Operation(summary = "Get alerts by location", description = "Retrieves alerts near a specific location")
    public ResponseEntity<List<CommunityAlert>> getAlertsByLocation(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "10") double radiusInKm) {
        List<CommunityAlert> alerts = alertService.getAlertsByLocation(latitude, longitude, radiusInKm);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/filter")
    @Operation(summary = "Filter alerts", description = "Retrieves alerts filtered by type and severity")
    public ResponseEntity<List<CommunityAlert>> getFilteredAlerts(
            @RequestParam AlertType alertType,
            @RequestParam AlertSeverity severity) {
        List<CommunityAlert> alerts = alertService.getActiveAlertsByTypeAndSeverity(alertType, severity);
        return ResponseEntity.ok(alerts);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get alert by ID", description = "Retrieves a specific community alert by its ID")
    public ResponseEntity<CommunityAlert> getAlertById(@PathVariable String id) {
        CommunityAlert alert = alertService.getAlertById(id);
        return ResponseEntity.ok(alert);
    }
}
