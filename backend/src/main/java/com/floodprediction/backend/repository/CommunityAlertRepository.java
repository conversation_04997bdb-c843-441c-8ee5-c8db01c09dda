package com.floodprediction.backend.repository;

import com.floodprediction.backend.model.AlertSeverity;
import com.floodprediction.backend.model.AlertType;
import com.floodprediction.backend.model.CommunityAlert;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CommunityAlertRepository extends MongoRepository<CommunityAlert, String> {
    
    List<CommunityAlert> findByReporter_Id(String reporterId);
    
    List<CommunityAlert> findByAlertType(AlertType alertType);
    
    List<CommunityAlert> findBySeverity(AlertSeverity severity);
    
    List<CommunityAlert> findByActiveTrue();
    
    List<CommunityAlert> findByVerifiedTrue();
    
    @Query("{'location.coordinates': {$near: {$geometry: {type: 'Point', coordinates: [?0, ?1]}, $maxDistance: ?2}}}")
    List<CommunityAlert> findByLocationNear(double longitude, double latitude, double maxDistanceInMeters);
    
    Page<CommunityAlert> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);
    
    @Query("{'location.city': ?0, 'active': true}")
    List<CommunityAlert> findActiveAlertsByCity(String city);
    
    @Query("{'severity': {$in: ['HIGH', 'CRITICAL']}, 'active': true}")
    List<CommunityAlert> findHighSeverityActiveAlerts();
    
    @Query("{'alertType': ?0, 'severity': ?1, 'active': true}")
    List<CommunityAlert> findActiveAlertsByTypeAndSeverity(AlertType alertType, AlertSeverity severity);
}
