package com.floodprediction.backend.dto;

import com.floodprediction.backend.model.AlertSeverity;
import com.floodprediction.backend.model.AlertType;
import com.floodprediction.backend.model.Location;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityAlertDto {
    
    private String id;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    @NotNull(message = "Alert type is required")
    private AlertType alertType;
    
    @NotNull(message = "Severity is required")
    private AlertSeverity severity;
    
    @NotNull(message = "Location is required")
    private Location location;
    
    @Builder.Default
    private List<String> imageUrls = new ArrayList<>();
}
