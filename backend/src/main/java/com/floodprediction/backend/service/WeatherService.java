package com.floodprediction.backend.service;

import com.floodprediction.backend.model.CommunityAlert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class WeatherService {

    private final RestTemplate restTemplate;

    /**
     * Gets current weather data for a location
     * In a real implementation, this would call a weather API
     * For now, we'll simulate the data
     */
    public CommunityAlert.WeatherData getWeatherData(double latitude, double longitude) {
        // In a real implementation, you would call a weather API like OpenWeatherMap
        // For example:
        // String apiUrl = "https://api.openweathermap.org/data/2.5/weather?lat=" + latitude + "&lon=" + longitude + "&appid=YOUR_API_KEY&units=metric";
        // ResponseEntity<Map> response = restTemplate.getForEntity(apiUrl, Map.class);
        // Map<String, Object> weatherData = response.getBody();
        
        // For now, we'll simulate the data based on location
        return simulateWeatherData(latitude, longitude);
    }
    
    private CommunityAlert.WeatherData simulateWeatherData(double latitude, double longitude) {
        // This is a simplified simulation based on location
        // In a real implementation, you would get actual weather data from an API
        
        // Create a map of Indian cities with their typical weather patterns
        Map<String, CommunityAlert.WeatherData> cityWeatherPatterns = new HashMap<>();
        
        // Mumbai
        cityWeatherPatterns.put("Mumbai", CommunityAlert.WeatherData.builder()
                .rainfall(isMonsooonSeason() ? 25.0 : 2.0)
                .waterLevel(isMonsooonSeason() ? 3.5 : 1.2)
                .temperature(30.0)
                .humidity(85.0)
                .weatherCondition(isMonsooonSeason() ? "Heavy Rain" : "Partly Cloudy")
                .build());
        
        // Delhi
        cityWeatherPatterns.put("Delhi", CommunityAlert.WeatherData.builder()
                .rainfall(isMonsooonSeason() ? 15.0 : 0.5)
                .waterLevel(isMonsooonSeason() ? 2.8 : 1.0)
                .temperature(35.0)
                .humidity(65.0)
                .weatherCondition(isMonsooonSeason() ? "Moderate Rain" : "Clear")
                .build());
        
        // Chennai
        cityWeatherPatterns.put("Chennai", CommunityAlert.WeatherData.builder()
                .rainfall(isMonsooonSeason() ? 20.0 : 1.0)
                .waterLevel(isMonsooonSeason() ? 3.2 : 1.1)
                .temperature(32.0)
                .humidity(80.0)
                .weatherCondition(isMonsooonSeason() ? "Heavy Rain" : "Partly Cloudy")
                .build());
        
        // Kolkata
        cityWeatherPatterns.put("Kolkata", CommunityAlert.WeatherData.builder()
                .rainfall(isMonsooonSeason() ? 22.0 : 1.5)
                .waterLevel(isMonsooonSeason() ? 3.0 : 1.3)
                .temperature(31.0)
                .humidity(82.0)
                .weatherCondition(isMonsooonSeason() ? "Heavy Rain" : "Cloudy")
                .build());
        
        // Bangalore
        cityWeatherPatterns.put("Bangalore", CommunityAlert.WeatherData.builder()
                .rainfall(isMonsooonSeason() ? 12.0 : 0.8)
                .waterLevel(isMonsooonSeason() ? 2.0 : 0.9)
                .temperature(26.0)
                .humidity(70.0)
                .weatherCondition(isMonsooonSeason() ? "Light Rain" : "Clear")
                .build());
        
        // Find the closest city based on latitude and longitude
        String closestCity = findClosestCity(latitude, longitude);
        
        // Get the weather pattern for the closest city
        CommunityAlert.WeatherData baseWeatherData = cityWeatherPatterns.getOrDefault(closestCity, 
                CommunityAlert.WeatherData.builder()
                        .rainfall(5.0)
                        .waterLevel(1.5)
                        .temperature(28.0)
                        .humidity(75.0)
                        .weatherCondition("Partly Cloudy")
                        .build());
        
        // Add some randomness to make it more realistic
        return CommunityAlert.WeatherData.builder()
                .rainfall(baseWeatherData.getRainfall() + (Math.random() * 5 - 2.5))
                .waterLevel(baseWeatherData.getWaterLevel() + (Math.random() * 0.5 - 0.25))
                .temperature(baseWeatherData.getTemperature() + (Math.random() * 4 - 2))
                .humidity(baseWeatherData.getHumidity() + (Math.random() * 10 - 5))
                .weatherCondition(baseWeatherData.getWeatherCondition())
                .build();
    }
    
    private boolean isMonsooonSeason() {
        // Simplified check for monsoon season (June to September)
        int month = java.time.LocalDate.now().getMonthValue();
        return month >= 6 && month <= 9;
    }
    
    private String findClosestCity(double latitude, double longitude) {
        // Simplified city coordinates
        Map<String, double[]> cityCoordinates = new HashMap<>();
        cityCoordinates.put("Mumbai", new double[]{19.0760, 72.8777});
        cityCoordinates.put("Delhi", new double[]{28.6139, 77.2090});
        cityCoordinates.put("Chennai", new double[]{13.0827, 80.2707});
        cityCoordinates.put("Kolkata", new double[]{22.5726, 88.3639});
        cityCoordinates.put("Bangalore", new double[]{12.9716, 77.5946});
        
        String closestCity = "Mumbai"; // Default
        double minDistance = Double.MAX_VALUE;
        
        for (Map.Entry<String, double[]> entry : cityCoordinates.entrySet()) {
            double cityLat = entry.getValue()[0];
            double cityLon = entry.getValue()[1];
            
            double distance = calculateDistance(latitude, longitude, cityLat, cityLon);
            
            if (distance < minDistance) {
                minDistance = distance;
                closestCity = entry.getKey();
            }
        }
        
        return closestCity;
    }
    
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // Haversine formula to calculate distance between two points on Earth
        double R = 6371; // Earth's radius in km
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
}
