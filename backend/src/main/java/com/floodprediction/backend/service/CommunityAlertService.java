package com.floodprediction.backend.service;

import com.floodprediction.backend.dto.AlertCommentDto;
import com.floodprediction.backend.dto.AlertUpdateDto;
import com.floodprediction.backend.dto.CommunityAlertDto;
import com.floodprediction.backend.exception.ResourceNotFoundException;
import com.floodprediction.backend.model.AlertSeverity;
import com.floodprediction.backend.model.AlertType;
import com.floodprediction.backend.model.CommunityAlert;
import com.floodprediction.backend.model.User;
import com.floodprediction.backend.repository.CommunityAlertRepository;
import com.floodprediction.backend.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommunityAlertService {

    private final CommunityAlertRepository alertRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;
    private final WeatherService weatherService;

    @Transactional
    public CommunityAlert createAlert(CommunityAlertDto alertDto, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        CommunityAlert alert = CommunityAlert.builder()
                .title(alertDto.getTitle())
                .description(alertDto.getDescription())
                .alertType(alertDto.getAlertType())
                .severity(alertDto.getSeverity())
                .location(alertDto.getLocation())
                .reporter(user)
                .imageUrls(alertDto.getImageUrls())
                .active(true)
                .build();

        // Fetch current weather data for the location
        try {
            CommunityAlert.WeatherData weatherData = weatherService.getWeatherData(
                    alertDto.getLocation().getCoordinates()[1], // latitude
                    alertDto.getLocation().getCoordinates()[0]  // longitude
            );
            alert.setWeatherData(weatherData);
        } catch (Exception e) {
            log.error("Failed to fetch weather data for alert", e);
        }

        CommunityAlert savedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the new alert
        messagingTemplate.convertAndSend("/topic/alerts", savedAlert);
        
        // Send targeted notifications to users in the same area
        messagingTemplate.convertAndSend("/topic/alerts/city/" + alert.getLocation().getCity(), savedAlert);
        
        return savedAlert;
    }

    @Transactional(readOnly = true)
    public List<CommunityAlert> getAllAlerts() {
        return alertRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<CommunityAlert> getAlertsPaginated(Pageable pageable) {
        return alertRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public CommunityAlert getAlertById(String id) {
        return alertRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + id));
    }

    @Transactional
    public CommunityAlert updateAlert(String id, CommunityAlertDto alertDto, String username) {
        CommunityAlert alert = alertRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + id));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        alert.setTitle(alertDto.getTitle());
        alert.setDescription(alertDto.getDescription());
        alert.setAlertType(alertDto.getAlertType());
        alert.setSeverity(alertDto.getSeverity());
        alert.setLocation(alertDto.getLocation());
        alert.setImageUrls(alertDto.getImageUrls());
        
        // Add update history
        CommunityAlert.AlertUpdate update = CommunityAlert.AlertUpdate.builder()
                .updateText("Alert information updated")
                .timestamp(LocalDateTime.now())
                .updatedBy(user)
                .build();
        
        alert.getUpdates().add(update);
        
        CommunityAlert updatedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the updated alert
        messagingTemplate.convertAndSend("/topic/alerts/" + id, updatedAlert);
        
        return updatedAlert;
    }

    @Transactional
    public CommunityAlert addUpdate(String alertId, AlertUpdateDto updateDto, String username) {
        CommunityAlert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + alertId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        CommunityAlert.AlertUpdate update = CommunityAlert.AlertUpdate.builder()
                .updateText(updateDto.getUpdateText())
                .timestamp(LocalDateTime.now())
                .updatedBy(user)
                .build();
        
        alert.getUpdates().add(update);
        
        CommunityAlert updatedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the alert update
        messagingTemplate.convertAndSend("/topic/alerts/" + alertId + "/updates", update);
        
        return updatedAlert;
    }

    @Transactional
    public CommunityAlert addComment(String alertId, AlertCommentDto commentDto, String username) {
        CommunityAlert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + alertId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        CommunityAlert.AlertComment comment = CommunityAlert.AlertComment.builder()
                .id(UUID.randomUUID().toString())
                .text(commentDto.getText())
                .user(user)
                .timestamp(LocalDateTime.now())
                .build();
        
        alert.getComments().add(comment);
        
        CommunityAlert updatedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the new comment
        messagingTemplate.convertAndSend("/topic/alerts/" + alertId + "/comments", comment);
        
        return updatedAlert;
    }

    @Transactional
    public CommunityAlert verifyAlert(String alertId, String username) {
        CommunityAlert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + alertId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        // Increment verification count
        alert.setVerificationCount(alert.getVerificationCount() + 1);
        
        // If verification count reaches threshold, mark as verified
        if (alert.getVerificationCount() >= 3) {
            alert.setVerified(true);
        }
        
        // Add update about verification
        CommunityAlert.AlertUpdate update = CommunityAlert.AlertUpdate.builder()
                .updateText("Alert verified by " + user.getName())
                .timestamp(LocalDateTime.now())
                .updatedBy(user)
                .build();
        
        alert.getUpdates().add(update);
        
        CommunityAlert updatedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the verification
        messagingTemplate.convertAndSend("/topic/alerts/" + alertId, updatedAlert);
        
        return updatedAlert;
    }

    @Transactional
    public CommunityAlert resolveAlert(String alertId, String username) {
        CommunityAlert alert = alertRepository.findById(alertId)
                .orElseThrow(() -> new ResourceNotFoundException("Alert not found with id: " + alertId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));

        alert.setActive(false);
        alert.setResolvedAt(LocalDateTime.now());
        
        // Add update about resolution
        CommunityAlert.AlertUpdate update = CommunityAlert.AlertUpdate.builder()
                .updateText("Alert marked as resolved by " + user.getName())
                .timestamp(LocalDateTime.now())
                .updatedBy(user)
                .build();
        
        alert.getUpdates().add(update);
        
        CommunityAlert updatedAlert = alertRepository.save(alert);
        
        // Notify subscribers about the resolution
        messagingTemplate.convertAndSend("/topic/alerts/" + alertId, updatedAlert);
        
        return updatedAlert;
    }

    @Transactional(readOnly = true)
    public List<CommunityAlert> getAlertsByLocation(double latitude, double longitude, double radiusInKm) {
        // Convert km to meters
        double radiusInMeters = radiusInKm * 1000;
        return alertRepository.findByLocationNear(longitude, latitude, radiusInMeters);
    }

    @Transactional(readOnly = true)
    public List<CommunityAlert> getActiveAlertsByCity(String city) {
        return alertRepository.findActiveAlertsByCity(city);
    }

    @Transactional(readOnly = true)
    public List<CommunityAlert> getHighSeverityActiveAlerts() {
        return alertRepository.findHighSeverityActiveAlerts();
    }

    @Transactional(readOnly = true)
    public List<CommunityAlert> getActiveAlertsByTypeAndSeverity(AlertType alertType, AlertSeverity severity) {
        return alertRepository.findActiveAlertsByTypeAndSeverity(alertType, severity);
    }
}
