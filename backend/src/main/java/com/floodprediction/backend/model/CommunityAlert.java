package com.floodprediction.backend.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "community_alerts")
public class CommunityAlert {
    
    @Id
    private String id;
    
    @NotBlank
    private String title;
    
    @NotBlank
    private String description;
    
    @NotNull
    private AlertType alertType;
    
    @NotNull
    private AlertSeverity severity;
    
    @NotNull
    private Location location;
    
    @DBRef
    private User reporter;
    
    @Builder.Default
    private List<String> imageUrls = new ArrayList<>();
    
    @Builder.Default
    private int verificationCount = 0;
    
    @Builder.Default
    private boolean verified = false;
    
    @Builder.Default
    private boolean active = true;
    
    @Builder.Default
    private List<AlertUpdate> updates = new ArrayList<>();
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private LocalDateTime resolvedAt;
    
    @Builder.Default
    private WeatherData weatherData = new WeatherData();
    
    @Builder.Default
    private List<AlertComment> comments = new ArrayList<>();
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AlertUpdate {
        private String updateText;
        private LocalDateTime timestamp;
        @DBRef
        private User updatedBy;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AlertComment {
        private String id;
        private String text;
        @DBRef
        private User user;
        private LocalDateTime timestamp;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WeatherData {
        private Double rainfall; // in mm
        private Double waterLevel; // in meters
        private Double temperature; // in Celsius
        private Double humidity; // percentage
        private String weatherCondition; // e.g., "Heavy Rain", "Clear"
    }
}
