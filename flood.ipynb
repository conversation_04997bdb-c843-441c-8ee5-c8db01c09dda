import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import accuracy_score
from sklearn.metrics import classification_report, confusion_matrix
import joblib

df = pd.read_csv("flood_risk_dataset_india.csv") 
df.head()

df.shape

df.info()

dflandcover=df['Land Cover']

count=dflandcover.value_counts()
explode = [0.1 if i == 0 else 0 for i in range (len(count))]
plt.figure(figsize=(10,6))
plt.pie(count,labels=count.index,autopct='%1.1f%%', startangle=90,shadow=True,explode=explode)
plt.title('Proportions of Land Covers')
plt.show()

dfsoil=df['Soil Type']

count=dfsoil.value_counts()
explode = [0.1 if i == 0 else 0 for i in range (len(count))]
plt.figure(figsize=(10,6))
plt.pie(count,labels=count.index,autopct='%1.1f%%', startangle=90,shadow=True,explode=explode)
plt.title('Proportions of Soil Types')
plt.show()

number = df[['Rainfall (mm)','Temperature (°C)','Humidity (%)','River Discharge (m³/s)','Water Level (m)','Elevation (m)','Population Density']]
plt.figure(figsize=(10,8))
plt.title("Correlation Map")
sns.heatmap(number.corr(),annot=True, fmt='.2f')

# Encode categorical features
label_encoders = {}
for col in ['Land Cover', 'Soil Type']:
    le = LabelEncoder()
    df[col] = le.fit_transform(df[col])
    label_encoders[col] = le

# Handle missing values if any
df = df.dropna()  # or use fillna()
# Features and target
X = df.drop(columns=['Flood Occurred', 'Latitude', 'Longitude'])
y = df['Flood Occurred']

# Scale the features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.1, random_state=42)

model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)
# Evaluate
y_pred = model.predict(X_test)
rand_for_acc= accuracy_score(y_test,y_pred) * 100
print(rand_for_acc)

model_2 = LogisticRegression()
model_2.fit(X_train,y_train)
y_pred_2 = model_2.predict(X_test)
log_reg_acc = accuracy_score(y_test,y_pred_2) * 100 
print(log_reg_acc)

model_3 = GaussianNB()
model_3.fit(X_train,y_train)
y_pred_3 = model_3.predict(X_test)
g_acc  = accuracy_score(y_test,y_pred_2) * 100
print(g_acc)

model_4 = DecisionTreeClassifier()
model_4.fit(X_train,y_train)
y_pred_4 = model_4.predict(X_test)
dc_acc = accuracy_score(y_test,y_pred_4) * 100
print(dc_acc)

import plotly.graph_objects as go
models = ['Random Forest', 'Logistic Regression', 'GaussianNB', 'Decision Tree']
accuracies = [rand_for_acc, log_reg_acc, g_acc, dc_acc]
fig = go.Figure()

fig.add_trace(go.Scatter(
    x=models,
    y=accuracies,
    mode='lines+markers+text',
    text=[f'{acc:.2f}%' for acc in accuracies],
    textposition='top center',
    line=dict(color='royalblue', width=3),
    marker=dict(size=10)
))

joblib.dump(model, 'flood_model.pkl')
joblib.dump(scaler, 'scaler.pkl')
joblib.dump(label_encoders, 'label_encoders.pkl')

def predict_flood(input_data):
    """
    input_data: dict with keys matching the features
    Returns prediction: 1 = flood, 0 = no flood
    """
    # Encode categorical
    for col in ['Land Cover', 'Soil Type']:
        input_data[col] = label_encoders[col].transform([input_data[col]])[0]

    # Convert to DataFrame
    input_df = pd.DataFrame([input_data])

    # Scale
    input_scaled = scaler.transform(input_df)

    # Predict
    prediction = model.predict(input_scaled)[0]
    return "Flood Likely ⚠️" if prediction == 1 else "No Flood ✅"


sample = {
    'Rainfall (mm)': 150,
    'Temperature (°C)': 30,
    'Humidity (%)': 85,
    'River Discharge (m³/s)': 800,
    'Water Level (m)': 6.5,
    'Elevation (m)': 150,
    'Land Cover': 'Urban',
    'Soil Type': 'Clay',
    'Population Density': 1200,
    'Infrastructure': 1,
    'Historical Floods': 1
}

predict_flood(sample)

import numpy as np
print(np.__version__)

from lime.lime_tabular import LimeTabularExplainer

explainer = LimeTabularExplainer(
    training_data=X_train,
    feature_names=X.columns,
    class_names=['No Flood', 'Flood'],
    mode='classification'
)
# Pick a test instance to explain
i = 0  
exp = explainer.explain_instance(
    data_row=X_test[i],
    predict_fn=model.predict_proba,
    num_features=10
)
exp.show_in_notebook(show_table=True, show_all=False)