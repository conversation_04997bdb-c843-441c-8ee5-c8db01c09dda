"""
India Flood Risk Database

This module contains historical flood data and risk factors for major Indian cities.
Data is compiled from multiple sources including:
- India Meteorological Department (IMD)
- Central Water Commission (CWC)
- National Disaster Management Authority (NDMA)
- Research papers on flood vulnerability in India
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

# Ensure the data directory exists
os.makedirs('data', exist_ok=True)

# Historical major flood events in India (city-wise)
# Format: City, State, Year, Month, Severity (1-10), Affected Area (sq km), Casualties, Economic Loss (Crores INR)
HISTORICAL_FLOODS = [
    # North India
    ("Delhi", "Delhi", 2013, 6, 7, 150, 35, 1200),
    ("Delhi", "Delhi", 2010, 7, 6, 120, 25, 800),
    ("Delhi", "Delhi", 2018, 7, 5, 100, 15, 600),
    
    # Mumbai and Maharashtra
    ("Mumbai", "Maharashtra", 2005, 7, 10, 450, 1094, 28000),
    ("Mumbai", "Maharashtra", 2017, 8, 8, 350, 14, 3500),
    ("Mumbai", "Maharashtra", 2019, 7, 7, 300, 32, 2500),
    ("Pune", "Maharashtra", 2019, 8, 6, 120, 24, 1500),
    ("Kolhapur", "Maharashtra", 2019, 8, 8, 200, 43, 2000),
    ("Sangli", "Maharashtra", 2019, 8, 8, 180, 40, 1800),
    
    # Gujarat
    ("Ahmedabad", "Gujarat", 2017, 7, 7, 200, 45, 1500),
    ("Vadodara", "Gujarat", 2019, 8, 8, 150, 17, 1200),
    ("Surat", "Gujarat", 2006, 8, 9, 250, 150, 3500),
    
    # East India
    ("Kolkata", "West Bengal", 2007, 7, 7, 300, 50, 2000),
    ("Kolkata", "West Bengal", 2017, 9, 6, 250, 25, 1500),
    ("Patna", "Bihar", 2019, 9, 9, 350, 73, 3500),
    ("Patna", "Bihar", 2017, 8, 8, 300, 65, 3000),
    ("Patna", "Bihar", 2016, 8, 7, 280, 40, 2500),
    ("Guwahati", "Assam", 2022, 6, 9, 400, 192, 4500),
    ("Guwahati", "Assam", 2020, 7, 8, 350, 110, 3800),
    ("Guwahati", "Assam", 2017, 7, 8, 320, 85, 3200),
    
    # South India
    ("Chennai", "Tamil Nadu", 2015, 12, 10, 500, 470, 20000),
    ("Chennai", "Tamil Nadu", 2005, 10, 7, 300, 100, 5000),
    ("Chennai", "Tamil Nadu", 2021, 11, 6, 250, 20, 3000),
    ("Hyderabad", "Telangana", 2020, 10, 8, 250, 50, 5000),
    ("Hyderabad", "Telangana", 2016, 9, 6, 180, 15, 2000),
    ("Kochi", "Kerala", 2018, 8, 10, 400, 483, 20000),
    ("Kochi", "Kerala", 2019, 8, 7, 250, 121, 8000),
    ("Bengaluru", "Karnataka", 2022, 9, 7, 200, 5, 1500),
    ("Bengaluru", "Karnataka", 2017, 8, 5, 150, 3, 1000),
    
    # Central India
    ("Bhopal", "Madhya Pradesh", 2019, 9, 6, 150, 25, 1200),
    ("Bhopal", "Madhya Pradesh", 2016, 7, 5, 120, 15, 900),
    ("Lucknow", "Uttar Pradesh", 2018, 8, 7, 200, 40, 1800),
    ("Lucknow", "Uttar Pradesh", 2021, 9, 6, 180, 30, 1500),
    ("Varanasi", "Uttar Pradesh", 2019, 9, 8, 150, 35, 1200),
    ("Varanasi", "Uttar Pradesh", 2013, 8, 9, 180, 42, 1500),
]

# Geographical and environmental risk factors for major Indian cities
# Format: City, State, Elevation (m), River Proximity (1-10), Drainage Quality (1-10),
#         Avg Annual Rainfall (mm), Flood Plain Area (%), Urbanization Level (1-10)
CITY_RISK_FACTORS = [
    # North India
    ("Delhi", "Delhi", 216, 8, 5, 797, 15, 9),
    ("Chandigarh", "Chandigarh", 321, 5, 7, 1059, 8, 8),
    ("Jaipur", "Rajasthan", 431, 3, 6, 650, 5, 7),
    ("Shimla", "Himachal Pradesh", 2276, 4, 5, 1575, 7, 6),
    ("Dehradun", "Uttarakhand", 435, 7, 5, 2073, 12, 6),
    
    # Mumbai and Maharashtra
    ("Mumbai", "Maharashtra", 14, 9, 4, 2386, 30, 10),
    ("Pune", "Maharashtra", 560, 7, 6, 722, 15, 8),
    ("Nagpur", "Maharashtra", 310, 6, 5, 1064, 10, 7),
    ("Kolhapur", "Maharashtra", 569, 8, 4, 1025, 20, 6),
    ("Sangli", "Maharashtra", 549, 9, 3, 691, 25, 6),
    
    # Gujarat
    ("Ahmedabad", "Gujarat", 53, 8, 5, 782, 18, 9),
    ("Vadodara", "Gujarat", 39, 9, 4, 933, 22, 8),
    ("Surat", "Gujarat", 13, 9, 3, 1188, 28, 8),
    ("Rajkot", "Gujarat", 128, 6, 5, 621, 12, 7),
    
    # East India
    ("Kolkata", "West Bengal", 9, 10, 3, 1582, 35, 10),
    ("Patna", "Bihar", 53, 10, 2, 1100, 40, 7),
    ("Guwahati", "Assam", 55, 10, 3, 1698, 45, 7),
    ("Bhubaneswar", "Odisha", 45, 8, 4, 1498, 25, 7),
    ("Ranchi", "Jharkhand", 644, 5, 5, 1413, 10, 6),
    
    # South India
    ("Chennai", "Tamil Nadu", 7, 8, 4, 1400, 30, 9),
    ("Hyderabad", "Telangana", 505, 7, 5, 812, 15, 9),
    ("Bengaluru", "Karnataka", 920, 5, 6, 831, 12, 10),
    ("Kochi", "Kerala", 2, 9, 4, 3005, 35, 8),
    ("Thiruvananthapuram", "Kerala", 10, 7, 5, 1827, 20, 7),
    ("Coimbatore", "Tamil Nadu", 411, 6, 6, 618, 10, 8),
    
    # Central India
    ("Bhopal", "Madhya Pradesh", 500, 6, 5, 1146, 12, 7),
    ("Indore", "Madhya Pradesh", 553, 5, 6, 945, 8, 8),
    ("Lucknow", "Uttar Pradesh", 123, 8, 4, 896, 20, 8),
    ("Varanasi", "Uttar Pradesh", 80, 10, 3, 1026, 30, 7),
    ("Raipur", "Chhattisgarh", 298, 6, 5, 1292, 15, 6),
]

# Seasonal rainfall patterns (monthly averages in mm)
# Format: City, Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec
RAINFALL_PATTERNS = [
    # North India
    ("Delhi", 19, 20, 13, 8, 19, 65, 211, 173, 117, 10, 3, 8),
    ("Chandigarh", 43, 36, 30, 21, 31, 85, 311, 307, 143, 27, 9, 22),
    ("Jaipur", 7, 10, 5, 4, 18, 76, 215, 200, 84, 10, 3, 3),
    
    # Mumbai and Maharashtra
    ("Mumbai", 0, 1, 1, 1, 12, 523, 799, 529, 312, 74, 8, 2),
    ("Pune", 1, 1, 2, 15, 39, 137, 181, 132, 144, 66, 21, 4),
    ("Nagpur", 10, 11, 17, 11, 13, 163, 295, 246, 186, 43, 10, 5),
    
    # Gujarat
    ("Ahmedabad", 2, 1, 1, 1, 7, 86, 307, 213, 108, 5, 3, 2),
    ("Vadodara", 2, 1, 1, 2, 10, 124, 366, 228, 138, 22, 5, 2),
    ("Surat", 1, 1, 0, 1, 12, 207, 494, 258, 148, 32, 12, 2),
    
    # East India
    ("Kolkata", 11, 21, 34, 54, 134, 297, 325, 328, 252, 114, 19, 4),
    ("Patna", 15, 11, 6, 8, 27, 133, 311, 292, 194, 65, 4, 3),
    ("Guwahati", 12, 22, 62, 184, 294, 315, 351, 284, 177, 87, 17, 5),
    
    # South India
    ("Chennai", 25, 13, 3, 14, 55, 64, 99, 142, 132, 294, 374, 135),
    ("Hyderabad", 3, 6, 13, 24, 39, 106, 171, 168, 163, 85, 23, 5),
    ("Bengaluru", 2, 9, 12, 44, 107, 86, 114, 137, 175, 126, 59, 16),
    ("Kochi", 21, 28, 40, 107, 284, 649, 418, 323, 270, 358, 200, 50),
    
    # Central India
    ("Bhopal", 15, 11, 5, 3, 10, 146, 366, 356, 170, 34, 9, 5),
    ("Lucknow", 19, 12, 6, 4, 14, 88, 257, 274, 177, 34, 3, 4),
    ("Varanasi", 19, 11, 5, 4, 13, 104, 276, 262, 194, 38, 5, 4),
]

# River systems and their flood impact on cities
# Format: River Name, Cities Affected, Flood Frequency (1-10), Severity Impact (1-10)
RIVER_FLOOD_IMPACT = [
    ("Yamuna", ["Delhi", "Agra", "Mathura"], 7, 8),
    ("Ganges", ["Varanasi", "Patna", "Kolkata", "Haridwar"], 8, 9),
    ("Brahmaputra", ["Guwahati", "Dibrugarh"], 9, 10),
    ("Mithi", ["Mumbai"], 8, 9),
    ("Sabarmati", ["Ahmedabad"], 6, 7),
    ("Musi", ["Hyderabad"], 5, 7),
    ("Adyar", ["Chennai"], 7, 9),
    ("Cooum", ["Chennai"], 7, 8),
    ("Kosi", ["Patna", "Purnia"], 9, 10),
    ("Krishna", ["Sangli", "Kolhapur", "Vijayawada"], 7, 8),
    ("Godavari", ["Nashik", "Rajahmundry"], 7, 8),
    ("Narmada", ["Jabalpur", "Bharuch"], 6, 7),
    ("Tapi", ["Surat"], 7, 8),
    ("Periyar", ["Kochi"], 8, 9),
    ("Cauvery", ["Tiruchirappalli", "Thanjavur"], 6, 7),
]

# Climate change impact projections (2030)
# Format: Region, Rainfall Increase (%), Extreme Event Frequency Increase (%), Sea Level Rise Impact (1-10)
CLIMATE_CHANGE_PROJECTIONS = [
    ("North India", 15, 30, 2),
    ("Western India", 12, 35, 8),
    ("Eastern India", 18, 40, 7),
    ("Southern India", 10, 25, 6),
    ("Central India", 14, 30, 1),
    ("Northeastern India", 20, 45, 5),
    ("Coastal Areas", 15, 40, 9),
]

def create_city_flood_risk_database():
    """Create a comprehensive database of flood risk for Indian cities"""
    
    # Convert historical floods to DataFrame
    historical_df = pd.DataFrame(HISTORICAL_FLOODS, 
                                columns=["City", "State", "Year", "Month", "Severity", 
                                         "Affected_Area_sqkm", "Casualties", "Economic_Loss_Crores"])
    
    # Convert city risk factors to DataFrame
    risk_factors_df = pd.DataFrame(CITY_RISK_FACTORS,
                                  columns=["City", "State", "Elevation_m", "River_Proximity", 
                                           "Drainage_Quality", "Avg_Annual_Rainfall_mm", 
                                           "Flood_Plain_Area_Percent", "Urbanization_Level"])
    
    # Convert rainfall patterns to DataFrame
    months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    rainfall_df = pd.DataFrame(RAINFALL_PATTERNS, columns=["City"] + months)
    
    # Calculate aggregate statistics for each city from historical data
    city_stats = []
    
    for city in risk_factors_df["City"].unique():
        # Filter historical floods for this city
        city_floods = historical_df[historical_df["City"] == city]
        
        if len(city_floods) > 0:
            avg_severity = city_floods["Severity"].mean()
            max_severity = city_floods["Severity"].max()
            flood_frequency = len(city_floods) / (2023 - 2000)  # Events per year since 2000
            avg_affected_area = city_floods["Affected_Area_sqkm"].mean()
            total_casualties = city_floods["Casualties"].sum()
            total_economic_loss = city_floods["Economic_Loss_Crores"].sum()
            last_major_flood = city_floods["Year"].max()
        else:
            avg_severity = 0
            max_severity = 0
            flood_frequency = 0
            avg_affected_area = 0
            total_casualties = 0
            total_economic_loss = 0
            last_major_flood = None
        
        # Get risk factors for this city
        city_risk = risk_factors_df[risk_factors_df["City"] == city]
        if len(city_risk) > 0:
            elevation = city_risk["Elevation_m"].values[0]
            river_proximity = city_risk["River_Proximity"].values[0]
            drainage_quality = city_risk["Drainage_Quality"].values[0]
            avg_rainfall = city_risk["Avg_Annual_Rainfall_mm"].values[0]
            flood_plain_area = city_risk["Flood_Plain_Area_Percent"].values[0]
            urbanization = city_risk["Urbanization_Level"].values[0]
            state = city_risk["State"].values[0]
        else:
            elevation = None
            river_proximity = None
            drainage_quality = None
            avg_rainfall = None
            flood_plain_area = None
            urbanization = None
            state = None
        
        # Get rainfall pattern for this city
        city_rainfall = rainfall_df[rainfall_df["City"] == city]
        if len(city_rainfall) > 0:
            monsoon_rainfall = city_rainfall[["Jun", "Jul", "Aug", "Sep"]].values[0].sum()
            max_monthly_rainfall = city_rainfall[months].values[0].max()
            rainfall_variability = city_rainfall[months].values[0].std()
        else:
            monsoon_rainfall = None
            max_monthly_rainfall = None
            rainfall_variability = None
        
        # Find rivers affecting this city
        affecting_rivers = []
        for river, cities, freq, severity in RIVER_FLOOD_IMPACT:
            if city in cities:
                affecting_rivers.append((river, freq, severity))
        
        river_flood_risk = 0
        if affecting_rivers:
            river_flood_risk = max([freq * sev for _, freq, sev in affecting_rivers]) / 100
        
        # Find climate change projection for this region
        climate_projection = None
        if state:
            if state in ["Delhi", "Uttar Pradesh", "Haryana", "Punjab", "Himachal Pradesh", "Uttarakhand"]:
                climate_projection = "North India"
            elif state in ["Maharashtra", "Gujarat", "Rajasthan"]:
                climate_projection = "Western India"
            elif state in ["West Bengal", "Bihar", "Odisha", "Jharkhand"]:
                climate_projection = "Eastern India"
            elif state in ["Tamil Nadu", "Kerala", "Karnataka", "Andhra Pradesh", "Telangana"]:
                climate_projection = "Southern India"
            elif state in ["Madhya Pradesh", "Chhattisgarh"]:
                climate_projection = "Central India"
            elif state in ["Assam", "Meghalaya", "Tripura", "Manipur", "Nagaland", "Arunachal Pradesh", "Sikkim"]:
                climate_projection = "Northeastern India"
        
        climate_risk = 0
        if climate_projection:
            for region, rainfall_inc, extreme_inc, sea_level in CLIMATE_CHANGE_PROJECTIONS:
                if region == climate_projection:
                    climate_risk = (rainfall_inc + extreme_inc) / 100
                    break
                elif region == "Coastal Areas" and elevation < 20:
                    coastal_risk = sea_level / 10
                    climate_risk = max(climate_risk, coastal_risk)
        
        # Calculate overall flood risk score (0-100)
        base_risk = 0
        if avg_severity > 0:  # If there's historical data
            historical_risk = (avg_severity / 10) * 0.3 + (flood_frequency / 1) * 0.2
            base_risk += historical_risk * 40  # Historical data contributes 40%
        
        geographical_risk = 0
        if elevation is not None:
            # Lower elevation means higher risk
            elevation_risk = max(0, (100 - min(elevation, 100)) / 100)
            geographical_risk += elevation_risk * 0.3
        
        if river_proximity is not None:
            geographical_risk += (river_proximity / 10) * 0.3
        
        if flood_plain_area is not None:
            geographical_risk += (flood_plain_area / 100) * 0.4
        
        base_risk += geographical_risk * 30  # Geographical factors contribute 30%
        
        infrastructure_risk = 0
        if drainage_quality is not None:
            # Lower drainage quality means higher risk
            infrastructure_risk += ((10 - drainage_quality) / 10) * 0.5
        
        if urbanization is not None:
            infrastructure_risk += (urbanization / 10) * 0.5
        
        base_risk += infrastructure_risk * 15  # Infrastructure contributes 15%
        
        # Climate and rainfall
        climate_rainfall_risk = 0
        if avg_rainfall is not None:
            rainfall_risk = min(avg_rainfall / 3000, 1)  # Normalize to 0-1
            climate_rainfall_risk += rainfall_risk * 0.4
        
        if rainfall_variability is not None:
            variability_risk = min(rainfall_variability / 200, 1)  # Normalize to 0-1
            climate_rainfall_risk += variability_risk * 0.2
        
        climate_rainfall_risk += river_flood_risk * 0.2
        climate_rainfall_risk += climate_risk * 0.2
        
        base_risk += climate_rainfall_risk * 15  # Climate and rainfall contribute 15%
        
        # Final risk score (0-100)
        flood_risk_score = min(round(base_risk), 100)
        
        city_stats.append({
            "City": city,
            "State": state,
            "Historical_Avg_Severity": round(avg_severity, 2),
            "Historical_Max_Severity": max_severity,
            "Flood_Frequency_Per_Year": round(flood_frequency, 2),
            "Avg_Affected_Area_sqkm": round(avg_affected_area, 2),
            "Total_Casualties_Since_2000": total_casualties,
            "Total_Economic_Loss_Crores": total_economic_loss,
            "Last_Major_Flood": last_major_flood,
            "Elevation_m": elevation,
            "River_Proximity_Score": river_proximity,
            "Drainage_Quality_Score": drainage_quality,
            "Avg_Annual_Rainfall_mm": avg_rainfall,
            "Monsoon_Rainfall_mm": monsoon_rainfall,
            "Max_Monthly_Rainfall_mm": max_monthly_rainfall,
            "Rainfall_Variability": round(rainfall_variability, 2) if rainfall_variability is not None else None,
            "Flood_Plain_Area_Percent": flood_plain_area,
            "Urbanization_Level": urbanization,
            "Affecting_Rivers": [river for river, _, _ in affecting_rivers],
            "River_Flood_Risk": round(river_flood_risk * 10, 2),
            "Climate_Change_Risk": round(climate_risk * 10, 2),
            "Flood_Risk_Score": flood_risk_score
        })
    
    # Create final DataFrame
    city_flood_risk_df = pd.DataFrame(city_stats)
    
    # Save to CSV
    city_flood_risk_df.to_csv('data/india_city_flood_risk.csv', index=False)
    
    return city_flood_risk_df

if __name__ == "__main__":
    # Create the database
    flood_risk_db = create_city_flood_risk_database()
    print(f"Created flood risk database with {len(flood_risk_db)} Indian cities.")
    print(flood_risk_db[["City", "State", "Flood_Risk_Score"]].sort_values(by="Flood_Risk_Score", ascending=False).head(10))
