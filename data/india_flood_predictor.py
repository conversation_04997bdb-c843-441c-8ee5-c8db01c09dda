"""
India Flood Risk Predictor

This module provides functions to predict flood risk for Indian cities based on real data.
It uses historical flood data, geographical factors, and current weather conditions
to generate accurate flood risk assessments.
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
import requests
from pathlib import Path
import random

# Ensure data directory exists
os.makedirs('data', exist_ok=True)

# Check if the flood risk database exists, if not create it
FLOOD_RISK_DB_PATH = 'data/india_city_flood_risk.csv'

if not os.path.exists(FLOOD_RISK_DB_PATH):
    from data.india_flood_data import create_city_flood_risk_database
    flood_risk_db = create_city_flood_risk_database()
else:
    flood_risk_db = pd.read_csv(FLOOD_RISK_DB_PATH)

# Weather API configuration
OPENWEATHER_API_KEY = os.environ.get('OPENWEATHER_API_KEY', '********************************')

# Indian cities with coordinates (latitude, longitude)
INDIAN_CITIES_COORDS = {
    "Mumbai": (19.0760, 72.8777),
    "Delhi": (28.6139, 77.2090),
    "Bengaluru": (12.9716, 77.5946),
    "Hyderabad": (17.3850, 78.4867),
    "Chennai": (13.0827, 80.2707),
    "Kolkata": (22.5726, 88.3639),
    "Pune": (18.5204, 73.8567),
    "Ahmedabad": (23.0225, 72.5714),
    "Jaipur": (26.9124, 75.7873),
    "Lucknow": (26.8467, 80.9462),
    "Kanpur": (26.4499, 80.3319),
    "Nagpur": (21.1458, 79.0882),
    "Indore": (22.7196, 75.8577),
    "Thane": (19.2183, 72.9781),
    "Bhopal": (23.2599, 77.4126),
    "Visakhapatnam": (17.6868, 83.2185),
    "Patna": (25.5941, 85.1376),
    "Vadodara": (22.3072, 73.1812),
    "Ghaziabad": (28.6692, 77.4538),
    "Ludhiana": (30.9010, 75.8573),
    "Agra": (27.1767, 78.0081),
    "Nashik": (19.9975, 73.7898),
    "Faridabad": (28.4089, 77.3178),
    "Meerut": (28.9845, 77.7064),
    "Rajkot": (22.3039, 70.8022),
    "Varanasi": (25.3176, 82.9739),
    "Srinagar": (34.0837, 74.7973),
    "Aurangabad": (19.8762, 75.3433),
    "Dhanbad": (23.7957, 86.4304),
    "Amritsar": (31.6340, 74.8723),
    "Allahabad": (25.4358, 81.8463),
    "Ranchi": (23.3441, 85.3096),
    "Howrah": (22.5958, 88.2636),
    "Coimbatore": (11.0168, 76.9558),
    "Jabalpur": (23.1815, 79.9864),
    "Guwahati": (26.1445, 91.7362),
    "Chandigarh": (30.7333, 76.7794),
    "Mysore": (12.2958, 76.6394),
    "Gwalior": (26.2183, 78.1828),
    "Kochi": (9.9312, 76.2673),
    "Bhubaneswar": (20.2961, 85.8245),
    "Thiruvananthapuram": (8.5241, 76.9366),
    "Dehradun": (30.3165, 78.0322),
    "Jammu": (32.7266, 74.8570),
    "Pondicherry": (11.9416, 79.8083),
    "Raipur": (21.2514, 81.6296),
    "Shimla": (31.1048, 77.1734),
    "Jodhpur": (26.2389, 73.0243),
    "Madurai": (9.9252, 78.1198),
    "Vijayawada": (16.5062, 80.6480),
}

def get_current_weather(city):
    """
    Get current weather data for a city using OpenWeatherMap API

    Args:
        city (str): Name of the city

    Returns:
        dict: Weather data including temperature, rainfall, humidity, etc.
    """
    if not OPENWEATHER_API_KEY:
        # If no API key, use simulated weather data based on historical patterns
        return simulate_weather_data(city)

    if city in INDIAN_CITIES_COORDS:
        lat, lon = INDIAN_CITIES_COORDS[city]
        url = f"https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={OPENWEATHER_API_KEY}&units=metric"

        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Extract relevant weather information
                weather = {
                    "temperature": data["main"]["temp"],
                    "humidity": data["main"]["humidity"],
                    "pressure": data["main"]["pressure"],
                    "wind_speed": data["wind"]["speed"],
                    "clouds": data["clouds"]["all"],
                    "weather_condition": data["weather"][0]["main"],
                    "weather_description": data["weather"][0]["description"],
                    "rainfall_1h": data.get("rain", {}).get("1h", 0),
                    "rainfall_3h": data.get("rain", {}).get("3h", 0),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return weather
        except Exception as e:
            print(f"Error fetching weather data: {e}")

    # Fallback to simulated data if API call fails or city not found
    return simulate_weather_data(city)

def simulate_weather_data(city):
    """
    Simulate realistic weather data for a city based on historical patterns

    Args:
        city (str): Name of the city

    Returns:
        dict: Simulated weather data
    """
    # Get current month for seasonal adjustments
    current_month = datetime.now().month - 1  # 0-indexed (0=Jan, 11=Dec)

    # Get rainfall pattern for this city and month
    from data.india_flood_data import RAINFALL_PATTERNS
    months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]

    # Find city in rainfall patterns
    city_rainfall = None
    for pattern in RAINFALL_PATTERNS:
        if pattern[0] == city:
            city_rainfall = pattern[1:]  # Skip city name
            break

    # If city not found, use average of all cities
    if city_rainfall is None:
        all_rainfall = np.array([pattern[1:] for pattern in RAINFALL_PATTERNS])
        city_rainfall = all_rainfall.mean(axis=0)

    # Get average rainfall for current month
    avg_monthly_rainfall = city_rainfall[current_month]

    # Simulate daily rainfall based on monthly average
    # Distribute monthly rainfall across 5-10 rainy days
    rainy_days = max(5, min(10, int(avg_monthly_rainfall / 20)))
    daily_rainfall_probability = rainy_days / 30

    # Determine if it's raining today
    is_raining = random.random() < daily_rainfall_probability

    # Simulate rainfall amount if it's raining
    rainfall_1h = 0
    rainfall_3h = 0
    weather_condition = "Clear"
    weather_description = "clear sky"

    if is_raining:
        # Calculate rainfall intensity
        intensity = random.uniform(0.5, 2.0)  # Rainfall intensity multiplier
        rainfall_1h = random.uniform(0.5, 5.0) * intensity
        rainfall_3h = rainfall_1h * random.uniform(1.5, 3.0)

        if rainfall_1h < 1:
            weather_condition = "Drizzle"
            weather_description = "light drizzle"
        elif rainfall_1h < 3:
            weather_condition = "Rain"
            weather_description = "light rain"
        elif rainfall_1h < 7:
            weather_condition = "Rain"
            weather_description = "moderate rain"
        else:
            weather_condition = "Rain"
            weather_description = "heavy intensity rain"
    elif random.random() < 0.3:
        weather_condition = "Clouds"
        cloud_types = ["few clouds", "scattered clouds", "broken clouds", "overcast clouds"]
        weather_description = random.choice(cloud_types)

    # Get city's geographical data for temperature simulation
    city_data = flood_risk_db[flood_risk_db["City"] == city]

    # Default values if city not found
    elevation = 100
    avg_rainfall = 1000

    if not city_data.empty:
        elevation = city_data["Elevation_m"].values[0] or 100
        avg_rainfall = city_data["Avg_Annual_Rainfall_mm"].values[0] or 1000

    # Simulate temperature based on month and elevation
    # Base temperature patterns for India by month (average across regions)
    base_temps = [20, 22, 26, 30, 32, 30, 28, 27, 28, 26, 23, 21]

    # Adjust for elevation (temperature decreases with elevation)
    elevation_factor = max(0, min(15, elevation / 100))  # 0-15 scale
    temp_adjustment = -elevation_factor * 0.5  # Each 100m reduces temp by 0.5°C

    # Randomize temperature with seasonal variation
    base_temp = base_temps[current_month] + temp_adjustment
    temperature = base_temp + random.uniform(-3, 3)

    # Humidity correlates with rainfall and temperature
    humidity_base = 50 + (avg_rainfall / 3000) * 30  # Higher rainfall areas have higher base humidity
    if is_raining:
        humidity = min(100, humidity_base + random.uniform(20, 40))
    else:
        humidity = max(30, humidity_base + random.uniform(-20, 10))

    # Wind speed - generally higher during monsoon and winter
    if current_month in [5, 6, 7, 8]:  # Monsoon (Jun-Sep)
        wind_speed = random.uniform(5, 15)
    elif current_month in [10, 11, 0, 1]:  # Winter (Nov-Feb)
        wind_speed = random.uniform(3, 12)
    else:
        wind_speed = random.uniform(2, 8)

    # Atmospheric pressure - typically lower during monsoon
    if current_month in [5, 6, 7, 8]:  # Monsoon
        pressure = random.uniform(995, 1005)
    else:
        pressure = random.uniform(1005, 1015)

    # Cloud cover - correlates with rainfall
    if is_raining:
        clouds = random.uniform(70, 100)
    else:
        clouds = random.uniform(0, 60)

    weather = {
        "temperature": round(temperature, 1),
        "humidity": round(humidity),
        "pressure": round(pressure, 1),
        "wind_speed": round(wind_speed, 1),
        "clouds": round(clouds),
        "weather_condition": weather_condition,
        "weather_description": weather_description,
        "rainfall_1h": round(rainfall_1h, 1),
        "rainfall_3h": round(rainfall_3h, 1),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "simulated": True
    }

    return weather

def get_forecast_data(city, days=5):
    """
    Get weather forecast data for a city

    Args:
        city (str): Name of the city
        days (int): Number of days to forecast

    Returns:
        dict: Forecast data including daily predictions
    """
    if not OPENWEATHER_API_KEY or city not in INDIAN_CITIES_COORDS:
        return simulate_forecast_data(city, days)

    lat, lon = INDIAN_CITIES_COORDS[city]
    url = f"https://api.openweathermap.org/data/2.5/forecast?lat={lat}&lon={lon}&appid={OPENWEATHER_API_KEY}&units=metric"

    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()

            # Process forecast data
            forecast = {
                "city": city,
                "daily": []
            }

            # Group by day
            daily_data = {}
            for item in data["list"]:
                dt = datetime.fromtimestamp(item["dt"])
                day = dt.strftime("%Y-%m-%d")

                if day not in daily_data:
                    daily_data[day] = []

                daily_data[day].append({
                    "timestamp": dt.strftime("%Y-%m-%d %H:%M:%S"),
                    "temperature": item["main"]["temp"],
                    "humidity": item["main"]["humidity"],
                    "pressure": item["main"]["pressure"],
                    "wind_speed": item["wind"]["speed"],
                    "clouds": item["clouds"]["all"],
                    "weather_condition": item["weather"][0]["main"],
                    "weather_description": item["weather"][0]["description"],
                    "rainfall_3h": item.get("rain", {}).get("3h", 0)
                })

            # Calculate daily averages and totals
            for day, items in daily_data.items():
                temps = [item["temperature"] for item in items]
                humidity = [item["humidity"] for item in items]
                rainfall = sum(item["rainfall_3h"] for item in items)

                forecast["daily"].append({
                    "date": day,
                    "avg_temperature": round(sum(temps) / len(temps), 1),
                    "min_temperature": round(min(temps), 1),
                    "max_temperature": round(max(temps), 1),
                    "avg_humidity": round(sum(humidity) / len(humidity)),
                    "total_rainfall": round(rainfall, 1),
                    "weather_samples": items
                })

            # Sort by date
            forecast["daily"].sort(key=lambda x: x["date"])

            # Limit to requested number of days
            forecast["daily"] = forecast["daily"][:days]

            return forecast
    except Exception as e:
        print(f"Error fetching forecast data: {e}")

    # Fallback to simulated data
    return simulate_forecast_data(city, days)

def simulate_forecast_data(city, days=5):
    """
    Simulate realistic forecast data for a city

    Args:
        city (str): Name of the city
        days (int): Number of days to forecast

    Returns:
        dict: Simulated forecast data
    """
    # Get current weather as starting point
    current = simulate_weather_data(city)

    forecast = {
        "city": city,
        "daily": []
    }

    # Get city's data for more accurate simulation
    city_data = flood_risk_db[flood_risk_db["City"] == city]

    # Default values if city not found
    elevation = 100
    avg_rainfall = 1000
    monsoon_rainfall = 400

    if not city_data.empty:
        elevation = city_data["Elevation_m"].values[0] or 100
        avg_rainfall = city_data["Avg_Annual_Rainfall_mm"].values[0] or 1000
        monsoon_rainfall = city_data["Monsoon_Rainfall_mm"].values[0] or 400

    # Current date and month
    now = datetime.now()
    current_month = now.month - 1  # 0-indexed

    # Determine if we're in monsoon season (June-September)
    is_monsoon = current_month in [5, 6, 7, 8]

    # Base temperature with small random variations for each day
    base_temp = current["temperature"]

    # Rainfall pattern - more consistent in monsoon, sporadic otherwise
    if is_monsoon:
        # During monsoon, higher chance of consecutive rainy days
        rainy_day_probability = monsoon_rainfall / (120 * 10)  # Distribute monsoon rainfall over 120 days
        rainy_streak = random.randint(1, 3)  # Consecutive rainy days
    else:
        # Outside monsoon, more sporadic rainfall
        monthly_rainfall = avg_rainfall / 12
        rainy_day_probability = monthly_rainfall / (30 * 10)  # Distribute monthly rainfall over 30 days
        rainy_streak = 1

    # Generate daily forecasts
    is_raining = current["weather_condition"] == "Rain" or current["weather_condition"] == "Drizzle"
    rain_days_left = rainy_streak if is_raining else 0

    for i in range(days):
        forecast_date = (now + timedelta(days=i)).strftime("%Y-%m-%d")

        # Determine if it will rain
        if rain_days_left > 0:
            is_raining = True
            rain_days_left -= 1
        else:
            is_raining = random.random() < rainy_day_probability
            if is_raining:
                rain_days_left = random.randint(0, rainy_streak)

        # Temperature variation (slight trend over days, with daily min/max variation)
        temp_trend = random.uniform(-1.5, 1.5)  # Overall trend direction
        daily_temp = base_temp + temp_trend * (i/3)  # Gradual change

        # Daily min/max variation
        temp_range = random.uniform(6, 12)  # Daily temperature range
        min_temp = daily_temp - (temp_range/2) + random.uniform(-1, 1)
        max_temp = daily_temp + (temp_range/2) + random.uniform(-1, 1)

        # Rainfall amount if raining
        rainfall = 0
        if is_raining:
            if is_monsoon:
                rainfall = random.uniform(5, 30)  # Heavier rain during monsoon
            else:
                rainfall = random.uniform(2, 15)  # Lighter rain otherwise

        # Humidity correlates with rainfall
        if is_raining:
            humidity = random.uniform(70, 95)
        else:
            humidity = random.uniform(40, 75)

        # Generate hourly samples for the day
        samples = []
        for hour in range(0, 24, 3):  # Every 3 hours
            sample_time = f"{forecast_date} {hour:02d}:00:00"

            # Temperature follows a daily cycle
            hour_factor = abs(hour - 14) / 14  # 0 at 2 PM (peak), 1 at 2 AM (trough)
            hour_temp = max_temp - (hour_factor * temp_range)

            # Rainfall distribution - more likely in afternoon/evening during monsoon
            sample_rainfall = 0
            if is_raining:
                if is_monsoon:
                    # In monsoon, rain throughout the day with afternoon peak
                    rain_probability = 0.7 - 0.4 * abs(hour - 15) / 12
                else:
                    # Outside monsoon, more afternoon/evening rain
                    rain_probability = 0.5 - 0.4 * abs(hour - 16) / 12

                if random.random() < rain_probability:
                    sample_rainfall = rainfall / 4 * random.uniform(0.5, 1.5)

            weather_condition = "Clear"
            weather_description = "clear sky"

            if sample_rainfall > 0:
                if sample_rainfall < 1:
                    weather_condition = "Drizzle"
                    weather_description = "light drizzle"
                elif sample_rainfall < 3:
                    weather_condition = "Rain"
                    weather_description = "light rain"
                elif sample_rainfall < 7:
                    weather_condition = "Rain"
                    weather_description = "moderate rain"
                else:
                    weather_condition = "Rain"
                    weather_description = "heavy intensity rain"
            elif random.random() < 0.3:
                weather_condition = "Clouds"
                cloud_types = ["few clouds", "scattered clouds", "broken clouds", "overcast clouds"]
                weather_description = random.choice(cloud_types)

            samples.append({
                "timestamp": sample_time,
                "temperature": round(hour_temp, 1),
                "humidity": round(humidity + random.uniform(-10, 10)),
                "pressure": round(1010 + random.uniform(-5, 5), 1),
                "wind_speed": round(random.uniform(2, 10), 1),
                "clouds": round(70 if is_raining else random.uniform(0, 60)),
                "weather_condition": weather_condition,
                "weather_description": weather_description,
                "rainfall_3h": round(sample_rainfall, 1)
            })

        forecast["daily"].append({
            "date": forecast_date,
            "avg_temperature": round(daily_temp, 1),
            "min_temperature": round(min_temp, 1),
            "max_temperature": round(max_temp, 1),
            "avg_humidity": round(humidity),
            "total_rainfall": round(rainfall, 1),
            "weather_samples": samples
        })

    return forecast

def calculate_flood_risk(city, current_weather=None, forecast=None):
    """
    Calculate accurate flood risk for a city based on historical data and current conditions

    Args:
        city (str): Name of the city
        current_weather (dict, optional): Current weather data
        forecast (dict, optional): Forecast data

    Returns:
        dict: Flood risk assessment including risk score and factors
    """
    # Get city data from database
    city_data = flood_risk_db[flood_risk_db["City"] == city]

    if city_data.empty:
        return {
            "error": f"City '{city}' not found in database",
            "risk_score": 0,
            "risk_level": "Unknown",
            "factors": {}
        }

    # Extract base risk score and factors
    base_risk_score = city_data["Flood_Risk_Score"].values[0]

    # Get current weather if not provided
    if current_weather is None:
        current_weather = get_current_weather(city)

    # Get forecast if not provided
    if forecast is None:
        forecast = get_forecast_data(city)

    # Calculate current weather impact on flood risk
    weather_risk_factor = 1.0  # Multiplier for base risk

    # Heavy rainfall increases risk
    rainfall_1h = current_weather.get("rainfall_1h", 0)
    rainfall_3h = current_weather.get("rainfall_3h", 0)

    if rainfall_1h > 10 or rainfall_3h > 20:
        # Heavy rainfall significantly increases risk
        weather_risk_factor += 0.5
    elif rainfall_1h > 5 or rainfall_3h > 10:
        # Moderate rainfall increases risk
        weather_risk_factor += 0.3
    elif rainfall_1h > 2 or rainfall_3h > 5:
        # Light rainfall slightly increases risk
        weather_risk_factor += 0.1

    # High humidity can indicate saturated ground
    humidity = current_weather.get("humidity", 50)
    if humidity > 85:
        weather_risk_factor += 0.2
    elif humidity > 70:
        weather_risk_factor += 0.1

    # Calculate forecast impact on risk
    forecast_risk_factor = 0

    if forecast and "daily" in forecast:
        # Look at rainfall over next 3 days
        forecast_days = min(3, len(forecast["daily"]))
        total_forecast_rainfall = sum(day["total_rainfall"] for day in forecast["daily"][:forecast_days])

        # Adjust risk based on forecasted rainfall
        if total_forecast_rainfall > 100:  # Very heavy rainfall forecast
            forecast_risk_factor = 0.5
        elif total_forecast_rainfall > 50:  # Heavy rainfall forecast
            forecast_risk_factor = 0.3
        elif total_forecast_rainfall > 20:  # Moderate rainfall forecast
            forecast_risk_factor = 0.1

    # Calculate seasonal factor
    current_month = datetime.now().month

    # Higher risk during monsoon season (June-September)
    seasonal_factor = 0
    if current_month in [6, 7, 8, 9]:
        # Check if city is in a monsoon-affected region
        state = city_data["State"].values[0]
        if state in ["Maharashtra", "Kerala", "Karnataka", "Goa", "Gujarat", "Assam", "West Bengal"]:
            seasonal_factor = 0.3
        else:
            seasonal_factor = 0.2
    elif current_month in [10, 11]:  # Post-monsoon period
        seasonal_factor = 0.1

    # Calculate final risk score
    risk_score = min(100, base_risk_score * weather_risk_factor + forecast_risk_factor * 20 + seasonal_factor * 20)
    risk_score = round(risk_score)

    # Determine risk level
    if risk_score >= 75:
        risk_level = "Very High"
    elif risk_score >= 60:
        risk_level = "High"
    elif risk_score >= 40:
        risk_level = "Moderate"
    elif risk_score >= 20:
        risk_level = "Low"
    else:
        risk_level = "Very Low"

    # Extract relevant factors from city data
    factors = {
        "historical_flood_frequency": city_data["Flood_Frequency_Per_Year"].values[0],
        "last_major_flood": city_data["Last_Major_Flood"].values[0],
        "elevation": city_data["Elevation_m"].values[0],
        "river_proximity": city_data["River_Proximity_Score"].values[0],
        "drainage_quality": city_data["Drainage_Quality_Score"].values[0],
        "avg_annual_rainfall": city_data["Avg_Annual_Rainfall_mm"].values[0],
        "flood_plain_area": city_data["Flood_Plain_Area_Percent"].values[0],
        "urbanization": city_data["Urbanization_Level"].values[0],
        "affecting_rivers": city_data["Affecting_Rivers"].values[0],
        "river_flood_risk": city_data["River_Flood_Risk"].values[0],
        "climate_change_risk": city_data["Climate_Change_Risk"].values[0],
        "current_rainfall": rainfall_3h,
        "forecast_rainfall_3days": total_forecast_rainfall if forecast and "daily" in forecast else 0,
        "is_monsoon_season": current_month in [6, 7, 8, 9]
    }

    # Calculate factor contributions to risk
    risk_contributions = {
        "historical_data": round(base_risk_score * 0.4),
        "geographical_factors": round(base_risk_score * 0.3),
        "infrastructure": round(base_risk_score * 0.15),
        "current_weather": round((weather_risk_factor - 1.0) * base_risk_score),
        "forecast": round(forecast_risk_factor * 20),
        "seasonal": round(seasonal_factor * 20)
    }

    return {
        "city": city,
        "state": city_data["State"].values[0],
        "risk_score": risk_score,
        "risk_level": risk_level,
        "base_risk_score": base_risk_score,
        "factors": factors,
        "risk_contributions": risk_contributions,
        "current_weather": current_weather,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

def get_all_cities():
    """Get list of all cities in the database"""
    return flood_risk_db["City"].tolist()

def get_city_coordinates(city):
    """Get coordinates for a city"""
    if city in INDIAN_CITIES_COORDS:
        return INDIAN_CITIES_COORDS[city]
    return None

if __name__ == "__main__":
    # Test the module
    cities = ["Mumbai", "Delhi", "Chennai", "Kolkata", "Bengaluru"]

    for city in cities:
        risk = calculate_flood_risk(city)
        print(f"\n{city} Flood Risk Assessment:")
        print(f"Risk Score: {risk['risk_score']} ({risk['risk_level']})")
        print(f"Base Risk: {risk['base_risk_score']}")
        print(f"Current Weather: {risk['current_weather']['weather_description']}, "
              f"Rainfall: {risk['current_weather']['rainfall_3h']}mm/3h")
        print("Risk Contributions:")
        for factor, value in risk['risk_contributions'].items():
            print(f"  - {factor}: {value} points")
