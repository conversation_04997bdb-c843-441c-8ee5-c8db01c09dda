# 🌊 Floodguard: AI-Powered Risk Mapping System for Flood Management in India

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![React](https://img.shields.io/badge/React-18.0+-61DAFB.svg)](https://reactjs.org/)
[![Flask](https://img.shields.io/badge/Flask-2.0+-000000.svg)](https://flask.palletsprojects.com/)

## 🎯 Overview

Floodguard is a comprehensive AI-powered flood risk mapping and prediction system specifically designed for flood management in India. The application combines machine learning algorithms, real-time weather data, and interactive visualization to provide accurate flood risk assessments and early warning systems.

## ✨ Key Features

### 🤖 **AI-Powered Predictions**
- Advanced machine learning models for flood risk assessment
- Real-time weather data integration
- Historical flood data analysis for Indian regions
- Predictive analytics with high accuracy

### 🗺️ **Interactive Mapping**
- Dynamic flood risk visualization using Leaflet maps
- Real-time risk level indicators
- Geographic data overlay for better insights
- Mobile-responsive map interface

### 📊 **Comprehensive Dashboard**
- Professional Material-UI interface
- Real-time weather forecasts
- Risk timeline visualization
- Community reporting system

### 🌍 **India-Specific Features**
- Optimized for Indian geographical conditions
- Historical flood data from Indian regions
- Weather patterns specific to Indian climate
- City-wise flood risk assessments

## 🛠️ Technology Stack

### **Frontend**
- **React 18** - Modern UI framework
- **Material-UI (MUI)** - Professional component library
- **Leaflet** - Interactive mapping
- **Chart.js** - Data visualization
- **Axios** - API communication

### **Backend**
- **Flask** - Python web framework
- **scikit-learn** - Machine learning
- **pandas** - Data processing
- **NumPy** - Numerical computations

### **Additional Tools**
- **Java Backend** - Additional processing capabilities
- **Weather APIs** - Real-time weather data
- **Responsive Design** - Mobile-first approach

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. Create a virtual environment (optional but recommended):
   ```
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the Flask backend:
   ```
   python app.py
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

### Production Deployment

To build the frontend for production:

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Build the React app:
   ```
   npm run build
   ```

3. The Flask app is configured to serve the built React app from the `frontend/build` directory.

## API Endpoints

- `GET /api/map-data`: Returns flood risk data for map visualization
- `GET /api/options`: Returns options for dropdown menus (land cover and soil types)
- `POST /api/predict`: Makes a flood risk prediction based on input data

## Model Information

The application uses a rule-based model that considers various factors to predict flood risk:

- Rainfall
- Elevation
- Water level
- River discharge
- Historical flood data
- Humidity
- Land cover
- Soil type

Each factor contributes to a risk score, which determines the overall flood risk prediction.

## License

This project is open source and available under the MIT License.
