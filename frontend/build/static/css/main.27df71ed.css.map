{"version": 3, "file": "static/css/main.27df71ed.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,kGAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CAEA,mBCRE,8DAA0E,CDW1E,iBAAkB,CCVlB,kBAAmB,CDWnB,8BAAwC,CCVxC,gCAA0C,CDQ1C,YAAa,CCPb,eAAgB,CDMhB,UAIF,CCNA,2BACE,GACE,4BAA0C,CAC1C,kBACF,CACA,IACE,2BAA6C,CAC7C,oBACF,CACA,GACE,2BAA2C,CAC3C,kBACF,CACF,CAEA,wBACE,GACE,mDAAyE,CACzE,kBACF,CACA,IACE,gDAA0E,CAC1E,qBACF,CACA,GACE,mDAAyE,CACzE,kBACF,CACF,CAEA,yBACE,MACE,4BACF,CACA,IACE,6BACF,CACF,CAGA,kBACE,qCAAsC,CACtC,8CACF,CAEA,iBACE,mCAAoC,CACpC,8CACF,CAEA,wBACE,kCACF,CAGA,yCAGE,cAAe,CADf,gBAAiB,CADjB,eAAgB,CAGhB,iBAAkB,CAClB,oBACF,CAGA,eAEE,WAAY,CACZ,iBAAkB,CAFlB,UAGF,CAGA,eAEE,kBAAmB,CADnB,iBAAkB,CAElB,8BACF,CAGA,4DACE,SACF,CAEA,kEACE,oBAA+B,CAC/B,iBACF,CAEA,kEACE,oBAAmC,CACnC,iBACF,CAEA,wEACE,oBACF,CAGA,yCACE,WACF,CAGA,+CAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,kBAAmB,CACnB,yEAGwC,CAExC,eAAgB,CADhB,WAAY,CAEZ,0CACF,CAEA,qDAEE,0EAGwC,CAJxC,0BAKF,CAEA,mCAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,+BACF,CAGA,+BACE,kBAAmB,CAGnB,eAAgB,CADhB,WAEF,CAEA,kDALE,+BAOF,CAGA,eACE,oDACF,CAEA,+BACE,kEACF,CAEA,0BACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,wCACE,GAGE,+BAA0B,CAA1B,uBAA0B,CAF1B,SAAU,CACV,mDAEF,CACA,IAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,UAAY,CACZ,oDAEF,CACA,GAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,SAAU,CACV,8CAEF,CACF,CAEA,yBACE,GACE,SAAU,CACV,oCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,+BACE,eAAgB,CAChB,cACF,CAGA,eACE,kBACF,CAGA,kCAEE,SAAU,CADV,8BAEF,CAEA,oDACE,SACF,CAGA,sBAME,8BAA+C,CAL/C,qBAAuB,CAEvB,4BAA8B,CAD9B,yCAAqD,CAErD,eAGF,CAEA,8CAJE,kCAA2B,CAA3B,0BAWF,CAPA,wBACE,8BAAgD,CAChD,qBAAuB,CACvB,uBAAyB,CACzB,eAAiB,CACjB,iCAEF,CAEA,8BACE,4DAAgE,CAChE,oBAAuB,CACvB,qBACF,CAEA,6BAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,8BAAyC,CAIzC,oCAAqD,CAFrD,2BAA6B,CAD7B,qBAIF,CAEA,+BACE,uBAAyB,CACzB,oBACF,CAEA,qCACE,uBACF,CAGA,gDAUE,kBAAmB,CAJnB,iBAAkB,CAHlB,aAAc,CAMd,YAAa,CARb,cAAe,CAGf,eAAiB,CAIjB,WAAY,CAGZ,sBAAuB,CACvB,UAAW,CAVX,mBAAoB,CAGpB,uBAAyB,CAEzB,UAMF,CAEA,sDAEE,kDAAqD,CAErD,+BAA6C,CAH7C,UAAY,CAEZ,kCAEF,CAGA,cACE,2BACF,CAEA,qBACE,mCACF,CAEA,wBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAGA,gBAEE,cAAe,CADf,uBAEF,CAEA,sBAGE,sBAAuB,CAFvB,oBAAuB,CACvB,qBAEF,CAEA,mBASE,kCAA2B,CAA3B,0BAA2B,CAR3B,8BAA8C,CAE9C,qBAAuB,CACvB,2BAA6B,CAI7B,qCAAwD,CANxD,oBAAuB,CAIvB,cAAe,CADf,eAAiB,CAEjB,0BAGF,CAEA,0BACE,oCACF,CAGA,yBACE,+BAEE,yBAA2B,CAD3B,wBAEF,CAEA,sBACE,mBACF,CAEA,mCAEE,mBACF,CACF,CAEA,yBACE,+BAEE,yBAA2B,CAD3B,wBAEF,CAEA,sBACE,mBACF,CACF,CAGA,yBACE,yBAA0B,CAC1B,kBACF,CAEA,+BACE,qDACF,CAOA,kEACE,qBACF,CAGA,mCACE,+BACE,8BAA6C,CAC7C,oBACF,CAEA,+CACE,8BAA6C,CAC7C,oCACF,CACF", "sources": ["index.css", "components/FloodMap.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: '<PERSON><PERSON>', 'Se<PERSON>e UI', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n.leaflet-container {\n  width: 100%;\n  height: 500px;\n  border-radius: 8px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n", "/* ========================================\n   ENHANCED FLOOD MAP STYLES\n   Modern, Attractive & Interactive Design\n   ======================================== */\n\n/* ===== GLOBAL MAP STYLES ===== */\n.leaflet-container {\n  background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n}\n\n/* ===== ENHANCED MARKER ANIMATIONS ===== */\n@keyframes pulse-high-risk {\n  0% {\n    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);\n    transform: scale(1);\n  }\n  50% {\n    box-shadow: 0 0 0 15px rgba(255, 68, 68, 0.2);\n    transform: scale(1.1);\n  }\n  100% {\n    box-shadow: 0 0 0 25px rgba(255, 68, 68, 0);\n    transform: scale(1);\n  }\n}\n\n@keyframes pulse-search {\n  0% {\n    box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);\n    transform: scale(1);\n  }\n  50% {\n    box-shadow: 0 0 0 12px rgba(58, 134, 255, 0.1), 0 6px 16px rgba(0,0,0,0.4);\n    transform: scale(1.05);\n  }\n  100% {\n    box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);\n    transform: scale(1);\n  }\n}\n\n@keyframes glow-low-risk {\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(0, 230, 118, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 15px rgba(0, 230, 118, 0.8);\n  }\n}\n\n/* ===== MARKER STYLES ===== */\n.high-risk-marker {\n  animation: pulse-high-risk 3s infinite;\n  filter: drop-shadow(0 0 8px rgba(255, 68, 68, 0.6));\n}\n\n.low-risk-marker {\n  animation: glow-low-risk 4s infinite;\n  filter: drop-shadow(0 0 5px rgba(0, 230, 118, 0.4));\n}\n\n.search-location-marker {\n  animation: pulse-search 2s infinite;\n}\n\n/* ===== POPUP ENHANCEMENTS ===== */\n.scrollable-popup .leaflet-popup-content {\n  overflow-y: auto;\n  max-height: 400px;\n  margin-right: 0;\n  padding-right: 8px;\n  scrollbar-width: thin;\n}\n\n/* Ensure popup content is properly sized */\n.popup-content {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* Ensure popup is positioned correctly relative to the map */\n.leaflet-popup {\n  position: absolute;\n  margin-bottom: 30px; /* Provide space for the popup tip */\n  transform-origin: bottom center;\n}\n\n/* Style the scrollbar */\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb {\n  background: rgba(58, 134, 255, 0.3);\n  border-radius: 3px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(58, 134, 255, 0.5);\n}\n\n/* Ensure popup content has proper spacing */\n.scrollable-popup .leaflet-popup-content {\n  padding: 2px;\n}\n\n/* ===== GLASSMORPHISM POPUP EFFECTS ===== */\n.enhanced-popup .leaflet-popup-content-wrapper {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.2),\n    0 8px 32px rgba(0, 0, 0, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3);\n  padding: 4px;\n  overflow: hidden;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.enhanced-popup .leaflet-popup-content-wrapper:hover {\n  transform: translateY(-2px);\n  box-shadow:\n    0 25px 50px rgba(0, 0, 0, 0.25),\n    0 12px 40px rgba(0, 0, 0, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.4);\n}\n\n.enhanced-popup .leaflet-popup-tip {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n/* Standard popup styling */\n.leaflet-popup-content-wrapper {\n  border-radius: 12px;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n  padding: 2px;\n  overflow: hidden;\n}\n\n.leaflet-popup-tip {\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n/* ===== ENHANCED POPUP ANIMATIONS ===== */\n.leaflet-popup {\n  animation: popup-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.enhanced-popup .leaflet-popup {\n  animation: popup-glassmorphism-entrance 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n@keyframes popup-slide-up {\n  from {\n    opacity: 0;\n    transform: scale(0.85) translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n@keyframes popup-glassmorphism-entrance {\n  from {\n    opacity: 0;\n    transform: scale(0.8) translateY(30px) rotateX(10deg);\n    backdrop-filter: blur(0px);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.02) translateY(-5px) rotateX(0deg);\n    backdrop-filter: blur(10px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0) rotateX(0deg);\n    backdrop-filter: blur(20px);\n  }\n}\n\n@keyframes popup-fade-in {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* Ensure popup stays within map bounds */\n.leaflet-popup-content-wrapper {\n  max-height: 80vh;\n  max-width: 80vw;\n}\n\n/* Ensure popup is positioned correctly */\n.leaflet-popup {\n  margin-bottom: 20px;\n}\n\n/* Add animation to popups */\n.leaflet-fade-anim .leaflet-popup {\n  transition: opacity 0.25s linear;\n  opacity: 0;\n}\n\n.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {\n  opacity: 1;\n}\n\n/* ===== ENHANCED MAP CONTROLS ===== */\n.leaflet-control-zoom {\n  border: none !important;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;\n  border-radius: 12px !important;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.9) !important;\n}\n\n.leaflet-control-zoom a {\n  background: rgba(255, 255, 255, 0.95) !important;\n  border: none !important;\n  color: #2c3e50 !important;\n  font-weight: bold;\n  transition: all 0.3s ease !important;\n  backdrop-filter: blur(10px);\n}\n\n.leaflet-control-zoom a:hover {\n  background: linear-gradient(135deg, #3A86FF, #06FFA5) !important;\n  color: white !important;\n  transform: scale(1.05);\n}\n\n.leaflet-control-attribution {\n  background: rgba(0, 0, 0, 0.7) !important;\n  color: rgba(255, 255, 255, 0.8) !important;\n  border-radius: 8px !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1) !important;\n}\n\n.leaflet-control-attribution a {\n  color: #3A86FF !important;\n  text-decoration: none;\n}\n\n.leaflet-control-attribution a:hover {\n  color: #06FFA5 !important;\n}\n\n/* ===== ENHANCED POPUP CLOSE BUTTON ===== */\n.leaflet-container a.leaflet-popup-close-button {\n  font-size: 20px;\n  padding: 6px 8px 0 0;\n  color: #3A86FF;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 4px;\n}\n\n.leaflet-container a.leaflet-popup-close-button:hover {\n  color: white;\n  background: linear-gradient(135deg, #FF595E, #FF8E53);\n  transform: scale(1.1) rotate(90deg);\n  box-shadow: 0 4px 12px rgba(255, 89, 94, 0.4);\n}\n\n/* ===== TILE LAYER ENHANCEMENTS ===== */\n.leaflet-tile {\n  transition: opacity 0.3s ease;\n}\n\n.leaflet-tile-loaded {\n  animation: tile-fade-in 0.5s ease-out;\n}\n\n@keyframes tile-fade-in {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n/* ===== RISK HEAT MAP ZONES ===== */\n.risk-heat-zone {\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.risk-heat-zone:hover {\n  opacity: 0.8 !important;\n  transform: scale(1.02);\n  filter: brightness(1.1);\n}\n\n.risk-zone-tooltip {\n  background: rgba(255, 68, 68, 0.95) !important;\n  color: white !important;\n  border: none !important;\n  border-radius: 8px !important;\n  font-weight: bold;\n  font-size: 12px;\n  padding: 8px 12px !important;\n  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4) !important;\n  backdrop-filter: blur(10px);\n}\n\n.risk-zone-tooltip::before {\n  border-top-color: rgba(255, 68, 68, 0.95) !important;\n}\n\n/* ===== RESPONSIVE DESIGN ===== */\n@media (max-width: 768px) {\n  .leaflet-popup-content-wrapper {\n    max-width: 90vw !important;\n    max-height: 70vh !important;\n  }\n\n  .leaflet-control-zoom {\n    transform: scale(0.9);\n  }\n\n  .high-risk-marker,\n  .low-risk-marker {\n    transform: scale(0.8);\n  }\n}\n\n@media (max-width: 480px) {\n  .leaflet-popup-content-wrapper {\n    max-width: 95vw !important;\n    max-height: 60vh !important;\n  }\n\n  .leaflet-control-zoom {\n    transform: scale(0.8);\n  }\n}\n\n/* ===== ACCESSIBILITY ENHANCEMENTS ===== */\n.leaflet-container:focus {\n  outline: 3px solid #3A86FF;\n  outline-offset: 2px;\n}\n\n.leaflet-popup-content-wrapper {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* ===== PERFORMANCE OPTIMIZATIONS ===== */\n.leaflet-tile-container {\n  will-change: transform;\n}\n\n.leaflet-zoom-anim .leaflet-zoom-animated {\n  will-change: transform;\n}\n\n/* ===== DARK MODE COMPATIBILITY ===== */\n@media (prefers-color-scheme: dark) {\n  .leaflet-popup-content-wrapper {\n    background: rgba(30, 30, 30, 0.95) !important;\n    color: white !important;\n  }\n\n  .enhanced-popup .leaflet-popup-content-wrapper {\n    background: rgba(20, 20, 20, 0.95) !important;\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n}\n"], "names": [], "sourceRoot": ""}