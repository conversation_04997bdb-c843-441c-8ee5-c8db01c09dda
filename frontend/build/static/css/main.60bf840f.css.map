{"version": 3, "file": "static/css/main.60bf840f.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,kGAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CAEA,mBAGE,iBAAkB,CAClB,8BAAwC,CAFxC,YAAa,CADb,UAIF,CCjBA,yCAGE,cAAe,CADf,gBAAiB,CADjB,eAAgB,CAGhB,iBAAkB,CAClB,oBACF,CAGA,eAEE,WAAY,CACZ,iBAAkB,CAFlB,UAGF,CAGA,eAEE,kBAAmB,CADnB,iBAAkB,CAElB,8BACF,CAGA,4DACE,SACF,CAEA,kEACE,oBAA+B,CAC/B,iBACF,CAEA,kEACE,oBAAmC,CACnC,iBACF,CAEA,wEACE,oBACF,CAGA,yCACE,WACF,CAGA,+BACE,kBAAmB,CAGnB,eAAgB,CADhB,WAEF,CAEA,kDALE,+BAOF,CAGA,eACE,oCACF,CAEA,yBACE,GACE,SAAU,CACV,oCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,+BACE,eAAgB,CAChB,cACF,CAGA,eACE,kBACF,CAGA,kCAEE,SAAU,CADV,8BAEF,CAEA,oDACE,SACF,CAGA,gDAGE,aAAc,CAFd,cAAe,CACf,mBAEF,CAEA,sDACE,aACF", "sources": ["index.css", "components/FloodMap.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: '<PERSON><PERSON>', 'Se<PERSON>e UI', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n.leaflet-container {\n  width: 100%;\n  height: 500px;\n  border-radius: 8px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n", "/* Custom styles for the Leaflet map */\n\n/* Make popups scrollable */\n.scrollable-popup .leaflet-popup-content {\n  overflow-y: auto;\n  max-height: 350px;\n  margin-right: 0;\n  padding-right: 5px;\n  scrollbar-width: thin; /* For Firefox */\n}\n\n/* Ensure popup content is properly sized */\n.popup-content {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n/* Ensure popup is positioned correctly relative to the map */\n.leaflet-popup {\n  position: absolute;\n  margin-bottom: 30px; /* Provide space for the popup tip */\n  transform-origin: bottom center;\n}\n\n/* Style the scrollbar */\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb {\n  background: rgba(58, 134, 255, 0.3);\n  border-radius: 3px;\n}\n\n.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(58, 134, 255, 0.5);\n}\n\n/* Ensure popup content has proper spacing */\n.scrollable-popup .leaflet-popup-content {\n  padding: 2px;\n}\n\n/* Improve popup appearance */\n.leaflet-popup-content-wrapper {\n  border-radius: 12px;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n  padding: 2px;\n  overflow: hidden;\n}\n\n.leaflet-popup-tip {\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n/* Add a subtle animation when popup opens */\n.leaflet-popup {\n  animation: popup-fade-in 0.3s ease-out;\n}\n\n@keyframes popup-fade-in {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* Ensure popup stays within map bounds */\n.leaflet-popup-content-wrapper {\n  max-height: 80vh;\n  max-width: 80vw;\n}\n\n/* Ensure popup is positioned correctly */\n.leaflet-popup {\n  margin-bottom: 20px;\n}\n\n/* Add animation to popups */\n.leaflet-fade-anim .leaflet-popup {\n  transition: opacity 0.25s linear;\n  opacity: 0;\n}\n\n.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {\n  opacity: 1;\n}\n\n/* Improve popup close button */\n.leaflet-container a.leaflet-popup-close-button {\n  font-size: 18px;\n  padding: 4px 4px 0 0;\n  color: #3A86FF;\n}\n\n.leaflet-container a.leaflet-popup-close-button:hover {\n  color: #FF595E;\n}\n"], "names": [], "sourceRoot": ""}