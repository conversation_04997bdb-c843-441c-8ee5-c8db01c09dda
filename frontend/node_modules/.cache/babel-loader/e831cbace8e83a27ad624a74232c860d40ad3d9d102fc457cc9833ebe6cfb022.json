{"ast": null, "code": "import { useEffect, useRef } from 'react';\nexport function useAttribution(map, attribution) {\n  const attributionRef = useRef(attribution);\n  useEffect(function updateAttribution() {\n    if (attribution !== attributionRef.current && map.attributionControl != null) {\n      if (attributionRef.current != null) {\n        map.attributionControl.removeAttribution(attributionRef.current);\n      }\n      if (attribution != null) {\n        map.attributionControl.addAttribution(attribution);\n      }\n    }\n    attributionRef.current = attribution;\n  }, [map, attribution]);\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useAttribution", "map", "attribution", "attributionRef", "updateAttribution", "current", "attributionControl", "removeAttribution", "addAttribution"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/attribution.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function useAttribution(map, attribution) {\n    const attributionRef = useRef(attribution);\n    useEffect(function updateAttribution() {\n        if (attribution !== attributionRef.current && map.attributionControl != null) {\n            if (attributionRef.current != null) {\n                map.attributionControl.removeAttribution(attributionRef.current);\n            }\n            if (attribution != null) {\n                map.attributionControl.addAttribution(attribution);\n            }\n        }\n        attributionRef.current = attribution;\n    }, [\n        map,\n        attribution\n    ]);\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAO,SAASC,cAAcA,CAACC,GAAG,EAAEC,WAAW,EAAE;EAC7C,MAAMC,cAAc,GAAGJ,MAAM,CAACG,WAAW,CAAC;EAC1CJ,SAAS,CAAC,SAASM,iBAAiBA,CAAA,EAAG;IACnC,IAAIF,WAAW,KAAKC,cAAc,CAACE,OAAO,IAAIJ,GAAG,CAACK,kBAAkB,IAAI,IAAI,EAAE;MAC1E,IAAIH,cAAc,CAACE,OAAO,IAAI,IAAI,EAAE;QAChCJ,GAAG,CAACK,kBAAkB,CAACC,iBAAiB,CAACJ,cAAc,CAACE,OAAO,CAAC;MACpE;MACA,IAAIH,WAAW,IAAI,IAAI,EAAE;QACrBD,GAAG,CAACK,kBAAkB,CAACE,cAAc,CAACN,WAAW,CAAC;MACtD;IACJ;IACAC,cAAc,CAACE,OAAO,GAAGH,WAAW;EACxC,CAAC,EAAE,CACCD,GAAG,EACHC,WAAW,CACd,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}