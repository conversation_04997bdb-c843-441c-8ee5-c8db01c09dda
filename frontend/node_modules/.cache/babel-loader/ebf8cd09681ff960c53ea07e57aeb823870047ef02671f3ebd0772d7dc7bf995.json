{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>}from'react-leaflet';import{Box,Typography,Skeleton}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FloodMap=_ref=>{let{mapData}=_ref;if(!mapData||mapData.length===0){return/*#__PURE__*/_jsx(Box,{sx:{width:'100%',height:500,borderRadius:2},children:/*#__PURE__*/_jsx(Skeleton,{variant:\"rectangular\",width:\"100%\",height:\"100%\"})});}return/*#__PURE__*/_jsxs(MapContainer,{center:[22.0,80.0],zoom:5,scrollWheelZoom:true,children:[/*#__PURE__*/_jsx(<PERSON><PERSON><PERSON><PERSON><PERSON>,{attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",url:\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"}),mapData.map((point,index)=>/*#__PURE__*/_jsx(CircleMarker,{center:[point.Latitude,point.Longitude],radius:5,pathOptions:{color:point.color,fillColor:point.color,fillOpacity:0.8},children:/*#__PURE__*/_jsxs(Popup,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Flood Risk:\"}),\" \",point.Flood_Prediction===1?'High':'Low']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Rainfall:\"}),\" \",point['Rainfall (mm)'],\" mm\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Elevation:\"}),\" \",point['Elevation (m)'],\" m\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Water Level:\"}),\" \",point['Water Level (m)'],\" m\"]})]})},index))]});};export default FloodMap;", "map": {"version": 3, "names": ["React", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CircleMarker", "Popup", "Box", "Typography", "Skeleton", "jsx", "_jsx", "jsxs", "_jsxs", "FloodMap", "_ref", "mapData", "length", "sx", "width", "height", "borderRadius", "children", "variant", "center", "zoom", "scrollWheelZoom", "attribution", "url", "map", "point", "index", "Latitude", "Longitude", "radius", "pathOptions", "color", "fillColor", "fillOpacity", "Flood_Prediction"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleMarker, <PERSON><PERSON> } from 'react-leaflet';\nimport { Box, Typography, Skeleton } from '@mui/material';\n\nconst FloodMap = ({ mapData }) => {\n  if (!mapData || mapData.length === 0) {\n    return (\n      <Box sx={{ width: '100%', height: 500, borderRadius: 2 }}>\n        <Skeleton variant=\"rectangular\" width=\"100%\" height=\"100%\" />\n      </Box>\n    );\n  }\n\n  return (\n    <MapContainer center={[22.0, 80.0]} zoom={5} scrollWheelZoom={true}>\n      <TileLayer\n        attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n        url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n      />\n      {mapData.map((point, index) => (\n        <CircleMarker\n          key={index}\n          center={[point.Latitude, point.Longitude]}\n          radius={5}\n          pathOptions={{\n            color: point.color,\n            fillColor: point.color,\n            fillOpacity: 0.8\n          }}\n        >\n          <Popup>\n            <Typography variant=\"subtitle2\">\n              <strong>Flood Risk:</strong> {point.Flood_Prediction === 1 ? 'High' : 'Low'}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Rainfall:</strong> {point['Rainfall (mm)']} mm\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Elevation:</strong> {point['Elevation (m)']} m\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Water Level:</strong> {point['Water Level (m)']} m\n            </Typography>\n          </Popup>\n        </CircleMarker>\n      ))}\n    </MapContainer>\n  );\n};\n\nexport default FloodMap;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,YAAY,CAA<PERSON>,SAAS,CAAEC,YAAY,CAAEC,KAAK,KAAQ,eAAe,CAC5E,OAASC,GAAG,CAAEC,UAAU,CAAEC,QAAQ,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1D,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAC3B,GAAI,CAACC,OAAO,EAAIA,OAAO,CAACC,MAAM,GAAK,CAAC,CAAE,CACpC,mBACEN,IAAA,CAACJ,GAAG,EAACW,EAAE,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,GAAG,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAC,QAAA,cACvDX,IAAA,CAACF,QAAQ,EAACc,OAAO,CAAC,aAAa,CAACJ,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACV,YAAY,EAACqB,MAAM,CAAE,CAAC,IAAI,CAAE,IAAI,CAAE,CAACC,IAAI,CAAE,CAAE,CAACC,eAAe,CAAE,IAAK,CAAAJ,QAAA,eACjEX,IAAA,CAACP,SAAS,EACRuB,WAAW,CAAC,yFAAyF,CACrGC,GAAG,CAAC,oDAAoD,CACzD,CAAC,CACDZ,OAAO,CAACa,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACxBpB,IAAA,CAACN,YAAY,EAEXmB,MAAM,CAAE,CAACM,KAAK,CAACE,QAAQ,CAAEF,KAAK,CAACG,SAAS,CAAE,CAC1CC,MAAM,CAAE,CAAE,CACVC,WAAW,CAAE,CACXC,KAAK,CAAEN,KAAK,CAACM,KAAK,CAClBC,SAAS,CAAEP,KAAK,CAACM,KAAK,CACtBE,WAAW,CAAE,GACf,CAAE,CAAAhB,QAAA,cAEFT,KAAA,CAACP,KAAK,EAAAgB,QAAA,eACJT,KAAA,CAACL,UAAU,EAACe,OAAO,CAAC,WAAW,CAAAD,QAAA,eAC7BX,IAAA,WAAAW,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAACQ,KAAK,CAACS,gBAAgB,GAAK,CAAC,CAAG,MAAM,CAAG,KAAK,EACjE,CAAC,cACb1B,KAAA,CAACL,UAAU,EAACe,OAAO,CAAC,OAAO,CAAAD,QAAA,eACzBX,IAAA,WAAAW,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAACQ,KAAK,CAAC,eAAe,CAAC,CAAC,KACrD,EAAY,CAAC,cACbjB,KAAA,CAACL,UAAU,EAACe,OAAO,CAAC,OAAO,CAAAD,QAAA,eACzBX,IAAA,WAAAW,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACQ,KAAK,CAAC,eAAe,CAAC,CAAC,IACtD,EAAY,CAAC,cACbjB,KAAA,CAACL,UAAU,EAACe,OAAO,CAAC,OAAO,CAAAD,QAAA,eACzBX,IAAA,WAAAW,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACQ,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAC1D,EAAY,CAAC,EACR,CAAC,EAtBHC,KAuBO,CACf,CAAC,EACU,CAAC,CAEnB,CAAC,CAED,cAAe,CAAAjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}