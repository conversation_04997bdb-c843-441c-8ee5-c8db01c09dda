{"ast": null, "code": "'use strict';\n\nvar utils = require('../../utils/iframe'),\n  random = require('../../utils/random'),\n  browser = require('../../utils/browser'),\n  urlUtils = require('../../utils/url'),\n  inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n  utils.polluteGlobalNamespace();\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n  this._createScript(urlWithId);\n\n  // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n  this.timeoutId = setTimeout(function () {\n    debug('timeout');\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\ninherits(JsonpReceiver, EventEmitter);\nJsonpReceiver.prototype.abort = function () {\n  debug('abort');\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n    this._abort(err);\n  }\n};\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\nJsonpReceiver.prototype._callback = function (data) {\n  debug('_callback', data);\n  this._cleanup();\n  if (this.aborting) {\n    return;\n  }\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\nJsonpReceiver.prototype._abort = function (err) {\n  debug('_abort', err);\n  this._cleanup();\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\nJsonpReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n  if (this.script) {\n    var script = this.script;\n    // Unfortunately, you can't really abort script loading of\n    // the script.\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror = script.onload = script.onclick = null;\n    this.script = null;\n  }\n  delete global[utils.WPrefix][this.id];\n};\nJsonpReceiver.prototype._scriptError = function () {\n  debug('_scriptError');\n  var self = this;\n  if (this.errorTimer) {\n    return;\n  }\n  this.errorTimer = setTimeout(function () {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\nJsonpReceiver.prototype._createScript = function (url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2; // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n  script.onload = function () {\n    debug('onload');\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  };\n\n  // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n  script.onreadystatechange = function () {\n    debug('onreadystatechange', script.readyState);\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {\n          // intentionally empty\n        }\n      }\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  };\n  // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {\n        // intentionally empty\n      }\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\nmodule.exports = JsonpReceiver;", "map": {"version": 3, "names": ["utils", "require", "random", "browser", "urlUtils", "inherits", "EventEmitter", "debug", "process", "env", "NODE_ENV", "JsonpReceiver", "url", "self", "call", "polluteGlobalNamespace", "id", "string", "urlWithId", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "WPrefix", "global", "_callback", "bind", "_createScript", "timeoutId", "setTimeout", "_abort", "Error", "timeout", "prototype", "abort", "err", "code", "scriptErrorTimeout", "data", "_cleanup", "aborting", "emit", "removeAllListeners", "message", "clearTimeout", "script2", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "script", "onreadystatechange", "onerror", "onload", "onclick", "_scriptError", "errorTimer", "loaded<PERSON>kay", "document", "createElement", "src", "type", "charset", "readyState", "test", "htmlFor", "x", "async", "attachEvent", "isOpera", "event", "text", "head", "getElementsByTagName", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/receiver/jsonp.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../../utils/iframe')\n  , random = require('../../utils/random')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\n\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n\n  utils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n  this._createScript(urlWithId);\n\n  // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n  this.timeoutId = setTimeout(function() {\n    debug('timeout');\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\n\ninherits(JsonpReceiver, EventEmitter);\n\nJsonpReceiver.prototype.abort = function() {\n  debug('abort');\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n    this._abort(err);\n  }\n};\n\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\n\nJsonpReceiver.prototype._callback = function(data) {\n  debug('_callback', data);\n  this._cleanup();\n\n  if (this.aborting) {\n    return;\n  }\n\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._abort = function(err) {\n  debug('_abort', err);\n  this._cleanup();\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n  if (this.script) {\n    var script = this.script;\n    // Unfortunately, you can't really abort script loading of\n    // the script.\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror =\n        script.onload = script.onclick = null;\n    this.script = null;\n  }\n  delete global[utils.WPrefix][this.id];\n};\n\nJsonpReceiver.prototype._scriptError = function() {\n  debug('_scriptError');\n  var self = this;\n  if (this.errorTimer) {\n    return;\n  }\n\n  this.errorTimer = setTimeout(function() {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\n\nJsonpReceiver.prototype._createScript = function(url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2;  // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n  script.onload = function() {\n    debug('onload');\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  };\n\n  // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n  script.onreadystatechange = function() {\n    debug('onreadystatechange', script.readyState);\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {\n          // intentionally empty\n        }\n      }\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  };\n  // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {\n        // intentionally empty\n      }\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\n\nmodule.exports = JsonpReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EACrCC,MAAM,GAAGD,OAAO,CAAC,oBAAoB,CAAC;EACtCE,OAAO,GAAGF,OAAO,CAAC,qBAAqB,CAAC;EACxCG,QAAQ,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACrCI,QAAQ,GAAGJ,OAAO,CAAC,UAAU,CAAC;EAC9BK,YAAY,GAAGL,OAAO,CAAC,QAAQ,CAAC,CAACK,YAAY;AAGjD,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGN,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC;AAC1D;AAEA,SAASU,aAAaA,CAACC,GAAG,EAAE;EAC1BL,KAAK,CAACK,GAAG,CAAC;EACV,IAAIC,IAAI,GAAG,IAAI;EACfP,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EAEvBd,KAAK,CAACe,sBAAsB,CAAC,CAAC;EAE9B,IAAI,CAACC,EAAE,GAAG,GAAG,GAAGd,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC;EAChC,IAAIC,SAAS,GAAGd,QAAQ,CAACe,QAAQ,CAACP,GAAG,EAAE,IAAI,GAAGQ,kBAAkB,CAACpB,KAAK,CAACqB,OAAO,GAAG,GAAG,GAAG,IAAI,CAACL,EAAE,CAAC,CAAC;EAEhGM,MAAM,CAACtB,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAI,CAACL,EAAE,CAAC,GAAG,IAAI,CAACO,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;EAC1D,IAAI,CAACC,aAAa,CAACP,SAAS,CAAC;;EAE7B;EACA,IAAI,CAACQ,SAAS,GAAGC,UAAU,CAAC,YAAW;IACrCpB,KAAK,CAAC,SAAS,CAAC;IAChBM,IAAI,CAACe,MAAM,CAAC,IAAIC,KAAK,CAAC,0CAA0C,CAAC,CAAC;EACpE,CAAC,EAAElB,aAAa,CAACmB,OAAO,CAAC;AAC3B;AAEAzB,QAAQ,CAACM,aAAa,EAAEL,YAAY,CAAC;AAErCK,aAAa,CAACoB,SAAS,CAACC,KAAK,GAAG,YAAW;EACzCzB,KAAK,CAAC,OAAO,CAAC;EACd,IAAIe,MAAM,CAACtB,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAI,CAACL,EAAE,CAAC,EAAE;IAClC,IAAIiB,GAAG,GAAG,IAAIJ,KAAK,CAAC,yBAAyB,CAAC;IAC9CI,GAAG,CAACC,IAAI,GAAG,IAAI;IACf,IAAI,CAACN,MAAM,CAACK,GAAG,CAAC;EAClB;AACF,CAAC;AAEDtB,aAAa,CAACmB,OAAO,GAAG,KAAK;AAC7BnB,aAAa,CAACwB,kBAAkB,GAAG,IAAI;AAEvCxB,aAAa,CAACoB,SAAS,CAACR,SAAS,GAAG,UAASa,IAAI,EAAE;EACjD7B,KAAK,CAAC,WAAW,EAAE6B,IAAI,CAAC;EACxB,IAAI,CAACC,QAAQ,CAAC,CAAC;EAEf,IAAI,IAAI,CAACC,QAAQ,EAAE;IACjB;EACF;EAEA,IAAIF,IAAI,EAAE;IACR7B,KAAK,CAAC,SAAS,EAAE6B,IAAI,CAAC;IACtB,IAAI,CAACG,IAAI,CAAC,SAAS,EAAEH,IAAI,CAAC;EAC5B;EACA,IAAI,CAACG,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC;EACnC,IAAI,CAACC,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAED7B,aAAa,CAACoB,SAAS,CAACH,MAAM,GAAG,UAASK,GAAG,EAAE;EAC7C1B,KAAK,CAAC,QAAQ,EAAE0B,GAAG,CAAC;EACpB,IAAI,CAACI,QAAQ,CAAC,CAAC;EACf,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,IAAI,CAAC,OAAO,EAAEN,GAAG,CAACC,IAAI,EAAED,GAAG,CAACQ,OAAO,CAAC;EACzC,IAAI,CAACD,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAED7B,aAAa,CAACoB,SAAS,CAACM,QAAQ,GAAG,YAAW;EAC5C9B,KAAK,CAAC,UAAU,CAAC;EACjBmC,YAAY,CAAC,IAAI,CAAChB,SAAS,CAAC;EAC5B,IAAI,IAAI,CAACiB,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,CAACC,UAAU,CAACC,WAAW,CAAC,IAAI,CAACF,OAAO,CAAC;IACjD,IAAI,CAACA,OAAO,GAAG,IAAI;EACrB;EACA,IAAI,IAAI,CAACG,MAAM,EAAE;IACf,IAAIA,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB;IACA;IACAA,MAAM,CAACF,UAAU,CAACC,WAAW,CAACC,MAAM,CAAC;IACrCA,MAAM,CAACC,kBAAkB,GAAGD,MAAM,CAACE,OAAO,GACtCF,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACI,OAAO,GAAG,IAAI;IACzC,IAAI,CAACJ,MAAM,GAAG,IAAI;EACpB;EACA,OAAOxB,MAAM,CAACtB,KAAK,CAACqB,OAAO,CAAC,CAAC,IAAI,CAACL,EAAE,CAAC;AACvC,CAAC;AAEDL,aAAa,CAACoB,SAAS,CAACoB,YAAY,GAAG,YAAW;EAChD5C,KAAK,CAAC,cAAc,CAAC;EACrB,IAAIM,IAAI,GAAG,IAAI;EACf,IAAI,IAAI,CAACuC,UAAU,EAAE;IACnB;EACF;EAEA,IAAI,CAACA,UAAU,GAAGzB,UAAU,CAAC,YAAW;IACtC,IAAI,CAACd,IAAI,CAACwC,UAAU,EAAE;MACpBxC,IAAI,CAACe,MAAM,CAAC,IAAIC,KAAK,CAAC,0CAA0C,CAAC,CAAC;IACpE;EACF,CAAC,EAAElB,aAAa,CAACwB,kBAAkB,CAAC;AACtC,CAAC;AAEDxB,aAAa,CAACoB,SAAS,CAACN,aAAa,GAAG,UAASb,GAAG,EAAE;EACpDL,KAAK,CAAC,eAAe,EAAEK,GAAG,CAAC;EAC3B,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIiC,MAAM,GAAG,IAAI,CAACA,MAAM,GAAGxB,MAAM,CAACgC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAClE,IAAIZ,OAAO,CAAC,CAAE;;EAEdG,MAAM,CAAC9B,EAAE,GAAG,GAAG,GAAGd,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC;EAClC6B,MAAM,CAACU,GAAG,GAAG5C,GAAG;EAChBkC,MAAM,CAACW,IAAI,GAAG,iBAAiB;EAC/BX,MAAM,CAACY,OAAO,GAAG,OAAO;EACxBZ,MAAM,CAACE,OAAO,GAAG,IAAI,CAACG,YAAY,CAAC3B,IAAI,CAAC,IAAI,CAAC;EAC7CsB,MAAM,CAACG,MAAM,GAAG,YAAW;IACzB1C,KAAK,CAAC,QAAQ,CAAC;IACfM,IAAI,CAACe,MAAM,CAAC,IAAIC,KAAK,CAAC,yCAAyC,CAAC,CAAC;EACnE,CAAC;;EAED;EACA;EACAiB,MAAM,CAACC,kBAAkB,GAAG,YAAW;IACrCxC,KAAK,CAAC,oBAAoB,EAAEuC,MAAM,CAACa,UAAU,CAAC;IAC9C,IAAI,eAAe,CAACC,IAAI,CAACd,MAAM,CAACa,UAAU,CAAC,EAAE;MAC3C,IAAIb,MAAM,IAAIA,MAAM,CAACe,OAAO,IAAIf,MAAM,CAACI,OAAO,EAAE;QAC9CrC,IAAI,CAACwC,UAAU,GAAG,IAAI;QACtB,IAAI;UACF;UACAP,MAAM,CAACI,OAAO,CAAC,CAAC;QAClB,CAAC,CAAC,OAAOY,CAAC,EAAE;UACV;QAAA;MAEJ;MACA,IAAIhB,MAAM,EAAE;QACVjC,IAAI,CAACe,MAAM,CAAC,IAAIC,KAAK,CAAC,qDAAqD,CAAC,CAAC;MAC/E;IACF;EACF,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOiB,MAAM,CAACiB,KAAK,KAAK,WAAW,IAAIzC,MAAM,CAACgC,QAAQ,CAACU,WAAW,EAAE;IACtE;IACA;IACA;IACA,IAAI,CAAC7D,OAAO,CAAC8D,OAAO,CAAC,CAAC,EAAE;MACtB;MACA,IAAI;QACFnB,MAAM,CAACe,OAAO,GAAGf,MAAM,CAAC9B,EAAE;QAC1B8B,MAAM,CAACoB,KAAK,GAAG,SAAS;MAC1B,CAAC,CAAC,OAAOJ,CAAC,EAAE;QACV;MAAA;MAEFhB,MAAM,CAACiB,KAAK,GAAG,IAAI;IACrB,CAAC,MAAM;MACL;MACApB,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGrB,MAAM,CAACgC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAChEZ,OAAO,CAACwB,IAAI,GAAG,uCAAuC,GAAGrB,MAAM,CAAC9B,EAAE,GAAG,mCAAmC;MACxG8B,MAAM,CAACiB,KAAK,GAAGpB,OAAO,CAACoB,KAAK,GAAG,KAAK;IACtC;EACF;EACA,IAAI,OAAOjB,MAAM,CAACiB,KAAK,KAAK,WAAW,EAAE;IACvCjB,MAAM,CAACiB,KAAK,GAAG,IAAI;EACrB;EAEA,IAAIK,IAAI,GAAG9C,MAAM,CAACgC,QAAQ,CAACe,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1DD,IAAI,CAACE,YAAY,CAACxB,MAAM,EAAEsB,IAAI,CAACG,UAAU,CAAC;EAC1C,IAAI5B,OAAO,EAAE;IACXyB,IAAI,CAACE,YAAY,CAAC3B,OAAO,EAAEyB,IAAI,CAACG,UAAU,CAAC;EAC7C;AACF,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG9D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}