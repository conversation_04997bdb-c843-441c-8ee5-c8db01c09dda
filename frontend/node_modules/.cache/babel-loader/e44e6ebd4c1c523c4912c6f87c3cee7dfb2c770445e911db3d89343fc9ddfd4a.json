{"ast": null, "code": "/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  constructor(onFrame, onIncomingPing) {\n    this.onFrame = onFrame;\n    this.onIncomingPing = onIncomingPing;\n    this._encoder = new TextEncoder();\n    this._decoder = new TextDecoder();\n    this._token = [];\n    this._initState();\n  }\n  parseChunk(segment) {\n    let appendMissingNULLonIncoming = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let chunk;\n    if (typeof segment === 'string') {\n      chunk = this._encoder.encode(segment);\n    } else {\n      chunk = new Uint8Array(segment);\n    }\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n  _collectFrame(byte) {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n  _collectCommand(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectHeaders(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n  _reinjectByte(byte) {\n    this._onByte(byte);\n  }\n  _collectHeaderKey(byte) {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectHeaderValue(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(header => {\n      return header[0] === 'content-length';\n    })[0];\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n  _collectBodyNullTerminated(byte) {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _collectBodyFixedSize(byte) {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if (this._bodyBytesRemaining-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n  _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n    try {\n      this.onFrame(this._results);\n    } catch (e) {\n      console.log(\"Ignoring an exception thrown by a frame handler. Original exception: \", e);\n    }\n    this._initState();\n  }\n  // Rec Descent Parser helpers\n  _consumeByte(byte) {\n    this._token.push(byte);\n  }\n  _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n  _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n  _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined\n    };\n    this._token = [];\n    this._headerKey = undefined;\n    this._onByte = this._collectFrame;\n  }\n}", "map": {"version": 3, "names": ["NULL", "LF", "CR", "COLON", "<PERSON><PERSON><PERSON>", "constructor", "onFrame", "onIncomingPing", "_encoder", "TextEncoder", "_decoder", "TextDecoder", "_token", "_initState", "parseChunk", "segment", "appendMissingNULLonIncoming", "arguments", "length", "undefined", "chunk", "encode", "Uint8Array", "chunkWithNull", "set", "i", "byte", "_onByte", "_collectFrame", "_collectCommand", "_reinjectByte", "_results", "command", "_consumeTokenAsUTF8", "_collectHeaders", "_consumeByte", "_setupCollectBody", "_collect<PERSON><PERSON><PERSON><PERSON>ey", "_<PERSON><PERSON><PERSON>", "_collectHeaderValue", "headers", "push", "contentLengthHeader", "filter", "header", "_bodyBytesRemaining", "parseInt", "_collectBodyFixedSize", "_collectBodyNullTerminated", "_retrievedBody", "binaryBody", "_consumeTokenAsRaw", "e", "console", "log", "decode", "rawResult"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@stomp/stompjs/src/parser.ts"], "sourcesContent": ["import { IRawFrameType } from './types.js';\n\n/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  private readonly _encoder = new TextEncoder();\n  private readonly _decoder = new TextDecoder();\n\n  // @ts-ignore - it always has a value\n  private _results: IRawFrameType;\n\n  private _token: number[] = [];\n  private _headerKey: string | undefined;\n  private _bodyBytesRemaining: number | undefined;\n\n  // @ts-ignore - it always has a value\n  private _onByte: (byte: number) => void;\n\n  public constructor(\n    public onFrame: (rawFrame: IRawFrameType) => void,\n    public onIncomingPing: () => void\n  ) {\n    this._initState();\n  }\n\n  public parseChunk(\n    segment: string | ArrayBuffer,\n    appendMissingNULLonIncoming: boolean = false\n  ) {\n    let chunk: Uint8Array;\n\n    if (typeof segment === 'string') {\n      chunk = this._encoder.encode(segment);\n    } else {\n      chunk = new Uint8Array(segment);\n    }\n\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n\n  private _collectFrame(byte: number): void {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n\n  private _collectCommand(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaders(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n\n  private _reinjectByte(byte: number) {\n    this._onByte(byte);\n  }\n\n  private _collectHeaderKey(byte: number): void {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaderValue(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([\n        this._headerKey as string,\n        this._consumeTokenAsUTF8(),\n      ]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(\n      (header: [string, string]) => {\n        return header[0] === 'content-length';\n      }\n    )[0];\n\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n\n  private _collectBodyNullTerminated(byte: number): void {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectBodyFixedSize(byte: number): void {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if ((this._bodyBytesRemaining as number)-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n\n    try {\n      this.onFrame(this._results);\n    } catch (e) {\n      console.log(\n        `Ignoring an exception thrown by a frame handler. Original exception: `,\n        e\n      );\n    }\n\n    this._initState();\n  }\n\n  // Rec Descent Parser helpers\n\n  private _consumeByte(byte: number) {\n    this._token.push(byte);\n  }\n\n  private _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n\n  private _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n\n  private _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined,\n    };\n\n    this._token = [];\n    this._headerKey = undefined;\n\n    this._onByte = this._collectFrame;\n  }\n}\n"], "mappings": "AAEA;;;AAGA,MAAMA,IAAI,GAAG,CAAC;AACd;;;AAGA,MAAMC,EAAE,GAAG,EAAE;AACb;;;AAGA,MAAMC,EAAE,GAAG,EAAE;AACb;;;AAGA,MAAMC,KAAK,GAAG,EAAE;AAEhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,OAAM,MAAOC,MAAM;EAcjBC,YACSC,OAA0C,EAC1CC,cAA0B;IAD1B,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IAfN,KAAAC,QAAQ,GAAG,IAAIC,WAAW,EAAE;IAC5B,KAAAC,QAAQ,GAAG,IAAIC,WAAW,EAAE;IAKrC,KAAAC,MAAM,GAAa,EAAE;IAW3B,IAAI,CAACC,UAAU,EAAE;EACnB;EAEOC,UAAUA,CACfC,OAA6B,EACe;IAAA,IAA5CC,2BAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuC,KAAK;IAE5C,IAAIG,KAAiB;IAErB,IAAI,OAAOL,OAAO,KAAK,QAAQ,EAAE;MAC/BK,KAAK,GAAG,IAAI,CAACZ,QAAQ,CAACa,MAAM,CAACN,OAAO,CAAC;IACvC,CAAC,MAAM;MACLK,KAAK,GAAG,IAAIE,UAAU,CAACP,OAAO,CAAC;IACjC;IAEA;IACA;IACA;IACA;IACA,IAAIC,2BAA2B,IAAII,KAAK,CAACA,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MAChE,MAAMK,aAAa,GAAG,IAAID,UAAU,CAACF,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC;MACtDK,aAAa,CAACC,GAAG,CAACJ,KAAK,EAAE,CAAC,CAAC;MAC3BG,aAAa,CAACH,KAAK,CAACF,MAAM,CAAC,GAAG,CAAC;MAC/BE,KAAK,GAAGG,aAAa;IACvB;IAEA;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACF,MAAM,EAAEO,CAAC,EAAE,EAAE;MACrC,MAAMC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC;MACrB,IAAI,CAACE,OAAO,CAACD,IAAI,CAAC;IACpB;EACF;EAEA;EACA;EAEQE,aAAaA,CAACF,IAAY;IAChC,IAAIA,IAAI,KAAK1B,IAAI,EAAE;MACjB;MACA;IACF;IACA,IAAI0B,IAAI,KAAKxB,EAAE,EAAE;MACf;MACA;IACF;IACA,IAAIwB,IAAI,KAAKzB,EAAE,EAAE;MACf;MACA,IAAI,CAACM,cAAc,EAAE;MACrB;IACF;IAEA,IAAI,CAACoB,OAAO,GAAG,IAAI,CAACE,eAAe;IACnC,IAAI,CAACC,aAAa,CAACJ,IAAI,CAAC;EAC1B;EAEQG,eAAeA,CAACH,IAAY;IAClC,IAAIA,IAAI,KAAKxB,EAAE,EAAE;MACf;MACA;IACF;IACA,IAAIwB,IAAI,KAAKzB,EAAE,EAAE;MACf,IAAI,CAAC8B,QAAQ,CAACC,OAAO,GAAG,IAAI,CAACC,mBAAmB,EAAE;MAClD,IAAI,CAACN,OAAO,GAAG,IAAI,CAACO,eAAe;MACnC;IACF;IAEA,IAAI,CAACC,YAAY,CAACT,IAAI,CAAC;EACzB;EAEQQ,eAAeA,CAACR,IAAY;IAClC,IAAIA,IAAI,KAAKxB,EAAE,EAAE;MACf;MACA;IACF;IACA,IAAIwB,IAAI,KAAKzB,EAAE,EAAE;MACf,IAAI,CAACmC,iBAAiB,EAAE;MACxB;IACF;IACA,IAAI,CAACT,OAAO,GAAG,IAAI,CAACU,iBAAiB;IACrC,IAAI,CAACP,aAAa,CAACJ,IAAI,CAAC;EAC1B;EAEQI,aAAaA,CAACJ,IAAY;IAChC,IAAI,CAACC,OAAO,CAACD,IAAI,CAAC;EACpB;EAEQW,iBAAiBA,CAACX,IAAY;IACpC,IAAIA,IAAI,KAAKvB,KAAK,EAAE;MAClB,IAAI,CAACmC,UAAU,GAAG,IAAI,CAACL,mBAAmB,EAAE;MAC5C,IAAI,CAACN,OAAO,GAAG,IAAI,CAACY,mBAAmB;MACvC;IACF;IACA,IAAI,CAACJ,YAAY,CAACT,IAAI,CAAC;EACzB;EAEQa,mBAAmBA,CAACb,IAAY;IACtC,IAAIA,IAAI,KAAKxB,EAAE,EAAE;MACf;MACA;IACF;IACA,IAAIwB,IAAI,KAAKzB,EAAE,EAAE;MACf,IAAI,CAAC8B,QAAQ,CAACS,OAAO,CAACC,IAAI,CAAC,CACzB,IAAI,CAACH,UAAoB,EACzB,IAAI,CAACL,mBAAmB,EAAE,CAC3B,CAAC;MACF,IAAI,CAACK,UAAU,GAAGnB,SAAS;MAC3B,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACO,eAAe;MACnC;IACF;IACA,IAAI,CAACC,YAAY,CAACT,IAAI,CAAC;EACzB;EAEQU,iBAAiBA,CAAA;IACvB,MAAMM,mBAAmB,GAAG,IAAI,CAACX,QAAQ,CAACS,OAAO,CAACG,MAAM,CACrDC,MAAwB,IAAI;MAC3B,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,gBAAgB;IACvC,CAAC,CACF,CAAC,CAAC,CAAC;IAEJ,IAAIF,mBAAmB,EAAE;MACvB,IAAI,CAACG,mBAAmB,GAAGC,QAAQ,CAACJ,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/D,IAAI,CAACf,OAAO,GAAG,IAAI,CAACoB,qBAAqB;IAC3C,CAAC,MAAM;MACL,IAAI,CAACpB,OAAO,GAAG,IAAI,CAACqB,0BAA0B;IAChD;EACF;EAEQA,0BAA0BA,CAACtB,IAAY;IAC7C,IAAIA,IAAI,KAAK1B,IAAI,EAAE;MACjB,IAAI,CAACiD,cAAc,EAAE;MACrB;IACF;IACA,IAAI,CAACd,YAAY,CAACT,IAAI,CAAC;EACzB;EAEQqB,qBAAqBA,CAACrB,IAAY;IACxC;IACA,IAAK,IAAI,CAACmB,mBAA8B,EAAE,KAAK,CAAC,EAAE;MAChD,IAAI,CAACI,cAAc,EAAE;MACrB;IACF;IACA,IAAI,CAACd,YAAY,CAACT,IAAI,CAAC;EACzB;EAEQuB,cAAcA,CAAA;IACpB,IAAI,CAAClB,QAAQ,CAACmB,UAAU,GAAG,IAAI,CAACC,kBAAkB,EAAE;IAEpD,IAAI;MACF,IAAI,CAAC7C,OAAO,CAAC,IAAI,CAACyB,QAAQ,CAAC;IAC7B,CAAC,CAAC,OAAOqB,CAAC,EAAE;MACVC,OAAO,CAACC,GAAG,0EAETF,CAAC,CACF;IACH;IAEA,IAAI,CAACvC,UAAU,EAAE;EACnB;EAEA;EAEQsB,YAAYA,CAACT,IAAY;IAC/B,IAAI,CAACd,MAAM,CAAC6B,IAAI,CAACf,IAAI,CAAC;EACxB;EAEQO,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAACvB,QAAQ,CAAC6C,MAAM,CAAC,IAAI,CAACJ,kBAAkB,EAAE,CAAC;EACxD;EAEQA,kBAAkBA,CAAA;IACxB,MAAMK,SAAS,GAAG,IAAIlC,UAAU,CAAC,IAAI,CAACV,MAAM,CAAC;IAC7C,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,OAAO4C,SAAS;EAClB;EAEQ3C,UAAUA,CAAA;IAChB,IAAI,CAACkB,QAAQ,GAAG;MACdC,OAAO,EAAEb,SAAS;MAClBqB,OAAO,EAAE,EAAE;MACXU,UAAU,EAAE/B;KACb;IAED,IAAI,CAACP,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC0B,UAAU,GAAGnB,SAAS;IAE3B,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACC,aAAa;EACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}