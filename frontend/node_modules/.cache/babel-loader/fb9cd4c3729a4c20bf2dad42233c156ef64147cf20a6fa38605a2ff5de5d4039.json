{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useRef}from'react';import{Box,Typography,Paper,Slider,Grid,Button,CircularProgress,Chip,useTheme,IconButton,Tooltip,Divider}from'@mui/material';import{useSpring,animated}from'react-spring';import{Line}from'react-chartjs-2';import{Chart as ChartJS,CategoryScale,LinearScale,PointElement,LineElement,Title,Tooltip as ChartTooltip,Legend,Filler}from'chart.js';import CalendarTodayIcon from'@mui/icons-material/CalendarToday';import AccessTimeIcon from'@mui/icons-material/AccessTime';import RefreshIcon from'@mui/icons-material/Refresh';import InfoIcon from'@mui/icons-material/Info';import WaterDropIcon from'@mui/icons-material/WaterDrop';import ThunderstormIcon from'@mui/icons-material/Thunderstorm';import LocationOnIcon from'@mui/icons-material/LocationOn';// Import the LocationSelector component\nimport LocationSelector from'./LocationSelector';// Register ChartJS components\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,PointElement,LineElement,Title,ChartTooltip,Legend,Filler);const TimelineRiskPredictor=_ref=>{let{formData,onForecastGenerated}=_ref;const theme=useTheme();const[loading,setLoading]=useState(false);const[forecastData,setForecastData]=useState(null);const[timeRange,setTimeRange]=useState(48);// Default 48 hours\nconst[selectedTimeIndex,setSelectedTimeIndex]=useState(null);const initialForecastGenerated=useRef(false);const[selectedLocation,setSelectedLocation]=useState({name:'Delhi',state:'Delhi',coordinates:[77.2090,28.6139]});// Animation for the component\nconst fadeIn=useSpring({from:{opacity:0,transform:'translateY(20px)'},to:{opacity:1,transform:'translateY(0)'},config:{tension:280,friction:20},delay:200});// Handle location selection\nconst handleLocationSelect=useCallback(location=>{setSelectedLocation(location);// Reset selected time index when location changes\nsetSelectedTimeIndex(null);},[]);// Fetch real forecast data from the API\nconst generateForecast=useCallback(()=>{// Check if formData is available and has the required properties\nif(!formData||!formData.rainfall||!formData.water_level||!formData.discharge){console.log(\"Form data is incomplete, cannot generate forecast\");return;}setLoading(true);// Call the weather forecast API\nfetch('/api/weather-forecast',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(_objectSpread(_objectSpread(_objectSpread({},formData),{},{hours:timeRange,// Use the selected location\nlocation:\"\".concat(selectedLocation.name,\",\").concat(selectedLocation.state,\",India\")},selectedLocation.userCoordinates&&{latitude:selectedLocation.userCoordinates[1],longitude:selectedLocation.userCoordinates[0]}),!selectedLocation.userCoordinates&&{latitude:selectedLocation.coordinates[1],longitude:selectedLocation.coordinates[0]}))}).then(response=>{if(!response.ok){throw new Error('Network response was not ok');}return response.json();}).then(data=>{var _data$summary,_data$summary2;// Set the forecast data from the API response\nsetForecastData({timestamps:data.timestamps,rainfallData:data.rainfallData,waterLevelData:data.waterLevelData,dischargeData:data.dischargeData,riskScoreData:data.riskScoreData,temperatureData:data.temperatureData,humidityData:data.humidityData,location:(_data$summary=data.summary)===null||_data$summary===void 0?void 0:_data$summary.location,country:(_data$summary2=data.summary)===null||_data$summary2===void 0?void 0:_data$summary2.country});// Notify parent component\nif(onForecastGenerated){onForecastGenerated({maxRiskScore:data.summary.maxRiskScore,maxRiskTime:data.summary.maxRiskTime,riskTrend:data.summary.riskTrend,location:data.summary.location,country:data.summary.country});}setLoading(false);}).catch(error=>{console.error('Error fetching forecast data:',error);setLoading(false);});},[formData,timeRange,selectedLocation,onForecastGenerated]);// Handle time range change\nconst handleTimeRangeChange=(event,newValue)=>{setTimeRange(newValue);};// Handle chart click to select a specific time\nconst handleChartClick=(_,elements)=>{if(elements.length>0){setSelectedTimeIndex(elements[0].index);}};// Prepare chart data\nconst chartData=forecastData?{labels:forecastData.timestamps,datasets:[{label:'Flood Risk Score',data:forecastData.riskScoreData,borderColor:'rgba(58, 134, 255, 0.8)',borderWidth:3,backgroundColor:ctx=>{const gradient=ctx.chart.ctx.createLinearGradient(0,0,0,400);gradient.addColorStop(0,'rgba(76, 201, 240, 0.6)');gradient.addColorStop(1,'rgba(58, 134, 255, 0.05)');return gradient;},fill:true,tension:0.4,pointRadius:ctx=>ctx.dataIndex===selectedTimeIndex?8:4,pointBackgroundColor:ctx=>{const value=ctx.raw;if(value>70)return theme.palette.error.main;if(value>40)return theme.palette.warning.main;return theme.palette.success.main;},pointBorderColor:'#fff',pointBorderWidth:2,pointHoverRadius:8,pointHoverBackgroundColor:ctx=>{const value=ctx.raw;if(value>70)return theme.palette.error.dark;if(value>40)return theme.palette.warning.dark;return theme.palette.success.dark;},pointHoverBorderColor:'#fff',pointHoverBorderWidth:3},// Add rainfall data as a secondary dataset\n{label:'Rainfall (mm)',data:forecastData.rainfallData,borderColor:'rgba(255, 89, 94, 0.7)',borderWidth:2,backgroundColor:'rgba(255, 89, 94, 0.1)',borderDash:[5,5],fill:true,tension:0.4,pointRadius:0,yAxisID:'y1'},// Add temperature data if available\n...(forecastData.temperatureData?[{label:'Temperature (°C)',data:forecastData.temperatureData,borderColor:'rgba(255, 159, 28, 0.7)',borderWidth:2,backgroundColor:'rgba(255, 159, 28, 0.0)',tension:0.4,pointRadius:0,yAxisID:'y2',hidden:true// Hidden by default, can be toggled by user\n}]:[])]}:null;// Chart options\nconst chartOptions={responsive:true,maintainAspectRatio:false,interaction:{mode:'index',intersect:false},animations:{tension:{duration:1000,easing:'easeInOutCubic',from:0.8,to:0.4,loop:false}},plugins:{legend:{position:'top',labels:{usePointStyle:true,padding:15,font:{size:12,weight:'bold'}}},tooltip:{backgroundColor:'rgba(255, 255, 255, 0.9)',titleColor:'#333',bodyColor:'#333',titleFont:{size:14,weight:'bold'},bodyFont:{size:13},padding:12,borderColor:'rgba(58, 134, 255, 0.3)',borderWidth:1,displayColors:true,boxWidth:8,boxHeight:8,boxPadding:4,usePointStyle:true,callbacks:{label:function(context){const value=context.raw;if(context.dataset.label==='Flood Risk Score'){let riskLevel='Low';let emoji='✅';if(value>70){riskLevel='High';emoji='⚠️';}else if(value>40){riskLevel='Medium';emoji='⚠️';}return\"\".concat(emoji,\" Risk Score: \").concat(value.toFixed(1),\" (\").concat(riskLevel,\")\");}else{return\"\\u2614 Rainfall: \".concat(value.toFixed(1),\" mm\");}}}}},scales:{y:{beginAtZero:true,max:100,title:{display:true,text:'Risk Score',font:{size:14,weight:'bold'},color:'rgba(58, 134, 255, 0.8)'},grid:{color:'rgba(0, 0, 0, 0.05)',borderDash:[5,5]},ticks:{font:{size:12},color:'rgba(0, 0, 0, 0.6)'}},y1:{beginAtZero:true,position:'right',max:Math.max(...((forecastData===null||forecastData===void 0?void 0:forecastData.rainfallData)||[0]))*1.2,title:{display:true,text:'Rainfall (mm)',font:{size:14,weight:'bold'},color:'rgba(255, 89, 94, 0.7)'},grid:{display:false},ticks:{font:{size:12},color:'rgba(255, 89, 94, 0.7)'}},y2:{beginAtZero:false,position:'right',min:Math.min(...((forecastData===null||forecastData===void 0?void 0:forecastData.temperatureData)||[20]))-5,max:Math.max(...((forecastData===null||forecastData===void 0?void 0:forecastData.temperatureData)||[30]))+5,title:{display:true,text:'Temperature (°C)',font:{size:14,weight:'bold'},color:'rgba(255, 159, 28, 0.7)'},grid:{display:false},ticks:{font:{size:12},color:'rgba(255, 159, 28, 0.7)'},display:false// Hidden by default, will be shown when temperature dataset is toggled\n},x:{title:{display:true,text:'Time',font:{size:14,weight:'bold'}},ticks:{maxRotation:45,minRotation:45,font:{size:11},color:'rgba(0, 0, 0, 0.6)'},grid:{color:'rgba(0, 0, 0, 0.05)'}}},onClick:handleChartClick};// Generate forecast when the component mounts with valid form data or when location changes\nuseEffect(()=>{// Only generate forecast if we have valid form data\nif(formData&&formData.rainfall&&formData.water_level&&formData.discharge){// If this is the first time, mark it as generated\nif(!initialForecastGenerated.current){initialForecastGenerated.current=true;}// Generate the forecast\ngenerateForecast();}},[formData,selectedLocation,generateForecast]);return/*#__PURE__*/_jsx(animated.div,{style:fadeIn,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:3,mb:4,position:'relative'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",gutterBottom:true,sx:{fontWeight:600,color:theme.palette.primary.dark},children:[\"Temporal Flood Risk Prediction\",/*#__PURE__*/_jsx(Tooltip,{title:\"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{ml:1,mb:1},children:/*#__PURE__*/_jsx(InfoIcon,{fontSize:\"small\"})})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,children:\"See how flood risk may evolve over the next hours and days based on your input parameters and weather forecasts.\"}),/*#__PURE__*/_jsx(LocationSelector,{onLocationSelect:handleLocationSelect,initialLocation:selectedLocation}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:8,children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,children:\"Forecast Time Range (hours)\"}),/*#__PURE__*/_jsx(Slider,{value:timeRange,onChange:handleTimeRangeChange,\"aria-labelledby\":\"time-range-slider\",valueLabelDisplay:\"auto\",step:12,marks:[{value:12,label:'12h'},{value:24,label:'24h'},{value:48,label:'48h'},{value:72,label:'72h'}],min:12,max:72})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,sx:{display:'flex',alignItems:'center',justifyContent:'flex-end'},children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:generateForecast,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(RefreshIcon,{}),children:forecastData?'Refresh Forecast':'Generate Forecast'})})]}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:300},children:/*#__PURE__*/_jsx(CircularProgress,{})}):forecastData?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{height:350,mb:3,p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(240,245,255,0.9) 100%)',boxShadow:'inset 0 0 15px rgba(58, 134, 255, 0.1)',border:'1px solid rgba(58, 134, 255, 0.2)',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-20,right:-20,width:100,height:100,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:-30,left:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(255,89,94,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:2,fontWeight:600,color:theme.palette.primary.dark,textAlign:'center',textShadow:'0px 1px 2px rgba(0,0,0,0.05)'},children:\"Temporal Flood Risk Analysis\"}),/*#__PURE__*/_jsx(Line,{data:chartData,options:chartOptions})]}),selectedTimeIndex!==null&&/*#__PURE__*/_jsx(Box,{sx:{mt:2,mb:3},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:0,overflow:'hidden',borderRadius:3,boxShadow:'0 8px 20px rgba(0, 0, 0, 0.08)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:2,background:'linear-gradient(90deg, rgba(58,134,255,0.9) 0%, rgba(76,201,240,0.9) 100%)',color:'white',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-20,right:-20,width:100,height:100,borderRadius:'50%',background:'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:600,textShadow:'0 1px 2px rgba(0,0,0,0.1)'},children:[/*#__PURE__*/_jsx(CalendarTodayIcon,{fontSize:\"small\",sx:{mr:1,verticalAlign:'middle'}}),forecastData.timestamps[selectedTimeIndex]]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:0.5,opacity:0.9},children:\"Detailed forecast information\"})]}),/*#__PURE__*/_jsx(Box,{sx:{p:2},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:\"linear-gradient(135deg, \".concat(forecastData.riskScoreData[selectedTimeIndex]>70?'rgba(255,89,94,0.1)':forecastData.riskScoreData[selectedTimeIndex]>40?'rgba(251,133,0,0.1)':'rgba(6,214,160,0.1)',\" 0%, rgba(255,255,255,0.7) 100%)\"),border:\"1px solid \".concat(forecastData.riskScoreData[selectedTimeIndex]>70?'rgba(255,89,94,0.3)':forecastData.riskScoreData[selectedTimeIndex]>40?'rgba(251,133,0,0.3)':'rgba(6,214,160,0.3)'),height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Risk Score\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:forecastData.riskScoreData[selectedTimeIndex]>70?theme.palette.error.main:forecastData.riskScoreData[selectedTimeIndex]>40?theme.palette.warning.main:theme.palette.success.main},children:forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Chip,{label:forecastData.riskScoreData[selectedTimeIndex]>70?'High Risk':forecastData.riskScoreData[selectedTimeIndex]>40?'Medium Risk':'Low Risk',size:\"small\",color:forecastData.riskScoreData[selectedTimeIndex]>70?'error':forecastData.riskScoreData[selectedTimeIndex]>40?'warning':'success',sx:{mt:1,fontWeight:'bold',boxShadow:'0 2px 5px rgba(0,0,0,0.1)'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,89,94,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(255,89,94,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:[/*#__PURE__*/_jsx(ThunderstormIcon,{fontSize:\"small\",sx:{mr:0.5,verticalAlign:'middle'}}),\"Rainfall\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(255,89,94,0.8)'},children:forecastData.rainfallData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"millimeters\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(76,201,240,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(76,201,240,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:[/*#__PURE__*/_jsx(WaterDropIcon,{fontSize:\"small\",sx:{mr:0.5,verticalAlign:'middle'}}),\"Water Level\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(76,201,240,0.8)'},children:forecastData.waterLevelData[selectedTimeIndex].toFixed(2)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"meters\"})]})}),forecastData.temperatureData&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,159,28,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(255,159,28,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Temperature\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(255,159,28,0.8)'},children:forecastData.temperatureData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"\\xB0C\"})]})}),forecastData.humidityData&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(111,134,214,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(111,134,214,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Humidity\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(111,134,214,0.8)'},children:forecastData.humidityData[selectedTimeIndex].toFixed(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"%\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(58,134,255,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(58,134,255,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Discharge\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(58,134,255,0.8)'},children:forecastData.dischargeData[selectedTimeIndex].toFixed(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"m\\xB3/s\"})]})})]})})]})}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3,p:2,borderRadius:2,backgroundColor:'rgba(76, 201, 240, 0.1)',border:'1px dashed rgba(58, 134, 255, 0.3)',display:'flex',alignItems:'flex-start'},children:[/*#__PURE__*/_jsx(InfoIcon,{fontSize:\"small\",sx:{mr:1.5,mt:0.3,color:theme.palette.primary.main}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{color:theme.palette.primary.dark,fontWeight:500,mb:0.5},children:[\"Interactive Forecast Visualization for \",forecastData.location||selectedLocation.name,\", \",selectedLocation.state]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"This forecast is based on real-time weather data and hydrological modeling for your selected location.\",/*#__PURE__*/_jsx(\"strong\",{children:\" Click on any point in the chart\"}),\" to see detailed predictions for that specific time. The blue line shows flood risk score, while the red dashed line shows rainfall intensity.\",forecastData.temperatureData&&' Temperature data is also available (toggle in chart legend).']})]})]})]}):/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',height:350,background:'linear-gradient(135deg, rgba(240,245,255,0.8) 0%, rgba(230,240,255,0.8) 100%)',borderRadius:3,border:'1px dashed rgba(58, 134, 255, 0.3)',position:'relative',overflow:'hidden',p:3},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-30,right:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:-30,left:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(AccessTimeIcon,{sx:{fontSize:80,color:'rgba(58, 134, 255, 0.3)',mb:3,filter:'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary.dark\",align:\"center\",sx:{mb:1,fontWeight:600},children:\"Temporal Flood Risk Prediction\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",align:\"center\",sx:{mb:3,maxWidth:450},children:\"Generate a forecast to visualize how flood risk may change over the next hours and days based on current conditions.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:generateForecast,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(RefreshIcon,{}),sx:{px:3,py:1.2,borderRadius:8,boxShadow:'0 4px 14px rgba(58, 134, 255, 0.3)','&:hover':{boxShadow:'0 6px 20px rgba(58, 134, 255, 0.4)',transform:'translateY(-2px)'},transition:'all 0.3s ease'},children:\"Generate Forecast\"})]})]})});};export default TimelineRiskPredictor;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Paper", "Slide<PERSON>", "Grid", "<PERSON><PERSON>", "CircularProgress", "Chip", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "Divider", "useSpring", "animated", "Line", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "ChartTooltip", "Legend", "Filler", "CalendarTodayIcon", "AccessTimeIcon", "RefreshIcon", "InfoIcon", "WaterDropIcon", "ThunderstormIcon", "LocationOnIcon", "LocationSelector", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "register", "TimelineRiskPredictor", "_ref", "formData", "onForecastGenerated", "theme", "loading", "setLoading", "forecastData", "setForecastData", "timeRange", "setTimeRange", "selectedTimeIndex", "setSelectedTimeIndex", "initialForecastGenerated", "selectedLocation", "setSelectedLocation", "name", "state", "coordinates", "fadeIn", "from", "opacity", "transform", "to", "config", "tension", "friction", "delay", "handleLocationSelect", "location", "generateForecast", "rainfall", "water_level", "discharge", "console", "log", "fetch", "method", "headers", "body", "JSON", "stringify", "_objectSpread", "hours", "concat", "userCoordinates", "latitude", "longitude", "then", "response", "ok", "Error", "json", "data", "_data$summary", "_data$summary2", "timestamps", "rainfallData", "waterLevelData", "dischargeData", "riskScoreData", "temperatureData", "humidityData", "summary", "country", "maxRiskScore", "maxRiskTime", "riskTrend", "catch", "error", "handleTimeRangeChange", "event", "newValue", "handleChartClick", "_", "elements", "length", "index", "chartData", "labels", "datasets", "label", "borderColor", "borderWidth", "backgroundColor", "ctx", "gradient", "chart", "createLinearGradient", "addColorStop", "fill", "pointRadius", "dataIndex", "pointBackgroundColor", "value", "raw", "palette", "main", "warning", "success", "pointBorderColor", "pointBorderWidth", "pointHoverRadius", "pointHoverBackgroundColor", "dark", "pointHoverBorderColor", "pointHoverBorderWidth", "borderDash", "yAxisID", "hidden", "chartOptions", "responsive", "maintainAspectRatio", "interaction", "mode", "intersect", "animations", "duration", "easing", "loop", "plugins", "legend", "position", "usePointStyle", "padding", "font", "size", "weight", "tooltip", "titleColor", "bodyColor", "titleFont", "bodyFont", "displayColors", "boxWidth", "boxHeight", "boxPadding", "callbacks", "context", "dataset", "riskLevel", "emoji", "toFixed", "scales", "y", "beginAtZero", "max", "title", "display", "text", "color", "grid", "ticks", "y1", "Math", "y2", "min", "x", "maxRotation", "minRotation", "onClick", "current", "div", "style", "children", "elevation", "sx", "p", "mb", "variant", "gutterBottom", "fontWeight", "primary", "ml", "fontSize", "paragraph", "onLocationSelect", "initialLocation", "container", "spacing", "item", "xs", "sm", "onChange", "valueLabelDisplay", "step", "marks", "alignItems", "justifyContent", "disabled", "startIcon", "height", "borderRadius", "background", "boxShadow", "border", "overflow", "top", "right", "width", "bottom", "left", "textAlign", "textShadow", "options", "mt", "mr", "verticalAlign", "md", "flexDirection", "filter", "align", "max<PERSON><PERSON><PERSON>", "px", "py", "transition"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Slider,\n  Grid,\n  Button,\n  CircularProgress,\n  Chip,\n  useTheme,\n  IconButton,\n  Tooltip,\n  Divider\n} from '@mui/material';\nimport { useSpring, animated } from 'react-spring';\nimport { Line } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler\n} from 'chart.js';\nimport CalendarTodayIcon from '@mui/icons-material/CalendarToday';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport InfoIcon from '@mui/icons-material/Info';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\n\n// Import the LocationSelector component\nimport LocationSelector from './LocationSelector';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\nconst TimelineRiskPredictor = ({ formData, onForecastGenerated }) => {\n  const theme = useTheme();\n  const [loading, setLoading] = useState(false);\n  const [forecastData, setForecastData] = useState(null);\n  const [timeRange, setTimeRange] = useState(48); // Default 48 hours\n  const [selectedTimeIndex, setSelectedTimeIndex] = useState(null);\n  const initialForecastGenerated = useRef(false);\n  const [selectedLocation, setSelectedLocation] = useState({\n    name: 'Delhi',\n    state: 'Delhi',\n    coordinates: [77.2090, 28.6139]\n  });\n\n  // Animation for the component\n  const fadeIn = useSpring({\n    from: { opacity: 0, transform: 'translateY(20px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { tension: 280, friction: 20 },\n    delay: 200\n  });\n\n  // Handle location selection\n  const handleLocationSelect = useCallback((location) => {\n    setSelectedLocation(location);\n    // Reset selected time index when location changes\n    setSelectedTimeIndex(null);\n  }, []);\n\n  // Fetch real forecast data from the API\n  const generateForecast = useCallback(() => {\n    // Check if formData is available and has the required properties\n    if (!formData || !formData.rainfall || !formData.water_level || !formData.discharge) {\n      console.log(\"Form data is incomplete, cannot generate forecast\");\n      return;\n    }\n\n    setLoading(true);\n\n    // Call the weather forecast API\n    fetch('/api/weather-forecast', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        ...formData,\n        hours: timeRange,\n        // Use the selected location\n        location: `${selectedLocation.name},${selectedLocation.state},India`,\n        // If we have precise coordinates from geolocation, use them\n        ...(selectedLocation.userCoordinates && {\n          latitude: selectedLocation.userCoordinates[1],\n          longitude: selectedLocation.userCoordinates[0]\n        }),\n        // Otherwise use the city coordinates\n        ...(!selectedLocation.userCoordinates && {\n          latitude: selectedLocation.coordinates[1],\n          longitude: selectedLocation.coordinates[0]\n        })\n      }),\n    })\n      .then(response => {\n        if (!response.ok) {\n          throw new Error('Network response was not ok');\n        }\n        return response.json();\n      })\n      .then(data => {\n        // Set the forecast data from the API response\n        setForecastData({\n          timestamps: data.timestamps,\n          rainfallData: data.rainfallData,\n          waterLevelData: data.waterLevelData,\n          dischargeData: data.dischargeData,\n          riskScoreData: data.riskScoreData,\n          temperatureData: data.temperatureData,\n          humidityData: data.humidityData,\n          location: data.summary?.location,\n          country: data.summary?.country\n        });\n\n        // Notify parent component\n        if (onForecastGenerated) {\n          onForecastGenerated({\n            maxRiskScore: data.summary.maxRiskScore,\n            maxRiskTime: data.summary.maxRiskTime,\n            riskTrend: data.summary.riskTrend,\n            location: data.summary.location,\n            country: data.summary.country\n          });\n        }\n\n        setLoading(false);\n      })\n      .catch(error => {\n        console.error('Error fetching forecast data:', error);\n        setLoading(false);\n      });\n  }, [formData, timeRange, selectedLocation, onForecastGenerated]);\n\n  // Handle time range change\n  const handleTimeRangeChange = (event, newValue) => {\n    setTimeRange(newValue);\n  };\n\n  // Handle chart click to select a specific time\n  const handleChartClick = (_, elements) => {\n    if (elements.length > 0) {\n      setSelectedTimeIndex(elements[0].index);\n    }\n  };\n\n  // Prepare chart data\n  const chartData = forecastData ? {\n    labels: forecastData.timestamps,\n    datasets: [\n      {\n        label: 'Flood Risk Score',\n        data: forecastData.riskScoreData,\n        borderColor: 'rgba(58, 134, 255, 0.8)',\n        borderWidth: 3,\n        backgroundColor: (ctx) => {\n          const gradient = ctx.chart.ctx.createLinearGradient(0, 0, 0, 400);\n          gradient.addColorStop(0, 'rgba(76, 201, 240, 0.6)');\n          gradient.addColorStop(1, 'rgba(58, 134, 255, 0.05)');\n          return gradient;\n        },\n        fill: true,\n        tension: 0.4,\n        pointRadius: (ctx) => ctx.dataIndex === selectedTimeIndex ? 8 : 4,\n        pointBackgroundColor: (ctx) => {\n          const value = ctx.raw;\n          if (value > 70) return theme.palette.error.main;\n          if (value > 40) return theme.palette.warning.main;\n          return theme.palette.success.main;\n        },\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointHoverRadius: 8,\n        pointHoverBackgroundColor: (ctx) => {\n          const value = ctx.raw;\n          if (value > 70) return theme.palette.error.dark;\n          if (value > 40) return theme.palette.warning.dark;\n          return theme.palette.success.dark;\n        },\n        pointHoverBorderColor: '#fff',\n        pointHoverBorderWidth: 3,\n      },\n      // Add rainfall data as a secondary dataset\n      {\n        label: 'Rainfall (mm)',\n        data: forecastData.rainfallData,\n        borderColor: 'rgba(255, 89, 94, 0.7)',\n        borderWidth: 2,\n        backgroundColor: 'rgba(255, 89, 94, 0.1)',\n        borderDash: [5, 5],\n        fill: true,\n        tension: 0.4,\n        pointRadius: 0,\n        yAxisID: 'y1',\n      },\n      // Add temperature data if available\n      ...(forecastData.temperatureData ? [{\n        label: 'Temperature (°C)',\n        data: forecastData.temperatureData,\n        borderColor: 'rgba(255, 159, 28, 0.7)',\n        borderWidth: 2,\n        backgroundColor: 'rgba(255, 159, 28, 0.0)',\n        tension: 0.4,\n        pointRadius: 0,\n        yAxisID: 'y2',\n        hidden: true, // Hidden by default, can be toggled by user\n      }] : [])\n    ]\n  } : null;\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    interaction: {\n      mode: 'index',\n      intersect: false,\n    },\n    animations: {\n      tension: {\n        duration: 1000,\n        easing: 'easeInOutCubic',\n        from: 0.8,\n        to: 0.4,\n        loop: false\n      }\n    },\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12,\n            weight: 'bold'\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#333',\n        bodyColor: '#333',\n        titleFont: {\n          size: 14,\n          weight: 'bold'\n        },\n        bodyFont: {\n          size: 13\n        },\n        padding: 12,\n        borderColor: 'rgba(58, 134, 255, 0.3)',\n        borderWidth: 1,\n        displayColors: true,\n        boxWidth: 8,\n        boxHeight: 8,\n        boxPadding: 4,\n        usePointStyle: true,\n        callbacks: {\n          label: function(context) {\n            const value = context.raw;\n\n            if (context.dataset.label === 'Flood Risk Score') {\n              let riskLevel = 'Low';\n              let emoji = '✅';\n\n              if (value > 70) {\n                riskLevel = 'High';\n                emoji = '⚠️';\n              } else if (value > 40) {\n                riskLevel = 'Medium';\n                emoji = '⚠️';\n              }\n\n              return `${emoji} Risk Score: ${value.toFixed(1)} (${riskLevel})`;\n            } else {\n              return `☔ Rainfall: ${value.toFixed(1)} mm`;\n            }\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Risk Score',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(58, 134, 255, 0.8)'\n        },\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)',\n          borderDash: [5, 5]\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(0, 0, 0, 0.6)'\n        }\n      },\n      y1: {\n        beginAtZero: true,\n        position: 'right',\n        max: Math.max(...forecastData?.rainfallData || [0]) * 1.2,\n        title: {\n          display: true,\n          text: 'Rainfall (mm)',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(255, 89, 94, 0.7)'\n        },\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(255, 89, 94, 0.7)'\n        }\n      },\n      y2: {\n        beginAtZero: false,\n        position: 'right',\n        min: Math.min(...(forecastData?.temperatureData || [20])) - 5,\n        max: Math.max(...(forecastData?.temperatureData || [30])) + 5,\n        title: {\n          display: true,\n          text: 'Temperature (°C)',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(255, 159, 28, 0.7)'\n        },\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(255, 159, 28, 0.7)'\n        },\n        display: false, // Hidden by default, will be shown when temperature dataset is toggled\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Time',\n          font: {\n            size: 14,\n            weight: 'bold'\n          }\n        },\n        ticks: {\n          maxRotation: 45,\n          minRotation: 45,\n          font: {\n            size: 11\n          },\n          color: 'rgba(0, 0, 0, 0.6)'\n        },\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)'\n        }\n      }\n    },\n    onClick: handleChartClick\n  };\n\n  // Generate forecast when the component mounts with valid form data or when location changes\n  useEffect(() => {\n    // Only generate forecast if we have valid form data\n    if (formData && formData.rainfall && formData.water_level && formData.discharge) {\n      // If this is the first time, mark it as generated\n      if (!initialForecastGenerated.current) {\n        initialForecastGenerated.current = true;\n      }\n      // Generate the forecast\n      generateForecast();\n    }\n  }, [formData, selectedLocation, generateForecast]);\n\n  return (\n    <animated.div style={fadeIn}>\n      <Paper elevation={3} sx={{ p: 3, mb: 4, position: 'relative' }}>\n        <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.dark }}>\n          Temporal Flood Risk Prediction\n          <Tooltip title=\"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\">\n            <IconButton size=\"small\" sx={{ ml: 1, mb: 1 }}>\n              <InfoIcon fontSize=\"small\" />\n            </IconButton>\n          </Tooltip>\n        </Typography>\n\n        <Typography variant=\"body1\" paragraph>\n          See how flood risk may evolve over the next hours and days based on your input parameters and weather forecasts.\n        </Typography>\n\n        {/* Location Selector */}\n        <LocationSelector\n          onLocationSelect={handleLocationSelect}\n          initialLocation={selectedLocation}\n        />\n\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12} sm={8}>\n            <Typography gutterBottom>\n              Forecast Time Range (hours)\n            </Typography>\n            <Slider\n              value={timeRange}\n              onChange={handleTimeRangeChange}\n              aria-labelledby=\"time-range-slider\"\n              valueLabelDisplay=\"auto\"\n              step={12}\n              marks={[\n                { value: 12, label: '12h' },\n                { value: 24, label: '24h' },\n                { value: 48, label: '48h' },\n                { value: 72, label: '72h' }\n              ]}\n              min={12}\n              max={72}\n            />\n          </Grid>\n          <Grid item xs={12} sm={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={generateForecast}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <RefreshIcon />}\n            >\n              {forecastData ? 'Refresh Forecast' : 'Generate Forecast'}\n            </Button>\n          </Grid>\n        </Grid>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>\n            <CircularProgress />\n          </Box>\n        ) : forecastData ? (\n          <>\n            <Box\n              sx={{\n                height: 350,\n                mb: 3,\n                p: 2,\n                borderRadius: 2,\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(240,245,255,0.9) 100%)',\n                boxShadow: 'inset 0 0 15px rgba(58, 134, 255, 0.1)',\n                border: '1px solid rgba(58, 134, 255, 0.2)',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n            >\n              {/* Decorative elements */}\n              <Box\n                sx={{\n                  position: 'absolute',\n                  top: -20,\n                  right: -20,\n                  width: 100,\n                  height: 100,\n                  borderRadius: '50%',\n                  background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                }}\n              />\n              <Box\n                sx={{\n                  position: 'absolute',\n                  bottom: -30,\n                  left: -30,\n                  width: 150,\n                  height: 150,\n                  borderRadius: '50%',\n                  background: 'radial-gradient(circle, rgba(255,89,94,0.1) 0%, rgba(0,0,0,0) 70%)',\n                }}\n              />\n\n              {/* Chart title */}\n              <Typography\n                variant=\"h6\"\n                sx={{\n                  mb: 2,\n                  fontWeight: 600,\n                  color: theme.palette.primary.dark,\n                  textAlign: 'center',\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.05)'\n                }}\n              >\n                Temporal Flood Risk Analysis\n              </Typography>\n\n              {/* The chart */}\n              <Line data={chartData} options={chartOptions} />\n            </Box>\n\n            {selectedTimeIndex !== null && (\n              <Box sx={{ mt: 2, mb: 3 }}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: 0,\n                    overflow: 'hidden',\n                    borderRadius: 3,\n                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.08)'\n                  }}\n                >\n                  {/* Header with timestamp */}\n                  <Box\n                    sx={{\n                      p: 2,\n                      background: 'linear-gradient(90deg, rgba(58,134,255,0.9) 0%, rgba(76,201,240,0.9) 100%)',\n                      color: 'white',\n                      position: 'relative',\n                      overflow: 'hidden'\n                    }}\n                  >\n                    {/* Decorative water ripple effect */}\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: -20,\n                        right: -20,\n                        width: 100,\n                        height: 100,\n                        borderRadius: '50%',\n                        background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)',\n                      }}\n                    />\n\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, textShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n                      <CalendarTodayIcon fontSize=\"small\" sx={{ mr: 1, verticalAlign: 'middle' }} />\n                      {forecastData.timestamps[selectedTimeIndex]}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ mt: 0.5, opacity: 0.9 }}>\n                      Detailed forecast information\n                    </Typography>\n                  </Box>\n\n                  {/* Content with metrics */}\n                  <Box sx={{ p: 2 }}>\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: `linear-gradient(135deg, ${\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.1)' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.1)' :\n                              'rgba(6,214,160,0.1)'\n                            } 0%, rgba(255,255,255,0.7) 100%)`,\n                            border: `1px solid ${\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.3)' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.3)' :\n                              'rgba(6,214,160,0.3)'\n                            }`,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            Risk Score\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{\n                            fontWeight: 'bold',\n                            color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? theme.palette.error.main :\n                                  forecastData.riskScoreData[selectedTimeIndex] > 40 ? theme.palette.warning.main :\n                                  theme.palette.success.main\n                          }}>\n                            {forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}\n                          </Typography>\n                          <Chip\n                            label={\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'High Risk' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'Medium Risk' : 'Low Risk'\n                            }\n                            size=\"small\"\n                            color={\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'error' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'warning' : 'success'\n                            }\n                            sx={{\n                              mt: 1,\n                              fontWeight: 'bold',\n                              boxShadow: '0 2px 5px rgba(0,0,0,0.1)'\n                            }}\n                          />\n                        </Box>\n                      </Grid>\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(255,89,94,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(255,89,94,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            <ThunderstormIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                            Rainfall\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(255,89,94,0.8)' }}>\n                            {forecastData.rainfallData[selectedTimeIndex].toFixed(1)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            millimeters\n                          </Typography>\n                        </Box>\n                      </Grid>\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(76,201,240,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(76,201,240,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            <WaterDropIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                            Water Level\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(76,201,240,0.8)' }}>\n                            {forecastData.waterLevelData[selectedTimeIndex].toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            meters\n                          </Typography>\n                        </Box>\n                      </Grid>\n\n                      {forecastData.temperatureData && (\n                        <Grid item xs={12} sm={6} md={3}>\n                          <Box\n                            sx={{\n                              p: 2,\n                              borderRadius: 2,\n                              background: 'linear-gradient(135deg, rgba(255,159,28,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                              border: '1px solid rgba(255,159,28,0.3)',\n                              height: '100%',\n                              display: 'flex',\n                              flexDirection: 'column',\n                              justifyContent: 'center',\n                              alignItems: 'center',\n                              textAlign: 'center'\n                            }}\n                          >\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                              Temperature\n                            </Typography>\n                            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(255,159,28,0.8)' }}>\n                              {forecastData.temperatureData[selectedTimeIndex].toFixed(1)}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                              °C\n                            </Typography>\n                          </Box>\n                        </Grid>\n                      )}\n\n                      {forecastData.humidityData && (\n                        <Grid item xs={12} sm={6} md={3}>\n                          <Box\n                            sx={{\n                              p: 2,\n                              borderRadius: 2,\n                              background: 'linear-gradient(135deg, rgba(111,134,214,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                              border: '1px solid rgba(111,134,214,0.3)',\n                              height: '100%',\n                              display: 'flex',\n                              flexDirection: 'column',\n                              justifyContent: 'center',\n                              alignItems: 'center',\n                              textAlign: 'center'\n                            }}\n                          >\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                              Humidity\n                            </Typography>\n                            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(111,134,214,0.8)' }}>\n                              {forecastData.humidityData[selectedTimeIndex].toFixed(0)}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                              %\n                            </Typography>\n                          </Box>\n                        </Grid>\n                      )}\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(58,134,255,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(58,134,255,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            Discharge\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(58,134,255,0.8)' }}>\n                            {forecastData.dischargeData[selectedTimeIndex].toFixed(0)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            m³/s\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Box>\n                </Paper>\n              </Box>\n            )}\n\n            <Box\n              sx={{\n                mt: 3,\n                p: 2,\n                borderRadius: 2,\n                backgroundColor: 'rgba(76, 201, 240, 0.1)',\n                border: '1px dashed rgba(58, 134, 255, 0.3)',\n                display: 'flex',\n                alignItems: 'flex-start'\n              }}\n            >\n              <InfoIcon\n                fontSize=\"small\"\n                sx={{\n                  mr: 1.5,\n                  mt: 0.3,\n                  color: theme.palette.primary.main\n                }}\n              />\n              <Box>\n                <Typography variant=\"body2\" sx={{ color: theme.palette.primary.dark, fontWeight: 500, mb: 0.5 }}>\n                  Interactive Forecast Visualization for {forecastData.location || selectedLocation.name}, {selectedLocation.state}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  This forecast is based on real-time weather data and hydrological modeling for your selected location.\n                  <strong> Click on any point in the chart</strong> to see detailed predictions for that specific time.\n                  The blue line shows flood risk score, while the red dashed line shows rainfall intensity.\n                  {forecastData.temperatureData && ' Temperature data is also available (toggle in chart legend).'}\n                </Typography>\n              </Box>\n            </Box>\n          </>\n        ) : (\n          <Box sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            height: 350,\n            background: 'linear-gradient(135deg, rgba(240,245,255,0.8) 0%, rgba(230,240,255,0.8) 100%)',\n            borderRadius: 3,\n            border: '1px dashed rgba(58, 134, 255, 0.3)',\n            position: 'relative',\n            overflow: 'hidden',\n            p: 3\n          }}>\n            {/* Decorative elements */}\n            <Box\n              sx={{\n                position: 'absolute',\n                top: -30,\n                right: -30,\n                width: 150,\n                height: 150,\n                borderRadius: '50%',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                bottom: -30,\n                left: -30,\n                width: 150,\n                height: 150,\n                borderRadius: '50%',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n              }}\n            />\n\n            <AccessTimeIcon sx={{\n              fontSize: 80,\n              color: 'rgba(58, 134, 255, 0.3)',\n              mb: 3,\n              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'\n            }} />\n\n            <Typography variant=\"h6\" color=\"primary.dark\" align=\"center\" sx={{ mb: 1, fontWeight: 600 }}>\n              Temporal Flood Risk Prediction\n            </Typography>\n\n            <Typography variant=\"body1\" color=\"text.secondary\" align=\"center\" sx={{ mb: 3, maxWidth: 450 }}>\n              Generate a forecast to visualize how flood risk may change over the next hours and days based on current conditions.\n            </Typography>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={generateForecast}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <RefreshIcon />}\n              sx={{\n                px: 3,\n                py: 1.2,\n                borderRadius: 8,\n                boxShadow: '0 4px 14px rgba(58, 134, 255, 0.3)',\n                '&:hover': {\n                  boxShadow: '0 6px 20px rgba(58, 134, 255, 0.4)',\n                  transform: 'translateY(-2px)'\n                },\n                transition: 'all 0.3s ease'\n              }}\n            >\n              Generate Forecast\n            </Button>\n          </Box>\n        )}\n      </Paper>\n    </animated.div>\n  );\n};\n\nexport default TimelineRiskPredictor;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,MAAM,CACNC,gBAAgB,CAChBC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,OAAO,CACPC,OAAO,KACF,eAAe,CACtB,OAASC,SAAS,CAAEC,QAAQ,KAAQ,cAAc,CAClD,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,KAAK,CACLX,OAAO,GAAI,CAAAY,YAAY,CACvBC,MAAM,CACNC,MAAM,KACD,UAAU,CACjB,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAE3D;AACA,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACAtB,OAAO,CAACuB,QAAQ,CACdtB,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,KAAK,CACLC,YAAY,CACZC,MAAM,CACNC,MACF,CAAC,CAED,KAAM,CAAAgB,qBAAqB,CAAGC,IAAA,EAAuC,IAAtC,CAAEC,QAAQ,CAAEC,mBAAoB,CAAC,CAAAF,IAAA,CAC9D,KAAM,CAAAG,KAAK,CAAGpC,QAAQ,CAAC,CAAC,CACxB,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmD,YAAY,CAAEC,eAAe,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChD,KAAM,CAACuD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAAyD,wBAAwB,CAAGtD,MAAM,CAAC,KAAK,CAAC,CAC9C,KAAM,CAACuD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,CACvD4D,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,CAAC,OAAO,CAAE,OAAO,CAChC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,MAAM,CAAG/C,SAAS,CAAC,CACvBgD,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,SAAS,CAAE,kBAAmB,CAAC,CACnDC,EAAE,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC9CE,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACtCC,KAAK,CAAE,GACT,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,oBAAoB,CAAGtE,WAAW,CAAEuE,QAAQ,EAAK,CACrDd,mBAAmB,CAACc,QAAQ,CAAC,CAC7B;AACAjB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAkB,gBAAgB,CAAGxE,WAAW,CAAC,IAAM,CACzC;AACA,GAAI,CAAC4C,QAAQ,EAAI,CAACA,QAAQ,CAAC6B,QAAQ,EAAI,CAAC7B,QAAQ,CAAC8B,WAAW,EAAI,CAAC9B,QAAQ,CAAC+B,SAAS,CAAE,CACnFC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChE,OACF,CAEA7B,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA8B,KAAK,CAAC,uBAAuB,CAAE,CAC7BC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACfxC,QAAQ,MACXyC,KAAK,CAAElC,SAAS,CAChB;AACAoB,QAAQ,IAAAe,MAAA,CAAK9B,gBAAgB,CAACE,IAAI,MAAA4B,MAAA,CAAI9B,gBAAgB,CAACG,KAAK,UAAQ,EAEhEH,gBAAgB,CAAC+B,eAAe,EAAI,CACtCC,QAAQ,CAAEhC,gBAAgB,CAAC+B,eAAe,CAAC,CAAC,CAAC,CAC7CE,SAAS,CAAEjC,gBAAgB,CAAC+B,eAAe,CAAC,CAAC,CAC/C,CAAC,EAEG,CAAC/B,gBAAgB,CAAC+B,eAAe,EAAI,CACvCC,QAAQ,CAAEhC,gBAAgB,CAACI,WAAW,CAAC,CAAC,CAAC,CACzC6B,SAAS,CAAEjC,gBAAgB,CAACI,WAAW,CAAC,CAAC,CAC3C,CAAC,CACF,CACH,CAAC,CAAC,CACC8B,IAAI,CAACC,QAAQ,EAAI,CAChB,GAAI,CAACA,QAAQ,CAACC,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CAAC,6BAA6B,CAAC,CAChD,CACA,MAAO,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CACxB,CAAC,CAAC,CACDJ,IAAI,CAACK,IAAI,EAAI,KAAAC,aAAA,CAAAC,cAAA,CACZ;AACA/C,eAAe,CAAC,CACdgD,UAAU,CAAEH,IAAI,CAACG,UAAU,CAC3BC,YAAY,CAAEJ,IAAI,CAACI,YAAY,CAC/BC,cAAc,CAAEL,IAAI,CAACK,cAAc,CACnCC,aAAa,CAAEN,IAAI,CAACM,aAAa,CACjCC,aAAa,CAAEP,IAAI,CAACO,aAAa,CACjCC,eAAe,CAAER,IAAI,CAACQ,eAAe,CACrCC,YAAY,CAAET,IAAI,CAACS,YAAY,CAC/BjC,QAAQ,EAAAyB,aAAA,CAAED,IAAI,CAACU,OAAO,UAAAT,aAAA,iBAAZA,aAAA,CAAczB,QAAQ,CAChCmC,OAAO,EAAAT,cAAA,CAAEF,IAAI,CAACU,OAAO,UAAAR,cAAA,iBAAZA,cAAA,CAAcS,OACzB,CAAC,CAAC,CAEF;AACA,GAAI7D,mBAAmB,CAAE,CACvBA,mBAAmB,CAAC,CAClB8D,YAAY,CAAEZ,IAAI,CAACU,OAAO,CAACE,YAAY,CACvCC,WAAW,CAAEb,IAAI,CAACU,OAAO,CAACG,WAAW,CACrCC,SAAS,CAAEd,IAAI,CAACU,OAAO,CAACI,SAAS,CACjCtC,QAAQ,CAAEwB,IAAI,CAACU,OAAO,CAAClC,QAAQ,CAC/BmC,OAAO,CAAEX,IAAI,CAACU,OAAO,CAACC,OACxB,CAAC,CAAC,CACJ,CAEA1D,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACD8D,KAAK,CAACC,KAAK,EAAI,CACdnC,OAAO,CAACmC,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD/D,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,CAACJ,QAAQ,CAAEO,SAAS,CAAEK,gBAAgB,CAAEX,mBAAmB,CAAC,CAAC,CAEhE;AACA,KAAM,CAAAmE,qBAAqB,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CACjD9D,YAAY,CAAC8D,QAAQ,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CACxC,GAAIA,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CACvBhE,oBAAoB,CAAC+D,QAAQ,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGvE,YAAY,CAAG,CAC/BwE,MAAM,CAAExE,YAAY,CAACiD,UAAU,CAC/BwB,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,kBAAkB,CACzB5B,IAAI,CAAE9C,YAAY,CAACqD,aAAa,CAChCsB,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAGC,GAAG,EAAK,CACxB,KAAM,CAAAC,QAAQ,CAAGD,GAAG,CAACE,KAAK,CAACF,GAAG,CAACG,oBAAoB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,GAAG,CAAC,CACjEF,QAAQ,CAACG,YAAY,CAAC,CAAC,CAAE,yBAAyB,CAAC,CACnDH,QAAQ,CAACG,YAAY,CAAC,CAAC,CAAE,0BAA0B,CAAC,CACpD,MAAO,CAAAH,QAAQ,CACjB,CAAC,CACDI,IAAI,CAAE,IAAI,CACVjE,OAAO,CAAE,GAAG,CACZkE,WAAW,CAAGN,GAAG,EAAKA,GAAG,CAACO,SAAS,GAAKjF,iBAAiB,CAAG,CAAC,CAAG,CAAC,CACjEkF,oBAAoB,CAAGR,GAAG,EAAK,CAC7B,KAAM,CAAAS,KAAK,CAAGT,GAAG,CAACU,GAAG,CACrB,GAAID,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAAC3B,KAAK,CAAC4B,IAAI,CAC/C,GAAIH,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACD,IAAI,CACjD,MAAO,CAAA7F,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACF,IAAI,CACnC,CAAC,CACDG,gBAAgB,CAAE,MAAM,CACxBC,gBAAgB,CAAE,CAAC,CACnBC,gBAAgB,CAAE,CAAC,CACnBC,yBAAyB,CAAGlB,GAAG,EAAK,CAClC,KAAM,CAAAS,KAAK,CAAGT,GAAG,CAACU,GAAG,CACrB,GAAID,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAAC3B,KAAK,CAACmC,IAAI,CAC/C,GAAIV,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACM,IAAI,CACjD,MAAO,CAAApG,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACK,IAAI,CACnC,CAAC,CACDC,qBAAqB,CAAE,MAAM,CAC7BC,qBAAqB,CAAE,CACzB,CAAC,CACD;AACA,CACEzB,KAAK,CAAE,eAAe,CACtB5B,IAAI,CAAE9C,YAAY,CAACkD,YAAY,CAC/ByB,WAAW,CAAE,wBAAwB,CACrCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAE,wBAAwB,CACzCuB,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAClBjB,IAAI,CAAE,IAAI,CACVjE,OAAO,CAAE,GAAG,CACZkE,WAAW,CAAE,CAAC,CACdiB,OAAO,CAAE,IACX,CAAC,CACD;AACA,IAAIrG,YAAY,CAACsD,eAAe,CAAG,CAAC,CAClCoB,KAAK,CAAE,kBAAkB,CACzB5B,IAAI,CAAE9C,YAAY,CAACsD,eAAe,CAClCqB,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAE,yBAAyB,CAC1C3D,OAAO,CAAE,GAAG,CACZkE,WAAW,CAAE,CAAC,CACdiB,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,IAAM;AAChB,CAAC,CAAC,CAAG,EAAE,CAAC,CAEZ,CAAC,CAAG,IAAI,CAER;AACA,KAAM,CAAAC,YAAY,CAAG,CACnBC,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,WAAW,CAAE,CACXC,IAAI,CAAE,OAAO,CACbC,SAAS,CAAE,KACb,CAAC,CACDC,UAAU,CAAE,CACV3F,OAAO,CAAE,CACP4F,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,gBAAgB,CACxBlG,IAAI,CAAE,GAAG,CACTG,EAAE,CAAE,GAAG,CACPgG,IAAI,CAAE,KACR,CACF,CAAC,CACDC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,KAAK,CACf3C,MAAM,CAAE,CACN4C,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACP5C,eAAe,CAAE,0BAA0B,CAC3C6C,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,CACTL,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDK,QAAQ,CAAE,CACRN,IAAI,CAAE,EACR,CAAC,CACDF,OAAO,CAAE,EAAE,CACX1C,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdkD,aAAa,CAAE,IAAI,CACnBC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CAAC,CACbb,aAAa,CAAE,IAAI,CACnBc,SAAS,CAAE,CACTxD,KAAK,CAAE,QAAAA,CAASyD,OAAO,CAAE,CACvB,KAAM,CAAA5C,KAAK,CAAG4C,OAAO,CAAC3C,GAAG,CAEzB,GAAI2C,OAAO,CAACC,OAAO,CAAC1D,KAAK,GAAK,kBAAkB,CAAE,CAChD,GAAI,CAAA2D,SAAS,CAAG,KAAK,CACrB,GAAI,CAAAC,KAAK,CAAG,GAAG,CAEf,GAAI/C,KAAK,CAAG,EAAE,CAAE,CACd8C,SAAS,CAAG,MAAM,CAClBC,KAAK,CAAG,IAAI,CACd,CAAC,IAAM,IAAI/C,KAAK,CAAG,EAAE,CAAE,CACrB8C,SAAS,CAAG,QAAQ,CACpBC,KAAK,CAAG,IAAI,CACd,CAEA,SAAAjG,MAAA,CAAUiG,KAAK,kBAAAjG,MAAA,CAAgBkD,KAAK,CAACgD,OAAO,CAAC,CAAC,CAAC,OAAAlG,MAAA,CAAKgG,SAAS,MAC/D,CAAC,IAAM,CACL,0BAAAhG,MAAA,CAAsBkD,KAAK,CAACgD,OAAO,CAAC,CAAC,CAAC,QACxC,CACF,CACF,CACF,CACF,CAAC,CACDC,MAAM,CAAE,CACNC,CAAC,CAAE,CACDC,WAAW,CAAE,IAAI,CACjBC,GAAG,CAAE,GAAG,CACRC,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,YAAY,CAClBxB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDuB,KAAK,CAAE,yBACT,CAAC,CACDC,IAAI,CAAE,CACJD,KAAK,CAAE,qBAAqB,CAC5B3C,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CACnB,CAAC,CACD6C,KAAK,CAAE,CACL3B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDwB,KAAK,CAAE,oBACT,CACF,CAAC,CACDG,EAAE,CAAE,CACFR,WAAW,CAAE,IAAI,CACjBvB,QAAQ,CAAE,OAAO,CACjBwB,GAAG,CAAEQ,IAAI,CAACR,GAAG,CAAC,IAAG,CAAA3I,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEkD,YAAY,GAAI,CAAC,CAAC,CAAC,EAAC,CAAG,GAAG,CACzD0F,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,eAAe,CACrBxB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDuB,KAAK,CAAE,wBACT,CAAC,CACDC,IAAI,CAAE,CACJH,OAAO,CAAE,KACX,CAAC,CACDI,KAAK,CAAE,CACL3B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDwB,KAAK,CAAE,wBACT,CACF,CAAC,CACDK,EAAE,CAAE,CACFV,WAAW,CAAE,KAAK,CAClBvB,QAAQ,CAAE,OAAO,CACjBkC,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAC,IAAI,CAAArJ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsD,eAAe,GAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAG,CAAC,CAC7DqF,GAAG,CAAEQ,IAAI,CAACR,GAAG,CAAC,IAAI,CAAA3I,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsD,eAAe,GAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAG,CAAC,CAC7DsF,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,kBAAkB,CACxBxB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDuB,KAAK,CAAE,yBACT,CAAC,CACDC,IAAI,CAAE,CACJH,OAAO,CAAE,KACX,CAAC,CACDI,KAAK,CAAE,CACL3B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDwB,KAAK,CAAE,yBACT,CAAC,CACDF,OAAO,CAAE,KAAO;AAClB,CAAC,CACDS,CAAC,CAAE,CACDV,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,MAAM,CACZxB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CACF,CAAC,CACDyB,KAAK,CAAE,CACLM,WAAW,CAAE,EAAE,CACfC,WAAW,CAAE,EAAE,CACflC,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDwB,KAAK,CAAE,oBACT,CAAC,CACDC,IAAI,CAAE,CACJD,KAAK,CAAE,qBACT,CACF,CACF,CAAC,CACDU,OAAO,CAAEvF,gBACX,CAAC,CAED;AACApH,SAAS,CAAC,IAAM,CACd;AACA,GAAI6C,QAAQ,EAAIA,QAAQ,CAAC6B,QAAQ,EAAI7B,QAAQ,CAAC8B,WAAW,EAAI9B,QAAQ,CAAC+B,SAAS,CAAE,CAC/E;AACA,GAAI,CAACpB,wBAAwB,CAACoJ,OAAO,CAAE,CACrCpJ,wBAAwB,CAACoJ,OAAO,CAAG,IAAI,CACzC,CACA;AACAnI,gBAAgB,CAAC,CAAC,CACpB,CACF,CAAC,CAAE,CAAC5B,QAAQ,CAAEY,gBAAgB,CAAEgB,gBAAgB,CAAC,CAAC,CAElD,mBACEpC,IAAA,CAACrB,QAAQ,CAAC6L,GAAG,EAACC,KAAK,CAAEhJ,MAAO,CAAAiJ,QAAA,cAC1BxK,KAAA,CAAClC,KAAK,EAAC2M,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAE9C,QAAQ,CAAE,UAAW,CAAE,CAAA0C,QAAA,eAC7DxK,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACC,YAAY,MAACJ,EAAE,CAAE,CAAEK,UAAU,CAAE,GAAG,CAAErB,KAAK,CAAElJ,KAAK,CAAC4F,OAAO,CAAC4E,OAAO,CAACpE,IAAK,CAAE,CAAA4D,QAAA,EAAC,gCAEhG,cAAA1K,IAAA,CAACxB,OAAO,EAACiL,KAAK,CAAC,8GAA8G,CAAAiB,QAAA,cAC3H1K,IAAA,CAACzB,UAAU,EAAC6J,IAAI,CAAC,OAAO,CAACwC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC5C1K,IAAA,CAACN,QAAQ,EAAC0L,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,CACN,CAAC,EACA,CAAC,cAEbpL,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACM,SAAS,MAAAX,QAAA,CAAC,kHAEtC,CAAY,CAAC,cAGb1K,IAAA,CAACF,gBAAgB,EACfwL,gBAAgB,CAAEpJ,oBAAqB,CACvCqJ,eAAe,CAAEnK,gBAAiB,CACnC,CAAC,cAEFlB,KAAA,CAAChC,IAAI,EAACsN,SAAS,MAACC,OAAO,CAAE,CAAE,CAACb,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxCxK,KAAA,CAAChC,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,eACvB1K,IAAA,CAACjC,UAAU,EAACiN,YAAY,MAAAN,QAAA,CAAC,6BAEzB,CAAY,CAAC,cACb1K,IAAA,CAAC/B,MAAM,EACLmI,KAAK,CAAErF,SAAU,CACjB8K,QAAQ,CAAEjH,qBAAsB,CAChC,kBAAgB,mBAAmB,CACnCkH,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,EAAG,CACTC,KAAK,CAAE,CACL,CAAE5F,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CACF2E,GAAG,CAAE,EAAG,CACRV,GAAG,CAAE,EAAG,CACT,CAAC,EACE,CAAC,cACPxJ,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAChB,EAAE,CAAE,CAAElB,OAAO,CAAE,MAAM,CAAEuC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,UAAW,CAAE,CAAAxB,QAAA,cAClG1K,IAAA,CAAC7B,MAAM,EACL4M,OAAO,CAAC,WAAW,CACnBnB,KAAK,CAAC,SAAS,CACfU,OAAO,CAAElI,gBAAiB,CAC1B+J,QAAQ,CAAExL,OAAQ,CAClByL,SAAS,CAAEzL,OAAO,cAAGX,IAAA,CAAC5B,gBAAgB,EAACgK,IAAI,CAAE,EAAG,CAACwB,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG5J,IAAA,CAACP,WAAW,GAAE,CAAE,CAAAiL,QAAA,CAErF7J,YAAY,CAAG,kBAAkB,CAAG,mBAAmB,CAClD,CAAC,CACL,CAAC,EACH,CAAC,CAENF,OAAO,cACNX,IAAA,CAAClC,GAAG,EAAC8M,EAAE,CAAE,CAAElB,OAAO,CAAE,MAAM,CAAEwC,cAAc,CAAE,QAAQ,CAAED,UAAU,CAAE,QAAQ,CAAEI,MAAM,CAAE,GAAI,CAAE,CAAA3B,QAAA,cACxF1K,IAAA,CAAC5B,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJyC,YAAY,cACdX,KAAA,CAAAE,SAAA,EAAAsK,QAAA,eACExK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFyB,MAAM,CAAE,GAAG,CACXvB,EAAE,CAAE,CAAC,CACLD,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,+EAA+E,CAC3FC,SAAS,CAAE,wCAAwC,CACnDC,MAAM,CAAE,mCAAmC,CAC3CzE,QAAQ,CAAE,UAAU,CACpB0E,QAAQ,CAAE,QACZ,CAAE,CAAAhC,QAAA,eAGF1K,IAAA,CAAClC,GAAG,EACF8M,EAAE,CAAE,CACF5C,QAAQ,CAAE,UAAU,CACpB2E,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVR,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cACFvM,IAAA,CAAClC,GAAG,EACF8M,EAAE,CAAE,CACF5C,QAAQ,CAAE,UAAU,CACpB8E,MAAM,CAAE,CAAC,EAAE,CACXC,IAAI,CAAE,CAAC,EAAE,CACTF,KAAK,CAAE,GAAG,CACVR,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,oEACd,CAAE,CACH,CAAC,cAGFvM,IAAA,CAACjC,UAAU,EACTgN,OAAO,CAAC,IAAI,CACZH,EAAE,CAAE,CACFE,EAAE,CAAE,CAAC,CACLG,UAAU,CAAE,GAAG,CACfrB,KAAK,CAAElJ,KAAK,CAAC4F,OAAO,CAAC4E,OAAO,CAACpE,IAAI,CACjCkG,SAAS,CAAE,QAAQ,CACnBC,UAAU,CAAE,8BACd,CAAE,CAAAvC,QAAA,CACH,8BAED,CAAY,CAAC,cAGb1K,IAAA,CAACpB,IAAI,EAAC+E,IAAI,CAAEyB,SAAU,CAAC8H,OAAO,CAAE9F,YAAa,CAAE,CAAC,EAC7C,CAAC,CAELnG,iBAAiB,GAAK,IAAI,eACzBjB,IAAA,CAAClC,GAAG,EAAC8M,EAAE,CAAE,CAAEuC,EAAE,CAAE,CAAC,CAAErC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACxBxK,KAAA,CAAClC,KAAK,EACJ2M,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJ6B,QAAQ,CAAE,QAAQ,CAClBJ,YAAY,CAAE,CAAC,CACfE,SAAS,CAAE,gCACb,CAAE,CAAA9B,QAAA,eAGFxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJ0B,UAAU,CAAE,4EAA4E,CACxF3C,KAAK,CAAE,OAAO,CACd5B,QAAQ,CAAE,UAAU,CACpB0E,QAAQ,CAAE,QACZ,CAAE,CAAAhC,QAAA,eAGF1K,IAAA,CAAClC,GAAG,EACF8M,EAAE,CAAE,CACF5C,QAAQ,CAAE,UAAU,CACpB2E,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVR,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,4EACd,CAAE,CACH,CAAC,cAEFrM,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,GAAG,CAAEgC,UAAU,CAAE,2BAA4B,CAAE,CAAAvC,QAAA,eACxF1K,IAAA,CAACT,iBAAiB,EAAC6L,QAAQ,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEwC,EAAE,CAAE,CAAC,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,CAC7ExM,YAAY,CAACiD,UAAU,CAAC7C,iBAAiB,CAAC,EACjC,CAAC,cACbjB,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAG,CAAExL,OAAO,CAAE,GAAI,CAAE,CAAA+I,QAAA,CAAC,+BAE3D,CAAY,CAAC,EACV,CAAC,cAGN1K,IAAA,CAAClC,GAAG,EAAC8M,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAH,QAAA,cAChBxK,KAAA,CAAChC,IAAI,EAACsN,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAf,QAAA,eACzB1K,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,4BAAArJ,MAAA,CACRrC,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1EJ,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1E,qBAAqB,oCACW,CAClCwL,MAAM,cAAAvJ,MAAA,CACJrC,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1EJ,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1E,qBAAqB,CACrB,CACFoL,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,CAAC,YAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAC3BK,UAAU,CAAE,MAAM,CAClBrB,KAAK,CAAE/I,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAGP,KAAK,CAAC4F,OAAO,CAAC3B,KAAK,CAAC4B,IAAI,CAC9E1F,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAGP,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACD,IAAI,CAC/E7F,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACF,IAC9B,CAAE,CAAAmE,QAAA,CACC7J,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CAC/C,CAAC,cACbpJ,IAAA,CAAC3B,IAAI,EACHkH,KAAK,CACH1E,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,WAAW,CAChEJ,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,aAAa,CAAG,UACtE,CACDmH,IAAI,CAAC,OAAO,CACZwB,KAAK,CACH/I,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,OAAO,CAC5DJ,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAAG,EAAE,CAAG,SAAS,CAAG,SAClE,CACD2J,EAAE,CAAE,CACFuC,EAAE,CAAE,CAAC,CACLlC,UAAU,CAAE,MAAM,CAClBuB,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,EACC,CAAC,CACF,CAAC,cAEPxM,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,6EAA6E,CACzFE,MAAM,CAAE,+BAA+B,CACvCJ,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEFxK,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,eAChF1K,IAAA,CAACJ,gBAAgB,EAACwL,QAAQ,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEwC,EAAE,CAAE,GAAG,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,WAEjF,EAAY,CAAC,cACbrN,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,MAAM,CAAErB,KAAK,CAAE,qBAAsB,CAAE,CAAAc,QAAA,CAC/E7J,YAAY,CAACkD,YAAY,CAAC9C,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CAC9C,CAAC,cACbpJ,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAI,CAAE,CAAAzC,QAAA,CAAC,aAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,cAEP1K,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FE,MAAM,CAAE,gCAAgC,CACxCJ,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEFxK,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,eAChF1K,IAAA,CAACL,aAAa,EAACyL,QAAQ,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEwC,EAAE,CAAE,GAAG,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,cAE9E,EAAY,CAAC,cACbrN,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,MAAM,CAAErB,KAAK,CAAE,sBAAuB,CAAE,CAAAc,QAAA,CAChF7J,YAAY,CAACmD,cAAc,CAAC/C,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CAChD,CAAC,cACbpJ,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAI,CAAE,CAAAzC,QAAA,CAAC,QAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,CAEN7J,YAAY,CAACsD,eAAe,eAC3BnE,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FE,MAAM,CAAE,gCAAgC,CACxCJ,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,CAAC,aAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,MAAM,CAAErB,KAAK,CAAE,sBAAuB,CAAE,CAAAc,QAAA,CAChF7J,YAAY,CAACsD,eAAe,CAAClD,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CACjD,CAAC,cACbpJ,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAI,CAAE,CAAAzC,QAAA,CAAC,OAEpE,CAAY,CAAC,EACV,CAAC,CACF,CACP,CAEA7J,YAAY,CAACuD,YAAY,eACxBpE,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,+EAA+E,CAC3FE,MAAM,CAAE,iCAAiC,CACzCJ,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,CAAC,UAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,MAAM,CAAErB,KAAK,CAAE,uBAAwB,CAAE,CAAAc,QAAA,CACjF7J,YAAY,CAACuD,YAAY,CAACnD,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CAC9C,CAAC,cACbpJ,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAI,CAAE,CAAAzC,QAAA,CAAC,GAEpE,CAAY,CAAC,EACV,CAAC,CACF,CACP,cAED1K,IAAA,CAAC9B,IAAI,EAACwN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0B,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FE,MAAM,CAAE,gCAAgC,CACxCJ,MAAM,CAAE,MAAM,CACd3C,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBe,SAAS,CAAE,QACb,CAAE,CAAAtC,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,CAAC,WAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACH,EAAE,CAAE,CAAEK,UAAU,CAAE,MAAM,CAAErB,KAAK,CAAE,sBAAuB,CAAE,CAAAc,QAAA,CAChF7J,YAAY,CAACoD,aAAa,CAAChD,iBAAiB,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,CAC/C,CAAC,cACbpJ,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAACgB,EAAE,CAAE,CAAEuC,EAAE,CAAE,GAAI,CAAE,CAAAzC,QAAA,CAAC,SAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,EACH,CAAC,CACJ,CAAC,EACD,CAAC,CACL,CACN,cAEDxK,KAAA,CAACpC,GAAG,EACF8M,EAAE,CAAE,CACFuC,EAAE,CAAE,CAAC,CACLtC,CAAC,CAAE,CAAC,CACJyB,YAAY,CAAE,CAAC,CACf5G,eAAe,CAAE,yBAAyB,CAC1C+G,MAAM,CAAE,oCAAoC,CAC5C/C,OAAO,CAAE,MAAM,CACfuC,UAAU,CAAE,YACd,CAAE,CAAAvB,QAAA,eAEF1K,IAAA,CAACN,QAAQ,EACP0L,QAAQ,CAAC,OAAO,CAChBR,EAAE,CAAE,CACFwC,EAAE,CAAE,GAAG,CACPD,EAAE,CAAE,GAAG,CACPvD,KAAK,CAAElJ,KAAK,CAAC4F,OAAO,CAAC4E,OAAO,CAAC3E,IAC/B,CAAE,CACH,CAAC,cACFrG,KAAA,CAACpC,GAAG,EAAA4M,QAAA,eACFxK,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEhB,KAAK,CAAElJ,KAAK,CAAC4F,OAAO,CAAC4E,OAAO,CAACpE,IAAI,CAAEmE,UAAU,CAAE,GAAG,CAAEH,EAAE,CAAE,GAAI,CAAE,CAAAJ,QAAA,EAAC,yCACxD,CAAC7J,YAAY,CAACsB,QAAQ,EAAIf,gBAAgB,CAACE,IAAI,CAAC,IAAE,CAACF,gBAAgB,CAACG,KAAK,EACtG,CAAC,cACbrB,KAAA,CAACnC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAAAc,QAAA,EAAC,wGAEjD,cAAA1K,IAAA,WAAA0K,QAAA,CAAQ,kCAAgC,CAAQ,CAAC,iJAEjD,CAAC7J,YAAY,CAACsD,eAAe,EAAI,+DAA+D,EACtF,CAAC,EACV,CAAC,EACH,CAAC,EACN,CAAC,cAEHjE,KAAA,CAACpC,GAAG,EAAC8M,EAAE,CAAE,CACPlB,OAAO,CAAE,MAAM,CACf6D,aAAa,CAAE,QAAQ,CACvBrB,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBI,MAAM,CAAE,GAAG,CACXE,UAAU,CAAE,+EAA+E,CAC3FD,YAAY,CAAE,CAAC,CACfG,MAAM,CAAE,oCAAoC,CAC5CzE,QAAQ,CAAE,UAAU,CACpB0E,QAAQ,CAAE,QAAQ,CAClB7B,CAAC,CAAE,CACL,CAAE,CAAAH,QAAA,eAEA1K,IAAA,CAAClC,GAAG,EACF8M,EAAE,CAAE,CACF5C,QAAQ,CAAE,UAAU,CACpB2E,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVR,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cACFvM,IAAA,CAAClC,GAAG,EACF8M,EAAE,CAAE,CACF5C,QAAQ,CAAE,UAAU,CACpB8E,MAAM,CAAE,CAAC,EAAE,CACXC,IAAI,CAAE,CAAC,EAAE,CACTF,KAAK,CAAE,GAAG,CACVR,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cAEFvM,IAAA,CAACR,cAAc,EAACoL,EAAE,CAAE,CAClBQ,QAAQ,CAAE,EAAE,CACZxB,KAAK,CAAE,yBAAyB,CAChCkB,EAAE,CAAE,CAAC,CACL0C,MAAM,CAAE,2CACV,CAAE,CAAE,CAAC,cAELxN,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,IAAI,CAACnB,KAAK,CAAC,cAAc,CAAC6D,KAAK,CAAC,QAAQ,CAAC7C,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEG,UAAU,CAAE,GAAI,CAAE,CAAAP,QAAA,CAAC,gCAE7F,CAAY,CAAC,cAEb1K,IAAA,CAACjC,UAAU,EAACgN,OAAO,CAAC,OAAO,CAACnB,KAAK,CAAC,gBAAgB,CAAC6D,KAAK,CAAC,QAAQ,CAAC7C,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAE4C,QAAQ,CAAE,GAAI,CAAE,CAAAhD,QAAA,CAAC,sHAEhG,CAAY,CAAC,cAEb1K,IAAA,CAAC7B,MAAM,EACL4M,OAAO,CAAC,WAAW,CACnBnB,KAAK,CAAC,SAAS,CACfU,OAAO,CAAElI,gBAAiB,CAC1B+J,QAAQ,CAAExL,OAAQ,CAClByL,SAAS,CAAEzL,OAAO,cAAGX,IAAA,CAAC5B,gBAAgB,EAACgK,IAAI,CAAE,EAAG,CAACwB,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG5J,IAAA,CAACP,WAAW,GAAE,CAAE,CACtFmL,EAAE,CAAE,CACF+C,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPtB,YAAY,CAAE,CAAC,CACfE,SAAS,CAAE,oCAAoC,CAC/C,SAAS,CAAE,CACTA,SAAS,CAAE,oCAAoC,CAC/C5K,SAAS,CAAE,kBACb,CAAC,CACDiM,UAAU,CAAE,eACd,CAAE,CAAAnD,QAAA,CACH,mBAED,CAAQ,CAAC,EACN,CACN,EACI,CAAC,CACI,CAAC,CAEnB,CAAC,CAED,cAAe,CAAApK,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}