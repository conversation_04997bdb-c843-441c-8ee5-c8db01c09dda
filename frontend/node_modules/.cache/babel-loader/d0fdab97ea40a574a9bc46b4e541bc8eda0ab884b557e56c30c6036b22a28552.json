{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits');\nfunction XHRFake(/* method, url, payload, opts */\n) {\n  var self = this;\n  EventEmitter.call(this);\n  this.to = setTimeout(function () {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\ninherits(XHRFake, EventEmitter);\nXHRFake.prototype.close = function () {\n  clearTimeout(this.to);\n};\nXHRFake.timeout = 2000;\nmodule.exports = XHRFake;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "XHRFake", "self", "call", "to", "setTimeout", "emit", "timeout", "prototype", "close", "clearTimeout", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/sender/xhr-fake.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  ;\n\nfunction XHRFake(/* method, url, payload, opts */) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.to = setTimeout(function() {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\n\ninherits(XHRFake, EventEmitter);\n\nXHRFake.prototype.close = function() {\n  clearTimeout(this.to);\n};\n\nXHRFake.timeout = 2000;\n\nmodule.exports = XHRFake;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAGlC,SAASE,OAAOA,CAAC;AAAA,EAAkC;EACjD,IAAIC,IAAI,GAAG,IAAI;EACfJ,YAAY,CAACK,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAI,CAACC,EAAE,GAAGC,UAAU,CAAC,YAAW;IAC9BH,IAAI,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC;EAChC,CAAC,EAAEL,OAAO,CAACM,OAAO,CAAC;AACrB;AAEAP,QAAQ,CAACC,OAAO,EAAEH,YAAY,CAAC;AAE/BG,OAAO,CAACO,SAAS,CAACC,KAAK,GAAG,YAAW;EACnCC,YAAY,CAAC,IAAI,CAACN,EAAE,CAAC;AACvB,CAAC;AAEDH,OAAO,CAACM,OAAO,GAAG,IAAI;AAEtBI,MAAM,CAACC,OAAO,GAAGX,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}