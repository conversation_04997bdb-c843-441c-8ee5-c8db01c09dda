{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { MapContainer, TileLayer, CircleMarker, Popup, useMap, LayerGroup, ZoomControl, Rectangle, Marker } from 'react-leaflet';\nimport L from 'leaflet';\nimport './FloodMap.css'; // Import custom CSS for map\nimport { Box, Typography, Skeleton, Paper, Chip, Divider, Card, CardContent, Grid, useTheme, Button, Tooltip, IconButton, Fade, TextField, InputAdornment, List, ListItem, ListItemText, ListItemIcon, Alert, Collapse, Snackbar } from '@mui/material';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport TerrainIcon from '@mui/icons-material/Terrain';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\nimport InfoIcon from '@mui/icons-material/Info';\nimport LayersIcon from '@mui/icons-material/Layers';\nimport MyLocationIcon from '@mui/icons-material/MyLocation';\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\nimport ZoomOutIcon from '@mui/icons-material/ZoomOut';\nimport SearchIcon from '@mui/icons-material/Search';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport CloseIcon from '@mui/icons-material/Close';\nimport WarningIcon from '@mui/icons-material/Warning';\nimport EngineeringIcon from '@mui/icons-material/Engineering';\nimport HomeIcon from '@mui/icons-material/Home';\nimport { useSpring, animated } from 'react-spring';\nimport axios from 'axios';\n\n// Legend component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MapLegend = () => {\n  _s();\n  const theme = useTheme();\n  const [expanded, setExpanded] = useState(false);\n  const legendAnimation = useSpring({\n    opacity: expanded ? 1 : 0.9,\n    height: expanded ? 280 : 40,\n    config: {\n      tension: 200,\n      friction: 20\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(animated.div, {\n    style: {\n      position: 'absolute',\n      bottom: 20,\n      right: 10,\n      zIndex: 1000,\n      backgroundColor: 'white',\n      padding: '10px 15px',\n      borderRadius: 8,\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      overflow: 'hidden',\n      width: 220,\n      ...legendAnimation\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: expanded ? 1 : 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        fontWeight: \"bold\",\n        children: \"Map Legend\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => setExpanded(!expanded),\n        sx: {\n          backgroundColor: expanded ? 'rgba(0,0,0,0.05)' : 'transparent',\n          transition: 'all 0.2s ease'\n        },\n        children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), expanded && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        fontWeight: \"bold\",\n        sx: {\n          mt: 1\n        },\n        children: \"Flood Risk Indicators\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 16,\n            height: 16,\n            borderRadius: '50%',\n            backgroundColor: theme.palette.error.main,\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"High Risk Area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mt: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 16,\n            height: 16,\n            borderRadius: '50%',\n            backgroundColor: theme.palette.success.main,\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Low Risk Area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        fontWeight: \"bold\",\n        sx: {\n          mt: 2\n        },\n        children: \"Risk Factors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(ThunderstormIcon, {\n          fontSize: \"small\",\n          sx: {\n            mr: 1,\n            color: theme.palette.info.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Heavy Rainfall (>200mm)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mt: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(TerrainIcon, {\n          fontSize: \"small\",\n          sx: {\n            mr: 1,\n            color: theme.palette.warning.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Low Elevation (<100m)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mt: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(WaterDropIcon, {\n          fontSize: \"small\",\n          sx: {\n            mr: 1,\n            color: theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"High Water Level (>8m)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          display: 'block',\n          mt: 2,\n          color: theme.palette.text.secondary\n        },\n        children: \"Click on markers for detailed information about specific locations.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n\n// Search component\n_s(MapLegend, \"mwDJGKNExhX0nkKvdyRwMtkIw7s=\", false, function () {\n  return [useTheme, useSpring];\n});\n_c = MapLegend;\nconst LocationSearch = ({\n  onLocationSelect\n}) => {\n  _s2();\n  const map = useMap();\n  const theme = useTheme();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [searching, setSearching] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Geocoding API (using OpenStreetMap Nominatim)\n  const searchLocation = useCallback(async query => {\n    if (!query || query.trim().length < 3) return;\n    setSearching(true);\n    setError(null);\n\n    // Clear any existing timeout to prevent memory leaks\n    let searchTimeout;\n    try {\n      // Add \"India\" to the search query to focus on Indian locations\n      const response = await axios.get(`https://nominatim.openstreetmap.org/search`, {\n        params: {\n          q: `${query}, India`,\n          format: 'json',\n          limit: 5,\n          countrycodes: 'in',\n          // Limit to India\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n      if (response.data && response.data.length > 0) {\n        setSearchResults(response.data);\n        setShowResults(true);\n      } else {\n        setSearchResults([]);\n        setError('No locations found. Try a different search term.');\n      }\n    } catch (err) {\n      console.error('Error searching for location:', err);\n      setError('Error searching for location. Please try again.');\n    } finally {\n      // Ensure searching state is properly reset with a slight delay\n      // This prevents UI flicker if multiple searches happen quickly\n      searchTimeout = setTimeout(() => {\n        setSearching(false);\n      }, 300);\n    }\n    return () => {\n      if (searchTimeout) clearTimeout(searchTimeout);\n    };\n  }, []);\n  const handleSearchChange = e => {\n    setSearchQuery(e.target.value);\n    if (e.target.value.trim().length === 0) {\n      setShowResults(false);\n    }\n  };\n  const handleSearchSubmit = e => {\n    e.preventDefault();\n    searchLocation(searchQuery);\n  };\n  const handleLocationClick = location => {\n    const lat = parseFloat(location.lat);\n    const lng = parseFloat(location.lon);\n\n    // Fly to the location\n    map.flyTo([lat, lng], 12, {\n      duration: 1.5\n    });\n\n    // Pass the selected location to parent component\n    onLocationSelect({\n      lat,\n      lng,\n      name: location.display_name,\n      type: location.type,\n      importance: location.importance,\n      address: location.address\n    });\n\n    // Clear search results\n    setShowResults(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'absolute',\n      top: 10,\n      left: 10,\n      zIndex: 1000,\n      width: 300,\n      maxWidth: 'calc(100% - 20px)'\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 1.5,\n        borderRadius: 2,\n        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearchSubmit,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search for a place in India...\",\n          value: searchQuery,\n          onChange: handleSearchChange,\n          variant: \"outlined\",\n          size: \"small\",\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this),\n            endAdornment: searching ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: 20,\n                  height: 20,\n                  borderRadius: '50%',\n                  border: '2px solid transparent',\n                  borderTopColor: theme.palette.primary.main,\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this) : searchQuery ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setSearchQuery('');\n                  setShowResults(false);\n                },\n                children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) : null,\n            sx: {\n              borderRadius: 2,\n              '&.Mui-focused': {\n                boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 1,\n          borderRadius: 1\n        },\n        onClose: () => setError(null),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: showResults && searchResults.length > 0,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            mt: 1,\n            maxHeight: 300,\n            overflow: 'auto'\n          },\n          children: searchResults.map((result, index) => {\n            var _result$address, _result$address2, _result$address3, _result$address4;\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleLocationClick(result),\n              sx: {\n                borderRadius: 1,\n                mb: 0.5,\n                '&:hover': {\n                  backgroundColor: theme.palette.action.hover\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  minWidth: 36\n                },\n                children: /*#__PURE__*/_jsxDEV(LocationOnIcon, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: ((_result$address = result.address) === null || _result$address === void 0 ? void 0 : _result$address.city) || ((_result$address2 = result.address) === null || _result$address2 === void 0 ? void 0 : _result$address2.town) || ((_result$address3 = result.address) === null || _result$address3 === void 0 ? void 0 : _result$address3.village) || ((_result$address4 = result.address) === null || _result$address4 === void 0 ? void 0 : _result$address4.state) || result.display_name.split(',')[0],\n                secondary: result.display_name,\n                secondaryTypographyProps: {\n                  noWrap: true,\n                  style: {\n                    fontSize: '0.75rem'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n\n// Map controls component\n_s2(LocationSearch, \"ZT45Dhxk40vrv6p+P2lDNHC0tWA=\", false, function () {\n  return [useMap, useTheme];\n});\n_c2 = LocationSearch;\nconst MapControls = () => {\n  _s3();\n  const map = useMap();\n  const theme = useTheme();\n  const handleZoomIn = () => {\n    map.zoomIn();\n  };\n  const handleZoomOut = () => {\n    map.zoomOut();\n  };\n  const handleLocate = () => {\n    map.locate({\n      setView: true,\n      maxZoom: 10\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'absolute',\n      top: 10,\n      right: 10,\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"Zoom In\",\n      placement: \"left\",\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleZoomIn,\n        sx: {\n          backgroundColor: 'white',\n          boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n          '&:hover': {\n            backgroundColor: theme.palette.grey[100]\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(ZoomInIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"Zoom Out\",\n      placement: \"left\",\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleZoomOut,\n        sx: {\n          backgroundColor: 'white',\n          boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n          '&:hover': {\n            backgroundColor: theme.palette.grey[100]\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(ZoomOutIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"My Location\",\n      placement: \"left\",\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleLocate,\n        sx: {\n          backgroundColor: 'white',\n          boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n          '&:hover': {\n            backgroundColor: theme.palette.grey[100]\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(MyLocationIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 385,\n    columnNumber: 5\n  }, this);\n};\n\n// Risk clusters component\n_s3(MapControls, \"aNgWutTDam7J16HADzz1FbQqsj0=\", false, function () {\n  return [useMap, useTheme];\n});\n_c3 = MapControls;\nconst RiskClusters = ({\n  mapData\n}) => {\n  _s4();\n  const map = useMap();\n  const theme = useTheme();\n\n  // Group points by risk level and proximity\n  useEffect(() => {\n    if (!mapData || mapData.length === 0) return;\n\n    // Find clusters of high-risk areas\n    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);\n    const clusters = [];\n\n    // Simple clustering algorithm (this is a simplified version)\n    highRiskPoints.forEach(point => {\n      const lat = point.Latitude;\n      const lng = point.Longitude;\n\n      // Check if point is already in a cluster\n      const inCluster = clusters.some(cluster => {\n        return Math.abs(cluster.centerLat - lat) < 1 && Math.abs(cluster.centerLng - lng) < 1;\n      });\n      if (!inCluster && highRiskPoints.filter(p => Math.abs(p.Latitude - lat) < 1 && Math.abs(p.Longitude - lng) < 1).length > 3) {\n        // Create a new cluster if there are at least 3 high-risk points in proximity\n        clusters.push({\n          centerLat: lat,\n          centerLng: lng,\n          count: highRiskPoints.filter(p => Math.abs(p.Latitude - lat) < 1 && Math.abs(p.Longitude - lng) < 1).length\n        });\n      }\n    });\n    return () => {\n      // Cleanup if needed\n    };\n  }, [mapData, map]);\n  return null; // Visual representation is handled by the markers\n};\n\n// Risk mitigation measures component\n_s4(RiskClusters, \"huawssHOZB3aewFt2Ws/ltaGkOU=\", false, function () {\n  return [useMap, useTheme];\n});\n_c4 = RiskClusters;\nconst RiskMitigationMeasures = ({\n  riskLevel,\n  riskFactors\n}) => {\n  _s5();\n  const theme = useTheme();\n\n  // Define mitigation measures based on risk factors\n  const getMitigationMeasures = () => {\n    const measures = [];\n\n    // General measures based on risk level\n    if (riskLevel === 'high') {\n      measures.push({\n        title: 'Evacuation Planning',\n        description: 'Develop and practice evacuation plans. Identify safe routes and emergency shelters.',\n        icon: /*#__PURE__*/_jsxDEV(HomeIcon, {\n          sx: {\n            color: theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 15\n        }, this)\n      });\n      measures.push({\n        title: 'Early Warning System',\n        description: 'Install flood early warning systems and stay updated with weather forecasts.',\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {\n          sx: {\n            color: theme.palette.error.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 15\n        }, this)\n      });\n    }\n\n    // Specific measures based on risk factors\n    riskFactors.forEach(factor => {\n      if (factor.factor === 'Heavy Rainfall' && factor.severity === 'high') {\n        measures.push({\n          title: 'Drainage Improvement',\n          description: 'Ensure proper drainage systems are in place and regularly maintained to handle heavy rainfall.',\n          icon: /*#__PURE__*/_jsxDEV(EngineeringIcon, {\n            sx: {\n              color: theme.palette.info.main\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 17\n          }, this)\n        });\n      }\n      if (factor.factor === 'Low Elevation' && factor.severity === 'high') {\n        measures.push({\n          title: 'Elevated Structures',\n          description: 'Consider raising the foundation of buildings or using stilts in flood-prone low-lying areas.',\n          icon: /*#__PURE__*/_jsxDEV(HomeIcon, {\n            sx: {\n              color: theme.palette.warning.main\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this)\n        });\n      }\n      if (factor.factor === 'High Water Level' && factor.severity === 'high') {\n        measures.push({\n          title: 'Flood Barriers',\n          description: 'Install temporary or permanent flood barriers, sandbags, or flood walls to protect property.',\n          icon: /*#__PURE__*/_jsxDEV(WaterDropIcon, {\n            sx: {\n              color: theme.palette.error.main\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 17\n          }, this)\n        });\n      }\n    });\n\n    // Add general measures if none specific were found\n    if (measures.length === 0) {\n      measures.push({\n        title: 'Regular Monitoring',\n        description: 'Monitor weather forecasts and water levels during monsoon season.',\n        icon: /*#__PURE__*/_jsxDEV(InfoIcon, {\n          sx: {\n            color: theme.palette.info.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 15\n        }, this)\n      });\n    }\n    return measures;\n  };\n  const measures = getMitigationMeasures();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle2\",\n      fontWeight: \"bold\",\n      gutterBottom: true,\n      children: \"Recommended Mitigation Measures:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this), measures.map((measure, index) => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        mb: 1.5,\n        p: 1,\n        borderRadius: 1,\n        backgroundColor: 'rgba(0, 0, 0, 0.02)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mr: 1.5,\n          mt: 0.5\n        },\n        children: measure.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"medium\",\n          children: measure.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: measure.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 554,\n    columnNumber: 5\n  }, this);\n};\n\n// Get location name from coordinates\n_s5(RiskMitigationMeasures, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c5 = RiskMitigationMeasures;\nconst useReverseGeocoding = () => {\n  _s6();\n  const [locationCache, setLocationCache] = useState({});\n  const getLocationName = useCallback(async (lat, lng) => {\n    // Check cache first\n    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      return locationCache[cacheKey];\n    }\n    try {\n      const response = await axios.get(`https://nominatim.openstreetmap.org/reverse`, {\n        params: {\n          lat,\n          lon: lng,\n          format: 'json',\n          zoom: 10,\n          // Adjust zoom level for appropriate place name detail\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n      if (response.data) {\n        // Extract the most relevant name from the response\n        const locationData = response.data;\n        let placeName = '';\n\n        // Try to get the most specific name first\n        if (locationData.address) {\n          placeName = locationData.address.village || locationData.address.town || locationData.address.city || locationData.address.county || locationData.address.state_district || locationData.address.state;\n        }\n\n        // If no specific name found, use the display name\n        if (!placeName && locationData.display_name) {\n          placeName = locationData.display_name.split(',')[0];\n        }\n\n        // If still no name, use coordinates\n        if (!placeName) {\n          placeName = `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;\n        }\n\n        // Cache the result\n        setLocationCache(prev => ({\n          ...prev,\n          [cacheKey]: {\n            name: placeName,\n            fullName: locationData.display_name || '',\n            address: locationData.address || {}\n          }\n        }));\n        return {\n          name: placeName,\n          fullName: locationData.display_name || '',\n          address: locationData.address || {}\n        };\n      }\n    } catch (error) {\n      console.error('Error in reverse geocoding:', error);\n    }\n\n    // Return a default if geocoding fails\n    return {\n      name: `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`,\n      fullName: '',\n      address: {}\n    };\n  }, [locationCache]);\n  return {\n    getLocationName,\n    locationCache\n  };\n};\n\n// Enhanced popup content\n_s6(useReverseGeocoding, \"asqXLf5YE7+K+xE0A74NuRWgwHc=\");\nconst EnhancedPopup = ({\n  point,\n  showMitigationMeasures = false,\n  locationName = null\n}) => {\n  _s7();\n  const theme = useTheme();\n  const isHighRisk = point.Flood_Prediction === 1;\n  const {\n    getLocationName,\n    locationCache\n  } = useReverseGeocoding();\n  const [location, setLocation] = useState(null);\n  const [loading, setLoading] = useState(!locationName);\n  useEffect(() => {\n    if (locationName) {\n      setLocation(locationName);\n      setLoading(false);\n      return;\n    }\n\n    // Check if we already have this location in cache\n    const cacheKey = `${point.Latitude.toFixed(4)},${point.Longitude.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      setLocation(locationCache[cacheKey]);\n      setLoading(false);\n      return;\n    }\n\n    // Fetch location name\n    const fetchLocationName = async () => {\n      setLoading(true);\n      const result = await getLocationName(point.Latitude, point.Longitude);\n      setLocation(result);\n      setLoading(false);\n    };\n    fetchLocationName();\n  }, [point, locationName, getLocationName, locationCache]);\n\n  // Determine risk factors\n  const riskFactors = [];\n  if (point['Rainfall (mm)'] > 200) {\n    riskFactors.push({\n      factor: 'Heavy Rainfall',\n      value: `${point['Rainfall (mm)']} mm`,\n      icon: /*#__PURE__*/_jsxDEV(ThunderstormIcon, {\n        fontSize: \"small\",\n        sx: {\n          color: theme.palette.info.main\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 13\n      }, this),\n      severity: 'high'\n    });\n  }\n  if (point['Elevation (m)'] < 100) {\n    riskFactors.push({\n      factor: 'Low Elevation',\n      value: `${point['Elevation (m)']} m`,\n      icon: /*#__PURE__*/_jsxDEV(TerrainIcon, {\n        fontSize: \"small\",\n        sx: {\n          color: theme.palette.warning.main\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 13\n      }, this),\n      severity: point['Elevation (m)'] < 50 ? 'high' : 'medium'\n    });\n  }\n  if (point['Water Level (m)'] > 6) {\n    riskFactors.push({\n      factor: 'High Water Level',\n      value: `${point['Water Level (m)']} m`,\n      icon: /*#__PURE__*/_jsxDEV(WaterDropIcon, {\n        fontSize: \"small\",\n        sx: {\n          color: theme.palette.error.main\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 13\n      }, this),\n      severity: point['Water Level (m)'] > 8 ? 'high' : 'medium'\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      maxWidth: showMitigationMeasures ? 350 : 280,\n      overflowX: 'hidden'\n    },\n    className: \"popup-content\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      fontWeight: \"bold\",\n      sx: {\n        mb: 0.5,\n        color: isHighRisk ? theme.palette.error.main : theme.palette.success.main,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1,\n        position: 'sticky',\n        top: 0,\n        backgroundColor: 'white',\n        zIndex: 10,\n        py: 0.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(WaterDropIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this), isHighRisk ? 'High Flood Risk Area' : 'Low Flood Risk Area']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        my: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 16,\n          height: 16,\n          borderRadius: '50%',\n          border: '2px solid transparent',\n          borderTopColor: theme.palette.primary.main,\n          animation: 'spin 1s linear infinite',\n          mr: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Loading location...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 9\n    }, this) : location ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        fontWeight: \"medium\",\n        children: location.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 11\n      }, this), location.fullName && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          display: 'block',\n          mb: 0.5\n        },\n        children: location.fullName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 1.5\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 791,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 1,\n      sx: {\n        mb: 1.5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: \"Rainfall\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"medium\",\n          children: [point['Rainfall (mm)'], \" mm\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: \"Elevation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"medium\",\n          children: [point['Elevation (m)'], \" m\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: \"Water Level\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"medium\",\n          children: [point['Water Level (m)'], \" m\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: \"Coordinates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"medium\",\n          children: [point.Latitude.toFixed(2), \", \", point.Longitude.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 793,\n      columnNumber: 7\n    }, this), riskFactors.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          display: 'block',\n          mb: 0.5\n        },\n        children: \"Risk Factors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 830,\n        columnNumber: 11\n      }, this), riskFactors.map((factor, idx) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 0.5\n        },\n        children: [factor.icon, /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            ml: 0.5\n          },\n          children: [factor.factor, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: factor.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 15\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true), isHighRisk && !showMitigationMeasures && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 1.5,\n        p: 1,\n        backgroundColor: 'rgba(255, 89, 94, 0.1)',\n        borderRadius: 1,\n        borderLeft: `3px solid ${theme.palette.error.main}`\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          display: 'block',\n          fontWeight: 'medium'\n        },\n        children: \"Recommendation:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"This area requires flood mitigation measures and close monitoring during heavy rainfall.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 846,\n      columnNumber: 9\n    }, this), showMitigationMeasures && /*#__PURE__*/_jsxDEV(RiskMitigationMeasures, {\n      riskLevel: isHighRisk ? 'high' : 'low',\n      riskFactors: riskFactors\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 733,\n    columnNumber: 5\n  }, this);\n};\n_s7(EnhancedPopup, \"/q6/+Pyb69r/t40Mg0Ql1m7XW5I=\", false, function () {\n  return [useTheme, useReverseGeocoding];\n});\n_c6 = EnhancedPopup;\nconst FloodMap = ({\n  mapData\n}) => {\n  _s8();\n  const theme = useTheme();\n  const [loading, setLoading] = useState(true);\n  const [searchedLocation, setSearchedLocation] = useState(null);\n  const [nearestPoint, setNearestPoint] = useState(null);\n  const [showLocationInfo, setShowLocationInfo] = useState(false);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  useEffect(() => {\n    if (mapData && mapData.length > 0) {\n      // Add a small delay to simulate loading for better UX, but ensure it stops\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 1000);\n      return () => {\n        clearTimeout(timer);\n        // Ensure loading is set to false when component unmounts\n        setLoading(false);\n      };\n    } else {\n      // If no data, still set loading to false after a short delay\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 500);\n      return () => clearTimeout(timer);\n    }\n  }, [mapData]);\n\n  // Find the nearest data point to a searched location\n  const findNearestPoint = useCallback(location => {\n    if (!mapData || mapData.length === 0) return null;\n    let nearest = null;\n    let minDistance = Infinity;\n    mapData.forEach(point => {\n      const distance = Math.sqrt(Math.pow(point.Latitude - location.lat, 2) + Math.pow(point.Longitude - location.lng, 2));\n      if (distance < minDistance) {\n        minDistance = distance;\n        nearest = point;\n      }\n    });\n\n    // Only consider it a match if it's reasonably close (within ~50km)\n    if (minDistance > 0.5) {\n      setNotification({\n        open: true,\n        message: 'No exact flood data for this location. Showing nearest available data point.',\n        severity: 'info'\n      });\n    }\n    return nearest;\n  }, [mapData]);\n\n  // Handle location selection from search\n  const handleLocationSelect = useCallback(location => {\n    setSearchedLocation(location);\n    const nearest = findNearestPoint(location);\n    setNearestPoint(nearest);\n    setShowLocationInfo(true);\n  }, [findNearestPoint]);\n\n  // Close notification\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n  if (!mapData || mapData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        height: 500,\n        borderRadius: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"rectangular\",\n        width: \"100%\",\n        height: \"100%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Count high and low risk areas\n  const highRiskCount = mapData.filter(point => point.Flood_Prediction === 1).length;\n  const lowRiskCount = mapData.filter(point => point.Flood_Prediction === 0).length;\n\n  // Calculate statistics\n  const avgRainfall = mapData.reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / mapData.length;\n  const avgElevation = mapData.reduce((sum, point) => sum + point['Elevation (m)'], 0) / mapData.length;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.error.light}20 0%, ${theme.palette.error.light}05 100%)`,\n              borderLeft: `4px solid ${theme.palette.error.main}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"High Risk Areas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              color: theme.palette.error.main,\n              children: highRiskCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.success.light}20 0%, ${theme.palette.success.light}05 100%)`,\n              borderLeft: `4px solid ${theme.palette.success.main}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Low Risk Areas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              color: theme.palette.success.main,\n              children: lowRiskCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.info.light}20 0%, ${theme.palette.info.light}05 100%)`,\n              borderLeft: `4px solid ${theme.palette.info.main}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Avg. Rainfall\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              color: theme.palette.info.main,\n              children: [avgRainfall.toFixed(1), \" \", /*#__PURE__*/_jsxDEV(Typography, {\n                component: \"span\",\n                variant: \"body2\",\n                children: \"mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1004,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.warning.light}20 0%, ${theme.palette.warning.light}05 100%)`,\n              borderLeft: `4px solid ${theme.palette.warning.main}`\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Avg. Elevation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              color: theme.palette.warning.main,\n              children: [avgElevation.toFixed(1), \" \", /*#__PURE__*/_jsxDEV(Typography, {\n                component: \"span\",\n                variant: \"body2\",\n                children: \"m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 965,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 964,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        height: 600,\n        borderRadius: 2,\n        overflow: 'hidden'\n      },\n      children: [loading && /*#__PURE__*/_jsxDEV(Fade, {\n        in: loading,\n        timeout: 300,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: 'rgba(255,255,255,0.8)',\n            zIndex: 1000\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                width: 60,\n                height: 60,\n                margin: '0 auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  borderRadius: '50%',\n                  border: '3px solid transparent',\n                  borderTopColor: theme.palette.primary.main,\n                  animation: 'spin 1s linear infinite',\n                  '@keyframes spin': {\n                    '0%': {\n                      transform: 'rotate(0deg)'\n                    },\n                    '100%': {\n                      transform: 'rotate(360deg)'\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mt: 2\n              },\n              children: \"Loading map data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1047,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MapContainer, {\n        center: [22.0, 80.0],\n        zoom: 5,\n        scrollWheelZoom: true,\n        style: {\n          height: '100%',\n          width: '100%',\n          borderRadius: 8\n        },\n        zoomControl: false,\n        children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n          attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n          url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1096,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayerGroup, {\n          children: mapData.filter(point => point.Flood_Prediction === 1).map((point, index) => /*#__PURE__*/_jsxDEV(CircleMarker, {\n            center: [point.Latitude, point.Longitude],\n            radius: 7,\n            pathOptions: {\n              color: theme.palette.error.main,\n              fillColor: theme.palette.error.main,\n              fillOpacity: 0.7,\n              weight: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              minWidth: 280,\n              maxWidth: 320,\n              maxHeight: 350,\n              autoPan: true,\n              autoPanPadding: [20, 20],\n              className: \"scrollable-popup\",\n              children: /*#__PURE__*/_jsxDEV(EnhancedPopup, {\n                point: point\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 19\n            }, this)\n          }, `high-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LayerGroup, {\n          children: mapData.filter(point => point.Flood_Prediction === 0).map((point, index) => /*#__PURE__*/_jsxDEV(CircleMarker, {\n            center: [point.Latitude, point.Longitude],\n            radius: 5,\n            pathOptions: {\n              color: theme.palette.success.main,\n              fillColor: theme.palette.success.main,\n              fillOpacity: 0.7,\n              weight: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              minWidth: 280,\n              maxWidth: 320,\n              maxHeight: 350,\n              autoPan: true,\n              autoPanPadding: [20, 20],\n              className: \"scrollable-popup\",\n              children: /*#__PURE__*/_jsxDEV(EnhancedPopup, {\n                point: point\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 19\n            }, this)\n          }, `low-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 11\n        }, this), searchedLocation && nearestPoint && /*#__PURE__*/_jsxDEV(LayerGroup, {\n          children: /*#__PURE__*/_jsxDEV(Marker, {\n            position: [searchedLocation.lat, searchedLocation.lng],\n            icon: L.divIcon({\n              className: 'custom-div-icon',\n              html: `\n                    <div style=\"\n                      background-color: white;\n                      border: 2px solid ${theme.palette.primary.main};\n                      border-radius: 50%;\n                      width: 12px;\n                      height: 12px;\n                      box-shadow: 0 0 0 4px ${theme.palette.primary.main}40;\n                    \"></div>\n                  `,\n              iconSize: [12, 12],\n              iconAnchor: [6, 6]\n            }),\n            children: /*#__PURE__*/_jsxDEV(Popup, {\n              minWidth: 300,\n              maxWidth: 350,\n              maxHeight: 400,\n              autoPan: true,\n              autoPanPadding: [30, 30],\n              className: \"scrollable-popup\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  maxHeight: '380px',\n                  overflowY: 'auto'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"bold\",\n                  gutterBottom: true,\n                  children: searchedLocation.name.split(',')[0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  paragraph: true,\n                  children: searchedLocation.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 1.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  paragraph: true,\n                  children: [\"Showing flood risk analysis for the nearest data point (\", (Math.sqrt(Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) + Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)) * 111).toFixed(1), \" km away).\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(EnhancedPopup, {\n                  point: nearestPoint,\n                  showMitigationMeasures: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RiskClusters, {\n          mapData: mapData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LocationSearch, {\n          onLocationSelect: handleLocationSelect\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MapControls, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MapLegend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1089,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 6000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this), searchedLocation && nearestPoint && showLocationInfo && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3,\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 3,\n          borderRadius: 2,\n          background: `linear-gradient(135deg, ${theme.palette.primary.light}10 0%, ${theme.palette.primary.light}01 100%)`,\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            right: 0,\n            width: '150px',\n            height: '150px',\n            background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n            borderRadius: '0 0 0 100%',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 600,\n              color: theme.palette.primary.main,\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationOnIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1284,\n              columnNumber: 17\n            }, this), \" \", searchedLocation.name.split(',')[0]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            color: \"text.secondary\",\n            children: searchedLocation.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                fontWeight: \"medium\",\n                children: \"Flood Risk Assessment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 2,\n                  borderRadius: 2,\n                  backgroundColor: nearestPoint.Flood_Prediction === 1 ? 'rgba(255, 89, 94, 0.1)' : 'rgba(6, 214, 160, 0.1)',\n                  borderLeft: `4px solid ${nearestPoint.Flood_Prediction === 1 ? theme.palette.error.main : theme.palette.success.main}`,\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"bold\",\n                  sx: {\n                    color: nearestPoint.Flood_Prediction === 1 ? theme.palette.error.main : theme.palette.success.main\n                  },\n                  children: nearestPoint.Flood_Prediction === 1 ? 'High Flood Risk Area' : 'Low Flood Risk Area'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: nearestPoint.Flood_Prediction === 1 ? 'This area has significant flood risk due to environmental and geographical factors. Residents should take precautionary measures.' : 'This area has minimal flood risk under normal conditions. However, it\\'s still important to stay informed during extreme weather events.'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1326,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Environmental Factors:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Paper, {\n                    elevation: 1,\n                    sx: {\n                      p: 1.5,\n                      textAlign: 'center',\n                      borderTop: `3px solid ${theme.palette.info.main}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(ThunderstormIcon, {\n                      sx: {\n                        color: theme.palette.info.main,\n                        mb: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1347,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Rainfall\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1348,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"medium\",\n                      children: [nearestPoint['Rainfall (mm)'], \" mm\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1351,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1339,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Paper, {\n                    elevation: 1,\n                    sx: {\n                      p: 1.5,\n                      textAlign: 'center',\n                      borderTop: `3px solid ${theme.palette.warning.main}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TerrainIcon, {\n                      sx: {\n                        color: theme.palette.warning.main,\n                        mb: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1366,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Elevation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1367,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"medium\",\n                      children: [nearestPoint['Elevation (m)'], \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1370,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1358,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Paper, {\n                    elevation: 1,\n                    sx: {\n                      p: 1.5,\n                      textAlign: 'center',\n                      borderTop: `3px solid ${theme.palette.error.main}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(WaterDropIcon, {\n                      sx: {\n                        color: theme.palette.error.main,\n                        mb: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Water Level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1386,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"medium\",\n                      children: [nearestPoint['Water Level (m)'], \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1389,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1377,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Paper, {\n                    elevation: 1,\n                    sx: {\n                      p: 1.5,\n                      textAlign: 'center',\n                      borderTop: `3px solid ${theme.palette.primary.main}`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(LocationOnIcon, {\n                      sx: {\n                        color: theme.palette.primary.main,\n                        mb: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1404,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Distance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1405,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"medium\",\n                      children: [(Math.sqrt(Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) + Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)) * 111).toFixed(1), \" km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1408,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1396,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1395,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1337,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                fontWeight: \"medium\",\n                children: \"Recommended Mitigation Measures\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RiskMitigationMeasures, {\n                riskLevel: nearestPoint.Flood_Prediction === 1 ? 'high' : 'low',\n                riskFactors: [...(nearestPoint['Rainfall (mm)'] > 200 ? [{\n                  factor: 'Heavy Rainfall',\n                  value: `${nearestPoint['Rainfall (mm)']} mm`,\n                  severity: 'high'\n                }] : []), ...(nearestPoint['Elevation (m)'] < 100 ? [{\n                  factor: 'Low Elevation',\n                  value: `${nearestPoint['Elevation (m)']} m`,\n                  severity: nearestPoint['Elevation (m)'] < 50 ? 'high' : 'medium'\n                }] : []), ...(nearestPoint['Water Level (m)'] > 6 ? [{\n                  factor: 'High Water Level',\n                  value: `${nearestPoint['Water Level (m)']} m`,\n                  severity: nearestPoint['Water Level (m)'] > 8 ? 'high' : 'medium'\n                }] : [])]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"primary\",\n                  onClick: () => setShowLocationInfo(false),\n                  startIcon: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 34\n                  }, this),\n                  sx: {\n                    mt: 2\n                  },\n                  children: \"Close Location Analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1419,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1247,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        fontWeight: \"medium\",\n        children: \"Flood Risk Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              fontWeight: \"bold\",\n              color: theme.palette.error.main,\n              gutterBottom: true,\n              children: [\"High Risk Areas (\", highRiskCount, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              paragraph: true,\n              children: \"These areas show significant flood risk due to factors like heavy rainfall, low elevation, or high water levels. Residents in these areas should be prepared for potential flooding during monsoon seasons.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Key characteristics:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                paddingLeft: '20px',\n                margin: '8px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average rainfall: \", (mapData.filter(point => point.Flood_Prediction === 1).reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / (highRiskCount || 1)).toFixed(1), \" mm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1484,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average elevation: \", (mapData.filter(point => point.Flood_Prediction === 1).reduce((sum, point) => sum + point['Elevation (m)'], 0) / (highRiskCount || 1)).toFixed(1), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1494,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average water level: \", (mapData.filter(point => point.Flood_Prediction === 1).reduce((sum, point) => sum + point['Water Level (m)'], 0) / (highRiskCount || 1)).toFixed(1), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1470,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 2,\n            sx: {\n              p: 2,\n              borderRadius: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              fontWeight: \"bold\",\n              color: theme.palette.success.main,\n              gutterBottom: true,\n              children: [\"Low Risk Areas (\", lowRiskCount, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              paragraph: true,\n              children: \"These areas have minimal flood risk due to favorable geographical and environmental conditions. They typically feature higher elevations, moderate rainfall, or effective drainage systems.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Key characteristics:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1527,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                paddingLeft: '20px',\n                margin: '8px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average rainfall: \", (mapData.filter(point => point.Flood_Prediction === 0).reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / (lowRiskCount || 1)).toFixed(1), \" mm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1531,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average elevation: \", (mapData.filter(point => point.Flood_Prediction === 0).reduce((sum, point) => sum + point['Elevation (m)'], 0) / (lowRiskCount || 1)).toFixed(1), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1540,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Average water level: \", (mapData.filter(point => point.Flood_Prediction === 0).reduce((sum, point) => sum + point['Water Level (m)'], 0) / (lowRiskCount || 1)).toFixed(1), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1551,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1550,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1529,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1518,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1517,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1463,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s8(FloodMap, \"BQW1DocMApQfzqpl3gOJiquB9Tc=\", false, function () {\n  return [useTheme];\n});\n_c7 = FloodMap;\nexport default FloodMap;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"MapLegend\");\n$RefreshReg$(_c2, \"LocationSearch\");\n$RefreshReg$(_c3, \"MapControls\");\n$RefreshReg$(_c4, \"RiskClusters\");\n$RefreshReg$(_c5, \"RiskMitigationMeasures\");\n$RefreshReg$(_c6, \"EnhancedPopup\");\n$RefreshReg$(_c7, \"FloodMap\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CircleMarker", "Popup", "useMap", "LayerGroup", "ZoomControl", "Rectangle", "<PERSON><PERSON>", "L", "Box", "Typography", "Skeleton", "Paper", "Chip", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "useTheme", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "IconButton", "Fade", "TextField", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemIcon", "<PERSON><PERSON>", "Collapse", "Snackbar", "WaterDropIcon", "TerrainIcon", "ThunderstormIcon", "InfoIcon", "LayersIcon", "MyLocationIcon", "ZoomInIcon", "ZoomOutIcon", "SearchIcon", "LocationOnIcon", "CloseIcon", "WarningIcon", "EngineeringIcon", "HomeIcon", "useSpring", "animated", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MapLegend", "_s", "theme", "expanded", "setExpanded", "legendAnimation", "opacity", "height", "config", "tension", "friction", "div", "style", "position", "bottom", "right", "zIndex", "backgroundColor", "padding", "borderRadius", "boxShadow", "overflow", "width", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "transition", "fontSize", "my", "mt", "palette", "error", "main", "mr", "success", "color", "info", "warning", "text", "secondary", "_c", "LocationSearch", "onLocationSelect", "_s2", "map", "searchQuery", "setSearch<PERSON>uery", "searchResults", "setSearchResults", "searching", "setSearching", "showResults", "setShowResults", "setError", "searchLocation", "query", "trim", "length", "searchTimeout", "response", "get", "params", "q", "format", "limit", "countrycodes", "addressdetails", "headers", "data", "err", "console", "setTimeout", "clearTimeout", "handleSearchChange", "e", "target", "value", "handleSearchSubmit", "preventDefault", "handleLocationClick", "location", "lat", "parseFloat", "lng", "lon", "flyTo", "duration", "name", "display_name", "type", "importance", "address", "top", "left", "max<PERSON><PERSON><PERSON>", "elevation", "p", "onSubmit", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "endAdornment", "border", "borderTopColor", "primary", "animation", "severity", "onClose", "in", "maxHeight", "result", "index", "_result$address", "_result$address2", "_result$address3", "_result$address4", "button", "action", "hover", "min<PERSON><PERSON><PERSON>", "city", "town", "village", "state", "split", "secondaryTypographyProps", "noWrap", "_c2", "MapControls", "_s3", "handleZoomIn", "zoomIn", "handleZoomOut", "zoomOut", "handleLocate", "locate", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON>", "flexDirection", "gap", "title", "placement", "grey", "_c3", "RiskClusters", "mapData", "_s4", "highRiskPoints", "filter", "point", "Flood_Prediction", "clusters", "for<PERSON>ach", "Latitude", "Longitude", "inCluster", "some", "cluster", "Math", "abs", "centerLat", "centerLng", "push", "count", "_c4", "RiskMitigationMeasures", "riskLevel", "riskFactors", "_s5", "getMitigationMeasures", "measures", "description", "icon", "factor", "gutterBottom", "measure", "_c5", "useReverseGeocoding", "_s6", "locationCache", "setLocationCache", "getLocationName", "cache<PERSON>ey", "toFixed", "zoom", "locationData", "placeName", "county", "state_district", "prev", "fullName", "Enhan<PERSON><PERSON><PERSON><PERSON>", "showMitigationMeasures", "locationName", "_s7", "isHighRisk", "setLocation", "loading", "setLoading", "fetchLocationName", "overflowX", "className", "py", "container", "spacing", "item", "xs", "idx", "ml", "borderLeft", "_c6", "FloodMap", "_s8", "searchedLocation", "setSearchedLocation", "nearestPoint", "setNearestPoint", "showLocationInfo", "setShowLocationInfo", "notification", "setNotification", "open", "message", "timer", "findNearestPoint", "nearest", "minDistance", "Infinity", "distance", "sqrt", "pow", "handleLocationSelect", "handleCloseNotification", "highRiskCount", "lowRiskCount", "avgRainfall", "reduce", "sum", "avgElevation", "sm", "md", "background", "light", "component", "timeout", "textAlign", "margin", "transform", "center", "scrollWheelZoom", "zoomControl", "attribution", "url", "radius", "pathOptions", "fillColor", "fillOpacity", "weight", "autoPan", "autoPanPadding", "divIcon", "html", "iconSize", "iconAnchor", "overflowY", "paragraph", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "borderTop", "startIcon", "paddingLeft", "_c7", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  CircleMarker,\n  Popup,\n  useMap,\n  LayerGroup,\n  ZoomControl,\n  Rectangle,\n  Marker\n} from 'react-leaflet';\nimport L from 'leaflet';\nimport './FloodMap.css'; // Import custom CSS for map\nimport {\n  Box,\n  Typography,\n  Skeleton,\n  Paper,\n  Chip,\n  Divider,\n  Card,\n  CardContent,\n  Grid,\n  useTheme,\n  Button,\n  Tooltip,\n  IconButton,\n  Fade,\n  TextField,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Alert,\n  Collapse,\n  Snackbar\n} from '@mui/material';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport TerrainIcon from '@mui/icons-material/Terrain';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\nimport InfoIcon from '@mui/icons-material/Info';\nimport LayersIcon from '@mui/icons-material/Layers';\nimport MyLocationIcon from '@mui/icons-material/MyLocation';\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\nimport ZoomOutIcon from '@mui/icons-material/ZoomOut';\nimport SearchIcon from '@mui/icons-material/Search';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport CloseIcon from '@mui/icons-material/Close';\nimport WarningIcon from '@mui/icons-material/Warning';\nimport EngineeringIcon from '@mui/icons-material/Engineering';\nimport HomeIcon from '@mui/icons-material/Home';\nimport { useSpring, animated } from 'react-spring';\nimport axios from 'axios';\n\n// Legend component\nconst MapLegend = () => {\n  const theme = useTheme();\n  const [expanded, setExpanded] = useState(false);\n\n  const legendAnimation = useSpring({\n    opacity: expanded ? 1 : 0.9,\n    height: expanded ? 280 : 40,\n    config: { tension: 200, friction: 20 }\n  });\n\n  return (\n    <animated.div style={{\n      position: 'absolute',\n      bottom: 20,\n      right: 10,\n      zIndex: 1000,\n      backgroundColor: 'white',\n      padding: '10px 15px',\n      borderRadius: 8,\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      overflow: 'hidden',\n      width: 220,\n      ...legendAnimation\n    }}>\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: expanded ? 1 : 0\n      }}>\n        <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n          Map Legend\n        </Typography>\n        <IconButton\n          size=\"small\"\n          onClick={() => setExpanded(!expanded)}\n          sx={{\n            backgroundColor: expanded ? 'rgba(0,0,0,0.05)' : 'transparent',\n            transition: 'all 0.2s ease'\n          }}\n        >\n          <InfoIcon fontSize=\"small\" />\n        </IconButton>\n      </Box>\n\n      {expanded && (\n        <>\n          <Divider sx={{ my: 1 }} />\n\n          <Typography variant=\"subtitle2\" fontWeight=\"bold\" sx={{ mt: 1 }}>\n            Flood Risk Indicators\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n            <Box sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              backgroundColor: theme.palette.error.main,\n              mr: 1\n            }} />\n            <Typography variant=\"body2\">High Risk Area</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <Box sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              backgroundColor: theme.palette.success.main,\n              mr: 1\n            }} />\n            <Typography variant=\"body2\">Low Risk Area</Typography>\n          </Box>\n\n          <Typography variant=\"subtitle2\" fontWeight=\"bold\" sx={{ mt: 2 }}>\n            Risk Factors\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n            <ThunderstormIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.info.main }} />\n            <Typography variant=\"body2\">Heavy Rainfall (>200mm)</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <TerrainIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.warning.main }} />\n            <Typography variant=\"body2\">Low Elevation (&lt;100m)</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <WaterDropIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.error.main }} />\n            <Typography variant=\"body2\">High Water Level (>8m)</Typography>\n          </Box>\n\n          <Typography variant=\"caption\" sx={{ display: 'block', mt: 2, color: theme.palette.text.secondary }}>\n            Click on markers for detailed information about specific locations.\n          </Typography>\n        </>\n      )}\n    </animated.div>\n  );\n};\n\n// Search component\nconst LocationSearch = ({ onLocationSelect }) => {\n  const map = useMap();\n  const theme = useTheme();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [searching, setSearching] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Geocoding API (using OpenStreetMap Nominatim)\n  const searchLocation = useCallback(async (query) => {\n    if (!query || query.trim().length < 3) return;\n\n    setSearching(true);\n    setError(null);\n\n    // Clear any existing timeout to prevent memory leaks\n    let searchTimeout;\n\n    try {\n      // Add \"India\" to the search query to focus on Indian locations\n      const response = await axios.get(`https://nominatim.openstreetmap.org/search`, {\n        params: {\n          q: `${query}, India`,\n          format: 'json',\n          limit: 5,\n          countrycodes: 'in', // Limit to India\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n\n      if (response.data && response.data.length > 0) {\n        setSearchResults(response.data);\n        setShowResults(true);\n      } else {\n        setSearchResults([]);\n        setError('No locations found. Try a different search term.');\n      }\n    } catch (err) {\n      console.error('Error searching for location:', err);\n      setError('Error searching for location. Please try again.');\n    } finally {\n      // Ensure searching state is properly reset with a slight delay\n      // This prevents UI flicker if multiple searches happen quickly\n      searchTimeout = setTimeout(() => {\n        setSearching(false);\n      }, 300);\n    }\n\n    return () => {\n      if (searchTimeout) clearTimeout(searchTimeout);\n    };\n  }, []);\n\n  const handleSearchChange = (e) => {\n    setSearchQuery(e.target.value);\n    if (e.target.value.trim().length === 0) {\n      setShowResults(false);\n    }\n  };\n\n  const handleSearchSubmit = (e) => {\n    e.preventDefault();\n    searchLocation(searchQuery);\n  };\n\n  const handleLocationClick = (location) => {\n    const lat = parseFloat(location.lat);\n    const lng = parseFloat(location.lon);\n\n    // Fly to the location\n    map.flyTo([lat, lng], 12, {\n      duration: 1.5\n    });\n\n    // Pass the selected location to parent component\n    onLocationSelect({\n      lat,\n      lng,\n      name: location.display_name,\n      type: location.type,\n      importance: location.importance,\n      address: location.address\n    });\n\n    // Clear search results\n    setShowResults(false);\n  };\n\n  return (\n    <Box sx={{\n      position: 'absolute',\n      top: 10,\n      left: 10,\n      zIndex: 1000,\n      width: 300,\n      maxWidth: 'calc(100% - 20px)'\n    }}>\n      <Paper\n        elevation={3}\n        sx={{\n          p: 1.5,\n          borderRadius: 2,\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n        }}\n      >\n        <form onSubmit={handleSearchSubmit}>\n          <TextField\n            fullWidth\n            placeholder=\"Search for a place in India...\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            variant=\"outlined\"\n            size=\"small\"\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n              endAdornment: searching ? (\n                <InputAdornment position=\"end\">\n                  <Box\n                    sx={{\n                      width: 20,\n                      height: 20,\n                      borderRadius: '50%',\n                      border: '2px solid transparent',\n                      borderTopColor: theme.palette.primary.main,\n                      animation: 'spin 1s linear infinite',\n                    }}\n                  />\n                </InputAdornment>\n              ) : searchQuery ? (\n                <InputAdornment position=\"end\">\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      setSearchQuery('');\n                      setShowResults(false);\n                    }}\n                  >\n                    <CloseIcon fontSize=\"small\" />\n                  </IconButton>\n                </InputAdornment>\n              ) : null,\n              sx: {\n                borderRadius: 2,\n                '&.Mui-focused': {\n                  boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`\n                }\n              }\n            }}\n          />\n        </form>\n\n        {error && (\n          <Alert\n            severity=\"warning\"\n            sx={{ mt: 1, borderRadius: 1 }}\n            onClose={() => setError(null)}\n          >\n            {error}\n          </Alert>\n        )}\n\n        <Collapse in={showResults && searchResults.length > 0}>\n          <List sx={{ mt: 1, maxHeight: 300, overflow: 'auto' }}>\n            {searchResults.map((result, index) => (\n              <ListItem\n                key={index}\n                button\n                onClick={() => handleLocationClick(result)}\n                sx={{\n                  borderRadius: 1,\n                  mb: 0.5,\n                  '&:hover': {\n                    backgroundColor: theme.palette.action.hover\n                  }\n                }}\n              >\n                <ListItemIcon sx={{ minWidth: 36 }}>\n                  <LocationOnIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={result.address?.city || result.address?.town || result.address?.village || result.address?.state || result.display_name.split(',')[0]}\n                  secondary={result.display_name}\n                  secondaryTypographyProps={{\n                    noWrap: true,\n                    style: { fontSize: '0.75rem' }\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </Collapse>\n      </Paper>\n    </Box>\n  );\n};\n\n// Map controls component\nconst MapControls = () => {\n  const map = useMap();\n  const theme = useTheme();\n\n  const handleZoomIn = () => {\n    map.zoomIn();\n  };\n\n  const handleZoomOut = () => {\n    map.zoomOut();\n  };\n\n  const handleLocate = () => {\n    map.locate({ setView: true, maxZoom: 10 });\n  };\n\n  return (\n    <Box sx={{\n      position: 'absolute',\n      top: 10,\n      right: 10,\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: 1\n    }}>\n      <Tooltip title=\"Zoom In\" placement=\"left\">\n        <IconButton\n          onClick={handleZoomIn}\n          sx={{\n            backgroundColor: 'white',\n            boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n            '&:hover': {\n              backgroundColor: theme.palette.grey[100]\n            }\n          }}\n        >\n          <ZoomInIcon />\n        </IconButton>\n      </Tooltip>\n\n      <Tooltip title=\"Zoom Out\" placement=\"left\">\n        <IconButton\n          onClick={handleZoomOut}\n          sx={{\n            backgroundColor: 'white',\n            boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n            '&:hover': {\n              backgroundColor: theme.palette.grey[100]\n            }\n          }}\n        >\n          <ZoomOutIcon />\n        </IconButton>\n      </Tooltip>\n\n      <Tooltip title=\"My Location\" placement=\"left\">\n        <IconButton\n          onClick={handleLocate}\n          sx={{\n            backgroundColor: 'white',\n            boxShadow: '0 2px 6px rgba(0,0,0,0.1)',\n            '&:hover': {\n              backgroundColor: theme.palette.grey[100]\n            }\n          }}\n        >\n          <MyLocationIcon />\n        </IconButton>\n      </Tooltip>\n    </Box>\n  );\n};\n\n// Risk clusters component\nconst RiskClusters = ({ mapData }) => {\n  const map = useMap();\n  const theme = useTheme();\n\n  // Group points by risk level and proximity\n  useEffect(() => {\n    if (!mapData || mapData.length === 0) return;\n\n    // Find clusters of high-risk areas\n    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);\n    const clusters = [];\n\n    // Simple clustering algorithm (this is a simplified version)\n    highRiskPoints.forEach(point => {\n      const lat = point.Latitude;\n      const lng = point.Longitude;\n\n      // Check if point is already in a cluster\n      const inCluster = clusters.some(cluster => {\n        return Math.abs(cluster.centerLat - lat) < 1 &&\n               Math.abs(cluster.centerLng - lng) < 1;\n      });\n\n      if (!inCluster && highRiskPoints.filter(p =>\n        Math.abs(p.Latitude - lat) < 1 &&\n        Math.abs(p.Longitude - lng) < 1\n      ).length > 3) {\n        // Create a new cluster if there are at least 3 high-risk points in proximity\n        clusters.push({\n          centerLat: lat,\n          centerLng: lng,\n          count: highRiskPoints.filter(p =>\n            Math.abs(p.Latitude - lat) < 1 &&\n            Math.abs(p.Longitude - lng) < 1\n          ).length\n        });\n      }\n    });\n\n    return () => {\n      // Cleanup if needed\n    };\n  }, [mapData, map]);\n\n  return null; // Visual representation is handled by the markers\n};\n\n// Risk mitigation measures component\nconst RiskMitigationMeasures = ({ riskLevel, riskFactors }) => {\n  const theme = useTheme();\n\n  // Define mitigation measures based on risk factors\n  const getMitigationMeasures = () => {\n    const measures = [];\n\n    // General measures based on risk level\n    if (riskLevel === 'high') {\n      measures.push({\n        title: 'Evacuation Planning',\n        description: 'Develop and practice evacuation plans. Identify safe routes and emergency shelters.',\n        icon: <HomeIcon sx={{ color: theme.palette.error.main }} />\n      });\n      measures.push({\n        title: 'Early Warning System',\n        description: 'Install flood early warning systems and stay updated with weather forecasts.',\n        icon: <WarningIcon sx={{ color: theme.palette.error.main }} />\n      });\n    }\n\n    // Specific measures based on risk factors\n    riskFactors.forEach(factor => {\n      if (factor.factor === 'Heavy Rainfall' && factor.severity === 'high') {\n        measures.push({\n          title: 'Drainage Improvement',\n          description: 'Ensure proper drainage systems are in place and regularly maintained to handle heavy rainfall.',\n          icon: <EngineeringIcon sx={{ color: theme.palette.info.main }} />\n        });\n      }\n\n      if (factor.factor === 'Low Elevation' && factor.severity === 'high') {\n        measures.push({\n          title: 'Elevated Structures',\n          description: 'Consider raising the foundation of buildings or using stilts in flood-prone low-lying areas.',\n          icon: <HomeIcon sx={{ color: theme.palette.warning.main }} />\n        });\n      }\n\n      if (factor.factor === 'High Water Level' && factor.severity === 'high') {\n        measures.push({\n          title: 'Flood Barriers',\n          description: 'Install temporary or permanent flood barriers, sandbags, or flood walls to protect property.',\n          icon: <WaterDropIcon sx={{ color: theme.palette.error.main }} />\n        });\n      }\n    });\n\n    // Add general measures if none specific were found\n    if (measures.length === 0) {\n      measures.push({\n        title: 'Regular Monitoring',\n        description: 'Monitor weather forecasts and water levels during monsoon season.',\n        icon: <InfoIcon sx={{ color: theme.palette.info.main }} />\n      });\n    }\n\n    return measures;\n  };\n\n  const measures = getMitigationMeasures();\n\n  return (\n    <Box sx={{ mt: 2 }}>\n      <Typography variant=\"subtitle2\" fontWeight=\"bold\" gutterBottom>\n        Recommended Mitigation Measures:\n      </Typography>\n\n      {measures.map((measure, index) => (\n        <Box\n          key={index}\n          sx={{\n            display: 'flex',\n            mb: 1.5,\n            p: 1,\n            borderRadius: 1,\n            backgroundColor: 'rgba(0, 0, 0, 0.02)'\n          }}\n        >\n          <Box sx={{ mr: 1.5, mt: 0.5 }}>\n            {measure.icon}\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\">\n              {measure.title}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {measure.description}\n            </Typography>\n          </Box>\n        </Box>\n      ))}\n    </Box>\n  );\n};\n\n// Get location name from coordinates\nconst useReverseGeocoding = () => {\n  const [locationCache, setLocationCache] = useState({});\n\n  const getLocationName = useCallback(async (lat, lng) => {\n    // Check cache first\n    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      return locationCache[cacheKey];\n    }\n\n    try {\n      const response = await axios.get(`https://nominatim.openstreetmap.org/reverse`, {\n        params: {\n          lat,\n          lon: lng,\n          format: 'json',\n          zoom: 10, // Adjust zoom level for appropriate place name detail\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n\n      if (response.data) {\n        // Extract the most relevant name from the response\n        const locationData = response.data;\n        let placeName = '';\n\n        // Try to get the most specific name first\n        if (locationData.address) {\n          placeName = locationData.address.village ||\n                     locationData.address.town ||\n                     locationData.address.city ||\n                     locationData.address.county ||\n                     locationData.address.state_district ||\n                     locationData.address.state;\n        }\n\n        // If no specific name found, use the display name\n        if (!placeName && locationData.display_name) {\n          placeName = locationData.display_name.split(',')[0];\n        }\n\n        // If still no name, use coordinates\n        if (!placeName) {\n          placeName = `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;\n        }\n\n        // Cache the result\n        setLocationCache(prev => ({\n          ...prev,\n          [cacheKey]: {\n            name: placeName,\n            fullName: locationData.display_name || '',\n            address: locationData.address || {}\n          }\n        }));\n\n        return {\n          name: placeName,\n          fullName: locationData.display_name || '',\n          address: locationData.address || {}\n        };\n      }\n    } catch (error) {\n      console.error('Error in reverse geocoding:', error);\n    }\n\n    // Return a default if geocoding fails\n    return {\n      name: `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`,\n      fullName: '',\n      address: {}\n    };\n  }, [locationCache]);\n\n  return { getLocationName, locationCache };\n};\n\n// Enhanced popup content\nconst EnhancedPopup = ({ point, showMitigationMeasures = false, locationName = null }) => {\n  const theme = useTheme();\n  const isHighRisk = point.Flood_Prediction === 1;\n  const { getLocationName, locationCache } = useReverseGeocoding();\n  const [location, setLocation] = useState(null);\n  const [loading, setLoading] = useState(!locationName);\n\n  useEffect(() => {\n    if (locationName) {\n      setLocation(locationName);\n      setLoading(false);\n      return;\n    }\n\n    // Check if we already have this location in cache\n    const cacheKey = `${point.Latitude.toFixed(4)},${point.Longitude.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      setLocation(locationCache[cacheKey]);\n      setLoading(false);\n      return;\n    }\n\n    // Fetch location name\n    const fetchLocationName = async () => {\n      setLoading(true);\n      const result = await getLocationName(point.Latitude, point.Longitude);\n      setLocation(result);\n      setLoading(false);\n    };\n\n    fetchLocationName();\n  }, [point, locationName, getLocationName, locationCache]);\n\n  // Determine risk factors\n  const riskFactors = [];\n  if (point['Rainfall (mm)'] > 200) {\n    riskFactors.push({\n      factor: 'Heavy Rainfall',\n      value: `${point['Rainfall (mm)']} mm`,\n      icon: <ThunderstormIcon fontSize=\"small\" sx={{ color: theme.palette.info.main }} />,\n      severity: 'high'\n    });\n  }\n\n  if (point['Elevation (m)'] < 100) {\n    riskFactors.push({\n      factor: 'Low Elevation',\n      value: `${point['Elevation (m)']} m`,\n      icon: <TerrainIcon fontSize=\"small\" sx={{ color: theme.palette.warning.main }} />,\n      severity: point['Elevation (m)'] < 50 ? 'high' : 'medium'\n    });\n  }\n\n  if (point['Water Level (m)'] > 6) {\n    riskFactors.push({\n      factor: 'High Water Level',\n      value: `${point['Water Level (m)']} m`,\n      icon: <WaterDropIcon fontSize=\"small\" sx={{ color: theme.palette.error.main }} />,\n      severity: point['Water Level (m)'] > 8 ? 'high' : 'medium'\n    });\n  }\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        maxWidth: showMitigationMeasures ? 350 : 280,\n        overflowX: 'hidden'\n      }}\n      className=\"popup-content\"\n    >\n      <Typography\n        variant=\"subtitle1\"\n        fontWeight=\"bold\"\n        sx={{\n          mb: 0.5,\n          color: isHighRisk ? theme.palette.error.main : theme.palette.success.main,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1,\n          position: 'sticky',\n          top: 0,\n          backgroundColor: 'white',\n          zIndex: 10,\n          py: 0.5\n        }}\n      >\n        <WaterDropIcon fontSize=\"small\" />\n        {isHighRisk ? 'High Flood Risk Area' : 'Low Flood Risk Area'}\n      </Typography>\n\n      {loading ? (\n        <Box sx={{ display: 'flex', alignItems: 'center', my: 1 }}>\n          <Box\n            sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              border: '2px solid transparent',\n              borderTopColor: theme.palette.primary.main,\n              animation: 'spin 1s linear infinite',\n              mr: 1\n            }}\n          />\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Loading location...\n          </Typography>\n        </Box>\n      ) : location ? (\n        <Box sx={{ mb: 1 }}>\n          <Typography variant=\"subtitle2\" fontWeight=\"medium\">\n            {location.name}\n          </Typography>\n          {location.fullName && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ display: 'block', mb: 0.5 }}>\n              {location.fullName}\n            </Typography>\n          )}\n        </Box>\n      ) : null}\n\n      <Divider sx={{ mb: 1.5 }} />\n\n      <Grid container spacing={1} sx={{ mb: 1.5 }}>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Rainfall\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Rainfall (mm)']} mm\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Elevation\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Elevation (m)']} m\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Water Level\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Water Level (m)']} m\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Coordinates\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point.Latitude.toFixed(2)}, {point.Longitude.toFixed(2)}\n          </Typography>\n        </Grid>\n      </Grid>\n\n      {riskFactors.length > 0 && (\n        <>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ display: 'block', mb: 0.5 }}>\n            Risk Factors:\n          </Typography>\n\n          {riskFactors.map((factor, idx) => (\n            <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>\n              {factor.icon}\n              <Typography variant=\"body2\" sx={{ ml: 0.5 }}>\n                {factor.factor}: <strong>{factor.value}</strong>\n              </Typography>\n            </Box>\n          ))}\n        </>\n      )}\n\n      {isHighRisk && !showMitigationMeasures && (\n        <Box sx={{\n          mt: 1.5,\n          p: 1,\n          backgroundColor: 'rgba(255, 89, 94, 0.1)',\n          borderRadius: 1,\n          borderLeft: `3px solid ${theme.palette.error.main}`\n        }}>\n          <Typography variant=\"caption\" sx={{ display: 'block', fontWeight: 'medium' }}>\n            Recommendation:\n          </Typography>\n          <Typography variant=\"body2\">\n            This area requires flood mitigation measures and close monitoring during heavy rainfall.\n          </Typography>\n        </Box>\n      )}\n\n      {showMitigationMeasures && (\n        <RiskMitigationMeasures\n          riskLevel={isHighRisk ? 'high' : 'low'}\n          riskFactors={riskFactors}\n        />\n      )}\n    </Box>\n  );\n};\n\nconst FloodMap = ({ mapData }) => {\n  const theme = useTheme();\n  const [loading, setLoading] = useState(true);\n  const [searchedLocation, setSearchedLocation] = useState(null);\n  const [nearestPoint, setNearestPoint] = useState(null);\n  const [showLocationInfo, setShowLocationInfo] = useState(false);\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });\n\n  useEffect(() => {\n    if (mapData && mapData.length > 0) {\n      // Add a small delay to simulate loading for better UX, but ensure it stops\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 1000);\n\n      return () => {\n        clearTimeout(timer);\n        // Ensure loading is set to false when component unmounts\n        setLoading(false);\n      };\n    } else {\n      // If no data, still set loading to false after a short delay\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 500);\n\n      return () => clearTimeout(timer);\n    }\n  }, [mapData]);\n\n  // Find the nearest data point to a searched location\n  const findNearestPoint = useCallback((location) => {\n    if (!mapData || mapData.length === 0) return null;\n\n    let nearest = null;\n    let minDistance = Infinity;\n\n    mapData.forEach(point => {\n      const distance = Math.sqrt(\n        Math.pow(point.Latitude - location.lat, 2) +\n        Math.pow(point.Longitude - location.lng, 2)\n      );\n\n      if (distance < minDistance) {\n        minDistance = distance;\n        nearest = point;\n      }\n    });\n\n    // Only consider it a match if it's reasonably close (within ~50km)\n    if (minDistance > 0.5) {\n      setNotification({\n        open: true,\n        message: 'No exact flood data for this location. Showing nearest available data point.',\n        severity: 'info'\n      });\n    }\n\n    return nearest;\n  }, [mapData]);\n\n  // Handle location selection from search\n  const handleLocationSelect = useCallback((location) => {\n    setSearchedLocation(location);\n    const nearest = findNearestPoint(location);\n    setNearestPoint(nearest);\n    setShowLocationInfo(true);\n  }, [findNearestPoint]);\n\n  // Close notification\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  if (!mapData || mapData.length === 0) {\n    return (\n      <Box sx={{ width: '100%', height: 500, borderRadius: 2 }}>\n        <Skeleton variant=\"rectangular\" width=\"100%\" height=\"100%\" />\n      </Box>\n    );\n  }\n\n  // Count high and low risk areas\n  const highRiskCount = mapData.filter(point => point.Flood_Prediction === 1).length;\n  const lowRiskCount = mapData.filter(point => point.Flood_Prediction === 0).length;\n\n  // Calculate statistics\n  const avgRainfall = mapData.reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / mapData.length;\n  const avgElevation = mapData.reduce((sum, point) => sum + point['Elevation (m)'], 0) / mapData.length;\n\n  return (\n    <>\n      <Box sx={{ mb: 3 }}>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.error.light}20 0%, ${theme.palette.error.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.error.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                High Risk Areas\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.error.main}>\n                {highRiskCount}\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.success.light}20 0%, ${theme.palette.success.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.success.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Low Risk Areas\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.success.main}>\n                {lowRiskCount}\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.info.light}20 0%, ${theme.palette.info.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.info.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Avg. Rainfall\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.info.main}>\n                {avgRainfall.toFixed(1)} <Typography component=\"span\" variant=\"body2\">mm</Typography>\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.warning.light}20 0%, ${theme.palette.warning.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.warning.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Avg. Elevation\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.warning.main}>\n                {avgElevation.toFixed(1)} <Typography component=\"span\" variant=\"body2\">m</Typography>\n              </Typography>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n\n      <Box sx={{ position: 'relative', height: 600, borderRadius: 2, overflow: 'hidden' }}>\n        {loading && (\n          <Fade in={loading} timeout={300}>\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: 'rgba(255,255,255,0.8)',\n                zIndex: 1000\n              }}\n            >\n              <Box sx={{ textAlign: 'center' }}>\n                <Box sx={{ position: 'relative', width: 60, height: 60, margin: '0 auto' }}>\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: '100%',\n                      height: '100%',\n                      borderRadius: '50%',\n                      border: '3px solid transparent',\n                      borderTopColor: theme.palette.primary.main,\n                      animation: 'spin 1s linear infinite',\n                      '@keyframes spin': {\n                        '0%': { transform: 'rotate(0deg)' },\n                        '100%': { transform: 'rotate(360deg)' }\n                      }\n                    }}\n                  />\n                </Box>\n                <Typography variant=\"body1\" sx={{ mt: 2 }}>\n                  Loading map data...\n                </Typography>\n              </Box>\n            </Box>\n          </Fade>\n        )}\n\n        <MapContainer\n          center={[22.0, 80.0]}\n          zoom={5}\n          scrollWheelZoom={true}\n          style={{ height: '100%', width: '100%', borderRadius: 8 }}\n          zoomControl={false}\n        >\n          <TileLayer\n            attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n            url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n          />\n\n          {/* High risk markers */}\n          <LayerGroup>\n            {mapData\n              .filter(point => point.Flood_Prediction === 1)\n              .map((point, index) => (\n                <CircleMarker\n                  key={`high-${index}`}\n                  center={[point.Latitude, point.Longitude]}\n                  radius={7}\n                  pathOptions={{\n                    color: theme.palette.error.main,\n                    fillColor: theme.palette.error.main,\n                    fillOpacity: 0.7,\n                    weight: 2\n                  }}\n                >\n                  <Popup\n                    minWidth={280}\n                    maxWidth={320}\n                    maxHeight={350}\n                    autoPan={true}\n                    autoPanPadding={[20, 20]}\n                    className=\"scrollable-popup\"\n                  >\n                    <EnhancedPopup point={point} />\n                  </Popup>\n                </CircleMarker>\n              ))}\n          </LayerGroup>\n\n          {/* Low risk markers */}\n          <LayerGroup>\n            {mapData\n              .filter(point => point.Flood_Prediction === 0)\n              .map((point, index) => (\n                <CircleMarker\n                  key={`low-${index}`}\n                  center={[point.Latitude, point.Longitude]}\n                  radius={5}\n                  pathOptions={{\n                    color: theme.palette.success.main,\n                    fillColor: theme.palette.success.main,\n                    fillOpacity: 0.7,\n                    weight: 1\n                  }}\n                >\n                  <Popup\n                    minWidth={280}\n                    maxWidth={320}\n                    maxHeight={350}\n                    autoPan={true}\n                    autoPanPadding={[20, 20]}\n                    className=\"scrollable-popup\"\n                  >\n                    <EnhancedPopup point={point} />\n                  </Popup>\n                </CircleMarker>\n              ))}\n          </LayerGroup>\n\n          {/* Searched location marker */}\n          {searchedLocation && nearestPoint && (\n            <LayerGroup>\n              {/* Marker for the exact searched location */}\n              <Marker\n                position={[searchedLocation.lat, searchedLocation.lng]}\n                icon={L.divIcon({\n                  className: 'custom-div-icon',\n                  html: `\n                    <div style=\"\n                      background-color: white;\n                      border: 2px solid ${theme.palette.primary.main};\n                      border-radius: 50%;\n                      width: 12px;\n                      height: 12px;\n                      box-shadow: 0 0 0 4px ${theme.palette.primary.main}40;\n                    \"></div>\n                  `,\n                  iconSize: [12, 12],\n                  iconAnchor: [6, 6]\n                })}\n              >\n                <Popup\n                  minWidth={300}\n                  maxWidth={350}\n                  maxHeight={400}\n                  autoPan={true}\n                  autoPanPadding={[30, 30]}\n                  className=\"scrollable-popup\"\n                >\n                  <Box sx={{ maxHeight: '380px', overflowY: 'auto' }}>\n                    <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom>\n                      {searchedLocation.name.split(',')[0]}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" paragraph>\n                      {searchedLocation.name}\n                    </Typography>\n                    <Divider sx={{ my: 1.5 }} />\n                    <Typography variant=\"body2\" paragraph>\n                      Showing flood risk analysis for the nearest data point ({(Math.sqrt(\n                        Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +\n                        Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)\n                      ) * 111).toFixed(1)} km away).\n                    </Typography>\n                    <EnhancedPopup point={nearestPoint} showMitigationMeasures={true} />\n                  </Box>\n                </Popup>\n              </Marker>\n\n              {/* Line connecting to the nearest data point */}\n              {/* We would need to use a Polyline here, but for simplicity we'll skip it */}\n            </LayerGroup>\n          )}\n\n          {/* Add risk clusters */}\n          <RiskClusters mapData={mapData} />\n\n          {/* Add search component */}\n          <LocationSearch onLocationSelect={handleLocationSelect} />\n\n          {/* Add map controls */}\n          <MapControls />\n\n          {/* Add legend */}\n          <MapLegend />\n        </MapContainer>\n\n        {/* Notification for search results */}\n        <Snackbar\n          open={notification.open}\n          autoHideDuration={6000}\n          onClose={handleCloseNotification}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={handleCloseNotification}\n            severity={notification.severity}\n            sx={{ width: '100%' }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      </Box>\n\n      {/* Searched Location Analysis */}\n      {searchedLocation && nearestPoint && showLocationInfo && (\n        <Box sx={{ mt: 3, mb: 4 }}>\n          <Paper\n            elevation={3}\n            sx={{\n              p: 3,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.primary.light}10 0%, ${theme.palette.primary.light}01 100%)`,\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n          >\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                right: 0,\n                width: '150px',\n                height: '150px',\n                background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 0 0 100%',\n                zIndex: 0\n              }}\n            />\n\n            <Box sx={{ position: 'relative', zIndex: 1 }}>\n              <Typography\n                variant=\"h4\"\n                component=\"h2\"\n                gutterBottom\n                sx={{\n                  fontWeight: 600,\n                  color: theme.palette.primary.main,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                }}\n              >\n                <LocationOnIcon /> {searchedLocation.name.split(',')[0]}\n              </Typography>\n\n              <Typography variant=\"body1\" paragraph color=\"text.secondary\">\n                {searchedLocation.name}\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Grid container spacing={3}>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n                    Flood Risk Assessment\n                  </Typography>\n\n                  <Box\n                    sx={{\n                      p: 2,\n                      borderRadius: 2,\n                      backgroundColor: nearestPoint.Flood_Prediction === 1\n                        ? 'rgba(255, 89, 94, 0.1)'\n                        : 'rgba(6, 214, 160, 0.1)',\n                      borderLeft: `4px solid ${nearestPoint.Flood_Prediction === 1\n                        ? theme.palette.error.main\n                        : theme.palette.success.main}`,\n                      mb: 2\n                    }}\n                  >\n                    <Typography\n                      variant=\"subtitle1\"\n                      fontWeight=\"bold\"\n                      sx={{\n                        color: nearestPoint.Flood_Prediction === 1\n                          ? theme.palette.error.main\n                          : theme.palette.success.main\n                      }}\n                    >\n                      {nearestPoint.Flood_Prediction === 1\n                        ? 'High Flood Risk Area'\n                        : 'Low Flood Risk Area'}\n                    </Typography>\n\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      {nearestPoint.Flood_Prediction === 1\n                        ? 'This area has significant flood risk due to environmental and geographical factors. Residents should take precautionary measures.'\n                        : 'This area has minimal flood risk under normal conditions. However, it\\'s still important to stay informed during extreme weather events.'}\n                    </Typography>\n                  </Box>\n\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Environmental Factors:\n                  </Typography>\n\n                  <Grid container spacing={2} sx={{ mb: 2 }}>\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.info.main}`\n                        }}\n                      >\n                        <ThunderstormIcon sx={{ color: theme.palette.info.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Rainfall\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Rainfall (mm)']} mm\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.warning.main}`\n                        }}\n                      >\n                        <TerrainIcon sx={{ color: theme.palette.warning.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Elevation\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Elevation (m)']} m\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.error.main}`\n                        }}\n                      >\n                        <WaterDropIcon sx={{ color: theme.palette.error.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Water Level\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Water Level (m)']} m\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.primary.main}`\n                        }}\n                      >\n                        <LocationOnIcon sx={{ color: theme.palette.primary.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Distance\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {(Math.sqrt(\n                            Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +\n                            Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)\n                          ) * 111).toFixed(1)} km\n                        </Typography>\n                      </Paper>\n                    </Grid>\n                  </Grid>\n                </Grid>\n\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n                    Recommended Mitigation Measures\n                  </Typography>\n\n                  <RiskMitigationMeasures\n                    riskLevel={nearestPoint.Flood_Prediction === 1 ? 'high' : 'low'}\n                    riskFactors={[\n                      ...(nearestPoint['Rainfall (mm)'] > 200 ? [{\n                        factor: 'Heavy Rainfall',\n                        value: `${nearestPoint['Rainfall (mm)']} mm`,\n                        severity: 'high'\n                      }] : []),\n                      ...(nearestPoint['Elevation (m)'] < 100 ? [{\n                        factor: 'Low Elevation',\n                        value: `${nearestPoint['Elevation (m)']} m`,\n                        severity: nearestPoint['Elevation (m)'] < 50 ? 'high' : 'medium'\n                      }] : []),\n                      ...(nearestPoint['Water Level (m)'] > 6 ? [{\n                        factor: 'High Water Level',\n                        value: `${nearestPoint['Water Level (m)']} m`,\n                        severity: nearestPoint['Water Level (m)'] > 8 ? 'high' : 'medium'\n                      }] : [])\n                    ]}\n                  />\n\n                  <Box sx={{ mt: 3 }}>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"primary\"\n                      onClick={() => setShowLocationInfo(false)}\n                      startIcon={<CloseIcon />}\n                      sx={{ mt: 2 }}\n                    >\n                      Close Location Analysis\n                    </Button>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n          </Paper>\n        </Box>\n      )}\n\n      <Box sx={{ mt: 3 }}>\n        <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n          Flood Risk Analysis\n        </Typography>\n\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={6}>\n            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" color={theme.palette.error.main} gutterBottom>\n                High Risk Areas ({highRiskCount})\n              </Typography>\n              <Typography variant=\"body2\" paragraph>\n                These areas show significant flood risk due to factors like heavy rainfall, low elevation,\n                or high water levels. Residents in these areas should be prepared for potential flooding\n                during monsoon seasons.\n              </Typography>\n              <Typography variant=\"body2\">\n                <strong>Key characteristics:</strong>\n              </Typography>\n              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average rainfall: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } mm\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average elevation: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average water level: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n              </ul>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" color={theme.palette.success.main} gutterBottom>\n                Low Risk Areas ({lowRiskCount})\n              </Typography>\n              <Typography variant=\"body2\" paragraph>\n                These areas have minimal flood risk due to favorable geographical and environmental conditions.\n                They typically feature higher elevations, moderate rainfall, or effective drainage systems.\n              </Typography>\n              <Typography variant=\"body2\">\n                <strong>Key characteristics:</strong>\n              </Typography>\n              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average rainfall: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } mm\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average elevation: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average water level: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n              </ul>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default FloodMap;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,YAAY,EACZC,SAAS,EACTC,YAAY,EACZC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,SAAS,EACTC,MAAM,QACD,eAAe;AACtB,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAO,gBAAgB,CAAC,CAAC;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,QAAQ,QACH,eAAe;AACtB,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AAClD,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM8D,eAAe,GAAGZ,SAAS,CAAC;IAChCa,OAAO,EAAEH,QAAQ,GAAG,CAAC,GAAG,GAAG;IAC3BI,MAAM,EAAEJ,QAAQ,GAAG,GAAG,GAAG,EAAE;IAC3BK,MAAM,EAAE;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG;EACvC,CAAC,CAAC;EAEF,oBACEb,OAAA,CAACH,QAAQ,CAACiB,GAAG;IAACC,KAAK,EAAE;MACnBC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,IAAI;MACZC,eAAe,EAAE,OAAO;MACxBC,OAAO,EAAE,WAAW;MACpBC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,4BAA4B;MACvCC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,GAAG;MACV,GAAGjB;IACL,CAAE;IAAAkB,QAAA,gBACA1B,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAEzB,QAAQ,GAAG,CAAC,GAAG;MACrB,CAAE;MAAAoB,QAAA,gBACA1B,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,WAAW;QAACC,UAAU,EAAC,MAAM;QAAAP,QAAA,EAAC;MAElD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrC,OAAA,CAAC7B,UAAU;QACTmE,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEA,CAAA,KAAMhC,WAAW,CAAC,CAACD,QAAQ,CAAE;QACtCqB,EAAE,EAAE;UACFP,eAAe,EAAEd,QAAQ,GAAG,kBAAkB,GAAG,aAAa;UAC9DkC,UAAU,EAAE;QACd,CAAE;QAAAd,QAAA,eAEF1B,OAAA,CAACf,QAAQ;UAACwD,QAAQ,EAAC;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEL/B,QAAQ,iBACPN,OAAA,CAAAE,SAAA;MAAAwB,QAAA,gBACE1B,OAAA,CAACpC,OAAO;QAAC+D,EAAE,EAAE;UAAEe,EAAE,EAAE;QAAE;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BrC,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,WAAW;QAACC,UAAU,EAAC,MAAM;QAACN,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,EAAC;MAEjE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbrC,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBACxD1B,OAAA,CAACzC,GAAG;UAACoE,EAAE,EAAE;YACPF,KAAK,EAAE,EAAE;YACTf,MAAM,EAAE,EAAE;YACVY,YAAY,EAAE,KAAK;YACnBF,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;YACzCC,EAAE,EAAE;UACN;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAc;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENrC,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEa,EAAE,EAAE;QAAI,CAAE;QAAAjB,QAAA,gBAC1D1B,OAAA,CAACzC,GAAG;UAACoE,EAAE,EAAE;YACPF,KAAK,EAAE,EAAE;YACTf,MAAM,EAAE,EAAE;YACVY,YAAY,EAAE,KAAK;YACnBF,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI;YAC3CC,EAAE,EAAE;UACN;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENrC,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,WAAW;QAACC,UAAU,EAAC,MAAM;QAACN,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,EAAC;MAEjE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbrC,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBACxD1B,OAAA,CAAChB,gBAAgB;UAACyD,QAAQ,EAAC,OAAO;UAACd,EAAE,EAAE;YAAEoB,EAAE,EAAE,CAAC;YAAEE,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAuB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENrC,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEa,EAAE,EAAE;QAAI,CAAE;QAAAjB,QAAA,gBAC1D1B,OAAA,CAACjB,WAAW;UAAC0D,QAAQ,EAAC,OAAO;UAACd,EAAE,EAAE;YAAEoB,EAAE,EAAE,CAAC;YAAEE,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClFrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAwB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENrC,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEa,EAAE,EAAE;QAAI,CAAE;QAAAjB,QAAA,gBAC1D1B,OAAA,CAAClB,aAAa;UAAC2D,QAAQ,EAAC,OAAO;UAACd,EAAE,EAAE;YAAEoB,EAAE,EAAE,CAAC;YAAEE,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClFrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAsB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENrC,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,SAAS;QAACL,EAAE,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEe,EAAE,EAAE,CAAC;UAAEM,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACQ,IAAI,CAACC;QAAU,CAAE;QAAA3B,QAAA,EAAC;MAEpG;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA,eACb,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEnB,CAAC;;AAED;AAAAjC,EAAA,CAvGMD,SAAS;EAAA,QACCnC,QAAQ,EAGE4B,SAAS;AAAA;AAAA0D,EAAA,GAJ7BnD,SAAS;AAwGf,MAAMoD,cAAc,GAAGA,CAAC;EAAEC;AAAiB,CAAC,KAAK;EAAAC,GAAA;EAC/C,MAAMC,GAAG,GAAGzG,MAAM,CAAC,CAAC;EACpB,MAAMoD,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EACxB,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmH,aAAa,EAAEC,gBAAgB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqH,SAAS,EAAEC,YAAY,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuH,WAAW,EAAEC,cAAc,CAAC,GAAGxH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmG,KAAK,EAAEsB,QAAQ,CAAC,GAAGzH,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM0H,cAAc,GAAGxH,WAAW,CAAC,MAAOyH,KAAK,IAAK;IAClD,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;IAEvCP,YAAY,CAAC,IAAI,CAAC;IAClBG,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIK,aAAa;IAEjB,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAM3E,KAAK,CAAC4E,GAAG,CAAC,4CAA4C,EAAE;QAC7EC,MAAM,EAAE;UACNC,CAAC,EAAE,GAAGP,KAAK,SAAS;UACpBQ,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,IAAI;UAAE;UACpBC,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE;UACP,iBAAiB,EAAE,gBAAgB;UACnC,YAAY,EAAE;QAChB;MACF,CAAC,CAAC;MAEF,IAAIR,QAAQ,CAACS,IAAI,IAAIT,QAAQ,CAACS,IAAI,CAACX,MAAM,GAAG,CAAC,EAAE;QAC7CT,gBAAgB,CAACW,QAAQ,CAACS,IAAI,CAAC;QAC/BhB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLJ,gBAAgB,CAAC,EAAE,CAAC;QACpBK,QAAQ,CAAC,kDAAkD,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;MACnDhB,QAAQ,CAAC,iDAAiD,CAAC;IAC7D,CAAC,SAAS;MACR;MACA;MACAK,aAAa,GAAGa,UAAU,CAAC,MAAM;QAC/BrB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,OAAO,MAAM;MACX,IAAIQ,aAAa,EAAEc,YAAY,CAACd,aAAa,CAAC;IAChD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,kBAAkB,GAAIC,CAAC,IAAK;IAChC5B,cAAc,CAAC4B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC9B,IAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAACpB,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MACtCL,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyB,kBAAkB,GAAIH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBxB,cAAc,CAACT,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMkC,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,MAAMC,GAAG,GAAGC,UAAU,CAACF,QAAQ,CAACC,GAAG,CAAC;IACpC,MAAME,GAAG,GAAGD,UAAU,CAACF,QAAQ,CAACI,GAAG,CAAC;;IAEpC;IACAxC,GAAG,CAACyC,KAAK,CAAC,CAACJ,GAAG,EAAEE,GAAG,CAAC,EAAE,EAAE,EAAE;MACxBG,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA5C,gBAAgB,CAAC;MACfuC,GAAG;MACHE,GAAG;MACHI,IAAI,EAAEP,QAAQ,CAACQ,YAAY;MAC3BC,IAAI,EAAET,QAAQ,CAACS,IAAI;MACnBC,UAAU,EAAEV,QAAQ,CAACU,UAAU;MAC/BC,OAAO,EAAEX,QAAQ,CAACW;IACpB,CAAC,CAAC;;IAEF;IACAvC,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACElE,OAAA,CAACzC,GAAG;IAACoE,EAAE,EAAE;MACPX,QAAQ,EAAE,UAAU;MACpB0F,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRxF,MAAM,EAAE,IAAI;MACZM,KAAK,EAAE,GAAG;MACVmF,QAAQ,EAAE;IACZ,CAAE;IAAAlF,QAAA,eACA1B,OAAA,CAACtC,KAAK;MACJmJ,SAAS,EAAE,CAAE;MACblF,EAAE,EAAE;QACFmF,CAAC,EAAE,GAAG;QACNxF,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,gBAEF1B,OAAA;QAAM+G,QAAQ,EAAEpB,kBAAmB;QAAAjE,QAAA,eACjC1B,OAAA,CAAC3B,SAAS;UACR2I,SAAS;UACTC,WAAW,EAAC,gCAAgC;UAC5CvB,KAAK,EAAE/B,WAAY;UACnBuD,QAAQ,EAAE3B,kBAAmB;UAC7BvD,OAAO,EAAC,UAAU;UAClBM,IAAI,EAAC,OAAO;UACZ6E,UAAU,EAAE;YACVC,cAAc,eACZpH,OAAA,CAAC1B,cAAc;cAAC0C,QAAQ,EAAC,OAAO;cAAAU,QAAA,eAC9B1B,OAAA,CAACV,UAAU;gBAAC2D,KAAK,EAAC;cAAQ;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACjB;YACDgF,YAAY,EAAEtD,SAAS,gBACrB/D,OAAA,CAAC1B,cAAc;cAAC0C,QAAQ,EAAC,KAAK;cAAAU,QAAA,eAC5B1B,OAAA,CAACzC,GAAG;gBACFoE,EAAE,EAAE;kBACFF,KAAK,EAAE,EAAE;kBACTf,MAAM,EAAE,EAAE;kBACVY,YAAY,EAAE,KAAK;kBACnBgG,MAAM,EAAE,uBAAuB;kBAC/BC,cAAc,EAAElH,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;kBAC1C2E,SAAS,EAAE;gBACb;cAAE;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC,GACfsB,WAAW,gBACb3D,OAAA,CAAC1B,cAAc;cAAC0C,QAAQ,EAAC,KAAK;cAAAU,QAAA,eAC5B1B,OAAA,CAAC7B,UAAU;gBACTmE,IAAI,EAAC,OAAO;gBACZC,OAAO,EAAEA,CAAA,KAAM;kBACbqB,cAAc,CAAC,EAAE,CAAC;kBAClBM,cAAc,CAAC,KAAK,CAAC;gBACvB,CAAE;gBAAAxC,QAAA,eAEF1B,OAAA,CAACR,SAAS;kBAACiD,QAAQ,EAAC;gBAAO;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,GACf,IAAI;YACRV,EAAE,EAAE;cACFL,YAAY,EAAE,CAAC;cACf,eAAe,EAAE;gBACfC,SAAS,EAAE,aAAalB,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;cACpD;YACF;UACF;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAENQ,KAAK,iBACJ7C,OAAA,CAACrB,KAAK;QACJ+I,QAAQ,EAAC,SAAS;QAClB/F,EAAE,EAAE;UAAEgB,EAAE,EAAE,CAAC;UAAErB,YAAY,EAAE;QAAE,CAAE;QAC/BqG,OAAO,EAAEA,CAAA,KAAMxD,QAAQ,CAAC,IAAI,CAAE;QAAAzC,QAAA,EAE7BmB;MAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDrC,OAAA,CAACpB,QAAQ;QAACgJ,EAAE,EAAE3D,WAAW,IAAIJ,aAAa,CAACU,MAAM,GAAG,CAAE;QAAA7C,QAAA,eACpD1B,OAAA,CAACzB,IAAI;UAACoD,EAAE,EAAE;YAAEgB,EAAE,EAAE,CAAC;YAAEkF,SAAS,EAAE,GAAG;YAAErG,QAAQ,EAAE;UAAO,CAAE;UAAAE,QAAA,EACnDmC,aAAa,CAACH,GAAG,CAAC,CAACoE,MAAM,EAAEC,KAAK;YAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;YAAA,oBAC/BnI,OAAA,CAACxB,QAAQ;cAEP4J,MAAM;cACN7F,OAAO,EAAEA,CAAA,KAAMsD,mBAAmB,CAACiC,MAAM,CAAE;cAC3CnG,EAAE,EAAE;gBACFL,YAAY,EAAE,CAAC;gBACfS,EAAE,EAAE,GAAG;gBACP,SAAS,EAAE;kBACTX,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACyF,MAAM,CAACC;gBACxC;cACF,CAAE;cAAA5G,QAAA,gBAEF1B,OAAA,CAACtB,YAAY;gBAACiD,EAAE,EAAE;kBAAE4G,QAAQ,EAAE;gBAAG,CAAE;gBAAA7G,QAAA,eACjC1B,OAAA,CAACT,cAAc;kBAAC0D,KAAK,EAAC;gBAAS;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACfrC,OAAA,CAACvB,YAAY;gBACX+I,OAAO,EAAE,EAAAQ,eAAA,GAAAF,MAAM,CAACrB,OAAO,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBQ,IAAI,OAAAP,gBAAA,GAAIH,MAAM,CAACrB,OAAO,cAAAwB,gBAAA,uBAAdA,gBAAA,CAAgBQ,IAAI,OAAAP,gBAAA,GAAIJ,MAAM,CAACrB,OAAO,cAAAyB,gBAAA,uBAAdA,gBAAA,CAAgBQ,OAAO,OAAAP,gBAAA,GAAIL,MAAM,CAACrB,OAAO,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBQ,KAAK,KAAIb,MAAM,CAACxB,YAAY,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;gBAC/IvF,SAAS,EAAEyE,MAAM,CAACxB,YAAa;gBAC/BuC,wBAAwB,EAAE;kBACxBC,MAAM,EAAE,IAAI;kBACZ/H,KAAK,EAAE;oBAAE0B,QAAQ,EAAE;kBAAU;gBAC/B;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GArBG0F,KAAK;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBF,CAAC;UAAA,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;;AAED;AAAAoB,GAAA,CA7MMF,cAAc;EAAA,QACNtG,MAAM,EACJe,QAAQ;AAAA;AAAA+K,GAAA,GAFlBxF,cAAc;AA8MpB,MAAMyF,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxB,MAAMvF,GAAG,GAAGzG,MAAM,CAAC,CAAC;EACpB,MAAMoD,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EAExB,MAAMkL,YAAY,GAAGA,CAAA,KAAM;IACzBxF,GAAG,CAACyF,MAAM,CAAC,CAAC;EACd,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B1F,GAAG,CAAC2F,OAAO,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB5F,GAAG,CAAC6F,MAAM,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;EAC5C,CAAC;EAED,oBACEzJ,OAAA,CAACzC,GAAG;IAACoE,EAAE,EAAE;MACPX,QAAQ,EAAE,UAAU;MACpB0F,GAAG,EAAE,EAAE;MACPxF,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,IAAI;MACZS,OAAO,EAAE,MAAM;MACf8H,aAAa,EAAE,QAAQ;MACvBC,GAAG,EAAE;IACP,CAAE;IAAAjI,QAAA,gBACA1B,OAAA,CAAC9B,OAAO;MAAC0L,KAAK,EAAC,SAAS;MAACC,SAAS,EAAC,MAAM;MAAAnI,QAAA,eACvC1B,OAAA,CAAC7B,UAAU;QACToE,OAAO,EAAE2G,YAAa;QACtBvH,EAAE,EAAE;UACFP,eAAe,EAAE,OAAO;UACxBG,SAAS,EAAE,2BAA2B;UACtC,SAAS,EAAE;YACTH,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACkH,IAAI,CAAC,GAAG;UACzC;QACF,CAAE;QAAApI,QAAA,eAEF1B,OAAA,CAACZ,UAAU;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEVrC,OAAA,CAAC9B,OAAO;MAAC0L,KAAK,EAAC,UAAU;MAACC,SAAS,EAAC,MAAM;MAAAnI,QAAA,eACxC1B,OAAA,CAAC7B,UAAU;QACToE,OAAO,EAAE6G,aAAc;QACvBzH,EAAE,EAAE;UACFP,eAAe,EAAE,OAAO;UACxBG,SAAS,EAAE,2BAA2B;UACtC,SAAS,EAAE;YACTH,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACkH,IAAI,CAAC,GAAG;UACzC;QACF,CAAE;QAAApI,QAAA,eAEF1B,OAAA,CAACX,WAAW;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEVrC,OAAA,CAAC9B,OAAO;MAAC0L,KAAK,EAAC,aAAa;MAACC,SAAS,EAAC,MAAM;MAAAnI,QAAA,eAC3C1B,OAAA,CAAC7B,UAAU;QACToE,OAAO,EAAE+G,YAAa;QACtB3H,EAAE,EAAE;UACFP,eAAe,EAAE,OAAO;UACxBG,SAAS,EAAE,2BAA2B;UACtC,SAAS,EAAE;YACTH,eAAe,EAAEf,KAAK,CAACuC,OAAO,CAACkH,IAAI,CAAC,GAAG;UACzC;QACF,CAAE;QAAApI,QAAA,eAEF1B,OAAA,CAACb,cAAc;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;;AAED;AAAA4G,GAAA,CA1EMD,WAAW;EAAA,QACH/L,MAAM,EACJe,QAAQ;AAAA;AAAA+L,GAAA,GAFlBf,WAAW;AA2EjB,MAAMgB,YAAY,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EACpC,MAAMxG,GAAG,GAAGzG,MAAM,CAAC,CAAC;EACpB,MAAMoD,KAAK,GAAGrC,QAAQ,CAAC,CAAC;;EAExB;EACArB,SAAS,CAAC,MAAM;IACd,IAAI,CAACsN,OAAO,IAAIA,OAAO,CAAC1F,MAAM,KAAK,CAAC,EAAE;;IAEtC;IACA,MAAM4F,cAAc,GAAGF,OAAO,CAACG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC;IAC5E,MAAMC,QAAQ,GAAG,EAAE;;IAEnB;IACAJ,cAAc,CAACK,OAAO,CAACH,KAAK,IAAI;MAC9B,MAAMtE,GAAG,GAAGsE,KAAK,CAACI,QAAQ;MAC1B,MAAMxE,GAAG,GAAGoE,KAAK,CAACK,SAAS;;MAE3B;MACA,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,IAAI,CAACC,OAAO,IAAI;QACzC,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACG,SAAS,GAAGjF,GAAG,CAAC,GAAG,CAAC,IACrC+E,IAAI,CAACC,GAAG,CAACF,OAAO,CAACI,SAAS,GAAGhF,GAAG,CAAC,GAAG,CAAC;MAC9C,CAAC,CAAC;MAEF,IAAI,CAAC0E,SAAS,IAAIR,cAAc,CAACC,MAAM,CAACtD,CAAC,IACvCgE,IAAI,CAACC,GAAG,CAACjE,CAAC,CAAC2D,QAAQ,GAAG1E,GAAG,CAAC,GAAG,CAAC,IAC9B+E,IAAI,CAACC,GAAG,CAACjE,CAAC,CAAC4D,SAAS,GAAGzE,GAAG,CAAC,GAAG,CAChC,CAAC,CAAC1B,MAAM,GAAG,CAAC,EAAE;QACZ;QACAgG,QAAQ,CAACW,IAAI,CAAC;UACZF,SAAS,EAAEjF,GAAG;UACdkF,SAAS,EAAEhF,GAAG;UACdkF,KAAK,EAAEhB,cAAc,CAACC,MAAM,CAACtD,CAAC,IAC5BgE,IAAI,CAACC,GAAG,CAACjE,CAAC,CAAC2D,QAAQ,GAAG1E,GAAG,CAAC,GAAG,CAAC,IAC9B+E,IAAI,CAACC,GAAG,CAACjE,CAAC,CAAC4D,SAAS,GAAGzE,GAAG,CAAC,GAAG,CAChC,CAAC,CAAC1B;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACX;IAAA,CACD;EACH,CAAC,EAAE,CAAC0F,OAAO,EAAEvG,GAAG,CAAC,CAAC;EAElB,OAAO,IAAI,CAAC,CAAC;AACf,CAAC;;AAED;AAAAwG,GAAA,CA/CMF,YAAY;EAAA,QACJ/M,MAAM,EACJe,QAAQ;AAAA;AAAAoN,GAAA,GAFlBpB,YAAY;AAgDlB,MAAMqB,sBAAsB,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAY,CAAC,KAAK;EAAAC,GAAA;EAC7D,MAAMnL,KAAK,GAAGrC,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMyN,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,QAAQ,GAAG,EAAE;;IAEnB;IACA,IAAIJ,SAAS,KAAK,MAAM,EAAE;MACxBI,QAAQ,CAACR,IAAI,CAAC;QACZtB,KAAK,EAAE,qBAAqB;QAC5B+B,WAAW,EAAE,qFAAqF;QAClGC,IAAI,eAAE5L,OAAA,CAACL,QAAQ;UAACgC,EAAE,EAAE;YAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC5D,CAAC,CAAC;MACFqJ,QAAQ,CAACR,IAAI,CAAC;QACZtB,KAAK,EAAE,sBAAsB;QAC7B+B,WAAW,EAAE,8EAA8E;QAC3FC,IAAI,eAAE5L,OAAA,CAACP,WAAW;UAACkC,EAAE,EAAE;YAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC/D,CAAC,CAAC;IACJ;;IAEA;IACAkJ,WAAW,CAACf,OAAO,CAACqB,MAAM,IAAI;MAC5B,IAAIA,MAAM,CAACA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,CAACnE,QAAQ,KAAK,MAAM,EAAE;QACpEgE,QAAQ,CAACR,IAAI,CAAC;UACZtB,KAAK,EAAE,sBAAsB;UAC7B+B,WAAW,EAAE,gGAAgG;UAC7GC,IAAI,eAAE5L,OAAA,CAACN,eAAe;YAACiC,EAAE,EAAE;cAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ;YAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAClE,CAAC,CAAC;MACJ;MAEA,IAAIwJ,MAAM,CAACA,MAAM,KAAK,eAAe,IAAIA,MAAM,CAACnE,QAAQ,KAAK,MAAM,EAAE;QACnEgE,QAAQ,CAACR,IAAI,CAAC;UACZtB,KAAK,EAAE,qBAAqB;UAC5B+B,WAAW,EAAE,8FAA8F;UAC3GC,IAAI,eAAE5L,OAAA,CAACL,QAAQ;YAACgC,EAAE,EAAE;cAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL;YAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC9D,CAAC,CAAC;MACJ;MAEA,IAAIwJ,MAAM,CAACA,MAAM,KAAK,kBAAkB,IAAIA,MAAM,CAACnE,QAAQ,KAAK,MAAM,EAAE;QACtEgE,QAAQ,CAACR,IAAI,CAAC;UACZtB,KAAK,EAAE,gBAAgB;UACvB+B,WAAW,EAAE,8FAA8F;UAC3GC,IAAI,eAAE5L,OAAA,CAAClB,aAAa;YAAC6C,EAAE,EAAE;cAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC;YAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACjE,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,IAAIqJ,QAAQ,CAACnH,MAAM,KAAK,CAAC,EAAE;MACzBmH,QAAQ,CAACR,IAAI,CAAC;QACZtB,KAAK,EAAE,oBAAoB;QAC3B+B,WAAW,EAAE,mEAAmE;QAChFC,IAAI,eAAE5L,OAAA,CAACf,QAAQ;UAAC0C,EAAE,EAAE;YAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ;UAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC3D,CAAC,CAAC;IACJ;IAEA,OAAOqJ,QAAQ;EACjB,CAAC;EAED,MAAMA,QAAQ,GAAGD,qBAAqB,CAAC,CAAC;EAExC,oBACEzL,OAAA,CAACzC,GAAG;IAACoE,EAAE,EAAE;MAAEgB,EAAE,EAAE;IAAE,CAAE;IAAAjB,QAAA,gBACjB1B,OAAA,CAACxC,UAAU;MAACwE,OAAO,EAAC,WAAW;MAACC,UAAU,EAAC,MAAM;MAAC6J,YAAY;MAAApK,QAAA,EAAC;IAE/D;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZqJ,QAAQ,CAAChI,GAAG,CAAC,CAACqI,OAAO,EAAEhE,KAAK,kBAC3B/H,OAAA,CAACzC,GAAG;MAEFoE,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfG,EAAE,EAAE,GAAG;QACP+E,CAAC,EAAE,CAAC;QACJxF,YAAY,EAAE,CAAC;QACfF,eAAe,EAAE;MACnB,CAAE;MAAAM,QAAA,gBAEF1B,OAAA,CAACzC,GAAG;QAACoE,EAAE,EAAE;UAAEoB,EAAE,EAAE,GAAG;UAAEJ,EAAE,EAAE;QAAI,CAAE;QAAAjB,QAAA,EAC3BqK,OAAO,CAACH;MAAI;QAAA1J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNrC,OAAA,CAACzC,GAAG;QAAAmE,QAAA,gBACF1B,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACC,UAAU,EAAC,QAAQ;UAAAP,QAAA,EAC5CqK,OAAO,CAACnC;QAAK;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,SAAS;UAACiB,KAAK,EAAC,gBAAgB;UAAAvB,QAAA,EACjDqK,OAAO,CAACJ;QAAW;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,GAnBD0F,KAAK;MAAA7F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoBP,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAmJ,GAAA,CAhGMH,sBAAsB;EAAA,QACZrN,QAAQ;AAAA;AAAAgO,GAAA,GADlBX,sBAAsB;AAiG5B,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1P,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtD,MAAM2P,eAAe,GAAGzP,WAAW,CAAC,OAAOmJ,GAAG,EAAEE,GAAG,KAAK;IACtD;IACA,MAAMqG,QAAQ,GAAG,GAAGvG,GAAG,CAACwG,OAAO,CAAC,CAAC,CAAC,IAAItG,GAAG,CAACsG,OAAO,CAAC,CAAC,CAAC,EAAE;IACtD,IAAIJ,aAAa,CAACG,QAAQ,CAAC,EAAE;MAC3B,OAAOH,aAAa,CAACG,QAAQ,CAAC;IAChC;IAEA,IAAI;MACF,MAAM7H,QAAQ,GAAG,MAAM3E,KAAK,CAAC4E,GAAG,CAAC,6CAA6C,EAAE;QAC9EC,MAAM,EAAE;UACNoB,GAAG;UACHG,GAAG,EAAED,GAAG;UACRpB,MAAM,EAAE,MAAM;UACd2H,IAAI,EAAE,EAAE;UAAE;UACVxH,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE;UACP,iBAAiB,EAAE,gBAAgB;UACnC,YAAY,EAAE;QAChB;MACF,CAAC,CAAC;MAEF,IAAIR,QAAQ,CAACS,IAAI,EAAE;QACjB;QACA,MAAMuH,YAAY,GAAGhI,QAAQ,CAACS,IAAI;QAClC,IAAIwH,SAAS,GAAG,EAAE;;QAElB;QACA,IAAID,YAAY,CAAChG,OAAO,EAAE;UACxBiG,SAAS,GAAGD,YAAY,CAAChG,OAAO,CAACiC,OAAO,IAC7B+D,YAAY,CAAChG,OAAO,CAACgC,IAAI,IACzBgE,YAAY,CAAChG,OAAO,CAAC+B,IAAI,IACzBiE,YAAY,CAAChG,OAAO,CAACkG,MAAM,IAC3BF,YAAY,CAAChG,OAAO,CAACmG,cAAc,IACnCH,YAAY,CAAChG,OAAO,CAACkC,KAAK;QACvC;;QAEA;QACA,IAAI,CAAC+D,SAAS,IAAID,YAAY,CAACnG,YAAY,EAAE;UAC3CoG,SAAS,GAAGD,YAAY,CAACnG,YAAY,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrD;;QAEA;QACA,IAAI,CAAC8D,SAAS,EAAE;UACdA,SAAS,GAAG,eAAe3G,GAAG,CAACwG,OAAO,CAAC,CAAC,CAAC,KAAKtG,GAAG,CAACsG,OAAO,CAAC,CAAC,CAAC,EAAE;QAChE;;QAEA;QACAH,gBAAgB,CAACS,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACP,QAAQ,GAAG;YACVjG,IAAI,EAAEqG,SAAS;YACfI,QAAQ,EAAEL,YAAY,CAACnG,YAAY,IAAI,EAAE;YACzCG,OAAO,EAAEgG,YAAY,CAAChG,OAAO,IAAI,CAAC;UACpC;QACF,CAAC,CAAC,CAAC;QAEH,OAAO;UACLJ,IAAI,EAAEqG,SAAS;UACfI,QAAQ,EAAEL,YAAY,CAACnG,YAAY,IAAI,EAAE;UACzCG,OAAO,EAAEgG,YAAY,CAAChG,OAAO,IAAI,CAAC;QACpC,CAAC;MACH;IACF,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;;IAEA;IACA,OAAO;MACLwD,IAAI,EAAE,eAAeN,GAAG,CAACwG,OAAO,CAAC,CAAC,CAAC,KAAKtG,GAAG,CAACsG,OAAO,CAAC,CAAC,CAAC,EAAE;MACxDO,QAAQ,EAAE,EAAE;MACZrG,OAAO,EAAE,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,CAAC0F,aAAa,CAAC,CAAC;EAEnB,OAAO;IAAEE,eAAe;IAAEF;EAAc,CAAC;AAC3C,CAAC;;AAED;AAAAD,GAAA,CAjFMD,mBAAmB;AAkFzB,MAAMc,aAAa,GAAGA,CAAC;EAAE1C,KAAK;EAAE2C,sBAAsB,GAAG,KAAK;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,GAAA;EACxF,MAAM7M,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EACxB,MAAMmP,UAAU,GAAG9C,KAAK,CAACC,gBAAgB,KAAK,CAAC;EAC/C,MAAM;IAAE+B,eAAe;IAAEF;EAAc,CAAC,GAAGF,mBAAmB,CAAC,CAAC;EAChE,MAAM,CAACnG,QAAQ,EAAEsH,WAAW,CAAC,GAAG1Q,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2Q,OAAO,EAAEC,UAAU,CAAC,GAAG5Q,QAAQ,CAAC,CAACuQ,YAAY,CAAC;EAErDtQ,SAAS,CAAC,MAAM;IACd,IAAIsQ,YAAY,EAAE;MAChBG,WAAW,CAACH,YAAY,CAAC;MACzBK,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,MAAMhB,QAAQ,GAAG,GAAGjC,KAAK,CAACI,QAAQ,CAAC8B,OAAO,CAAC,CAAC,CAAC,IAAIlC,KAAK,CAACK,SAAS,CAAC6B,OAAO,CAAC,CAAC,CAAC,EAAE;IAC7E,IAAIJ,aAAa,CAACG,QAAQ,CAAC,EAAE;MAC3Bc,WAAW,CAACjB,aAAa,CAACG,QAAQ,CAAC,CAAC;MACpCgB,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpCD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxF,MAAM,GAAG,MAAMuE,eAAe,CAAChC,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACK,SAAS,CAAC;MACrE0C,WAAW,CAACtF,MAAM,CAAC;MACnBwF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAClD,KAAK,EAAE4C,YAAY,EAAEZ,eAAe,EAAEF,aAAa,CAAC,CAAC;;EAEzD;EACA,MAAMZ,WAAW,GAAG,EAAE;EACtB,IAAIlB,KAAK,CAAC,eAAe,CAAC,GAAG,GAAG,EAAE;IAChCkB,WAAW,CAACL,IAAI,CAAC;MACfW,MAAM,EAAE,gBAAgB;MACxBnG,KAAK,EAAE,GAAG2E,KAAK,CAAC,eAAe,CAAC,KAAK;MACrCuB,IAAI,eAAE5L,OAAA,CAAChB,gBAAgB;QAACyD,QAAQ,EAAC,OAAO;QAACd,EAAE,EAAE;UAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ;QAAK;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnFqF,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,IAAI2C,KAAK,CAAC,eAAe,CAAC,GAAG,GAAG,EAAE;IAChCkB,WAAW,CAACL,IAAI,CAAC;MACfW,MAAM,EAAE,eAAe;MACvBnG,KAAK,EAAE,GAAG2E,KAAK,CAAC,eAAe,CAAC,IAAI;MACpCuB,IAAI,eAAE5L,OAAA,CAACjB,WAAW;QAAC0D,QAAQ,EAAC,OAAO;QAACd,EAAE,EAAE;UAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL;QAAK;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjFqF,QAAQ,EAAE2C,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG;IACnD,CAAC,CAAC;EACJ;EAEA,IAAIA,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;IAChCkB,WAAW,CAACL,IAAI,CAAC;MACfW,MAAM,EAAE,kBAAkB;MAC1BnG,KAAK,EAAE,GAAG2E,KAAK,CAAC,iBAAiB,CAAC,IAAI;MACtCuB,IAAI,eAAE5L,OAAA,CAAClB,aAAa;QAAC2D,QAAQ,EAAC,OAAO;QAACd,EAAE,EAAE;UAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC;QAAK;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjFqF,QAAQ,EAAE2C,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG;IACpD,CAAC,CAAC;EACJ;EAEA,oBACErK,OAAA,CAACzC,GAAG;IACFoE,EAAE,EAAE;MACFF,KAAK,EAAE,MAAM;MACbmF,QAAQ,EAAEoG,sBAAsB,GAAG,GAAG,GAAG,GAAG;MAC5CQ,SAAS,EAAE;IACb,CAAE;IACFC,SAAS,EAAC,eAAe;IAAA/L,QAAA,gBAEzB1B,OAAA,CAACxC,UAAU;MACTwE,OAAO,EAAC,WAAW;MACnBC,UAAU,EAAC,MAAM;MACjBN,EAAE,EAAE;QACFI,EAAE,EAAE,GAAG;QACPkB,KAAK,EAAEkK,UAAU,GAAG9M,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI,GAAGzC,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI;QACzElB,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpB6H,GAAG,EAAE,CAAC;QACN3I,QAAQ,EAAE,QAAQ;QAClB0F,GAAG,EAAE,CAAC;QACNtF,eAAe,EAAE,OAAO;QACxBD,MAAM,EAAE,EAAE;QACVuM,EAAE,EAAE;MACN,CAAE;MAAAhM,QAAA,gBAEF1B,OAAA,CAAClB,aAAa;QAAC2D,QAAQ,EAAC;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjC8K,UAAU,GAAG,sBAAsB,GAAG,qBAAqB;IAAA;MAAAjL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAEZgL,OAAO,gBACNrN,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE,QAAQ;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBACxD1B,OAAA,CAACzC,GAAG;QACFoE,EAAE,EAAE;UACFF,KAAK,EAAE,EAAE;UACTf,MAAM,EAAE,EAAE;UACVY,YAAY,EAAE,KAAK;UACnBgG,MAAM,EAAE,uBAAuB;UAC/BC,cAAc,EAAElH,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;UAC1C2E,SAAS,EAAE,yBAAyB;UACpC1E,EAAE,EAAE;QACN;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFrC,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,OAAO;QAACiB,KAAK,EAAC,gBAAgB;QAAAvB,QAAA,EAAC;MAEnD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,GACJyD,QAAQ,gBACV9F,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACjB1B,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,WAAW;QAACC,UAAU,EAAC,QAAQ;QAAAP,QAAA,EAChDoE,QAAQ,CAACO;MAAI;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACZyD,QAAQ,CAACgH,QAAQ,iBAChB9M,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,SAAS;QAACiB,KAAK,EAAC,gBAAgB;QAACtB,EAAE,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEG,EAAE,EAAE;QAAI,CAAE;QAAAL,QAAA,EACpFoE,QAAQ,CAACgH;MAAQ;QAAA5K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,IAAI,eAERrC,OAAA,CAACpC,OAAO;MAAC+D,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAI;IAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE5BrC,OAAA,CAACjC,IAAI;MAAC4P,SAAS;MAACC,OAAO,EAAE,CAAE;MAACjM,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAI,CAAE;MAAAL,QAAA,gBAC1C1B,OAAA,CAACjC,IAAI;QAAC8P,IAAI;QAACC,EAAE,EAAE,CAAE;QAAApM,QAAA,gBACf1B,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,SAAS;UAACiB,KAAK,EAAC,gBAAgB;UAAAvB,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACC,UAAU,EAAC,QAAQ;UAAAP,QAAA,GAC5C2I,KAAK,CAAC,eAAe,CAAC,EAAC,KAC1B;QAAA;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPrC,OAAA,CAACjC,IAAI;QAAC8P,IAAI;QAACC,EAAE,EAAE,CAAE;QAAApM,QAAA,gBACf1B,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,SAAS;UAACiB,KAAK,EAAC,gBAAgB;UAAAvB,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACC,UAAU,EAAC,QAAQ;UAAAP,QAAA,GAC5C2I,KAAK,CAAC,eAAe,CAAC,EAAC,IAC1B;QAAA;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPrC,OAAA,CAACjC,IAAI;QAAC8P,IAAI;QAACC,EAAE,EAAE,CAAE;QAAApM,QAAA,gBACf1B,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,SAAS;UAACiB,KAAK,EAAC,gBAAgB;UAAAvB,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACC,UAAU,EAAC,QAAQ;UAAAP,QAAA,GAC5C2I,KAAK,CAAC,iBAAiB,CAAC,EAAC,IAC5B;QAAA;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACPrC,OAAA,CAACjC,IAAI;QAAC8P,IAAI;QAACC,EAAE,EAAE,CAAE;QAAApM,QAAA,gBACf1B,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,SAAS;UAACiB,KAAK,EAAC,gBAAgB;UAAAvB,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACC,UAAU,EAAC,QAAQ;UAAAP,QAAA,GAC5C2I,KAAK,CAACI,QAAQ,CAAC8B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAClC,KAAK,CAACK,SAAS,CAAC6B,OAAO,CAAC,CAAC,CAAC;QAAA;UAAArK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAENkJ,WAAW,CAAChH,MAAM,GAAG,CAAC,iBACrBvE,OAAA,CAAAE,SAAA;MAAAwB,QAAA,gBACE1B,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,SAAS;QAACiB,KAAK,EAAC,gBAAgB;QAACtB,EAAE,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEG,EAAE,EAAE;QAAI,CAAE;QAAAL,QAAA,EAAC;MAExF;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZkJ,WAAW,CAAC7H,GAAG,CAAC,CAACmI,MAAM,EAAEkC,GAAG,kBAC3B/N,OAAA,CAACzC,GAAG;QAAWoE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAL,QAAA,GACnEmK,MAAM,CAACD,IAAI,eACZ5L,OAAA,CAACxC,UAAU;UAACwE,OAAO,EAAC,OAAO;UAACL,EAAE,EAAE;YAAEqM,EAAE,EAAE;UAAI,CAAE;UAAAtM,QAAA,GACzCmK,MAAM,CAACA,MAAM,EAAC,IAAE,eAAA7L,OAAA;YAAA0B,QAAA,EAASmK,MAAM,CAACnG;UAAK;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA,GAJL0L,GAAG;QAAA7L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACN,CAAC;IAAA,eACF,CACH,EAEA8K,UAAU,IAAI,CAACH,sBAAsB,iBACpChN,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QACPgB,EAAE,EAAE,GAAG;QACPmE,CAAC,EAAE,CAAC;QACJ1F,eAAe,EAAE,wBAAwB;QACzCE,YAAY,EAAE,CAAC;QACf2M,UAAU,EAAE,aAAa5N,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;MACnD,CAAE;MAAApB,QAAA,gBACA1B,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,SAAS;QAACL,EAAE,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEK,UAAU,EAAE;QAAS,CAAE;QAAAP,QAAA,EAAC;MAE9E;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,OAAO;QAAAN,QAAA,EAAC;MAE5B;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEA2K,sBAAsB,iBACrBhN,OAAA,CAACqL,sBAAsB;MACrBC,SAAS,EAAE6B,UAAU,GAAG,MAAM,GAAG,KAAM;MACvC5B,WAAW,EAAEA;IAAY;MAAArJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC6K,GAAA,CAxMIH,aAAa;EAAA,QACH/O,QAAQ,EAEqBiO,mBAAmB;AAAA;AAAAiC,GAAA,GAH1DnB,aAAa;AA0MnB,MAAMoB,QAAQ,GAAGA,CAAC;EAAElE;AAAQ,CAAC,KAAK;EAAAmE,GAAA;EAChC,MAAM/N,KAAK,GAAGrC,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACqP,OAAO,EAAEC,UAAU,CAAC,GAAG5Q,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2R,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5R,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6R,YAAY,EAAEC,eAAe,CAAC,GAAG9R,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+R,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhS,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiS,YAAY,EAAEC,eAAe,CAAC,GAAGlS,QAAQ,CAAC;IAAEmS,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEpH,QAAQ,EAAE;EAAO,CAAC,CAAC;EAEhG/K,SAAS,CAAC,MAAM;IACd,IAAIsN,OAAO,IAAIA,OAAO,CAAC1F,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,MAAMwK,KAAK,GAAG1J,UAAU,CAAC,MAAM;QAC7BiI,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM;QACXhI,YAAY,CAACyJ,KAAK,CAAC;QACnB;QACAzB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;IACH,CAAC,MAAM;MACL;MACA,MAAMyB,KAAK,GAAG1J,UAAU,CAAC,MAAM;QAC7BiI,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAMhI,YAAY,CAACyJ,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC9E,OAAO,CAAC,CAAC;;EAEb;EACA,MAAM+E,gBAAgB,GAAGpS,WAAW,CAAEkJ,QAAQ,IAAK;IACjD,IAAI,CAACmE,OAAO,IAAIA,OAAO,CAAC1F,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEjD,IAAI0K,OAAO,GAAG,IAAI;IAClB,IAAIC,WAAW,GAAGC,QAAQ;IAE1BlF,OAAO,CAACO,OAAO,CAACH,KAAK,IAAI;MACvB,MAAM+E,QAAQ,GAAGtE,IAAI,CAACuE,IAAI,CACxBvE,IAAI,CAACwE,GAAG,CAACjF,KAAK,CAACI,QAAQ,GAAG3E,QAAQ,CAACC,GAAG,EAAE,CAAC,CAAC,GAC1C+E,IAAI,CAACwE,GAAG,CAACjF,KAAK,CAACK,SAAS,GAAG5E,QAAQ,CAACG,GAAG,EAAE,CAAC,CAC5C,CAAC;MAED,IAAImJ,QAAQ,GAAGF,WAAW,EAAE;QAC1BA,WAAW,GAAGE,QAAQ;QACtBH,OAAO,GAAG5E,KAAK;MACjB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI6E,WAAW,GAAG,GAAG,EAAE;MACrBN,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,8EAA8E;QACvFpH,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,OAAOuH,OAAO;EAChB,CAAC,EAAE,CAAChF,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMsF,oBAAoB,GAAG3S,WAAW,CAAEkJ,QAAQ,IAAK;IACrDwI,mBAAmB,CAACxI,QAAQ,CAAC;IAC7B,MAAMmJ,OAAO,GAAGD,gBAAgB,CAAClJ,QAAQ,CAAC;IAC1C0I,eAAe,CAACS,OAAO,CAAC;IACxBP,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,EAAE,CAACM,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMQ,uBAAuB,GAAGA,CAAA,KAAM;IACpCZ,eAAe,CAAC/B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEgC,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,IAAI,CAAC5E,OAAO,IAAIA,OAAO,CAAC1F,MAAM,KAAK,CAAC,EAAE;IACpC,oBACEvE,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEf,MAAM,EAAE,GAAG;QAAEY,YAAY,EAAE;MAAE,CAAE;MAAAI,QAAA,eACvD1B,OAAA,CAACvC,QAAQ;QAACuE,OAAO,EAAC,aAAa;QAACP,KAAK,EAAC,MAAM;QAACf,MAAM,EAAC;MAAM;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAEV;;EAEA;EACA,MAAMoN,aAAa,GAAGxF,OAAO,CAACG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAAC/F,MAAM;EAClF,MAAMmL,YAAY,GAAGzF,OAAO,CAACG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAAC/F,MAAM;;EAEjF;EACA,MAAMoL,WAAW,GAAG1F,OAAO,CAAC2F,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,GAAGJ,OAAO,CAAC1F,MAAM;EACpG,MAAMuL,YAAY,GAAG7F,OAAO,CAAC2F,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,GAAGJ,OAAO,CAAC1F,MAAM;EAErG,oBACEvE,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACE1B,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjB1B,OAAA,CAACjC,IAAI;QAAC4P,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlM,QAAA,gBACzB1B,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACiC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eAC9B1B,OAAA,CAACtC,KAAK;YACJmJ,SAAS,EAAE,CAAE;YACblF,EAAE,EAAE;cACFmF,CAAC,EAAE,CAAC;cACJxF,YAAY,EAAE,CAAC;cACf2O,UAAU,EAAE,2BAA2B5P,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACqN,KAAK,UAAU7P,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACqN,KAAK,UAAU;cAC7GjC,UAAU,EAAE,aAAa5N,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;YACnD,CAAE;YAAApB,QAAA,gBAEF1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACiB,KAAK,EAAC,gBAAgB;cAAAvB,QAAA,EAAC;YAEvD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAK;cAAApB,QAAA,EACxE+N;YAAa;cAAAvN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACiC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eAC9B1B,OAAA,CAACtC,KAAK;YACJmJ,SAAS,EAAE,CAAE;YACblF,EAAE,EAAE;cACFmF,CAAC,EAAE,CAAC;cACJxF,YAAY,EAAE,CAAC;cACf2O,UAAU,EAAE,2BAA2B5P,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACkN,KAAK,UAAU7P,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACkN,KAAK,UAAU;cACjHjC,UAAU,EAAE,aAAa5N,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI;YACrD,CAAE;YAAApB,QAAA,gBAEF1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACiB,KAAK,EAAC,gBAAgB;cAAAvB,QAAA,EAAC;YAEvD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAK;cAAApB,QAAA,EAC1EgO;YAAY;cAAAxN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACiC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eAC9B1B,OAAA,CAACtC,KAAK;YACJmJ,SAAS,EAAE,CAAE;YACblF,EAAE,EAAE;cACFmF,CAAC,EAAE,CAAC;cACJxF,YAAY,EAAE,CAAC;cACf2O,UAAU,EAAE,2BAA2B5P,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACgN,KAAK,UAAU7P,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACgN,KAAK,UAAU;cAC3GjC,UAAU,EAAE,aAAa5N,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ,IAAI;YAClD,CAAE;YAAApB,QAAA,gBAEF1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACiB,KAAK,EAAC,gBAAgB;cAAAvB,QAAA,EAAC;YAEvD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ,IAAK;cAAApB,QAAA,GACvEiO,WAAW,CAACpD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAAvM,OAAA,CAACxC,UAAU;gBAAC2S,SAAS,EAAC,MAAM;gBAACnO,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACiC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eAC9B1B,OAAA,CAACtC,KAAK;YACJmJ,SAAS,EAAE,CAAE;YACblF,EAAE,EAAE;cACFmF,CAAC,EAAE,CAAC;cACJxF,YAAY,EAAE,CAAC;cACf2O,UAAU,EAAE,2BAA2B5P,KAAK,CAACuC,OAAO,CAACO,OAAO,CAAC+M,KAAK,UAAU7P,KAAK,CAACuC,OAAO,CAACO,OAAO,CAAC+M,KAAK,UAAU;cACjHjC,UAAU,EAAE,aAAa5N,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL,IAAI;YACrD,CAAE;YAAApB,QAAA,gBAEF1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACiB,KAAK,EAAC,gBAAgB;cAAAvB,QAAA,EAAC;YAEvD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL,IAAK;cAAApB,QAAA,GAC1EoO,YAAY,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAAvM,OAAA,CAACxC,UAAU;gBAAC2S,SAAS,EAAC,MAAM;gBAACnO,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENrC,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEX,QAAQ,EAAE,UAAU;QAAEN,MAAM,EAAE,GAAG;QAAEY,YAAY,EAAE,CAAC;QAAEE,QAAQ,EAAE;MAAS,CAAE;MAAAE,QAAA,GACjF2L,OAAO,iBACNrN,OAAA,CAAC5B,IAAI;QAACwJ,EAAE,EAAEyF,OAAQ;QAAC+C,OAAO,EAAE,GAAI;QAAA1O,QAAA,eAC9B1B,OAAA,CAACzC,GAAG;UACFoE,EAAE,EAAE;YACFX,QAAQ,EAAE,UAAU;YACpB0F,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPzF,KAAK,EAAE,CAAC;YACRD,MAAM,EAAE,CAAC;YACTW,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBT,eAAe,EAAE,uBAAuB;YACxCD,MAAM,EAAE;UACV,CAAE;UAAAO,QAAA,eAEF1B,OAAA,CAACzC,GAAG;YAACoE,EAAE,EAAE;cAAE0O,SAAS,EAAE;YAAS,CAAE;YAAA3O,QAAA,gBAC/B1B,OAAA,CAACzC,GAAG;cAACoE,EAAE,EAAE;gBAAEX,QAAQ,EAAE,UAAU;gBAAES,KAAK,EAAE,EAAE;gBAAEf,MAAM,EAAE,EAAE;gBAAE4P,MAAM,EAAE;cAAS,CAAE;cAAA5O,QAAA,eACzE1B,OAAA,CAACzC,GAAG;gBACFoE,EAAE,EAAE;kBACFX,QAAQ,EAAE,UAAU;kBACpB0F,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPlF,KAAK,EAAE,MAAM;kBACbf,MAAM,EAAE,MAAM;kBACdY,YAAY,EAAE,KAAK;kBACnBgG,MAAM,EAAE,uBAAuB;kBAC/BC,cAAc,EAAElH,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;kBAC1C2E,SAAS,EAAE,yBAAyB;kBACpC,iBAAiB,EAAE;oBACjB,IAAI,EAAE;sBAAE8I,SAAS,EAAE;oBAAe,CAAC;oBACnC,MAAM,EAAE;sBAAEA,SAAS,EAAE;oBAAiB;kBACxC;gBACF;cAAE;gBAAArO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAACL,EAAE,EAAE;gBAAEgB,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAE3C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAEDrC,OAAA,CAACnD,YAAY;QACX2T,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAE;QACrBhE,IAAI,EAAE,CAAE;QACRiE,eAAe,EAAE,IAAK;QACtB1P,KAAK,EAAE;UAAEL,MAAM,EAAE,MAAM;UAAEe,KAAK,EAAE,MAAM;UAAEH,YAAY,EAAE;QAAE,CAAE;QAC1DoP,WAAW,EAAE,KAAM;QAAAhP,QAAA,gBAEnB1B,OAAA,CAAClD,SAAS;UACR6T,WAAW,EAAC,yFAAyF;UACrGC,GAAG,EAAC;QAAoD;UAAA1O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAGFrC,OAAA,CAAC9C,UAAU;UAAAwE,QAAA,EACRuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7C5G,GAAG,CAAC,CAAC2G,KAAK,EAAEtC,KAAK,kBAChB/H,OAAA,CAACjD,YAAY;YAEXyT,MAAM,EAAE,CAACnG,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACK,SAAS,CAAE;YAC1CmG,MAAM,EAAE,CAAE;YACVC,WAAW,EAAE;cACX7N,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;cAC/BiO,SAAS,EAAE1Q,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;cACnCkO,WAAW,EAAE,GAAG;cAChBC,MAAM,EAAE;YACV,CAAE;YAAAvP,QAAA,eAEF1B,OAAA,CAAChD,KAAK;cACJuL,QAAQ,EAAE,GAAI;cACd3B,QAAQ,EAAE,GAAI;cACdiB,SAAS,EAAE,GAAI;cACfqJ,OAAO,EAAE,IAAK;cACdC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;cACzB1D,SAAS,EAAC,kBAAkB;cAAA/L,QAAA,eAE5B1B,OAAA,CAAC+M,aAAa;gBAAC1C,KAAK,EAAEA;cAAM;gBAAAnI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAnBH,QAAQ0F,KAAK,EAAE;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBR,CACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGbrC,OAAA,CAAC9C,UAAU;UAAAwE,QAAA,EACRuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7C5G,GAAG,CAAC,CAAC2G,KAAK,EAAEtC,KAAK,kBAChB/H,OAAA,CAACjD,YAAY;YAEXyT,MAAM,EAAE,CAACnG,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACK,SAAS,CAAE;YAC1CmG,MAAM,EAAE,CAAE;YACVC,WAAW,EAAE;cACX7N,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI;cACjCiO,SAAS,EAAE1Q,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI;cACrCkO,WAAW,EAAE,GAAG;cAChBC,MAAM,EAAE;YACV,CAAE;YAAAvP,QAAA,eAEF1B,OAAA,CAAChD,KAAK;cACJuL,QAAQ,EAAE,GAAI;cACd3B,QAAQ,EAAE,GAAI;cACdiB,SAAS,EAAE,GAAI;cACfqJ,OAAO,EAAE,IAAK;cACdC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;cACzB1D,SAAS,EAAC,kBAAkB;cAAA/L,QAAA,eAE5B1B,OAAA,CAAC+M,aAAa;gBAAC1C,KAAK,EAAEA;cAAM;gBAAAnI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAnBH,OAAO0F,KAAK,EAAE;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBP,CACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,EAGZgM,gBAAgB,IAAIE,YAAY,iBAC/BvO,OAAA,CAAC9C,UAAU;UAAAwE,QAAA,eAET1B,OAAA,CAAC3C,MAAM;YACL2D,QAAQ,EAAE,CAACqN,gBAAgB,CAACtI,GAAG,EAAEsI,gBAAgB,CAACpI,GAAG,CAAE;YACvD2F,IAAI,EAAEtO,CAAC,CAAC8T,OAAO,CAAC;cACd3D,SAAS,EAAE,iBAAiB;cAC5B4D,IAAI,EAAE;AACxB;AACA;AACA,0CAA0ChR,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;AACpE;AACA;AACA;AACA,8CAA8CzC,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;AACxE;AACA,mBAAmB;cACDwO,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;cAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;YACnB,CAAC,CAAE;YAAA7P,QAAA,eAEH1B,OAAA,CAAChD,KAAK;cACJuL,QAAQ,EAAE,GAAI;cACd3B,QAAQ,EAAE,GAAI;cACdiB,SAAS,EAAE,GAAI;cACfqJ,OAAO,EAAE,IAAK;cACdC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;cACzB1D,SAAS,EAAC,kBAAkB;cAAA/L,QAAA,eAE5B1B,OAAA,CAACzC,GAAG;gBAACoE,EAAE,EAAE;kBAAEkG,SAAS,EAAE,OAAO;kBAAE2J,SAAS,EAAE;gBAAO,CAAE;gBAAA9P,QAAA,gBACjD1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,WAAW;kBAACC,UAAU,EAAC,MAAM;kBAAC6J,YAAY;kBAAApK,QAAA,EAC3D2M,gBAAgB,CAAChI,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAC;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACbrC,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACiB,KAAK,EAAC,gBAAgB;kBAACwO,SAAS;kBAAA/P,QAAA,EACzD2M,gBAAgB,CAAChI;gBAAI;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACbrC,OAAA,CAACpC,OAAO;kBAAC+D,EAAE,EAAE;oBAAEe,EAAE,EAAE;kBAAI;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BrC,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACyP,SAAS;kBAAA/P,QAAA,GAAC,0DACoB,EAAC,CAACoJ,IAAI,CAACuE,IAAI,CACjEvE,IAAI,CAACwE,GAAG,CAACf,YAAY,CAAC9D,QAAQ,GAAG4D,gBAAgB,CAACtI,GAAG,EAAE,CAAC,CAAC,GACzD+E,IAAI,CAACwE,GAAG,CAACf,YAAY,CAAC7D,SAAS,GAAG2D,gBAAgB,CAACpI,GAAG,EAAE,CAAC,CAC3D,CAAC,GAAG,GAAG,EAAEsG,OAAO,CAAC,CAAC,CAAC,EAAC,YACtB;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrC,OAAA,CAAC+M,aAAa;kBAAC1C,KAAK,EAAEkE,YAAa;kBAACvB,sBAAsB,EAAE;gBAAK;kBAAA9K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIC,CACb,eAGDrC,OAAA,CAACgK,YAAY;UAACC,OAAO,EAAEA;QAAQ;UAAA/H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGlCrC,OAAA,CAACuD,cAAc;UAACC,gBAAgB,EAAE+L;QAAqB;UAAArN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1DrC,OAAA,CAACgJ,WAAW;UAAA9G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGfrC,OAAA,CAACG,SAAS;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGfrC,OAAA,CAACnB,QAAQ;QACPgQ,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxB6C,gBAAgB,EAAE,IAAK;QACvB/J,OAAO,EAAE6H,uBAAwB;QACjCmC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAnQ,QAAA,eAE3D1B,OAAA,CAACrB,KAAK;UACJgJ,OAAO,EAAE6H,uBAAwB;UACjC9H,QAAQ,EAAEiH,YAAY,CAACjH,QAAS;UAChC/F,EAAE,EAAE;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAErBiN,YAAY,CAACG;QAAO;UAAA5M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAGLgM,gBAAgB,IAAIE,YAAY,IAAIE,gBAAgB,iBACnDzO,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAEZ,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACxB1B,OAAA,CAACtC,KAAK;QACJmJ,SAAS,EAAE,CAAE;QACblF,EAAE,EAAE;UACFmF,CAAC,EAAE,CAAC;UACJxF,YAAY,EAAE,CAAC;UACf2O,UAAU,EAAE,2BAA2B5P,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC0I,KAAK,UAAU7P,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC0I,KAAK,UAAU;UACjHlP,QAAQ,EAAE,UAAU;UACpBQ,QAAQ,EAAE;QACZ,CAAE;QAAAE,QAAA,gBAEF1B,OAAA,CAACzC,GAAG;UACFoE,EAAE,EAAE;YACFX,QAAQ,EAAE,UAAU;YACpB0F,GAAG,EAAE,CAAC;YACNxF,KAAK,EAAE,CAAC;YACRO,KAAK,EAAE,OAAO;YACdf,MAAM,EAAE,OAAO;YACfuP,UAAU,EAAE,sEAAsE;YAClF3O,YAAY,EAAE,YAAY;YAC1BH,MAAM,EAAE;UACV;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFrC,OAAA,CAACzC,GAAG;UAACoE,EAAE,EAAE;YAAEX,QAAQ,EAAE,UAAU;YAAEG,MAAM,EAAE;UAAE,CAAE;UAAAO,QAAA,gBAC3C1B,OAAA,CAACxC,UAAU;YACTwE,OAAO,EAAC,IAAI;YACZmO,SAAS,EAAC,IAAI;YACdrE,YAAY;YACZnK,EAAE,EAAE;cACFM,UAAU,EAAE,GAAG;cACfgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;cACjClB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpB6H,GAAG,EAAE;YACP,CAAE;YAAAjI,QAAA,gBAEF1B,OAAA,CAACT,cAAc;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,KAAC,EAACgM,gBAAgB,CAAChI,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAAA;YAAA1G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEbrC,OAAA,CAACxC,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACyP,SAAS;YAACxO,KAAK,EAAC,gBAAgB;YAAAvB,QAAA,EACzD2M,gBAAgB,CAAChI;UAAI;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAEbrC,OAAA,CAACpC,OAAO;YAAC+D,EAAE,EAAE;cAAEe,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1BrC,OAAA,CAACjC,IAAI;YAAC4P,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAlM,QAAA,gBACzB1B,OAAA,CAACjC,IAAI;cAAC8P,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAtO,QAAA,gBACvB1B,OAAA,CAACxC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAC8J,YAAY;gBAAC7J,UAAU,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAE1D;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbrC,OAAA,CAACzC,GAAG;gBACFoE,EAAE,EAAE;kBACFmF,CAAC,EAAE,CAAC;kBACJxF,YAAY,EAAE,CAAC;kBACfF,eAAe,EAAEmN,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GAChD,wBAAwB,GACxB,wBAAwB;kBAC5B2D,UAAU,EAAE,aAAaM,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GACxDjK,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI,GACxBzC,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAI,EAAE;kBAChCf,EAAE,EAAE;gBACN,CAAE;gBAAAL,QAAA,gBAEF1B,OAAA,CAACxC,UAAU;kBACTwE,OAAO,EAAC,WAAW;kBACnBC,UAAU,EAAC,MAAM;kBACjBN,EAAE,EAAE;oBACFsB,KAAK,EAAEsL,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GACtCjK,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI,GACxBzC,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF;kBAC5B,CAAE;kBAAApB,QAAA,EAED6M,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GAChC,sBAAsB,GACtB;gBAAqB;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEbrC,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,EAAE,EAAE;oBAAEgB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,EACvC6M,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GAChC,mIAAmI,GACnI;gBAA0I;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENrC,OAAA,CAACxC,UAAU;gBAACwE,OAAO,EAAC,WAAW;gBAAC8J,YAAY;gBAAApK,QAAA,EAAC;cAE7C;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbrC,OAAA,CAACjC,IAAI;gBAAC4P,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAACjM,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACxC1B,OAAA,CAACjC,IAAI;kBAAC8P,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAApM,QAAA,eACf1B,OAAA,CAACtC,KAAK;oBACJmJ,SAAS,EAAE,CAAE;oBACblF,EAAE,EAAE;sBACFmF,CAAC,EAAE,GAAG;sBACNuJ,SAAS,EAAE,QAAQ;sBACnByB,SAAS,EAAE,aAAazR,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ,IAAI;oBACjD,CAAE;oBAAApB,QAAA,gBAEF1B,OAAA,CAAChB,gBAAgB;sBAAC2C,EAAE,EAAE;wBAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACM,IAAI,CAACJ,IAAI;wBAAEf,EAAE,EAAE;sBAAI;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrErC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,OAAO;sBAACiB,KAAK,EAAC,gBAAgB;sBAAAvB,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,QAAQ;sBAAAP,QAAA,GACzC6M,YAAY,CAAC,eAAe,CAAC,EAAC,KACjC;oBAAA;sBAAArM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;kBAAC8P,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAApM,QAAA,eACf1B,OAAA,CAACtC,KAAK;oBACJmJ,SAAS,EAAE,CAAE;oBACblF,EAAE,EAAE;sBACFmF,CAAC,EAAE,GAAG;sBACNuJ,SAAS,EAAE,QAAQ;sBACnByB,SAAS,EAAE,aAAazR,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL,IAAI;oBACpD,CAAE;oBAAApB,QAAA,gBAEF1B,OAAA,CAACjB,WAAW;sBAAC4C,EAAE,EAAE;wBAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACO,OAAO,CAACL,IAAI;wBAAEf,EAAE,EAAE;sBAAI;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnErC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,OAAO;sBAACiB,KAAK,EAAC,gBAAgB;sBAAAvB,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,QAAQ;sBAAAP,QAAA,GACzC6M,YAAY,CAAC,eAAe,CAAC,EAAC,IACjC;oBAAA;sBAAArM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;kBAAC8P,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAApM,QAAA,eACf1B,OAAA,CAACtC,KAAK;oBACJmJ,SAAS,EAAE,CAAE;oBACblF,EAAE,EAAE;sBACFmF,CAAC,EAAE,GAAG;sBACNuJ,SAAS,EAAE,QAAQ;sBACnByB,SAAS,EAAE,aAAazR,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;oBAClD,CAAE;oBAAApB,QAAA,gBAEF1B,OAAA,CAAClB,aAAa;sBAAC6C,EAAE,EAAE;wBAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAI;wBAAEf,EAAE,EAAE;sBAAI;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnErC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,OAAO;sBAACiB,KAAK,EAAC,gBAAgB;sBAAAvB,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,QAAQ;sBAAAP,QAAA,GACzC6M,YAAY,CAAC,iBAAiB,CAAC,EAAC,IACnC;oBAAA;sBAAArM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;kBAAC8P,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAApM,QAAA,eACf1B,OAAA,CAACtC,KAAK;oBACJmJ,SAAS,EAAE,CAAE;oBACblF,EAAE,EAAE;sBACFmF,CAAC,EAAE,GAAG;sBACNuJ,SAAS,EAAE,QAAQ;sBACnByB,SAAS,EAAE,aAAazR,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;oBACpD,CAAE;oBAAApB,QAAA,gBAEF1B,OAAA,CAACT,cAAc;sBAACoC,EAAE,EAAE;wBAAEsB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI;wBAAEf,EAAE,EAAE;sBAAI;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtErC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,OAAO;sBAACiB,KAAK,EAAC,gBAAgB;sBAAAvB,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;sBAACwE,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,QAAQ;sBAAAP,QAAA,GACzC,CAACoJ,IAAI,CAACuE,IAAI,CACTvE,IAAI,CAACwE,GAAG,CAACf,YAAY,CAAC9D,QAAQ,GAAG4D,gBAAgB,CAACtI,GAAG,EAAE,CAAC,CAAC,GACzD+E,IAAI,CAACwE,GAAG,CAACf,YAAY,CAAC7D,SAAS,GAAG2D,gBAAgB,CAACpI,GAAG,EAAE,CAAC,CAC3D,CAAC,GAAG,GAAG,EAAEsG,OAAO,CAAC,CAAC,CAAC,EAAC,KACtB;oBAAA;sBAAArK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEPrC,OAAA,CAACjC,IAAI;cAAC8P,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAtO,QAAA,gBACvB1B,OAAA,CAACxC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAC8J,YAAY;gBAAC7J,UAAU,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAE1D;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbrC,OAAA,CAACqL,sBAAsB;gBACrBC,SAAS,EAAEiD,YAAY,CAACjE,gBAAgB,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAChEiB,WAAW,EAAE,CACX,IAAIgD,YAAY,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;kBACzC1C,MAAM,EAAE,gBAAgB;kBACxBnG,KAAK,EAAE,GAAG6I,YAAY,CAAC,eAAe,CAAC,KAAK;kBAC5C7G,QAAQ,EAAE;gBACZ,CAAC,CAAC,GAAG,EAAE,CAAC,EACR,IAAI6G,YAAY,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;kBACzC1C,MAAM,EAAE,eAAe;kBACvBnG,KAAK,EAAE,GAAG6I,YAAY,CAAC,eAAe,CAAC,IAAI;kBAC3C7G,QAAQ,EAAE6G,YAAY,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG;gBAC1D,CAAC,CAAC,GAAG,EAAE,CAAC,EACR,IAAIA,YAAY,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC;kBACzC1C,MAAM,EAAE,kBAAkB;kBAC1BnG,KAAK,EAAE,GAAG6I,YAAY,CAAC,iBAAiB,CAAC,IAAI;kBAC7C7G,QAAQ,EAAE6G,YAAY,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG;gBAC3D,CAAC,CAAC,GAAG,EAAE,CAAC;cACR;gBAAArM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEFrC,OAAA,CAACzC,GAAG;gBAACoE,EAAE,EAAE;kBAAEgB,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,eACjB1B,OAAA,CAAC/B,MAAM;kBACL+D,OAAO,EAAC,UAAU;kBAClBiB,KAAK,EAAC,SAAS;kBACfV,OAAO,EAAEA,CAAA,KAAMmM,mBAAmB,CAAC,KAAK,CAAE;kBAC1CqD,SAAS,eAAE/R,OAAA,CAACR,SAAS;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBV,EAAE,EAAE;oBAAEgB,EAAE,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,EACf;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAEDrC,OAAA,CAACzC,GAAG;MAACoE,EAAE,EAAE;QAAEgB,EAAE,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBACjB1B,OAAA,CAACxC,UAAU;QAACwE,OAAO,EAAC,IAAI;QAAC8J,YAAY;QAAC7J,UAAU,EAAC,QAAQ;QAAAP,QAAA,EAAC;MAE1D;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbrC,OAAA,CAACjC,IAAI;QAAC4P,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlM,QAAA,gBACzB1B,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACkC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eACvB1B,OAAA,CAACtC,KAAK;YAACmJ,SAAS,EAAE,CAAE;YAAClF,EAAE,EAAE;cAAEmF,CAAC,EAAE,CAAC;cAAExF,YAAY,EAAE;YAAE,CAAE;YAAAI,QAAA,gBACjD1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACC,KAAK,CAACC,IAAK;cAACgJ,YAAY;cAAApK,QAAA,GAAC,mBAC7E,EAAC+N,aAAa,EAAC,GAClC;YAAA;cAAAvN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAACyP,SAAS;cAAA/P,QAAA,EAAC;YAItC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAAAN,QAAA,eACzB1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbrC,OAAA;cAAIe,KAAK,EAAE;gBAAEiR,WAAW,EAAE,MAAM;gBAAE1B,MAAM,EAAE;cAAQ,CAAE;cAAA5O,QAAA,gBAClD1B,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,oBACR,EAChB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IACzDoF,aAAa,IAAI,CAAC,CAAC,EAAElD,OAAO,CAAC,CAAC,CAAC,EACjC,KACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLrC,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,qBACP,EACjB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IACzDoF,aAAa,IAAI,CAAC,CAAC,EAAElD,OAAO,CAAC,CAAC,CAAC,EACjC,IACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLrC,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,uBACL,EACnB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAC3DoF,aAAa,IAAI,CAAC,CAAC,EAAElD,OAAO,CAAC,CAAC,CAAC,EACjC,IACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPrC,OAAA,CAACjC,IAAI;UAAC8P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACkC,EAAE,EAAE,CAAE;UAAAtO,QAAA,eACvB1B,OAAA,CAACtC,KAAK;YAACmJ,SAAS,EAAE,CAAE;YAAClF,EAAE,EAAE;cAAEmF,CAAC,EAAE,CAAC;cAAExF,YAAY,EAAE;YAAE,CAAE;YAAAI,QAAA,gBACjD1B,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAACgB,KAAK,EAAE5C,KAAK,CAACuC,OAAO,CAACI,OAAO,CAACF,IAAK;cAACgJ,YAAY;cAAApK,QAAA,GAAC,kBAChF,EAACgO,YAAY,EAAC,GAChC;YAAA;cAAAxN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAACyP,SAAS;cAAA/P,QAAA,EAAC;YAGtC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrC,OAAA,CAACxC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAAAN,QAAA,eACzB1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbrC,OAAA;cAAIe,KAAK,EAAE;gBAAEiR,WAAW,EAAE,MAAM;gBAAE1B,MAAM,EAAE;cAAQ,CAAE;cAAA5O,QAAA,gBAClD1B,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,oBACR,EAChB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IACzDqF,YAAY,IAAI,CAAC,CAAC,EAAEnD,OAAO,CAAC,CAAC,CAAC,EAChC,KACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLrC,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,qBACP,EACjB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IACzDqF,YAAY,IAAI,CAAC,CAAC,EAAEnD,OAAO,CAAC,CAAC,CAAC,EAChC,IACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLrC,OAAA;gBAAA0B,QAAA,eACE1B,OAAA,CAACxC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAAAN,QAAA,GAAC,uBACL,EACnB,CAACuI,OAAO,CACLG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,gBAAgB,KAAK,CAAC,CAAC,CAC7CsF,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAKwF,GAAG,GAAGxF,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAC3DqF,YAAY,IAAI,CAAC,CAAC,EAAEnD,OAAO,CAAC,CAAC,CAAC,EAChC,IACH;gBAAA;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC+L,GAAA,CAvrBID,QAAQ;EAAA,QACEnQ,QAAQ;AAAA;AAAAiU,GAAA,GADlB9D,QAAQ;AAyrBd,eAAeA,QAAQ;AAAC,IAAA7K,EAAA,EAAAyF,GAAA,EAAAgB,GAAA,EAAAqB,GAAA,EAAAY,GAAA,EAAAkC,GAAA,EAAA+D,GAAA;AAAAC,YAAA,CAAA5O,EAAA;AAAA4O,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}