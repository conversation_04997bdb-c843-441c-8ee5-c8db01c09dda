{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport clamp from '@mui/utils/clamp';\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(\"MUI: The value provided \".concat(value, \" is out of range [\").concat(min, \", \").concat(max, \"].\"));\n    }\n  }\n  return clamp(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nexport function hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(\".{1,\".concat(color.length >= 6 ? 2 : 1, \"}\"), 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  return colors ? \"rgb\".concat(colors.length === 4 ? 'a' : '', \"(\").concat(colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', '), \")\") : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? \"0\".concat(hex) : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nexport function decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (['rgb', 'rgba', 'hsl', 'hsla', 'color'].indexOf(type) === -1) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: Unsupported `\".concat(color, \"` color.\\nThe following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().\") : _formatMuiErrorMessage(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].indexOf(colorSpace) === -1) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: unsupported `\".concat(colorSpace, \"` color space.\\nThe following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.\") : _formatMuiErrorMessage(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nexport const colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.indexOf('hsl') !== -1 && idx !== 0 ? \"\".concat(val, \"%\") : val).join(' ');\n};\nexport const private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexport function recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.indexOf('rgb') !== -1) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.indexOf('hsl') !== -1) {\n    values[1] = \"\".concat(values[1], \"%\");\n    values[2] = \"\".concat(values[2], \"%\");\n  }\n  if (type.indexOf('color') !== -1) {\n    values = \"\".concat(colorSpace, \" \").concat(values.join(' '));\n  } else {\n    values = \"\".concat(values.join(', '));\n  }\n  return \"\".concat(type, \"(\").concat(values, \")\");\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nexport function rgbToHex(color) {\n  // Idempotent\n  if (color.indexOf('#') === 0) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return \"#\".concat(values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join(''));\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nexport function hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = function (n) {\n    let k = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (n + h / 30) % 12;\n    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  };\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nexport function getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nexport function getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = \"/\".concat(value);\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nexport function private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.indexOf('rgb') !== -1 || color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.indexOf('rgb') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function emphasize(color) {\n  let coefficient = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.15;\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nexport function private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nexport function blend(background, overlay, opacity) {\n  let gamma = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1.0;\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "clamp", "clampWrapper", "value", "min", "arguments", "length", "undefined", "max", "process", "env", "NODE_ENV", "console", "error", "concat", "hexToRgb", "color", "slice", "re", "RegExp", "colors", "match", "map", "n", "index", "parseInt", "Math", "round", "join", "intToHex", "int", "hex", "toString", "decomposeColor", "type", "char<PERSON>t", "marker", "indexOf", "substring", "Error", "values", "colorSpace", "split", "shift", "parseFloat", "colorChannel", "decomposedColor", "val", "idx", "private_safeColorChannel", "warning", "warn", "recomposeColor", "i", "rgbToHex", "hslToRgb", "h", "s", "l", "a", "f", "k", "rgb", "push", "getLuminance", "Number", "toFixed", "getContrastRatio", "foreground", "background", "lumA", "lumB", "alpha", "private_safeAlpha", "darken", "coefficient", "private_safeDarken", "lighten", "private_safeLighten", "emphasize", "private_safeEmphasize", "blend", "overlay", "opacity", "gamma", "blendChannel", "b", "o", "backgroundColor", "overlayColor"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/system/esm/colorManipulator.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport clamp from '@mui/utils/clamp';\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value, min = 0, max = 1) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(`MUI: The value provided ${value} is out of range [${min}, ${max}].`);\n    }\n  }\n  return clamp(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nexport function hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(`.{1,${color.length >= 6 ? 2 : 1}}`, 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  return colors ? `rgb${colors.length === 4 ? 'a' : ''}(${colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', ')})` : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? `0${hex}` : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nexport function decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (['rgb', 'rgba', 'hsl', 'hsla', 'color'].indexOf(type) === -1) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported \\`${color}\\` color.\nThe following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().` : _formatMuiErrorMessage(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].indexOf(colorSpace) === -1) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: unsupported \\`${colorSpace}\\` color space.\nThe following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.` : _formatMuiErrorMessage(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nexport const colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.indexOf('hsl') !== -1 && idx !== 0 ? `${val}%` : val).join(' ');\n};\nexport const private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexport function recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.indexOf('rgb') !== -1) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.indexOf('hsl') !== -1) {\n    values[1] = `${values[1]}%`;\n    values[2] = `${values[2]}%`;\n  }\n  if (type.indexOf('color') !== -1) {\n    values = `${colorSpace} ${values.join(' ')}`;\n  } else {\n    values = `${values.join(', ')}`;\n  }\n  return `${type}(${values})`;\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nexport function rgbToHex(color) {\n  // Idempotent\n  if (color.indexOf('#') === 0) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return `#${values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join('')}`;\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nexport function hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nexport function getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nexport function getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = `/${value}`;\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nexport function private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.indexOf('rgb') !== -1 || color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.indexOf('hsl') !== -1) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.indexOf('rgb') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.indexOf('color') !== -1) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function emphasize(color, coefficient = 0.15) {\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nexport function private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nexport function blend(background, overlay, opacity, gamma = 1.0) {\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE;AACA,OAAOC,KAAK,MAAM,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAoB;EAAA,IAAlBC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,GAAG,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC3C,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIR,KAAK,GAAGC,GAAG,IAAID,KAAK,GAAGK,GAAG,EAAE;MAC9BI,OAAO,CAACC,KAAK,4BAAAC,MAAA,CAA4BX,KAAK,wBAAAW,MAAA,CAAqBV,GAAG,QAAAU,MAAA,CAAKN,GAAG,OAAI,CAAC;IACrF;EACF;EACA,OAAOP,KAAK,CAACE,KAAK,EAAEC,GAAG,EAAEI,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,QAAQA,CAACC,KAAK,EAAE;EAC9BA,KAAK,GAAGA,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;EACtB,MAAMC,EAAE,GAAG,IAAIC,MAAM,QAAAL,MAAA,CAAQE,KAAK,CAACV,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAK,GAAG,CAAC;EAC/D,IAAIc,MAAM,GAAGJ,KAAK,CAACK,KAAK,CAACH,EAAE,CAAC;EAC5B,IAAIE,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACd,MAAM,KAAK,CAAC,EAAE;IACpCc,MAAM,GAAGA,MAAM,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC;EACjC;EACA,OAAOH,MAAM,SAAAN,MAAA,CAASM,MAAM,CAACd,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAAQ,MAAA,CAAIM,MAAM,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IAC/E,OAAOA,KAAK,GAAG,CAAC,GAAGC,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACF,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI;EACtF,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,SAAM,EAAE;AACvB;AACA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,GAAG,GAAGD,GAAG,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC5B,OAAOD,GAAG,CAACzB,MAAM,KAAK,CAAC,OAAAQ,MAAA,CAAOiB,GAAG,IAAKA,GAAG;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACjB,KAAK,EAAE;EACpC;EACA,IAAIA,KAAK,CAACkB,IAAI,EAAE;IACd,OAAOlB,KAAK;EACd;EACA,IAAIA,KAAK,CAACmB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,OAAOF,cAAc,CAAClB,QAAQ,CAACC,KAAK,CAAC,CAAC;EACxC;EACA,MAAMoB,MAAM,GAAGpB,KAAK,CAACqB,OAAO,CAAC,GAAG,CAAC;EACjC,MAAMH,IAAI,GAAGlB,KAAK,CAACsB,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC;EACvC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACC,OAAO,CAACH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE,MAAM,IAAIK,KAAK,CAAC9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,wBAAAG,MAAA,CAAyBE,KAAK,4GACOhB,sBAAsB,CAAC,CAAC,EAAEgB,KAAK,CAAC,CAAC;EAC7H;EACA,IAAIwB,MAAM,GAAGxB,KAAK,CAACsB,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEpB,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;EAC1D,IAAImC,UAAU;EACd,IAAIP,IAAI,KAAK,OAAO,EAAE;IACpBM,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC1BD,UAAU,GAAGD,MAAM,CAACG,KAAK,CAAC,CAAC;IAC3B,IAAIH,MAAM,CAAClC,MAAM,KAAK,CAAC,IAAIkC,MAAM,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACtDK,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACvB,KAAK,CAAC,CAAC,CAAC;IAChC;IACA,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,CAACoB,OAAO,CAACI,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5F,MAAM,IAAIF,KAAK,CAAC9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,wBAAAG,MAAA,CAAyB2B,UAAU,oHACEzC,sBAAsB,CAAC,EAAE,EAAEyC,UAAU,CAAC,CAAC;IACnI;EACF,CAAC,MAAM;IACLD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAC5B;EACAF,MAAM,GAAGA,MAAM,CAAClB,GAAG,CAACnB,KAAK,IAAIyC,UAAU,CAACzC,KAAK,CAAC,CAAC;EAC/C,OAAO;IACL+B,IAAI;IACJM,MAAM;IACNC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,YAAY,GAAG7B,KAAK,IAAI;EACnC,MAAM8B,eAAe,GAAGb,cAAc,CAACjB,KAAK,CAAC;EAC7C,OAAO8B,eAAe,CAACN,MAAM,CAACvB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACK,GAAG,CAAC,CAACyB,GAAG,EAAEC,GAAG,KAAKF,eAAe,CAACZ,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAIW,GAAG,KAAK,CAAC,MAAAlC,MAAA,CAAMiC,GAAG,SAAMA,GAAG,CAAC,CAACnB,IAAI,CAAC,GAAG,CAAC;AAClJ,CAAC;AACD,OAAO,MAAMqB,wBAAwB,GAAGA,CAACjC,KAAK,EAAEkC,OAAO,KAAK;EAC1D,IAAI;IACF,OAAOL,YAAY,CAAC7B,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd,IAAIqC,OAAO,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACuC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOlC,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoC,cAAcA,CAACpC,KAAK,EAAE;EACpC,MAAM;IACJkB,IAAI;IACJO;EACF,CAAC,GAAGzB,KAAK;EACT,IAAI;IACFwB;EACF,CAAC,GAAGxB,KAAK;EACT,IAAIkB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9B;IACAG,MAAM,GAAGA,MAAM,CAAClB,GAAG,CAAC,CAACC,CAAC,EAAE8B,CAAC,KAAKA,CAAC,GAAG,CAAC,GAAG5B,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC,GAAGA,CAAC,CAAC;EAC5D,CAAC,MAAM,IAAIW,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACrCG,MAAM,CAAC,CAAC,CAAC,MAAA1B,MAAA,CAAM0B,MAAM,CAAC,CAAC,CAAC,MAAG;IAC3BA,MAAM,CAAC,CAAC,CAAC,MAAA1B,MAAA,CAAM0B,MAAM,CAAC,CAAC,CAAC,MAAG;EAC7B;EACA,IAAIN,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAChCG,MAAM,MAAA1B,MAAA,CAAM2B,UAAU,OAAA3B,MAAA,CAAI0B,MAAM,CAACZ,IAAI,CAAC,GAAG,CAAC,CAAE;EAC9C,CAAC,MAAM;IACLY,MAAM,MAAA1B,MAAA,CAAM0B,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAE;EACjC;EACA,UAAAd,MAAA,CAAUoB,IAAI,OAAApB,MAAA,CAAI0B,MAAM;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,QAAQA,CAACtC,KAAK,EAAE;EAC9B;EACA,IAAIA,KAAK,CAACqB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAOrB,KAAK;EACd;EACA,MAAM;IACJwB;EACF,CAAC,GAAGP,cAAc,CAACjB,KAAK,CAAC;EACzB,WAAAF,MAAA,CAAW0B,MAAM,CAAClB,GAAG,CAAC,CAACC,CAAC,EAAE8B,CAAC,KAAKxB,QAAQ,CAACwB,CAAC,KAAK,CAAC,GAAG3B,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGJ,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2B,QAAQA,CAACvC,KAAK,EAAE;EAC9BA,KAAK,GAAGiB,cAAc,CAACjB,KAAK,CAAC;EAC7B,MAAM;IACJwB;EACF,CAAC,GAAGxB,KAAK;EACT,MAAMwC,CAAC,GAAGhB,MAAM,CAAC,CAAC,CAAC;EACnB,MAAMiB,CAAC,GAAGjB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMkB,CAAC,GAAGlB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMmB,CAAC,GAAGF,CAAC,GAAG/B,IAAI,CAACtB,GAAG,CAACsD,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAME,CAAC,GAAG,SAAAA,CAACrC,CAAC;IAAA,IAAEsC,CAAC,GAAAxD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAACkB,CAAC,GAAGiC,CAAC,GAAG,EAAE,IAAI,EAAE;IAAA,OAAKE,CAAC,GAAGC,CAAC,GAAGjC,IAAI,CAAClB,GAAG,CAACkB,IAAI,CAACtB,GAAG,CAACyD,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA;EACvF,IAAI3B,IAAI,GAAG,KAAK;EAChB,MAAM4B,GAAG,GAAG,CAACpC,IAAI,CAACC,KAAK,CAACiC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAElC,IAAI,CAACC,KAAK,CAACiC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAElC,IAAI,CAACC,KAAK,CAACiC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EACpF,IAAI5C,KAAK,CAACkB,IAAI,KAAK,MAAM,EAAE;IACzBA,IAAI,IAAI,GAAG;IACX4B,GAAG,CAACC,IAAI,CAACvB,MAAM,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,OAAOY,cAAc,CAAC;IACpBlB,IAAI;IACJM,MAAM,EAAEsB;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAAChD,KAAK,EAAE;EAClCA,KAAK,GAAGiB,cAAc,CAACjB,KAAK,CAAC;EAC7B,IAAI8C,GAAG,GAAG9C,KAAK,CAACkB,IAAI,KAAK,KAAK,IAAIlB,KAAK,CAACkB,IAAI,KAAK,MAAM,GAAGD,cAAc,CAACsB,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAACwB,MAAM,GAAGxB,KAAK,CAACwB,MAAM;EAC/GsB,GAAG,GAAGA,GAAG,CAACxC,GAAG,CAACyB,GAAG,IAAI;IACnB,IAAI/B,KAAK,CAACkB,IAAI,KAAK,OAAO,EAAE;MAC1Ba,GAAG,IAAI,GAAG,CAAC,CAAC;IACd;IACA,OAAOA,GAAG,IAAI,OAAO,GAAGA,GAAG,GAAG,KAAK,GAAG,CAAC,CAACA,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG;EACtE,CAAC,CAAC;;EAEF;EACA,OAAOkB,MAAM,CAAC,CAAC,MAAM,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACvD,MAAMC,IAAI,GAAGN,YAAY,CAACI,UAAU,CAAC;EACrC,MAAMG,IAAI,GAAGP,YAAY,CAACK,UAAU,CAAC;EACrC,OAAO,CAAC3C,IAAI,CAAClB,GAAG,CAAC8D,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,KAAK7C,IAAI,CAACtB,GAAG,CAACkE,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACxD,KAAK,EAAEb,KAAK,EAAE;EAClCa,KAAK,GAAGiB,cAAc,CAACjB,KAAK,CAAC;EAC7Bb,KAAK,GAAGD,YAAY,CAACC,KAAK,CAAC;EAC3B,IAAIa,KAAK,CAACkB,IAAI,KAAK,KAAK,IAAIlB,KAAK,CAACkB,IAAI,KAAK,KAAK,EAAE;IAChDlB,KAAK,CAACkB,IAAI,IAAI,GAAG;EACnB;EACA,IAAIlB,KAAK,CAACkB,IAAI,KAAK,OAAO,EAAE;IAC1BlB,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,OAAA1B,MAAA,CAAOX,KAAK,CAAE;EAC/B,CAAC,MAAM;IACLa,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,GAAGrC,KAAK;EACzB;EACA,OAAOiD,cAAc,CAACpC,KAAK,CAAC;AAC9B;AACA,OAAO,SAASyD,iBAAiBA,CAACzD,KAAK,EAAEb,KAAK,EAAE+C,OAAO,EAAE;EACvD,IAAI;IACF,OAAOsB,KAAK,CAACxD,KAAK,EAAEb,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOU,KAAK,EAAE;IACd,IAAIqC,OAAO,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACuC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOlC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0D,MAAMA,CAAC1D,KAAK,EAAE2D,WAAW,EAAE;EACzC3D,KAAK,GAAGiB,cAAc,CAACjB,KAAK,CAAC;EAC7B2D,WAAW,GAAGzE,YAAY,CAACyE,WAAW,CAAC;EACvC,IAAI3D,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCrB,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGmC,WAAW;EACpC,CAAC,MAAM,IAAI3D,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAIrB,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACjF,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BrC,KAAK,CAACwB,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,GAAGsB,WAAW;IACpC;EACF;EACA,OAAOvB,cAAc,CAACpC,KAAK,CAAC;AAC9B;AACA,OAAO,SAAS4D,kBAAkBA,CAAC5D,KAAK,EAAE2D,WAAW,EAAEzB,OAAO,EAAE;EAC9D,IAAI;IACF,OAAOwB,MAAM,CAAC1D,KAAK,EAAE2D,WAAW,CAAC;EACnC,CAAC,CAAC,OAAO9D,KAAK,EAAE;IACd,IAAIqC,OAAO,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACuC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOlC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6D,OAAOA,CAAC7D,KAAK,EAAE2D,WAAW,EAAE;EAC1C3D,KAAK,GAAGiB,cAAc,CAACjB,KAAK,CAAC;EAC7B2D,WAAW,GAAGzE,YAAY,CAACyE,WAAW,CAAC;EACvC,IAAI3D,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCrB,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAGxB,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,IAAImC,WAAW;EAC1D,CAAC,MAAM,IAAI3D,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC3C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BrC,KAAK,CAACwB,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,GAAG,GAAGrC,KAAK,CAACwB,MAAM,CAACa,CAAC,CAAC,IAAIsB,WAAW;IAC1D;EACF,CAAC,MAAM,IAAI3D,KAAK,CAACkB,IAAI,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BrC,KAAK,CAACwB,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGrC,KAAK,CAACwB,MAAM,CAACa,CAAC,CAAC,IAAIsB,WAAW;IACxD;EACF;EACA,OAAOvB,cAAc,CAACpC,KAAK,CAAC;AAC9B;AACA,OAAO,SAAS8D,mBAAmBA,CAAC9D,KAAK,EAAE2D,WAAW,EAAEzB,OAAO,EAAE;EAC/D,IAAI;IACF,OAAO2B,OAAO,CAAC7D,KAAK,EAAE2D,WAAW,CAAC;EACpC,CAAC,CAAC,OAAO9D,KAAK,EAAE;IACd,IAAIqC,OAAO,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACuC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOlC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+D,SAASA,CAAC/D,KAAK,EAAsB;EAAA,IAApB2D,WAAW,GAAAtE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACjD,OAAO2D,YAAY,CAAChD,KAAK,CAAC,GAAG,GAAG,GAAG0D,MAAM,CAAC1D,KAAK,EAAE2D,WAAW,CAAC,GAAGE,OAAO,CAAC7D,KAAK,EAAE2D,WAAW,CAAC;AAC7F;AACA,OAAO,SAASK,qBAAqBA,CAAChE,KAAK,EAAE2D,WAAW,EAAEzB,OAAO,EAAE;EACjE,IAAI;IACF,OAAO6B,SAAS,CAAC/D,KAAK,EAAE2D,WAAW,CAAC;EACtC,CAAC,CAAC,OAAO9D,KAAK,EAAE;IACd,IAAIqC,OAAO,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACuC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOlC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiE,KAAKA,CAACZ,UAAU,EAAEa,OAAO,EAAEC,OAAO,EAAe;EAAA,IAAbC,KAAK,GAAA/E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAC7D,MAAMgF,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK7D,IAAI,CAACC,KAAK,CAAC,CAAC2D,CAAC,KAAK,CAAC,GAAGF,KAAK,CAAC,IAAI,CAAC,GAAGD,OAAO,CAAC,GAAGI,CAAC,KAAK,CAAC,GAAGH,KAAK,CAAC,GAAGD,OAAO,KAAKC,KAAK,CAAC;EACnH,MAAMI,eAAe,GAAGvD,cAAc,CAACoC,UAAU,CAAC;EAClD,MAAMoB,YAAY,GAAGxD,cAAc,CAACiD,OAAO,CAAC;EAC5C,MAAMpB,GAAG,GAAG,CAACuB,YAAY,CAACG,eAAe,CAAChD,MAAM,CAAC,CAAC,CAAC,EAAEiD,YAAY,CAACjD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE6C,YAAY,CAACG,eAAe,CAAChD,MAAM,CAAC,CAAC,CAAC,EAAEiD,YAAY,CAACjD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE6C,YAAY,CAACG,eAAe,CAAChD,MAAM,CAAC,CAAC,CAAC,EAAEiD,YAAY,CAACjD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/M,OAAOY,cAAc,CAAC;IACpBlB,IAAI,EAAE,KAAK;IACXM,MAAM,EAAEsB;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}