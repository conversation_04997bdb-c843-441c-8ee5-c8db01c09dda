{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  this.bufferPosition = 0;\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function (status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\ninherits(XhrReceiver, EventEmitter);\nXhrReceiver.prototype._chunkHandler = function (status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n  for (var idx = -1;; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\nXhrReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\nXhrReceiver.prototype.abort = function () {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\nmodule.exports = XhrReceiver;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "XhrReceiver", "url", "AjaxObject", "call", "self", "bufferPosition", "xo", "on", "_<PERSON><PERSON><PERSON><PERSON>", "bind", "once", "status", "text", "reason", "emit", "_cleanup", "prototype", "idx", "buf", "slice", "indexOf", "msg", "removeAllListeners", "abort", "close", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/receiver/xhr.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\n\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n\n  this.bufferPosition = 0;\n\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function(status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\n\ninherits(XhrReceiver, EventEmitter);\n\nXhrReceiver.prototype._chunkHandler = function(status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n\n  for (var idx = -1; ; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\n\nXhrReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nXhrReceiver.prototype.abort = function() {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\n\nmodule.exports = XhrReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;AAGjD,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC;AACxD;AAEA,SAASM,WAAWA,CAACC,GAAG,EAAEC,UAAU,EAAE;EACpCN,KAAK,CAACK,GAAG,CAAC;EACVN,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EACvB,IAAIC,IAAI,GAAG,IAAI;EAEf,IAAI,CAACC,cAAc,GAAG,CAAC;EAEvB,IAAI,CAACC,EAAE,GAAG,IAAIJ,UAAU,CAAC,MAAM,EAAED,GAAG,EAAE,IAAI,CAAC;EAC3C,IAAI,CAACK,EAAE,CAACC,EAAE,CAAC,OAAO,EAAE,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClD,IAAI,CAACH,EAAE,CAACI,IAAI,CAAC,QAAQ,EAAE,UAASC,MAAM,EAAEC,IAAI,EAAE;IAC5ChB,KAAK,CAAC,QAAQ,EAAEe,MAAM,EAAEC,IAAI,CAAC;IAC7BR,IAAI,CAACI,aAAa,CAACG,MAAM,EAAEC,IAAI,CAAC;IAChCR,IAAI,CAACE,EAAE,GAAG,IAAI;IACd,IAAIO,MAAM,GAAGF,MAAM,KAAK,GAAG,GAAG,SAAS,GAAG,WAAW;IACrDf,KAAK,CAAC,OAAO,EAAEiB,MAAM,CAAC;IACtBT,IAAI,CAACU,IAAI,CAAC,OAAO,EAAE,IAAI,EAAED,MAAM,CAAC;IAChCT,IAAI,CAACW,QAAQ,CAAC,CAAC;EACjB,CAAC,CAAC;AACJ;AAEAtB,QAAQ,CAACO,WAAW,EAAEL,YAAY,CAAC;AAEnCK,WAAW,CAACgB,SAAS,CAACR,aAAa,GAAG,UAASG,MAAM,EAAEC,IAAI,EAAE;EAC3DhB,KAAK,CAAC,eAAe,EAAEe,MAAM,CAAC;EAC9B,IAAIA,MAAM,KAAK,GAAG,IAAI,CAACC,IAAI,EAAE;IAC3B;EACF;EAEA,KAAK,IAAIK,GAAG,GAAG,CAAC,CAAC,GAAI,IAAI,CAACZ,cAAc,IAAIY,GAAG,GAAG,CAAC,EAAE;IACnD,IAAIC,GAAG,GAAGN,IAAI,CAACO,KAAK,CAAC,IAAI,CAACd,cAAc,CAAC;IACzCY,GAAG,GAAGC,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC;IACvB,IAAIH,GAAG,KAAK,CAAC,CAAC,EAAE;MACd;IACF;IACA,IAAII,GAAG,GAAGH,GAAG,CAACC,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC;IAC3B,IAAII,GAAG,EAAE;MACPzB,KAAK,CAAC,SAAS,EAAEyB,GAAG,CAAC;MACrB,IAAI,CAACP,IAAI,CAAC,SAAS,EAAEO,GAAG,CAAC;IAC3B;EACF;AACF,CAAC;AAEDrB,WAAW,CAACgB,SAAS,CAACD,QAAQ,GAAG,YAAW;EAC1CnB,KAAK,CAAC,UAAU,CAAC;EACjB,IAAI,CAAC0B,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAEDtB,WAAW,CAACgB,SAAS,CAACO,KAAK,GAAG,YAAW;EACvC3B,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,IAAI,CAACU,EAAE,EAAE;IACX,IAAI,CAACA,EAAE,CAACkB,KAAK,CAAC,CAAC;IACf5B,KAAK,CAAC,OAAO,CAAC;IACd,IAAI,CAACkB,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC;IAChC,IAAI,CAACR,EAAE,GAAG,IAAI;EAChB;EACA,IAAI,CAACS,QAAQ,CAAC,CAAC;AACjB,CAAC;AAEDU,MAAM,CAACC,OAAO,GAAG1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}