{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Divider, Chip, Alert, AlertTitle, Paper, useTheme } from '@mui/material';\nimport { useSpring, animated, useTrail } from 'react-spring';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport WarningIcon from '@mui/icons-material/Warning';\nimport OpacityIcon from '@mui/icons-material/Opacity';\nimport TerrainIcon from '@mui/icons-material/Terrain';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\n\n// Animated progress circle component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimatedCircularProgress = ({\n  value,\n  size = 120,\n  thickness = 8,\n  color\n}) => {\n  _s();\n  const theme = useTheme();\n\n  // Calculate the circumference of the circle\n  const radius = (size - thickness) / 2;\n  const circumference = 2 * Math.PI * radius;\n\n  // Animation for the progress\n  const {\n    strokeDashoffset\n  } = useSpring({\n    from: {\n      strokeDashoffset: circumference\n    },\n    to: {\n      strokeDashoffset: circumference * (1 - value / 100)\n    },\n    config: {\n      tension: 120,\n      friction: 14\n    },\n    delay: 300\n  });\n  const {\n    opacity,\n    transform\n  } = useSpring({\n    from: {\n      opacity: 0,\n      transform: 'scale(0.8)'\n    },\n    to: {\n      opacity: 1,\n      transform: 'scale(1)'\n    },\n    config: {\n      tension: 120,\n      friction: 14\n    },\n    delay: 300\n  });\n  return /*#__PURE__*/_jsxDEV(animated.div, {\n    style: {\n      opacity,\n      transform\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: size,\n        height: size,\n        display: 'inline-flex'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: size,\n        height: size,\n        style: {\n          position: 'absolute'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: size / 2,\n          cy: size / 2,\n          r: radius,\n          fill: \"none\",\n          stroke: theme.palette.grey[200],\n          strokeWidth: thickness\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: size,\n        height: size,\n        style: {\n          position: 'absolute',\n          transform: 'rotate(-90deg)'\n        },\n        children: /*#__PURE__*/_jsxDEV(animated.circle, {\n          cx: size / 2,\n          cy: size / 2,\n          r: radius,\n          fill: \"none\",\n          stroke: color,\n          strokeWidth: thickness,\n          strokeDasharray: circumference,\n          strokeDashoffset: strokeDashoffset,\n          strokeLinecap: \"round\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          top: 0,\n          left: 0,\n          bottom: 0,\n          right: 0,\n          position: 'absolute',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"div\",\n          sx: {\n            fontWeight: 700,\n            color\n          },\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          component: \"div\",\n          sx: {\n            color: theme.palette.text.secondary\n          },\n          children: \"RISK SCORE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n\n// Get icon for a risk factor\n_s(AnimatedCircularProgress, \"GlvPfdldIQ77oN2tltQhGtpzpqo=\", false, function () {\n  return [useTheme, useSpring, useSpring];\n});\n_c = AnimatedCircularProgress;\nconst getFactorIcon = factorName => {\n  if (factorName.includes('Rainfall')) return /*#__PURE__*/_jsxDEV(ThunderstormIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 47\n  }, this);\n  if (factorName.includes('Water')) return /*#__PURE__*/_jsxDEV(OpacityIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 44\n  }, this);\n  if (factorName.includes('Elevation')) return /*#__PURE__*/_jsxDEV(TerrainIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 48\n  }, this);\n  if (factorName.includes('Flood')) return /*#__PURE__*/_jsxDEV(WaterDropIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 44\n  }, this);\n  return /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 10\n  }, this);\n};\nconst ResultDisplay = ({\n  prediction\n}) => {\n  _s2();\n  const theme = useTheme();\n  const {\n    prediction: floodPrediction,\n    risk_assessment\n  } = prediction;\n\n  // Animation for the alert\n  const alertAnimation = useSpring({\n    from: {\n      opacity: 0,\n      transform: 'translateY(-20px)'\n    },\n    to: {\n      opacity: 1,\n      transform: 'translateY(0)'\n    },\n    config: {\n      tension: 120,\n      friction: 14\n    }\n  });\n\n  // Animation for risk factors\n  const trail = useTrail(risk_assessment.factors.length, {\n    from: {\n      opacity: 0,\n      transform: 'translateY(10px)'\n    },\n    to: {\n      opacity: 1,\n      transform: 'translateY(0)'\n    },\n    config: {\n      mass: 1,\n      tension: 120,\n      friction: 14\n    },\n    delay: 600\n  });\n\n  // Determine risk color\n  const riskColor = risk_assessment.risk_level === 'High' ? theme.palette.error.main : risk_assessment.risk_level === 'Medium' ? theme.palette.warning.main : theme.palette.success.main;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      sx: {\n        fontWeight: 600,\n        color: theme.palette.primary.dark,\n        mb: 3\n      },\n      children: \"Prediction Result\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(animated.div, {\n      style: alertAnimation,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4\n        },\n        children: floodPrediction === 1 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(WaterDropIcon, {\n            fontSize: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 21\n          }, this),\n          sx: {\n            mb: 2,\n            borderRadius: 2,\n            boxShadow: '0 4px 12px rgba(255, 89, 94, 0.2)',\n            '& .MuiAlert-icon': {\n              fontSize: '1.5rem',\n              opacity: 0.9\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n            sx: {\n              fontWeight: 600,\n              fontSize: '1.1rem'\n            },\n            children: \"High Flood Risk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: [\"Based on the provided data, there is a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"high risk of flooding\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 56\n            }, this), \" in this area. Take necessary precautions and monitor weather conditions closely.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            fontSize: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 21\n          }, this),\n          sx: {\n            mb: 2,\n            borderRadius: 2,\n            boxShadow: '0 4px 12px rgba(6, 214, 160, 0.2)',\n            '& .MuiAlert-icon': {\n              fontSize: '1.5rem',\n              opacity: 0.9\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n            sx: {\n              fontWeight: 600,\n              fontSize: '1.1rem'\n            },\n            children: \"Low Flood Risk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: [\"Based on the provided data, there is a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"low risk of flooding\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 56\n            }, this), \" in this area. Continue to monitor weather conditions for any changes.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: {\n          xs: 'column',\n          sm: 'row'\n        },\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        mb: 4,\n        gap: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: {\n            xs: 'center',\n            sm: 'left'\n          },\n          mb: {\n            xs: 2,\n            sm: 0\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 600,\n            color: theme.palette.text.primary,\n            mb: 1\n          },\n          children: \"Risk Assessment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          sx: {\n            color: theme.palette.text.secondary\n          },\n          children: \"The overall flood risk is calculated based on multiple environmental factors.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: risk_assessment.risk_level,\n            color: risk_assessment.risk_level === 'High' ? 'error' : risk_assessment.risk_level === 'Medium' ? 'warning' : 'success',\n            sx: {\n              fontWeight: 'bold',\n              fontSize: '1rem',\n              py: 2,\n              px: 1,\n              borderRadius: 4,\n              boxShadow: `0 4px 12px ${risk_assessment.risk_level === 'High' ? 'rgba(255, 89, 94, 0.2)' : risk_assessment.risk_level === 'Medium' ? 'rgba(251, 86, 7, 0.2)' : 'rgba(6, 214, 160, 0.2)'}`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(AnimatedCircularProgress, {\n          value: risk_assessment.risk_score,\n          color: riskColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 3,\n        borderColor: 'rgba(0, 0, 0, 0.08)'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        sx: {\n          fontWeight: 600,\n          color: theme.palette.text.primary,\n          mb: 3\n        },\n        children: \"Key Risk Factors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), risk_assessment.factors.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 2\n        },\n        children: trail.map((style, index) => {\n          const factor = risk_assessment.factors[index];\n          const factorColor = factor.impact === 'Very High' ? theme.palette.error.main : factor.impact === 'High' ? theme.palette.warning.main : factor.impact === 'Medium' ? theme.palette.info.main : theme.palette.success.main;\n          return /*#__PURE__*/_jsxDEV(animated.div, {\n            style: style,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 2,\n              sx: {\n                p: 2,\n                borderRadius: 2,\n                borderLeft: `4px solid ${factorColor}`,\n                display: 'flex',\n                alignItems: 'center',\n                minWidth: 200,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-3px)',\n                  boxShadow: '0 6px 16px rgba(0, 0, 0, 0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mr: 1.5,\n                  color: factorColor\n                },\n                children: getFactorIcon(factor.factor)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"bold\",\n                  children: factor.factor\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [typeof factor.value === 'number' ? factor.value.toFixed(1) : factor.value, factor.threshold && ` (Threshold: ${factor.threshold})`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: factor.impact,\n                  size: \"small\",\n                  sx: {\n                    mt: 0.5,\n                    fontSize: '0.7rem',\n                    height: 20,\n                    backgroundColor: `${factorColor}20`,\n                    color: factorColor,\n                    fontWeight: 'bold'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"No significant risk factors identified.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s2(ResultDisplay, \"cBfOxvbHtnu3gY2j1H8oXAOHAbI=\", false, function () {\n  return [useTheme, useSpring, useTrail];\n});\n_c2 = ResultDisplay;\nexport default ResultDisplay;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnimatedCircularProgress\");\n$RefreshReg$(_c2, \"ResultDisplay\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Divider", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Paper", "useTheme", "useSpring", "animated", "useTrail", "WaterDropIcon", "CheckCircleIcon", "WarningIcon", "OpacityIcon", "TerrainIcon", "ThunderstormIcon", "jsxDEV", "_jsxDEV", "AnimatedCircularProgress", "value", "size", "thickness", "color", "_s", "theme", "radius", "circumference", "Math", "PI", "strokeDashoffset", "from", "to", "config", "tension", "friction", "delay", "opacity", "transform", "div", "style", "children", "sx", "position", "width", "height", "display", "cx", "cy", "r", "fill", "stroke", "palette", "grey", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "circle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeLinecap", "top", "left", "bottom", "right", "alignItems", "justifyContent", "flexDirection", "variant", "component", "fontWeight", "text", "secondary", "_c", "getFactorIcon", "factorName", "includes", "ResultDisplay", "prediction", "_s2", "floodPrediction", "risk_assessment", "alertAnimation", "trail", "factors", "length", "mass", "riskColor", "risk_level", "error", "main", "warning", "success", "gutterBottom", "primary", "dark", "mb", "severity", "icon", "fontSize", "borderRadius", "boxShadow", "xs", "sm", "gap", "textAlign", "paragraph", "mt", "label", "py", "px", "risk_score", "my", "borderColor", "flexWrap", "map", "index", "factor", "factorColor", "impact", "info", "elevation", "p", "borderLeft", "min<PERSON><PERSON><PERSON>", "transition", "mr", "toFixed", "threshold", "backgroundColor", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Divider, Chip, Alert, AlertTitle, Paper, useTheme } from '@mui/material';\nimport { useSpring, animated, useTrail } from 'react-spring';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport WarningIcon from '@mui/icons-material/Warning';\nimport OpacityIcon from '@mui/icons-material/Opacity';\nimport TerrainIcon from '@mui/icons-material/Terrain';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\n\n// Animated progress circle component\nconst AnimatedCircularProgress = ({ value, size = 120, thickness = 8, color }) => {\n  const theme = useTheme();\n\n  // Calculate the circumference of the circle\n  const radius = (size - thickness) / 2;\n  const circumference = 2 * Math.PI * radius;\n\n  // Animation for the progress\n  const { strokeDashoffset } = useSpring({\n    from: { strokeDashoffset: circumference },\n    to: { strokeDashoffset: circumference * (1 - value / 100) },\n    config: { tension: 120, friction: 14 },\n    delay: 300\n  });\n\n  const { opacity, transform } = useSpring({\n    from: { opacity: 0, transform: 'scale(0.8)' },\n    to: { opacity: 1, transform: 'scale(1)' },\n    config: { tension: 120, friction: 14 },\n    delay: 300\n  });\n\n  return (\n    <animated.div style={{ opacity, transform }}>\n      <Box sx={{ position: 'relative', width: size, height: size, display: 'inline-flex' }}>\n        {/* Background circle */}\n        <svg width={size} height={size} style={{ position: 'absolute' }}>\n          <circle\n            cx={size / 2}\n            cy={size / 2}\n            r={radius}\n            fill=\"none\"\n            stroke={theme.palette.grey[200]}\n            strokeWidth={thickness}\n          />\n        </svg>\n\n        {/* Progress circle */}\n        <svg width={size} height={size} style={{ position: 'absolute', transform: 'rotate(-90deg)' }}>\n          <animated.circle\n            cx={size / 2}\n            cy={size / 2}\n            r={radius}\n            fill=\"none\"\n            stroke={color}\n            strokeWidth={thickness}\n            strokeDasharray={circumference}\n            strokeDashoffset={strokeDashoffset}\n            strokeLinecap=\"round\"\n          />\n        </svg>\n\n        {/* Center text */}\n        <Box\n          sx={{\n            top: 0,\n            left: 0,\n            bottom: 0,\n            right: 0,\n            position: 'absolute',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            flexDirection: 'column'\n          }}\n        >\n          <Typography variant=\"h4\" component=\"div\" sx={{ fontWeight: 700, color }}>\n            {value}\n          </Typography>\n          <Typography variant=\"caption\" component=\"div\" sx={{ color: theme.palette.text.secondary }}>\n            RISK SCORE\n          </Typography>\n        </Box>\n      </Box>\n    </animated.div>\n  );\n};\n\n// Get icon for a risk factor\nconst getFactorIcon = (factorName) => {\n  if (factorName.includes('Rainfall')) return <ThunderstormIcon />;\n  if (factorName.includes('Water')) return <OpacityIcon />;\n  if (factorName.includes('Elevation')) return <TerrainIcon />;\n  if (factorName.includes('Flood')) return <WaterDropIcon />;\n  return <WarningIcon />;\n};\n\nconst ResultDisplay = ({ prediction }) => {\n  const theme = useTheme();\n  const { prediction: floodPrediction, risk_assessment } = prediction;\n\n  // Animation for the alert\n  const alertAnimation = useSpring({\n    from: { opacity: 0, transform: 'translateY(-20px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { tension: 120, friction: 14 }\n  });\n\n  // Animation for risk factors\n  const trail = useTrail(risk_assessment.factors.length, {\n    from: { opacity: 0, transform: 'translateY(10px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { mass: 1, tension: 120, friction: 14 },\n    delay: 600\n  });\n\n  // Determine risk color\n  const riskColor =\n    risk_assessment.risk_level === 'High' ? theme.palette.error.main :\n    risk_assessment.risk_level === 'Medium' ? theme.palette.warning.main :\n    theme.palette.success.main;\n\n  return (\n    <Box>\n      <Typography\n        variant=\"h3\"\n        gutterBottom\n        sx={{\n          fontWeight: 600,\n          color: theme.palette.primary.dark,\n          mb: 3\n        }}\n      >\n        Prediction Result\n      </Typography>\n\n      <animated.div style={alertAnimation}>\n        <Box sx={{ mb: 4 }}>\n          {floodPrediction === 1 ? (\n            <Alert\n              severity=\"error\"\n              icon={<WaterDropIcon fontSize=\"inherit\" />}\n              sx={{\n                mb: 2,\n                borderRadius: 2,\n                boxShadow: '0 4px 12px rgba(255, 89, 94, 0.2)',\n                '& .MuiAlert-icon': {\n                  fontSize: '1.5rem',\n                  opacity: 0.9\n                }\n              }}\n            >\n              <AlertTitle sx={{ fontWeight: 600, fontSize: '1.1rem' }}>High Flood Risk</AlertTitle>\n              <Typography variant=\"body1\">\n                Based on the provided data, there is a <strong>high risk of flooding</strong> in this area.\n                Take necessary precautions and monitor weather conditions closely.\n              </Typography>\n            </Alert>\n          ) : (\n            <Alert\n              severity=\"success\"\n              icon={<CheckCircleIcon fontSize=\"inherit\" />}\n              sx={{\n                mb: 2,\n                borderRadius: 2,\n                boxShadow: '0 4px 12px rgba(6, 214, 160, 0.2)',\n                '& .MuiAlert-icon': {\n                  fontSize: '1.5rem',\n                  opacity: 0.9\n                }\n              }}\n            >\n              <AlertTitle sx={{ fontWeight: 600, fontSize: '1.1rem' }}>Low Flood Risk</AlertTitle>\n              <Typography variant=\"body1\">\n                Based on the provided data, there is a <strong>low risk of flooding</strong> in this area.\n                Continue to monitor weather conditions for any changes.\n              </Typography>\n            </Alert>\n          )}\n        </Box>\n      </animated.div>\n\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: { xs: 'column', sm: 'row' },\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 4,\n          gap: 3\n        }}\n      >\n        <Box sx={{ textAlign: { xs: 'center', sm: 'left' }, mb: { xs: 2, sm: 0 } }}>\n          <Typography\n            variant=\"h5\"\n            gutterBottom\n            sx={{\n              fontWeight: 600,\n              color: theme.palette.text.primary,\n              mb: 1\n            }}\n          >\n            Risk Assessment\n          </Typography>\n\n          <Typography variant=\"body1\" paragraph sx={{ color: theme.palette.text.secondary }}>\n            The overall flood risk is calculated based on multiple environmental factors.\n          </Typography>\n\n          <Box sx={{ mt: 2 }}>\n            <Chip\n              label={risk_assessment.risk_level}\n              color={\n                risk_assessment.risk_level === 'High' ? 'error' :\n                risk_assessment.risk_level === 'Medium' ? 'warning' : 'success'\n              }\n              sx={{\n                fontWeight: 'bold',\n                fontSize: '1rem',\n                py: 2,\n                px: 1,\n                borderRadius: 4,\n                boxShadow: `0 4px 12px ${\n                  risk_assessment.risk_level === 'High' ? 'rgba(255, 89, 94, 0.2)' :\n                  risk_assessment.risk_level === 'Medium' ? 'rgba(251, 86, 7, 0.2)' :\n                  'rgba(6, 214, 160, 0.2)'\n                }`\n              }}\n            />\n          </Box>\n        </Box>\n\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          <AnimatedCircularProgress\n            value={risk_assessment.risk_score}\n            color={riskColor}\n          />\n        </Box>\n      </Box>\n\n      <Divider sx={{ my: 3, borderColor: 'rgba(0, 0, 0, 0.08)' }} />\n\n      <Box>\n        <Typography\n          variant=\"h5\"\n          gutterBottom\n          sx={{\n            fontWeight: 600,\n            color: theme.palette.text.primary,\n            mb: 3\n          }}\n        >\n          Key Risk Factors\n        </Typography>\n\n        {risk_assessment.factors.length > 0 ? (\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n            {trail.map((style, index) => {\n              const factor = risk_assessment.factors[index];\n              const factorColor =\n                factor.impact === 'Very High' ? theme.palette.error.main :\n                factor.impact === 'High' ? theme.palette.warning.main :\n                factor.impact === 'Medium' ? theme.palette.info.main :\n                theme.palette.success.main;\n\n              return (\n                <animated.div key={index} style={style}>\n                  <Paper\n                    elevation={2}\n                    sx={{\n                      p: 2,\n                      borderRadius: 2,\n                      borderLeft: `4px solid ${factorColor}`,\n                      display: 'flex',\n                      alignItems: 'center',\n                      minWidth: 200,\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        transform: 'translateY(-3px)',\n                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.1)'\n                      }\n                    }}\n                  >\n                    <Box sx={{ mr: 1.5, color: factorColor }}>\n                      {getFactorIcon(factor.factor)}\n                    </Box>\n                    <Box>\n                      <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                        {factor.factor}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {typeof factor.value === 'number' ? factor.value.toFixed(1) : factor.value}\n                        {factor.threshold && ` (Threshold: ${factor.threshold})`}\n                      </Typography>\n                      <Chip\n                        label={factor.impact}\n                        size=\"small\"\n                        sx={{\n                          mt: 0.5,\n                          fontSize: '0.7rem',\n                          height: 20,\n                          backgroundColor: `${factorColor}20`,\n                          color: factorColor,\n                          fontWeight: 'bold'\n                        }}\n                      />\n                    </Box>\n                  </Paper>\n                </animated.div>\n              );\n            })}\n          </Box>\n        ) : (\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            No significant risk factors identified.\n          </Typography>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default ResultDisplay;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAClG,SAASC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAC5D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,gBAAgB,MAAM,kCAAkC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS,GAAG,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAMC,KAAK,GAAGlB,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMmB,MAAM,GAAG,CAACL,IAAI,GAAGC,SAAS,IAAI,CAAC;EACrC,MAAMK,aAAa,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;;EAE1C;EACA,MAAM;IAAEI;EAAiB,CAAC,GAAGtB,SAAS,CAAC;IACrCuB,IAAI,EAAE;MAAED,gBAAgB,EAAEH;IAAc,CAAC;IACzCK,EAAE,EAAE;MAAEF,gBAAgB,EAAEH,aAAa,IAAI,CAAC,GAAGP,KAAK,GAAG,GAAG;IAAE,CAAC;IAC3Da,MAAM,EAAE;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACtCC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM;IAAEC,OAAO;IAAEC;EAAU,CAAC,GAAG9B,SAAS,CAAC;IACvCuB,IAAI,EAAE;MAAEM,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAa,CAAC;IAC7CN,EAAE,EAAE;MAAEK,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAW,CAAC;IACzCL,MAAM,EAAE;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACtCC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,oBACElB,OAAA,CAACT,QAAQ,CAAC8B,GAAG;IAACC,KAAK,EAAE;MAAEH,OAAO;MAAEC;IAAU,CAAE;IAAAG,QAAA,eAC1CvB,OAAA,CAAClB,GAAG;MAAC0C,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,KAAK,EAAEvB,IAAI;QAAEwB,MAAM,EAAExB,IAAI;QAAEyB,OAAO,EAAE;MAAc,CAAE;MAAAL,QAAA,gBAEnFvB,OAAA;QAAK0B,KAAK,EAAEvB,IAAK;QAACwB,MAAM,EAAExB,IAAK;QAACmB,KAAK,EAAE;UAAEG,QAAQ,EAAE;QAAW,CAAE;QAAAF,QAAA,eAC9DvB,OAAA;UACE6B,EAAE,EAAE1B,IAAI,GAAG,CAAE;UACb2B,EAAE,EAAE3B,IAAI,GAAG,CAAE;UACb4B,CAAC,EAAEvB,MAAO;UACVwB,IAAI,EAAC,MAAM;UACXC,MAAM,EAAE1B,KAAK,CAAC2B,OAAO,CAACC,IAAI,CAAC,GAAG,CAAE;UAChCC,WAAW,EAAEhC;QAAU;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxC,OAAA;QAAK0B,KAAK,EAAEvB,IAAK;QAACwB,MAAM,EAAExB,IAAK;QAACmB,KAAK,EAAE;UAAEG,QAAQ,EAAE,UAAU;UAAEL,SAAS,EAAE;QAAiB,CAAE;QAAAG,QAAA,eAC3FvB,OAAA,CAACT,QAAQ,CAACkD,MAAM;UACdZ,EAAE,EAAE1B,IAAI,GAAG,CAAE;UACb2B,EAAE,EAAE3B,IAAI,GAAG,CAAE;UACb4B,CAAC,EAAEvB,MAAO;UACVwB,IAAI,EAAC,MAAM;UACXC,MAAM,EAAE5B,KAAM;UACd+B,WAAW,EAAEhC,SAAU;UACvBsC,eAAe,EAAEjC,aAAc;UAC/BG,gBAAgB,EAAEA,gBAAiB;UACnC+B,aAAa,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxC,OAAA,CAAClB,GAAG;QACF0C,EAAE,EAAE;UACFoB,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;UACRtB,QAAQ,EAAE,UAAU;UACpBG,OAAO,EAAE,MAAM;UACfoB,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,aAAa,EAAE;QACjB,CAAE;QAAA3B,QAAA,gBAEFvB,OAAA,CAACjB,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAAC5B,EAAE,EAAE;YAAE6B,UAAU,EAAE,GAAG;YAAEhD;UAAM,CAAE;UAAAkB,QAAA,EACrErB;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACbxC,OAAA,CAACjB,UAAU;UAACoE,OAAO,EAAC,SAAS;UAACC,SAAS,EAAC,KAAK;UAAC5B,EAAE,EAAE;YAAEnB,KAAK,EAAEE,KAAK,CAAC2B,OAAO,CAACoB,IAAI,CAACC;UAAU,CAAE;UAAAhC,QAAA,EAAC;QAE3F;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB,CAAC;;AAED;AAAAlC,EAAA,CA9EML,wBAAwB;EAAA,QACdZ,QAAQ,EAOOC,SAAS,EAOPA,SAAS;AAAA;AAAAkE,EAAA,GAfpCvD,wBAAwB;AA+E9B,MAAMwD,aAAa,GAAIC,UAAU,IAAK;EACpC,IAAIA,UAAU,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE,oBAAO3D,OAAA,CAACF,gBAAgB;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChE,IAAIkB,UAAU,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE,oBAAO3D,OAAA,CAACJ,WAAW;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxD,IAAIkB,UAAU,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE,oBAAO3D,OAAA,CAACH,WAAW;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5D,IAAIkB,UAAU,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE,oBAAO3D,OAAA,CAACP,aAAa;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC1D,oBAAOxC,OAAA,CAACL,WAAW;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACxB,CAAC;AAED,MAAMoB,aAAa,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,GAAA;EACxC,MAAMvD,KAAK,GAAGlB,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAEwE,UAAU,EAAEE,eAAe;IAAEC;EAAgB,CAAC,GAAGH,UAAU;;EAEnE;EACA,MAAMI,cAAc,GAAG3E,SAAS,CAAC;IAC/BuB,IAAI,EAAE;MAAEM,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAoB,CAAC;IACpDN,EAAE,EAAE;MAAEK,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAgB,CAAC;IAC9CL,MAAM,EAAE;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMiD,KAAK,GAAG1E,QAAQ,CAACwE,eAAe,CAACG,OAAO,CAACC,MAAM,EAAE;IACrDvD,IAAI,EAAE;MAAEM,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAmB,CAAC;IACnDN,EAAE,EAAE;MAAEK,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAgB,CAAC;IAC9CL,MAAM,EAAE;MAAEsD,IAAI,EAAE,CAAC;MAAErD,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG,CAAC;IAC/CC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMoD,SAAS,GACbN,eAAe,CAACO,UAAU,KAAK,MAAM,GAAGhE,KAAK,CAAC2B,OAAO,CAACsC,KAAK,CAACC,IAAI,GAChET,eAAe,CAACO,UAAU,KAAK,QAAQ,GAAGhE,KAAK,CAAC2B,OAAO,CAACwC,OAAO,CAACD,IAAI,GACpElE,KAAK,CAAC2B,OAAO,CAACyC,OAAO,CAACF,IAAI;EAE5B,oBACEzE,OAAA,CAAClB,GAAG;IAAAyC,QAAA,gBACFvB,OAAA,CAACjB,UAAU;MACToE,OAAO,EAAC,IAAI;MACZyB,YAAY;MACZpD,EAAE,EAAE;QACF6B,UAAU,EAAE,GAAG;QACfhD,KAAK,EAAEE,KAAK,CAAC2B,OAAO,CAAC2C,OAAO,CAACC,IAAI;QACjCC,EAAE,EAAE;MACN,CAAE;MAAAxD,QAAA,EACH;IAED;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxC,OAAA,CAACT,QAAQ,CAAC8B,GAAG;MAACC,KAAK,EAAE2C,cAAe;MAAA1C,QAAA,eAClCvB,OAAA,CAAClB,GAAG;QAAC0C,EAAE,EAAE;UAAEuD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAChBwC,eAAe,KAAK,CAAC,gBACpB/D,OAAA,CAACd,KAAK;UACJ8F,QAAQ,EAAC,OAAO;UAChBC,IAAI,eAAEjF,OAAA,CAACP,aAAa;YAACyF,QAAQ,EAAC;UAAS;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3ChB,EAAE,EAAE;YACFuD,EAAE,EAAE,CAAC;YACLI,YAAY,EAAE,CAAC;YACfC,SAAS,EAAE,mCAAmC;YAC9C,kBAAkB,EAAE;cAClBF,QAAQ,EAAE,QAAQ;cAClB/D,OAAO,EAAE;YACX;UACF,CAAE;UAAAI,QAAA,gBAEFvB,OAAA,CAACb,UAAU;YAACqC,EAAE,EAAE;cAAE6B,UAAU,EAAE,GAAG;cAAE6B,QAAQ,EAAE;YAAS,CAAE;YAAA3D,QAAA,EAAC;UAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrFxC,OAAA,CAACjB,UAAU;YAACoE,OAAO,EAAC,OAAO;YAAA5B,QAAA,GAAC,yCACa,eAAAvB,OAAA;cAAAuB,QAAA,EAAQ;YAAqB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qFAE/E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,gBAERxC,OAAA,CAACd,KAAK;UACJ8F,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAEjF,OAAA,CAACN,eAAe;YAACwF,QAAQ,EAAC;UAAS;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7ChB,EAAE,EAAE;YACFuD,EAAE,EAAE,CAAC;YACLI,YAAY,EAAE,CAAC;YACfC,SAAS,EAAE,mCAAmC;YAC9C,kBAAkB,EAAE;cAClBF,QAAQ,EAAE,QAAQ;cAClB/D,OAAO,EAAE;YACX;UACF,CAAE;UAAAI,QAAA,gBAEFvB,OAAA,CAACb,UAAU;YAACqC,EAAE,EAAE;cAAE6B,UAAU,EAAE,GAAG;cAAE6B,QAAQ,EAAE;YAAS,CAAE;YAAA3D,QAAA,EAAC;UAAc;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpFxC,OAAA,CAACjB,UAAU;YAACoE,OAAO,EAAC,OAAO;YAAA5B,QAAA,GAAC,yCACa,eAAAvB,OAAA;cAAAuB,QAAA,EAAQ;YAAoB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0EAE9E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEfxC,OAAA,CAAClB,GAAG;MACF0C,EAAE,EAAE;QACFI,OAAO,EAAE,MAAM;QACfsB,aAAa,EAAE;UAAEmC,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAM,CAAC;QAC1CtC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/B8B,EAAE,EAAE,CAAC;QACLQ,GAAG,EAAE;MACP,CAAE;MAAAhE,QAAA,gBAEFvB,OAAA,CAAClB,GAAG;QAAC0C,EAAE,EAAE;UAAEgE,SAAS,EAAE;YAAEH,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAEP,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAA/D,QAAA,gBACzEvB,OAAA,CAACjB,UAAU;UACToE,OAAO,EAAC,IAAI;UACZyB,YAAY;UACZpD,EAAE,EAAE;YACF6B,UAAU,EAAE,GAAG;YACfhD,KAAK,EAAEE,KAAK,CAAC2B,OAAO,CAACoB,IAAI,CAACuB,OAAO;YACjCE,EAAE,EAAE;UACN,CAAE;UAAAxD,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbxC,OAAA,CAACjB,UAAU;UAACoE,OAAO,EAAC,OAAO;UAACsC,SAAS;UAACjE,EAAE,EAAE;YAAEnB,KAAK,EAAEE,KAAK,CAAC2B,OAAO,CAACoB,IAAI,CAACC;UAAU,CAAE;UAAAhC,QAAA,EAAC;QAEnF;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbxC,OAAA,CAAClB,GAAG;UAAC0C,EAAE,EAAE;YAAEkE,EAAE,EAAE;UAAE,CAAE;UAAAnE,QAAA,eACjBvB,OAAA,CAACf,IAAI;YACH0G,KAAK,EAAE3B,eAAe,CAACO,UAAW;YAClClE,KAAK,EACH2D,eAAe,CAACO,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/CP,eAAe,CAACO,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG,SACvD;YACD/C,EAAE,EAAE;cACF6B,UAAU,EAAE,MAAM;cAClB6B,QAAQ,EAAE,MAAM;cAChBU,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLV,YAAY,EAAE,CAAC;cACfC,SAAS,EAAE,cACTpB,eAAe,CAACO,UAAU,KAAK,MAAM,GAAG,wBAAwB,GAChEP,eAAe,CAACO,UAAU,KAAK,QAAQ,GAAG,uBAAuB,GACjE,wBAAwB;YAE5B;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxC,OAAA,CAAClB,GAAG;QAAC0C,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEqB,cAAc,EAAE;QAAS,CAAE;QAAA1B,QAAA,eACrDvB,OAAA,CAACC,wBAAwB;UACvBC,KAAK,EAAE8D,eAAe,CAAC8B,UAAW;UAClCzF,KAAK,EAAEiE;QAAU;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxC,OAAA,CAAChB,OAAO;MAACwC,EAAE,EAAE;QAAEuE,EAAE,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAsB;IAAE;MAAA3D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE9DxC,OAAA,CAAClB,GAAG;MAAAyC,QAAA,gBACFvB,OAAA,CAACjB,UAAU;QACToE,OAAO,EAAC,IAAI;QACZyB,YAAY;QACZpD,EAAE,EAAE;UACF6B,UAAU,EAAE,GAAG;UACfhD,KAAK,EAAEE,KAAK,CAAC2B,OAAO,CAACoB,IAAI,CAACuB,OAAO;UACjCE,EAAE,EAAE;QACN,CAAE;QAAAxD,QAAA,EACH;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZwB,eAAe,CAACG,OAAO,CAACC,MAAM,GAAG,CAAC,gBACjCpE,OAAA,CAAClB,GAAG;QAAC0C,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEqE,QAAQ,EAAE,MAAM;UAAEV,GAAG,EAAE;QAAE,CAAE;QAAAhE,QAAA,EACpD2C,KAAK,CAACgC,GAAG,CAAC,CAAC5E,KAAK,EAAE6E,KAAK,KAAK;UAC3B,MAAMC,MAAM,GAAGpC,eAAe,CAACG,OAAO,CAACgC,KAAK,CAAC;UAC7C,MAAME,WAAW,GACfD,MAAM,CAACE,MAAM,KAAK,WAAW,GAAG/F,KAAK,CAAC2B,OAAO,CAACsC,KAAK,CAACC,IAAI,GACxD2B,MAAM,CAACE,MAAM,KAAK,MAAM,GAAG/F,KAAK,CAAC2B,OAAO,CAACwC,OAAO,CAACD,IAAI,GACrD2B,MAAM,CAACE,MAAM,KAAK,QAAQ,GAAG/F,KAAK,CAAC2B,OAAO,CAACqE,IAAI,CAAC9B,IAAI,GACpDlE,KAAK,CAAC2B,OAAO,CAACyC,OAAO,CAACF,IAAI;UAE5B,oBACEzE,OAAA,CAACT,QAAQ,CAAC8B,GAAG;YAAaC,KAAK,EAAEA,KAAM;YAAAC,QAAA,eACrCvB,OAAA,CAACZ,KAAK;cACJoH,SAAS,EAAE,CAAE;cACbhF,EAAE,EAAE;gBACFiF,CAAC,EAAE,CAAC;gBACJtB,YAAY,EAAE,CAAC;gBACfuB,UAAU,EAAE,aAAaL,WAAW,EAAE;gBACtCzE,OAAO,EAAE,MAAM;gBACfoB,UAAU,EAAE,QAAQ;gBACpB2D,QAAQ,EAAE,GAAG;gBACbC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACTxF,SAAS,EAAE,kBAAkB;kBAC7BgE,SAAS,EAAE;gBACb;cACF,CAAE;cAAA7D,QAAA,gBAEFvB,OAAA,CAAClB,GAAG;gBAAC0C,EAAE,EAAE;kBAAEqF,EAAE,EAAE,GAAG;kBAAExG,KAAK,EAAEgG;gBAAY,CAAE;gBAAA9E,QAAA,EACtCkC,aAAa,CAAC2C,MAAM,CAACA,MAAM;cAAC;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNxC,OAAA,CAAClB,GAAG;gBAAAyC,QAAA,gBACFvB,OAAA,CAACjB,UAAU;kBAACoE,OAAO,EAAC,WAAW;kBAACE,UAAU,EAAC,MAAM;kBAAA9B,QAAA,EAC9C6E,MAAM,CAACA;gBAAM;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbxC,OAAA,CAACjB,UAAU;kBAACoE,OAAO,EAAC,OAAO;kBAAC9C,KAAK,EAAC,gBAAgB;kBAAAkB,QAAA,GAC/C,OAAO6E,MAAM,CAAClG,KAAK,KAAK,QAAQ,GAAGkG,MAAM,CAAClG,KAAK,CAAC4G,OAAO,CAAC,CAAC,CAAC,GAAGV,MAAM,CAAClG,KAAK,EACzEkG,MAAM,CAACW,SAAS,IAAI,gBAAgBX,MAAM,CAACW,SAAS,GAAG;gBAAA;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACbxC,OAAA,CAACf,IAAI;kBACH0G,KAAK,EAAES,MAAM,CAACE,MAAO;kBACrBnG,IAAI,EAAC,OAAO;kBACZqB,EAAE,EAAE;oBACFkE,EAAE,EAAE,GAAG;oBACPR,QAAQ,EAAE,QAAQ;oBAClBvD,MAAM,EAAE,EAAE;oBACVqF,eAAe,EAAE,GAAGX,WAAW,IAAI;oBACnChG,KAAK,EAAEgG,WAAW;oBAClBhD,UAAU,EAAE;kBACd;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAzCS2D,KAAK;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0CV,CAAC;QAEnB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENxC,OAAA,CAACjB,UAAU;QAACoE,OAAO,EAAC,OAAO;QAAC9C,KAAK,EAAC,gBAAgB;QAAAkB,QAAA,EAAC;MAEnD;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsB,GAAA,CA/NIF,aAAa;EAAA,QACHvE,QAAQ,EAICC,SAAS,EAOlBE,QAAQ;AAAA;AAAAyH,GAAA,GAZlBrD,aAAa;AAiOnB,eAAeA,aAAa;AAAC,IAAAJ,EAAA,EAAAyD,GAAA;AAAAC,YAAA,CAAA1D,EAAA;AAAA0D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}