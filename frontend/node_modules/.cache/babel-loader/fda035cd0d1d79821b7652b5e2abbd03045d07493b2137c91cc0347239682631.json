{"ast": null, "code": "import _objectWithoutProperties from \"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"bounds\", \"boundsOptions\", \"center\", \"children\", \"className\", \"id\", \"placeholder\", \"style\", \"whenReady\", \"zoom\"];\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { LeafletProvider, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';\nfunction MapContainerComponent(_ref, forwardedRef) {\n  let {\n      bounds,\n      boundsOptions,\n      center,\n      children,\n      className,\n      id,\n      placeholder,\n      style,\n      whenReady,\n      zoom\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const [props] = useState({\n    className,\n    id,\n    style\n  });\n  const [context, setContext] = useState(null);\n  useImperativeHandle(forwardedRef, () => {\n    var _context$map;\n    return (_context$map = context === null || context === void 0 ? void 0 : context.map) !== null && _context$map !== void 0 ? _context$map : null;\n  }, [context]);\n  const mapRef = useCallback(node => {\n    if (node !== null && context === null) {\n      const map = new LeafletMap(node, options);\n      if (center != null && zoom != null) {\n        map.setView(center, zoom);\n      } else if (bounds != null) {\n        map.fitBounds(bounds, boundsOptions);\n      }\n      if (whenReady != null) {\n        map.whenReady(whenReady);\n      }\n      setContext(createLeafletContext(map));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    return () => {\n      context === null || context === void 0 || context.map.remove();\n    };\n  }, [context]);\n  const contents = context ? /*#__PURE__*/React.createElement(LeafletProvider, {\n    value: context\n  }, children) : placeholder !== null && placeholder !== void 0 ? placeholder : null;\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, props, {\n    ref: mapRef\n  }), contents);\n}\nexport const MapContainer = /*#__PURE__*/forwardRef(MapContainerComponent);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "LeafletProvider", "createLeafletContext", "Map", "LeafletMap", "React", "forwardRef", "useCallback", "useEffect", "useImperativeHandle", "useState", "MapContainerComponent", "_ref", "forwardedRef", "bounds", "boundsOptions", "center", "children", "className", "id", "placeholder", "style", "when<PERSON><PERSON><PERSON>", "zoom", "options", "_objectWithoutProperties", "_excluded", "props", "context", "setContext", "_context$map", "map", "mapRef", "node", "<PERSON><PERSON><PERSON><PERSON>", "fitBounds", "remove", "contents", "createElement", "value", "ref", "MapContainer"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/react-leaflet/lib/MapContainer.js"], "sourcesContent": ["function _extends() {\n    _extends = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nimport { LeafletProvider, createLeafletContext } from '@react-leaflet/core';\nimport { Map as LeafletMap } from 'leaflet';\nimport React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';\nfunction MapContainerComponent({ bounds , boundsOptions , center , children , className , id , placeholder , style , whenReady , zoom , ...options }, forwardedRef) {\n    const [props] = useState({\n        className,\n        id,\n        style\n    });\n    const [context, setContext] = useState(null);\n    useImperativeHandle(forwardedRef, ()=>context?.map ?? null, [\n        context\n    ]);\n    const mapRef = useCallback((node)=>{\n        if (node !== null && context === null) {\n            const map = new LeafletMap(node, options);\n            if (center != null && zoom != null) {\n                map.setView(center, zoom);\n            } else if (bounds != null) {\n                map.fitBounds(bounds, boundsOptions);\n            }\n            if (whenReady != null) {\n                map.whenReady(whenReady);\n            }\n            setContext(createLeafletContext(map));\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    useEffect(()=>{\n        return ()=>{\n            context?.map.remove();\n        };\n    }, [\n        context\n    ]);\n    const contents = context ? /*#__PURE__*/ React.createElement(LeafletProvider, {\n        value: context\n    }, children) : placeholder ?? null;\n    return /*#__PURE__*/ React.createElement(\"div\", _extends({}, props, {\n        ref: mapRef\n    }), contents);\n}\nexport const MapContainer = /*#__PURE__*/ forwardRef(MapContainerComponent);\n"], "mappings": ";;AAAA,SAASA,QAAQA,CAAA,EAAG;EAChBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,MAAM,EAAE;IACzC,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAC;MACrC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MACzB,KAAI,IAAII,GAAG,IAAID,MAAM,EAAC;QAClB,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACnDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC7B;MACJ;IACJ;IACA,OAAOL,MAAM;EACjB,CAAC;EACD,OAAOH,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAC1C;AACA,SAASQ,eAAe,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC3E,SAASC,GAAG,IAAIC,UAAU,QAAQ,SAAS;AAC3C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AAChG,SAASC,qBAAqBA,CAAAC,IAAA,EAAwHC,YAAY,EAAE;EAAA,IAArI;MAAEC,MAAM;MAAGC,aAAa;MAAGC,MAAM;MAAGC,QAAQ;MAAGC,SAAS;MAAGC,EAAE;MAAGC,WAAW;MAAGC,KAAK;MAAGC,SAAS;MAAGC;IAAkB,CAAC,GAAAX,IAAA;IAATY,OAAO,GAAAC,wBAAA,CAAAb,IAAA,EAAAc,SAAA;EAC9I,MAAM,CAACC,KAAK,CAAC,GAAGjB,QAAQ,CAAC;IACrBQ,SAAS;IACTC,EAAE;IACFE;EACJ,CAAC,CAAC;EACF,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5CD,mBAAmB,CAACI,YAAY,EAAE;IAAA,IAAAiB,YAAA;IAAA,QAAAA,YAAA,GAAIF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,GAAG,cAAAD,YAAA,cAAAA,YAAA,GAAI,IAAI;EAAA,GAAE,CACxDF,OAAO,CACV,CAAC;EACF,MAAMI,MAAM,GAAGzB,WAAW,CAAE0B,IAAI,IAAG;IAC/B,IAAIA,IAAI,KAAK,IAAI,IAAIL,OAAO,KAAK,IAAI,EAAE;MACnC,MAAMG,GAAG,GAAG,IAAI3B,UAAU,CAAC6B,IAAI,EAAET,OAAO,CAAC;MACzC,IAAIR,MAAM,IAAI,IAAI,IAAIO,IAAI,IAAI,IAAI,EAAE;QAChCQ,GAAG,CAACG,OAAO,CAAClB,MAAM,EAAEO,IAAI,CAAC;MAC7B,CAAC,MAAM,IAAIT,MAAM,IAAI,IAAI,EAAE;QACvBiB,GAAG,CAACI,SAAS,CAACrB,MAAM,EAAEC,aAAa,CAAC;MACxC;MACA,IAAIO,SAAS,IAAI,IAAI,EAAE;QACnBS,GAAG,CAACT,SAAS,CAACA,SAAS,CAAC;MAC5B;MACAO,UAAU,CAAC3B,oBAAoB,CAAC6B,GAAG,CAAC,CAAC;IACzC;IACJ;EACA,CAAC,EAAE,EAAE,CAAC;EACNvB,SAAS,CAAC,MAAI;IACV,OAAO,MAAI;MACPoB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,GAAG,CAACK,MAAM,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CACCR,OAAO,CACV,CAAC;EACF,MAAMS,QAAQ,GAAGT,OAAO,GAAG,aAAcvB,KAAK,CAACiC,aAAa,CAACrC,eAAe,EAAE;IAC1EsC,KAAK,EAAEX;EACX,CAAC,EAAEX,QAAQ,CAAC,GAAGG,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,IAAI;EAClC,OAAO,aAAcf,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAElD,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IAChEa,GAAG,EAAER;EACT,CAAC,CAAC,EAAEK,QAAQ,CAAC;AACjB;AACA,OAAO,MAAMI,YAAY,GAAG,aAAcnC,UAAU,CAACK,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}