{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nfunction initializeValue(key, defaultValue) {\n  if (typeof window === 'undefined') {\n    return undefined;\n  }\n  let value;\n  try {\n    value = localStorage.getItem(key) || undefined;\n    if (!value) {\n      // the first time that user enters the site.\n      localStorage.setItem(key, defaultValue);\n    }\n  } catch (e) {\n    // Unsupported\n  }\n  return value || defaultValue;\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const [state, setState] = React.useState(() => {\n    const initialMode = initializeValue(modeStorageKey, defaultMode);\n    const lightColorScheme = initializeValue(\"\".concat(colorSchemeStorageKey, \"-light\"), defaultLightColorScheme);\n    const darkColorScheme = initializeValue(\"\".concat(colorSchemeStorageKey, \"-dark\"), defaultDarkColorScheme);\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode != null ? mode : defaultMode;\n      try {\n        localStorage.setItem(modeStorageKey, newMode);\n      } catch (e) {\n        // Unsupported\n      }\n      return _extends({}, currentState, {\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      });\n    });\n  }, [modeStorageKey, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        try {\n          localStorage.setItem(\"\".concat(colorSchemeStorageKey, \"-light\"), defaultLightColorScheme);\n          localStorage.setItem(\"\".concat(colorSchemeStorageKey, \"-dark\"), defaultDarkColorScheme);\n        } catch (e) {\n          // Unsupported\n        }\n        return _extends({}, currentState, {\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        });\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(\"`\".concat(value, \"` does not exist in `theme.colorSchemes`.\"));\n      } else {\n        setState(currentState => {\n          const newState = _extends({}, currentState);\n          processState(currentState, mode => {\n            try {\n              localStorage.setItem(\"\".concat(colorSchemeStorageKey, \"-\").concat(mode), value);\n            } catch (e) {\n              // Unsupported\n            }\n            if (mode === 'light') {\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = _extends({}, currentState);\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(\"`\".concat(newLightColorScheme, \"` does not exist in `theme.colorSchemes`.\"));\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            try {\n              localStorage.setItem(\"\".concat(colorSchemeStorageKey, \"-light\"), newLightColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(\"`\".concat(newDarkColorScheme, \"` does not exist in `theme.colorSchemes`.\"));\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            try {\n              localStorage.setItem(\"\".concat(colorSchemeStorageKey, \"-dark\"), newDarkColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, colorSchemeStorageKey, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event != null && event.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return _extends({}, currentState, {\n          systemMode\n        });\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    const handler = function () {\n      return mediaListener.current(...arguments);\n    };\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, []);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (storageWindow) {\n      const handleStorage = event => {\n        const value = event.newValue;\n        if (typeof event.key === 'string' && event.key.startsWith(colorSchemeStorageKey) && (!value || joinedColorSchemes.match(value))) {\n          // If the key is deleted, value will be null then reset color scheme to the default one.\n          if (event.key.endsWith('light')) {\n            setColorScheme({\n              light: value\n            });\n          }\n          if (event.key.endsWith('dark')) {\n            setColorScheme({\n              dark: value\n            });\n          }\n        }\n        if (event.key === modeStorageKey && (!value || ['light', 'dark', 'system'].includes(value))) {\n          setMode(value || defaultMode);\n        }\n      };\n      // For syncing color-scheme changes between iframes\n      storageWindow.addEventListener('storage', handleStorage);\n      return () => {\n        storageWindow.removeEventListener('storage', handleStorage);\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, modeStorageKey, colorSchemeStorageKey, joinedColorSchemes, defaultMode, storageWindow]);\n  return _extends({}, state, {\n    colorScheme,\n    setMode,\n    setColorScheme\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "DEFAULT_MODE_STORAGE_KEY", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "getSystemMode", "mode", "window", "mql", "matchMedia", "matches", "undefined", "processState", "state", "callback", "systemMode", "getColorScheme", "lightColorScheme", "darkColorScheme", "initializeValue", "key", "defaultValue", "value", "localStorage", "getItem", "setItem", "e", "useCurrentColorScheme", "options", "defaultMode", "defaultLightColorScheme", "defaultDarkColorScheme", "supportedColorSchemes", "modeStorageKey", "colorSchemeStorageKey", "storageWindow", "joinedColorSchemes", "join", "setState", "useState", "initialMode", "concat", "colorScheme", "setMode", "useCallback", "currentState", "newMode", "setColorScheme", "includes", "console", "error", "newState", "newLightColorScheme", "light", "newDarkColorScheme", "dark", "handleMediaQuery", "event", "mediaListener", "useRef", "current", "useEffect", "handler", "arguments", "media", "addListener", "removeListener", "handleStorage", "newValue", "startsWith", "match", "endsWith", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nfunction initializeValue(key, defaultValue) {\n  if (typeof window === 'undefined') {\n    return undefined;\n  }\n  let value;\n  try {\n    value = localStorage.getItem(key) || undefined;\n    if (!value) {\n      // the first time that user enters the site.\n      localStorage.setItem(key, defaultValue);\n    }\n  } catch (e) {\n    // Unsupported\n  }\n  return value || defaultValue;\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const [state, setState] = React.useState(() => {\n    const initialMode = initializeValue(modeStorageKey, defaultMode);\n    const lightColorScheme = initializeValue(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n    const darkColorScheme = initializeValue(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode != null ? mode : defaultMode;\n      try {\n        localStorage.setItem(modeStorageKey, newMode);\n      } catch (e) {\n        // Unsupported\n      }\n      return _extends({}, currentState, {\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      });\n    });\n  }, [modeStorageKey, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        try {\n          localStorage.setItem(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n          localStorage.setItem(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n        } catch (e) {\n          // Unsupported\n        }\n        return _extends({}, currentState, {\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        });\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(`\\`${value}\\` does not exist in \\`theme.colorSchemes\\`.`);\n      } else {\n        setState(currentState => {\n          const newState = _extends({}, currentState);\n          processState(currentState, mode => {\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-${mode}`, value);\n            } catch (e) {\n              // Unsupported\n            }\n            if (mode === 'light') {\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = _extends({}, currentState);\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(`\\`${newLightColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-light`, newLightColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(`\\`${newDarkColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-dark`, newDarkColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, colorSchemeStorageKey, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event != null && event.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return _extends({}, currentState, {\n          systemMode\n        });\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    const handler = (...args) => mediaListener.current(...args);\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, []);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (storageWindow) {\n      const handleStorage = event => {\n        const value = event.newValue;\n        if (typeof event.key === 'string' && event.key.startsWith(colorSchemeStorageKey) && (!value || joinedColorSchemes.match(value))) {\n          // If the key is deleted, value will be null then reset color scheme to the default one.\n          if (event.key.endsWith('light')) {\n            setColorScheme({\n              light: value\n            });\n          }\n          if (event.key.endsWith('dark')) {\n            setColorScheme({\n              dark: value\n            });\n          }\n        }\n        if (event.key === modeStorageKey && (!value || ['light', 'dark', 'system'].includes(value))) {\n          setMode(value || defaultMode);\n        }\n      };\n      // For syncing color-scheme changes between iframes\n      storageWindow.addEventListener('storage', handleStorage);\n      return () => {\n        storageWindow.removeEventListener('storage', handleStorage);\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, modeStorageKey, colorSchemeStorageKey, joinedColorSchemes, defaultMode, storageWindow]);\n  return _extends({}, state, {\n    colorScheme,\n    setMode,\n    setColorScheme\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,EAAEC,gCAAgC,QAAQ,gDAAgD;AAC3H,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,KAAK,QAAQ,EAAE;IACtD,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;IAC7D,IAAID,GAAG,CAACE,OAAO,EAAE;MACf,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB;EACA,OAAOC,SAAS;AAClB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACrC,IAAID,KAAK,CAACP,IAAI,KAAK,OAAO,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,OAAO,EAAE;IACrF,OAAOD,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACA,IAAID,KAAK,CAACP,IAAI,KAAK,MAAM,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,MAAM,EAAE;IACnF,OAAOD,QAAQ,CAAC,MAAM,CAAC;EACzB;EACA,OAAOH,SAAS;AAClB;AACA,OAAO,SAASK,cAAcA,CAACH,KAAK,EAAE;EACpC,OAAOD,YAAY,CAACC,KAAK,EAAEP,IAAI,IAAI;IACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOO,KAAK,CAACI,gBAAgB;IAC/B;IACA,IAAIX,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOO,KAAK,CAACK,eAAe;IAC9B;IACA,OAAOP,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,SAASQ,eAAeA,CAACC,GAAG,EAAEC,YAAY,EAAE;EAC1C,IAAI,OAAOd,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOI,SAAS;EAClB;EACA,IAAIW,KAAK;EACT,IAAI;IACFA,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACJ,GAAG,CAAC,IAAIT,SAAS;IAC9C,IAAI,CAACW,KAAK,EAAE;MACV;MACAC,YAAY,CAACE,OAAO,CAACL,GAAG,EAAEC,YAAY,CAAC;IACzC;EACF,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV;EAAA;EAEF,OAAOJ,KAAK,IAAID,YAAY;AAC9B;AACA,eAAe,SAASM,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,OAAO;IACrBC,uBAAuB;IACvBC,sBAAsB;IACtBC,qBAAqB,GAAG,EAAE;IAC1BC,cAAc,GAAG9B,wBAAwB;IACzC+B,qBAAqB,GAAG9B,gCAAgC;IACxD+B,aAAa,GAAG,OAAO5B,MAAM,KAAK,WAAW,GAAGI,SAAS,GAAGJ;EAC9D,CAAC,GAAGqB,OAAO;EACX,MAAMQ,kBAAkB,GAAGJ,qBAAqB,CAACK,IAAI,CAAC,GAAG,CAAC;EAC1D,MAAM,CAACxB,KAAK,EAAEyB,QAAQ,CAAC,GAAGpC,KAAK,CAACqC,QAAQ,CAAC,MAAM;IAC7C,MAAMC,WAAW,GAAGrB,eAAe,CAACc,cAAc,EAAEJ,WAAW,CAAC;IAChE,MAAMZ,gBAAgB,GAAGE,eAAe,IAAAsB,MAAA,CAAIP,qBAAqB,aAAUJ,uBAAuB,CAAC;IACnG,MAAMZ,eAAe,GAAGC,eAAe,IAAAsB,MAAA,CAAIP,qBAAqB,YAASH,sBAAsB,CAAC;IAChG,OAAO;MACLzB,IAAI,EAAEkC,WAAW;MACjBzB,UAAU,EAAEV,aAAa,CAACmC,WAAW,CAAC;MACtCvB,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMwB,WAAW,GAAG1B,cAAc,CAACH,KAAK,CAAC;EACzC,MAAM8B,OAAO,GAAGzC,KAAK,CAAC0C,WAAW,CAACtC,IAAI,IAAI;IACxCgC,QAAQ,CAACO,YAAY,IAAI;MACvB,IAAIvC,IAAI,KAAKuC,YAAY,CAACvC,IAAI,EAAE;QAC9B;QACA,OAAOuC,YAAY;MACrB;MACA,MAAMC,OAAO,GAAGxC,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGuB,WAAW;MACjD,IAAI;QACFN,YAAY,CAACE,OAAO,CAACQ,cAAc,EAAEa,OAAO,CAAC;MAC/C,CAAC,CAAC,OAAOpB,CAAC,EAAE;QACV;MAAA;MAEF,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAE4C,YAAY,EAAE;QAChCvC,IAAI,EAAEwC,OAAO;QACb/B,UAAU,EAAEV,aAAa,CAACyC,OAAO;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACb,cAAc,EAAEJ,WAAW,CAAC,CAAC;EACjC,MAAMkB,cAAc,GAAG7C,KAAK,CAAC0C,WAAW,CAACtB,KAAK,IAAI;IAChD,IAAI,CAACA,KAAK,EAAE;MACVgB,QAAQ,CAACO,YAAY,IAAI;QACvB,IAAI;UACFtB,YAAY,CAACE,OAAO,IAAAgB,MAAA,CAAIP,qBAAqB,aAAUJ,uBAAuB,CAAC;UAC/EP,YAAY,CAACE,OAAO,IAAAgB,MAAA,CAAIP,qBAAqB,YAASH,sBAAsB,CAAC;QAC/E,CAAC,CAAC,OAAOL,CAAC,EAAE;UACV;QAAA;QAEF,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAE4C,YAAY,EAAE;UAChC5B,gBAAgB,EAAEa,uBAAuB;UACzCZ,eAAe,EAAEa;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAIA,KAAK,IAAI,CAACc,kBAAkB,CAACY,QAAQ,CAAC1B,KAAK,CAAC,EAAE;QAChD2B,OAAO,CAACC,KAAK,KAAAT,MAAA,CAAMnB,KAAK,8CAA8C,CAAC;MACzE,CAAC,MAAM;QACLgB,QAAQ,CAACO,YAAY,IAAI;UACvB,MAAMM,QAAQ,GAAGlD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,YAAY,CAAC;UAC3CjC,YAAY,CAACiC,YAAY,EAAEvC,IAAI,IAAI;YACjC,IAAI;cACFiB,YAAY,CAACE,OAAO,IAAAgB,MAAA,CAAIP,qBAAqB,OAAAO,MAAA,CAAInC,IAAI,GAAIgB,KAAK,CAAC;YACjE,CAAC,CAAC,OAAOI,CAAC,EAAE;cACV;YAAA;YAEF,IAAIpB,IAAI,KAAK,OAAO,EAAE;cACpB6C,QAAQ,CAAClC,gBAAgB,GAAGK,KAAK;YACnC;YACA,IAAIhB,IAAI,KAAK,MAAM,EAAE;cACnB6C,QAAQ,CAACjC,eAAe,GAAGI,KAAK;YAClC;UACF,CAAC,CAAC;UACF,OAAO6B,QAAQ;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLb,QAAQ,CAACO,YAAY,IAAI;QACvB,MAAMM,QAAQ,GAAGlD,QAAQ,CAAC,CAAC,CAAC,EAAE4C,YAAY,CAAC;QAC3C,MAAMO,mBAAmB,GAAG9B,KAAK,CAAC+B,KAAK,KAAK,IAAI,GAAGvB,uBAAuB,GAAGR,KAAK,CAAC+B,KAAK;QACxF,MAAMC,kBAAkB,GAAGhC,KAAK,CAACiC,IAAI,KAAK,IAAI,GAAGxB,sBAAsB,GAAGT,KAAK,CAACiC,IAAI;QACpF,IAAIH,mBAAmB,EAAE;UACvB,IAAI,CAAChB,kBAAkB,CAACY,QAAQ,CAACI,mBAAmB,CAAC,EAAE;YACrDH,OAAO,CAACC,KAAK,KAAAT,MAAA,CAAMW,mBAAmB,8CAA8C,CAAC;UACvF,CAAC,MAAM;YACLD,QAAQ,CAAClC,gBAAgB,GAAGmC,mBAAmB;YAC/C,IAAI;cACF7B,YAAY,CAACE,OAAO,IAAAgB,MAAA,CAAIP,qBAAqB,aAAUkB,mBAAmB,CAAC;YAC7E,CAAC,CAAC,OAAOF,KAAK,EAAE;cACd;YAAA;UAEJ;QACF;QACA,IAAII,kBAAkB,EAAE;UACtB,IAAI,CAAClB,kBAAkB,CAACY,QAAQ,CAACM,kBAAkB,CAAC,EAAE;YACpDL,OAAO,CAACC,KAAK,KAAAT,MAAA,CAAMa,kBAAkB,8CAA8C,CAAC;UACtF,CAAC,MAAM;YACLH,QAAQ,CAACjC,eAAe,GAAGoC,kBAAkB;YAC7C,IAAI;cACF/B,YAAY,CAACE,OAAO,IAAAgB,MAAA,CAAIP,qBAAqB,YAASoB,kBAAkB,CAAC;YAC3E,CAAC,CAAC,OAAOJ,KAAK,EAAE;cACd;YAAA;UAEJ;QACF;QACA,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACf,kBAAkB,EAAEF,qBAAqB,EAAEJ,uBAAuB,EAAEC,sBAAsB,CAAC,CAAC;EAChG,MAAMyB,gBAAgB,GAAGtD,KAAK,CAAC0C,WAAW,CAACa,KAAK,IAAI;IAClD,IAAI5C,KAAK,CAACP,IAAI,KAAK,QAAQ,EAAE;MAC3BgC,QAAQ,CAACO,YAAY,IAAI;QACvB,MAAM9B,UAAU,GAAG0C,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC/C,OAAO,GAAG,MAAM,GAAG,OAAO;;QAEpE;QACA,IAAImC,YAAY,CAAC9B,UAAU,KAAKA,UAAU,EAAE;UAC1C,OAAO8B,YAAY;QACrB;QACA,OAAO5C,QAAQ,CAAC,CAAC,CAAC,EAAE4C,YAAY,EAAE;UAChC9B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,KAAK,CAACP,IAAI,CAAC,CAAC;;EAEhB;EACA,MAAMoD,aAAa,GAAGxD,KAAK,CAACyD,MAAM,CAACH,gBAAgB,CAAC;EACpDE,aAAa,CAACE,OAAO,GAAGJ,gBAAgB;EACxCtD,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,MAAMC,OAAO,GAAG,SAAAA,CAAA;MAAA,OAAaJ,aAAa,CAACE,OAAO,CAAC,GAAAG,SAAO,CAAC;IAAA;;IAE3D;IACA,MAAMC,KAAK,GAAGzD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;;IAE/D;IACAuD,KAAK,CAACC,WAAW,CAACH,OAAO,CAAC;IAC1BA,OAAO,CAACE,KAAK,CAAC;IACd,OAAO,MAAM;MACXA,KAAK,CAACE,cAAc,CAACJ,OAAO,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5D,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAI1B,aAAa,EAAE;MACjB,MAAMgC,aAAa,GAAGV,KAAK,IAAI;QAC7B,MAAMnC,KAAK,GAAGmC,KAAK,CAACW,QAAQ;QAC5B,IAAI,OAAOX,KAAK,CAACrC,GAAG,KAAK,QAAQ,IAAIqC,KAAK,CAACrC,GAAG,CAACiD,UAAU,CAACnC,qBAAqB,CAAC,KAAK,CAACZ,KAAK,IAAIc,kBAAkB,CAACkC,KAAK,CAAChD,KAAK,CAAC,CAAC,EAAE;UAC/H;UACA,IAAImC,KAAK,CAACrC,GAAG,CAACmD,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC/BxB,cAAc,CAAC;cACbM,KAAK,EAAE/B;YACT,CAAC,CAAC;UACJ;UACA,IAAImC,KAAK,CAACrC,GAAG,CAACmD,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC9BxB,cAAc,CAAC;cACbQ,IAAI,EAAEjC;YACR,CAAC,CAAC;UACJ;QACF;QACA,IAAImC,KAAK,CAACrC,GAAG,KAAKa,cAAc,KAAK,CAACX,KAAK,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC0B,QAAQ,CAAC1B,KAAK,CAAC,CAAC,EAAE;UAC3FqB,OAAO,CAACrB,KAAK,IAAIO,WAAW,CAAC;QAC/B;MACF,CAAC;MACD;MACAM,aAAa,CAACqC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;MACxD,OAAO,MAAM;QACXhC,aAAa,CAACsC,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;MAC7D,CAAC;IACH;IACA,OAAOxD,SAAS;EAClB,CAAC,EAAE,CAACoC,cAAc,EAAEJ,OAAO,EAAEV,cAAc,EAAEC,qBAAqB,EAAEE,kBAAkB,EAAEP,WAAW,EAAEM,aAAa,CAAC,CAAC;EACpH,OAAOlC,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAE;IACzB6B,WAAW;IACXC,OAAO;IACPI;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}