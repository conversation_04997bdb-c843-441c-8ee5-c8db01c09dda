{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nexport function withPane(props, context) {\n  var _props$pane;\n  const pane = (_props$pane = props.pane) !== null && _props$pane !== void 0 ? _props$pane : context.pane;\n  return pane ? _objectSpread(_objectSpread({}, props), {}, {\n    pane\n  }) : props;\n}", "map": {"version": 3, "names": ["with<PERSON>ane", "props", "context", "_props$pane", "pane", "_objectSpread"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/pane.js"], "sourcesContent": ["export function withPane(props, context) {\n    const pane = props.pane ?? context.pane;\n    return pane ? {\n        ...props,\n        pane\n    } : props;\n}\n"], "mappings": ";AAAA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAAA,IAAAC,WAAA;EACrC,MAAMC,IAAI,IAAAD,WAAA,GAAGF,KAAK,CAACG,IAAI,cAAAD,WAAA,cAAAA,WAAA,GAAID,OAAO,CAACE,IAAI;EACvC,OAAOA,IAAI,GAAAC,aAAA,CAAAA,aAAA,KACJJ,KAAK;IACRG;EAAI,KACJH,KAAK;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}