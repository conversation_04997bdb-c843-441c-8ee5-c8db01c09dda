{"ast": null, "code": "export function updateGridLayer(layer, props, prevProps) {\n  const {\n    opacity,\n    zIndex\n  } = props;\n  if (opacity != null && opacity !== prevProps.opacity) {\n    layer.setOpacity(opacity);\n  }\n  if (zIndex != null && zIndex !== prevProps.zIndex) {\n    layer.setZIndex(zIndex);\n  }\n}", "map": {"version": 3, "names": ["updateGridLayer", "layer", "props", "prevProps", "opacity", "zIndex", "setOpacity", "setZIndex"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/grid-layer.js"], "sourcesContent": ["export function updateGridLayer(layer, props, prevProps) {\n    const { opacity , zIndex  } = props;\n    if (opacity != null && opacity !== prevProps.opacity) {\n        layer.setOpacity(opacity);\n    }\n    if (zIndex != null && zIndex !== prevProps.zIndex) {\n        layer.setZIndex(zIndex);\n    }\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACrD,MAAM;IAAEC,OAAO;IAAGC;EAAQ,CAAC,GAAGH,KAAK;EACnC,IAAIE,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAKD,SAAS,CAACC,OAAO,EAAE;IAClDH,KAAK,CAACK,UAAU,CAACF,OAAO,CAAC;EAC7B;EACA,IAAIC,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAKF,SAAS,CAACE,MAAM,EAAE;IAC/CJ,KAAK,CAACM,SAAS,CAACF,MAAM,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}