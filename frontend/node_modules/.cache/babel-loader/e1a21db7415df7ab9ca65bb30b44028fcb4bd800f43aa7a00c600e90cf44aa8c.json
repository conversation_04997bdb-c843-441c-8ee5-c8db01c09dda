{"ast": null, "code": "'use client';\n\nexport { default } from './Grow';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/material/Grow/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Grow';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}