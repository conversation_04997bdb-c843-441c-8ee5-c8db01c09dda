{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { createContext, useContext } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n  return Object.freeze({\n    __version: CONTEXT_VERSION,\n    map\n  });\n}\nexport function extendContext(source, extra) {\n  return Object.freeze(_objectSpread(_objectSpread({}, source), extra));\n}\nexport const LeafletContext = createContext(null);\nexport const LeafletProvider = LeafletContext.Provider;\nexport function useLeafletContext() {\n  const context = useContext(LeafletContext);\n  if (context == null) {\n    throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n  }\n  return context;\n}", "map": {"version": 3, "names": ["createContext", "useContext", "CONTEXT_VERSION", "createLeafletContext", "map", "Object", "freeze", "__version", "extendContext", "source", "extra", "_objectSpread", "LeafletContext", "LeafletProvider", "Provider", "useLeafletContext", "context", "Error"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/context.js"], "sourcesContent": ["import { createContext, useContext } from 'react';\nexport const CONTEXT_VERSION = 1;\nexport function createLeafletContext(map) {\n    return Object.freeze({\n        __version: CONTEXT_VERSION,\n        map\n    });\n}\nexport function extendContext(source, extra) {\n    return Object.freeze({\n        ...source,\n        ...extra\n    });\n}\nexport const LeafletContext = createContext(null);\nexport const LeafletProvider = LeafletContext.Provider;\nexport function useLeafletContext() {\n    const context = useContext(LeafletContext);\n    if (context == null) {\n        throw new Error('No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>');\n    }\n    return context;\n}\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,OAAO,MAAMC,eAAe,GAAG,CAAC;AAChC,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtC,OAAOC,MAAM,CAACC,MAAM,CAAC;IACjBC,SAAS,EAAEL,eAAe;IAC1BE;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASI,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACzC,OAAOL,MAAM,CAACC,MAAM,CAAAK,aAAA,CAAAA,aAAA,KACbF,MAAM,GACNC,KAAK,CACX,CAAC;AACN;AACA,OAAO,MAAME,cAAc,GAAGZ,aAAa,CAAC,IAAI,CAAC;AACjD,OAAO,MAAMa,eAAe,GAAGD,cAAc,CAACE,QAAQ;AACtD,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAChC,MAAMC,OAAO,GAAGf,UAAU,CAACW,cAAc,CAAC;EAC1C,IAAII,OAAO,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIC,KAAK,CAAC,6FAA6F,CAAC;EAClH;EACA,OAAOD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}