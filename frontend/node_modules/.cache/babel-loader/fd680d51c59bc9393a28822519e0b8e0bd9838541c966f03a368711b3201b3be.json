{"ast": null, "code": "// We need to centralize the zIndex definitions as they work\n// like global values in the browser.\nconst zIndex = {\n  mobileStepper: 1000,\n  fab: 1050,\n  speedDial: 1050,\n  appBar: 1100,\n  drawer: 1200,\n  modal: 1300,\n  snackbar: 1400,\n  tooltip: 1500\n};\nexport default zIndex;", "map": {"version": 3, "names": ["zIndex", "mobileStepper", "fab", "speedDial", "appBar", "drawer", "modal", "snackbar", "tooltip"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/material/styles/zIndex.js"], "sourcesContent": ["// We need to centralize the zIndex definitions as they work\n// like global values in the browser.\nconst zIndex = {\n  mobileStepper: 1000,\n  fab: 1050,\n  speedDial: 1050,\n  appBar: 1100,\n  drawer: 1200,\n  modal: 1300,\n  snackbar: 1400,\n  tooltip: 1500\n};\nexport default zIndex;"], "mappings": "AAAA;AACA;AACA,MAAMA,MAAM,GAAG;EACbC,aAAa,EAAE,IAAI;EACnBC,GAAG,EAAE,IAAI;EACTC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE;AACX,CAAC;AACD,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}