{"ast": null, "code": "import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOveraly({\n  bounds,\n  url,\n  ...options\n}, ctx) {\n  const overlay = new LeafletImageOverlay(url, bounds, options);\n  return createElementObject(overlay, extendContext(ctx, {\n    overlayContainer: overlay\n  }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n  updateMediaOverlay(overlay, props, prevProps);\n  if (props.bounds !== prevProps.bounds) {\n    const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n    overlay.setBounds(bounds);\n  }\n  if (props.url !== prevProps.url) {\n    overlay.setUrl(props.url);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "updateMediaOverlay", "LatLngBounds", "ImageOverlay", "LeafletImageOverlay", "createImageOveraly", "bounds", "url", "options", "ctx", "overlay", "overlayContainer", "updateImageOverlay", "props", "prevProps", "setBounds", "setUrl"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/react-leaflet/lib/ImageOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOveraly({ bounds , url , ...options }, ctx) {\n    const overlay = new LeafletImageOverlay(url, bounds, options);\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (props.bounds !== prevProps.bounds) {\n        const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n        overlay.setBounds(bounds);\n    }\n    if (props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,qBAAqB;AAClH,SAASC,YAAY,EAAEC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC3E,OAAO,MAAMD,YAAY,GAAGJ,oBAAoB,CAAC,SAASM,kBAAkBA,CAAC;EAAEC,MAAM;EAAGC,GAAG;EAAG,GAAGC;AAAQ,CAAC,EAAEC,GAAG,EAAE;EAC7G,MAAMC,OAAO,GAAG,IAAIN,mBAAmB,CAACG,GAAG,EAAED,MAAM,EAAEE,OAAO,CAAC;EAC7D,OAAOV,mBAAmB,CAACY,OAAO,EAAEV,aAAa,CAACS,GAAG,EAAE;IACnDE,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,kBAAkBA,CAACF,OAAO,EAAEG,KAAK,EAAEC,SAAS,EAAE;EACtDb,kBAAkB,CAACS,OAAO,EAAEG,KAAK,EAAEC,SAAS,CAAC;EAC7C,IAAID,KAAK,CAACP,MAAM,KAAKQ,SAAS,CAACR,MAAM,EAAE;IACnC,MAAMA,MAAM,GAAGO,KAAK,CAACP,MAAM,YAAYJ,YAAY,GAAGW,KAAK,CAACP,MAAM,GAAG,IAAIJ,YAAY,CAACW,KAAK,CAACP,MAAM,CAAC;IACnGI,OAAO,CAACK,SAAS,CAACT,MAAM,CAAC;EAC7B;EACA,IAAIO,KAAK,CAACN,GAAG,KAAKO,SAAS,CAACP,GAAG,EAAE;IAC7BG,OAAO,CAACM,MAAM,CAACH,KAAK,CAACN,GAAG,CAAC;EAC7B;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}