{"ast": null, "code": "// src/utils.ts\nvar Any = class {};\nexport { Any };", "map": {"version": 3, "names": ["Any"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@react-spring/types/src/utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-empty-interface */\n/**\n * MIT License\n * Copyright (c) <PERSON>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\nimport * as React from 'react'\nimport { ReactElement, MutableRefObject } from 'react'\n\n/** Ensure each type of `T` is an array */\nexport type Arrify<T> = [T, T] extends [infer T, infer DT]\n  ? DT extends ReadonlyArray<any>\n    ? Array<DT[number]> extends DT\n      ? ReadonlyArray<T extends ReadonlyArray<infer U> ? U : T>\n      : DT\n    : ReadonlyArray<T extends ReadonlyArray<infer U> ? U : T>\n  : never\n\n/** Override the property types of `A` with `B` and merge any new properties */\nexport type Merge<A, B> = Remap<\n  { [P in keyof A]: P extends keyof B ? B[P] : A[P] } & Omit<B, keyof A>\n>\n\n/** Return the keys of `T` with values that are assignable to `U` */\nexport type AssignableKeys<T, U> = T extends object\n  ? U extends object\n    ? {\n        [P in Extract<keyof T, keyof U>]: T[P] extends U[P] ? P : never\n      }[Extract<keyof T, keyof U>]\n    : never\n  : never\n\n/** Better type errors for overloads with generic types */\nexport type Constrain<T, U> = [T] extends [Any] ? U : [T] extends [U] ? T : U\n\n/** Try to simplify `&` out of an object type */\nexport type Remap<T> = {} & {\n  [P in keyof T]: T[P]\n}\n\nexport type Pick<T, K extends keyof T> = {} & {\n  [P in K]: T[P]\n}\n\nexport type Omit<T, K> = Pick<T, Exclude<keyof T, K>>\n\nexport type Partial<T> = {} & {\n  [P in keyof T]?: T[P] | undefined\n}\n\nexport type Overwrite<T, U> = Remap<Omit<T, keyof U> & U>\n\nexport type MergeUnknown<T, U> = Remap<T & Omit<U, keyof T>>\n\nexport type MergeDefaults<T extends object, U extends Partial<T>> = Remap<\n  Omit<T, keyof U> & Partial<Pick<T, Extract<keyof U, keyof T>>>\n>\n\nexport type OneOrMore<T> = T | readonly T[]\n\nexport type Falsy = false | null | undefined\n\n// https://github.com/microsoft/TypeScript/issues/14829#issuecomment-504042546\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type StaticProps<T> = Omit<T, keyof T & 'prototype'>\n\nexport interface Lookup<T = any> {\n  [key: string]: T\n}\n\nexport type LoosePick<T, K> = {} & Pick<T, K & keyof T>\n\n/** Intersected with other object types to allow for unknown properties */\nexport interface UnknownProps extends Lookup<unknown> {}\n\n/** Use `[T] extends [Any]` to know if a type parameter is `any` */\nexport class Any {\n  // @ts-expect-error – dont worry about this.\n  private _: never\n}\n\nexport type AnyFn<In extends ReadonlyArray<any> = any[], Out = any> = (\n  ...args: In\n) => Out\n\n/** Ensure the given type is an object type */\nexport type ObjectType<T> = T extends object ? T : {}\n\n/** Intersect a union of objects but merge property types with _unions_ */\nexport type ObjectFromUnion<T extends object> = Remap<{\n  [P in keyof Intersect<T>]: T extends infer U\n    ? P extends keyof U\n      ? U[P]\n      : never\n    : never\n}>\n\n/** Convert a union to an intersection */\ntype Intersect<U> = (U extends any ? (k: U) => void : never) extends (\n  k: infer I\n) => void\n  ? I\n  : never\n\nexport type AllKeys<T> = T extends any ? keyof T : never\n\nexport type Exclusive<T> =\n  AllKeys<T> extends infer K\n    ? T extends any\n      ? Remap<\n          LoosePick<T, K> & {\n            [P in Exclude<K & keyof any, keyof T>]?: undefined\n          }\n        >\n      : never\n    : never\n/** An object that needs to be manually disposed of */\nexport interface Disposable {\n  dispose(): void\n}\n\n// react.d.ts\nexport type RefProp<T> = MutableRefObject<T | null | undefined>\n\n// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34237\nexport type ElementType<P = any> =\n  | React.ElementType<P>\n  | LeafFunctionComponent<P>\n\n// Function component without children\ntype LeafFunctionComponent<P> = {\n  (props: P): ReactElement | null\n  displayName?: string\n}\n\nexport type ComponentPropsWithRef<T extends ElementType> =\n  T extends React.ComponentClass<infer P>\n    ? React.PropsWithoutRef<P> & React.RefAttributes<InstanceType<T>>\n    : React.PropsWithRef<React.ComponentProps<T>>\n\n// In @types/react, a \"children\" prop is required by the \"FunctionComponent\" type.\nexport type ComponentType<P = {}> =\n  | React.ComponentClass<P>\n  | LeafFunctionComponent<P>\n"], "mappings": ";AA+FO,IAAMA,GAAA,GAAN,MAAU,EAGjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}