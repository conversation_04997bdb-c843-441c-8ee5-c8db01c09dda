{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"cssVarPrefix\", \"shouldSkipGeneratingVar\"];\nimport prepareCssVars from './prepareCssVars';\nfunction createCssVarsTheme(theme) {\n  const {\n      cssVarPrefix,\n      shouldSkipGeneratingVar\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  return _extends({}, theme, prepareCssVars(otherTheme, {\n    prefix: cssVarPrefix,\n    shouldSkipGeneratingVar\n  }));\n}\nexport default createCssVarsTheme;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "prepareCssVars", "createCssVarsTheme", "theme", "cssVarPrefix", "shouldSkipGeneratingVar", "otherTheme", "prefix"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/system/esm/cssVars/createCssVarsTheme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"cssVarPrefix\", \"shouldSkipGeneratingVar\"];\nimport prepareCssVars from './prepareCssVars';\nfunction createCssVarsTheme(theme) {\n  const {\n      cssVarPrefix,\n      shouldSkipGeneratingVar\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  return _extends({}, theme, prepareCssVars(otherTheme, {\n    prefix: cssVarPrefix,\n    shouldSkipGeneratingVar\n  }));\n}\nexport default createCssVarsTheme;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,yBAAyB,CAAC;AAC7D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,MAAM;MACFC,YAAY;MACZC;IACF,CAAC,GAAGF,KAAK;IACTG,UAAU,GAAGP,6BAA6B,CAACI,KAAK,EAAEH,SAAS,CAAC;EAC9D,OAAOF,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAEF,cAAc,CAACK,UAAU,EAAE;IACpDC,MAAM,EAAEH,YAAY;IACpBC;EACF,CAAC,CAAC,CAAC;AACL;AACA,eAAeH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}