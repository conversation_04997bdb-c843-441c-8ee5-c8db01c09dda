{"ast": null, "code": "/**\n * Neuromorphic design utilities for consistent styling across the application\n */\n\n// Base neuromorphic shadow effect\nexport const getNeuromorphicShadow = (color = '#f0f4f8', intensity = 1, inset = false) => {\n  const lightShadow = `rgba(255, 255, 255, ${0.5 * intensity})`;\n  const darkShadow = `rgba(174, 174, 192, ${0.3 * intensity})`;\n  const insetPrefix = inset ? 'inset ' : '';\n  return `\n    ${insetPrefix}6px 6px 12px ${darkShadow},\n    ${insetPrefix}-6px -6px 12px ${lightShadow}\n  `;\n};\n\n// Pressed effect for buttons and interactive elements\nexport const getPressedEffect = (color = '#f0f4f8') => {\n  return `\n    inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n    inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n  `;\n};\n\n// Neuromorphic styles for different component types\nexport const neuromorphicStyles = {\n  // Card style\n  card: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '16px',\n    boxShadow: getNeuromorphicShadow(),\n    transition: 'all 0.3s ease',\n    border: 'none',\n    '&:hover': {\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 1.2)\n    }\n  },\n  // Button style\n  button: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '12px',\n    boxShadow: getNeuromorphicShadow(),\n    transition: 'all 0.2s ease',\n    border: 'none',\n    '&:hover': {\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 1.2)\n    },\n    '&:active': {\n      boxShadow: getPressedEffect()\n    }\n  },\n  // Input field style\n  input: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '12px',\n    boxShadow: `inset 2px 2px 5px rgba(174, 174, 192, 0.2),\n                inset -2px -2px 5px rgba(255, 255, 255, 0.7)`,\n    border: 'none',\n    transition: 'all 0.3s ease',\n    '&:focus': {\n      boxShadow: `inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n                  inset -4px -4px 8px rgba(255, 255, 255, 0.5)`,\n      outline: 'none'\n    }\n  },\n  // Toggle/switch style\n  toggle: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '24px',\n    boxShadow: getNeuromorphicShadow(),\n    '& .MuiSwitch-thumb': {\n      boxShadow: '2px 2px 4px rgba(174, 174, 192, 0.3)'\n    }\n  },\n  // Slider style\n  slider: {\n    '& .MuiSlider-thumb': {\n      backgroundColor: '#f0f4f8',\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 0.8)\n    },\n    '& .MuiSlider-track': {\n      boxShadow: 'inset 1px 1px 2px rgba(174, 174, 192, 0.3)'\n    },\n    '& .MuiSlider-rail': {\n      boxShadow: 'inset 2px 2px 4px rgba(174, 174, 192, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.5)'\n    }\n  },\n  // Container style\n  container: {\n    backgroundColor: '#e6eef8',\n    borderRadius: '24px',\n    padding: '24px',\n    boxShadow: `\n      inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n      inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n    `\n  }\n};\n\n// Generate neuromorphic color palette\nexport const getNeuromorphicPalette = (baseColor = '#3a86ff') => {\n  return {\n    primary: {\n      main: baseColor,\n      light: '#e6eef8',\n      dark: '#2a6bc9',\n      contrastText: '#ffffff'\n    },\n    secondary: {\n      main: '#4cc9f0',\n      light: '#e6f7fc',\n      dark: '#3aa8cc',\n      contrastText: '#ffffff'\n    },\n    error: {\n      main: '#ff595e',\n      light: '#ffeeee',\n      dark: '#d04649',\n      contrastText: '#ffffff'\n    },\n    warning: {\n      main: '#ff9f1c',\n      light: '#fff4e6',\n      dark: '#d18016',\n      contrastText: '#ffffff'\n    },\n    success: {\n      main: '#06d6a0',\n      light: '#e6f9f5',\n      dark: '#05b184',\n      contrastText: '#ffffff'\n    },\n    background: {\n      default: '#e6eef8',\n      paper: '#f0f4f8'\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#2d3748',\n      disabled: '#718096',\n      contrast: '#ffffff'\n    }\n  };\n};", "map": {"version": 3, "names": ["getNeuromorphicShadow", "color", "intensity", "inset", "lightShadow", "darkShadow", "insetPrefix", "getPressedEffect", "neuromorphicStyles", "card", "backgroundColor", "borderRadius", "boxShadow", "transition", "border", "button", "input", "outline", "toggle", "slider", "container", "padding", "getNeuromorphicPalette", "baseColor", "primary", "main", "light", "dark", "contrastText", "secondary", "error", "warning", "success", "background", "default", "paper", "text", "disabled", "contrast"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js"], "sourcesContent": ["/**\n * Neuromorphic design utilities for consistent styling across the application\n */\n\n// Base neuromorphic shadow effect\nexport const getNeuromorphicShadow = (color = '#f0f4f8', intensity = 1, inset = false) => {\n  const lightShadow = `rgba(255, 255, 255, ${0.5 * intensity})`;\n  const darkShadow = `rgba(174, 174, 192, ${0.3 * intensity})`;\n  const insetPrefix = inset ? 'inset ' : '';\n\n  return `\n    ${insetPrefix}6px 6px 12px ${darkShadow},\n    ${insetPrefix}-6px -6px 12px ${lightShadow}\n  `;\n};\n\n// Pressed effect for buttons and interactive elements\nexport const getPressedEffect = (color = '#f0f4f8') => {\n  return `\n    inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n    inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n  `;\n};\n\n// Neuromorphic styles for different component types\nexport const neuromorphicStyles = {\n  // Card style\n  card: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '16px',\n    boxShadow: getNeuromorphicShadow(),\n    transition: 'all 0.3s ease',\n    border: 'none',\n    '&:hover': {\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 1.2),\n    }\n  },\n\n  // Button style\n  button: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '12px',\n    boxShadow: getNeuromorphicShadow(),\n    transition: 'all 0.2s ease',\n    border: 'none',\n    '&:hover': {\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 1.2),\n    },\n    '&:active': {\n      boxShadow: getPressedEffect(),\n    }\n  },\n\n  // Input field style\n  input: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '12px',\n    boxShadow: `inset 2px 2px 5px rgba(174, 174, 192, 0.2),\n                inset -2px -2px 5px rgba(255, 255, 255, 0.7)`,\n    border: 'none',\n    transition: 'all 0.3s ease',\n    '&:focus': {\n      boxShadow: `inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n                  inset -4px -4px 8px rgba(255, 255, 255, 0.5)`,\n      outline: 'none'\n    }\n  },\n\n  // Toggle/switch style\n  toggle: {\n    backgroundColor: '#f0f4f8',\n    borderRadius: '24px',\n    boxShadow: getNeuromorphicShadow(),\n    '& .MuiSwitch-thumb': {\n      boxShadow: '2px 2px 4px rgba(174, 174, 192, 0.3)',\n    }\n  },\n\n  // Slider style\n  slider: {\n    '& .MuiSlider-thumb': {\n      backgroundColor: '#f0f4f8',\n      boxShadow: getNeuromorphicShadow('#f0f4f8', 0.8),\n    },\n    '& .MuiSlider-track': {\n      boxShadow: 'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',\n    },\n    '& .MuiSlider-rail': {\n      boxShadow: 'inset 2px 2px 4px rgba(174, 174, 192, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.5)',\n    }\n  },\n\n  // Container style\n  container: {\n    backgroundColor: '#e6eef8',\n    borderRadius: '24px',\n    padding: '24px',\n    boxShadow: `\n      inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n      inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n    `\n  }\n};\n\n// Generate neuromorphic color palette\nexport const getNeuromorphicPalette = (baseColor = '#3a86ff') => {\n  return {\n    primary: {\n      main: baseColor,\n      light: '#e6eef8',\n      dark: '#2a6bc9',\n      contrastText: '#ffffff'\n    },\n    secondary: {\n      main: '#4cc9f0',\n      light: '#e6f7fc',\n      dark: '#3aa8cc',\n      contrastText: '#ffffff'\n    },\n    error: {\n      main: '#ff595e',\n      light: '#ffeeee',\n      dark: '#d04649',\n      contrastText: '#ffffff'\n    },\n    warning: {\n      main: '#ff9f1c',\n      light: '#fff4e6',\n      dark: '#d18016',\n      contrastText: '#ffffff'\n    },\n    success: {\n      main: '#06d6a0',\n      light: '#e6f9f5',\n      dark: '#05b184',\n      contrastText: '#ffffff'\n    },\n    background: {\n      default: '#e6eef8',\n      paper: '#f0f4f8'\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#2d3748',\n      disabled: '#718096',\n      contrast: '#ffffff'\n    }\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,qBAAqB,GAAGA,CAACC,KAAK,GAAG,SAAS,EAAEC,SAAS,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,KAAK;EACxF,MAAMC,WAAW,GAAG,uBAAuB,GAAG,GAAGF,SAAS,GAAG;EAC7D,MAAMG,UAAU,GAAG,uBAAuB,GAAG,GAAGH,SAAS,GAAG;EAC5D,MAAMI,WAAW,GAAGH,KAAK,GAAG,QAAQ,GAAG,EAAE;EAEzC,OAAO;AACT,MAAMG,WAAW,gBAAgBD,UAAU;AAC3C,MAAMC,WAAW,kBAAkBF,WAAW;AAC9C,GAAG;AACH,CAAC;;AAED;AACA,OAAO,MAAMG,gBAAgB,GAAGA,CAACN,KAAK,GAAG,SAAS,KAAK;EACrD,OAAO;AACT;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA,OAAO,MAAMO,kBAAkB,GAAG;EAChC;EACAC,IAAI,EAAE;IACJC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAEZ,qBAAqB,CAAC,CAAC;IAClCa,UAAU,EAAE,eAAe;IAC3BC,MAAM,EAAE,MAAM;IACd,SAAS,EAAE;MACTF,SAAS,EAAEZ,qBAAqB,CAAC,SAAS,EAAE,GAAG;IACjD;EACF,CAAC;EAED;EACAe,MAAM,EAAE;IACNL,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAEZ,qBAAqB,CAAC,CAAC;IAClCa,UAAU,EAAE,eAAe;IAC3BC,MAAM,EAAE,MAAM;IACd,SAAS,EAAE;MACTF,SAAS,EAAEZ,qBAAqB,CAAC,SAAS,EAAE,GAAG;IACjD,CAAC;IACD,UAAU,EAAE;MACVY,SAAS,EAAEL,gBAAgB,CAAC;IAC9B;EACF,CAAC;EAED;EACAS,KAAK,EAAE;IACLN,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE;AACf,6DAA6D;IACzDE,MAAM,EAAE,MAAM;IACdD,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTD,SAAS,EAAE;AACjB,+DAA+D;MACzDK,OAAO,EAAE;IACX;EACF,CAAC;EAED;EACAC,MAAM,EAAE;IACNR,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAEZ,qBAAqB,CAAC,CAAC;IAClC,oBAAoB,EAAE;MACpBY,SAAS,EAAE;IACb;EACF,CAAC;EAED;EACAO,MAAM,EAAE;IACN,oBAAoB,EAAE;MACpBT,eAAe,EAAE,SAAS;MAC1BE,SAAS,EAAEZ,qBAAqB,CAAC,SAAS,EAAE,GAAG;IACjD,CAAC;IACD,oBAAoB,EAAE;MACpBY,SAAS,EAAE;IACb,CAAC;IACD,mBAAmB,EAAE;MACnBA,SAAS,EAAE;IACb;EACF,CAAC;EAED;EACAQ,SAAS,EAAE;IACTV,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBU,OAAO,EAAE,MAAM;IACfT,SAAS,EAAE;AACf;AACA;AACA;EACE;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,sBAAsB,GAAGA,CAACC,SAAS,GAAG,SAAS,KAAK;EAC/D,OAAO;IACLC,OAAO,EAAE;MACPC,IAAI,EAAEF,SAAS;MACfG,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;MACTJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDE,KAAK,EAAE;MACLL,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDG,OAAO,EAAE;MACPN,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDI,OAAO,EAAE;MACPP,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE;IAChB,CAAC;IACDK,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJZ,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE,SAAS;MACpBQ,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE;IACZ;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}