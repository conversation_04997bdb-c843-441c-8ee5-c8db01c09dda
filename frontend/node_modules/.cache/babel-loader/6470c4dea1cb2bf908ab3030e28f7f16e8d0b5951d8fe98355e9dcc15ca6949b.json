{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/globals.ts\nvar globals_exports = {};\n__export(globals_exports, {\n  assign: () => assign,\n  colors: () => colors,\n  createStringInterpolator: () => createStringInterpolator,\n  skipAnimation: () => skipAnimation,\n  to: () => to,\n  willAdvance: () => willAdvance\n});\nimport { raf } from \"@react-spring/rafz\";\n\n// src/helpers.ts\nfunction noop() {}\nvar defineHidden = (obj, key, value) => Object.defineProperty(obj, key, {\n  value,\n  writable: true,\n  configurable: true\n});\nvar is = {\n  arr: Array.isArray,\n  obj: a => !!a && a.constructor.name === \"Object\",\n  fun: a => typeof a === \"function\",\n  str: a => typeof a === \"string\",\n  num: a => typeof a === \"number\",\n  und: a => a === void 0\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nvar each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], \"\".concat(i));\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nvar toArray = a => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nvar flushCalls = function (queue) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  return flush(queue, fn => fn(...args));\n};\nvar isSSR = () => typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\n\n// src/globals.ts\nvar createStringInterpolator;\nvar to;\nvar colors = null;\nvar skipAnimation = false;\nvar willAdvance = noop;\nvar assign = globals => {\n  if (globals.to) to = globals.to;\n  if (globals.now) raf.now = globals.now;\n  if (globals.colors !== void 0) colors = globals.colors;\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator) createStringInterpolator = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance) willAdvance = globals.willAdvance;\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop;\n};\n\n// src/FrameLoop.ts\nimport { raf as raf2 } from \"@react-spring/rafz\";\nvar startQueue = /* @__PURE__ */new Set();\nvar currentFrame = [];\nvar prevFrame = [];\nvar priority = 0;\nvar frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  /** Advance the given animation on every frame until idle. */\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      raf2.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      raf2(advance);\n    }\n  },\n  /** Advance all animations by the given time. */\n  advance,\n  /** Call this when an animation's priority changes. */\n  sort(animation) {\n    if (priority) {\n      raf2.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  raf2(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(findIndex(currentFrame, other => other.priority > animation.priority), 0, animation);\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\n\n// src/clamp.ts\nvar clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n// src/colors.ts\nvar colors2 = {\n  transparent: 0,\n  aliceblue: 4042850303,\n  antiquewhite: 4209760255,\n  aqua: 16777215,\n  aquamarine: 2147472639,\n  azure: 4043309055,\n  beige: 4126530815,\n  bisque: 4293182719,\n  black: 255,\n  blanchedalmond: 4293643775,\n  blue: 65535,\n  blueviolet: 2318131967,\n  brown: 2771004159,\n  burlywood: 3736635391,\n  burntsienna: 3934150143,\n  cadetblue: 1604231423,\n  chartreuse: 2147418367,\n  chocolate: 3530104575,\n  coral: 4286533887,\n  cornflowerblue: 1687547391,\n  cornsilk: 4294499583,\n  crimson: 3692313855,\n  cyan: 16777215,\n  darkblue: 35839,\n  darkcyan: 9145343,\n  darkgoldenrod: 3095792639,\n  darkgray: 2846468607,\n  darkgreen: 6553855,\n  darkgrey: 2846468607,\n  darkkhaki: 3182914559,\n  darkmagenta: 2332068863,\n  darkolivegreen: 1433087999,\n  darkorange: 4287365375,\n  darkorchid: 2570243327,\n  darkred: 2332033279,\n  darksalmon: 3918953215,\n  darkseagreen: 2411499519,\n  darkslateblue: 1211993087,\n  darkslategray: 793726975,\n  darkslategrey: 793726975,\n  darkturquoise: 13554175,\n  darkviolet: 2483082239,\n  deeppink: 4279538687,\n  deepskyblue: 12582911,\n  dimgray: 1768516095,\n  dimgrey: 1768516095,\n  dodgerblue: 512819199,\n  firebrick: 2988581631,\n  floralwhite: 4294635775,\n  forestgreen: 579543807,\n  fuchsia: 4278255615,\n  gainsboro: 3705462015,\n  ghostwhite: 4177068031,\n  gold: 4292280575,\n  goldenrod: 3668254975,\n  gray: 2155905279,\n  green: 8388863,\n  greenyellow: 2919182335,\n  grey: 2155905279,\n  honeydew: 4043305215,\n  hotpink: 4285117695,\n  indianred: 3445382399,\n  indigo: 1258324735,\n  ivory: 4294963455,\n  khaki: 4041641215,\n  lavender: 3873897215,\n  lavenderblush: 4293981695,\n  lawngreen: 2096890111,\n  lemonchiffon: 4294626815,\n  lightblue: 2916673279,\n  lightcoral: 4034953471,\n  lightcyan: 3774873599,\n  lightgoldenrodyellow: 4210742015,\n  lightgray: 3553874943,\n  lightgreen: 2431553791,\n  lightgrey: 3553874943,\n  lightpink: 4290167295,\n  lightsalmon: 4288707327,\n  lightseagreen: 548580095,\n  lightskyblue: 2278488831,\n  lightslategray: 2005441023,\n  lightslategrey: 2005441023,\n  lightsteelblue: 2965692159,\n  lightyellow: 4294959359,\n  lime: 16711935,\n  limegreen: 852308735,\n  linen: 4210091775,\n  magenta: 4278255615,\n  maroon: 2147483903,\n  mediumaquamarine: 1724754687,\n  mediumblue: 52735,\n  mediumorchid: 3126187007,\n  mediumpurple: 2473647103,\n  mediumseagreen: 1018393087,\n  mediumslateblue: 2070474495,\n  mediumspringgreen: 16423679,\n  mediumturquoise: 1221709055,\n  mediumvioletred: 3340076543,\n  midnightblue: 421097727,\n  mintcream: 4127193855,\n  mistyrose: 4293190143,\n  moccasin: 4293178879,\n  navajowhite: 4292783615,\n  navy: 33023,\n  oldlace: 4260751103,\n  olive: 2155872511,\n  olivedrab: 1804477439,\n  orange: 4289003775,\n  orangered: 4282712319,\n  orchid: 3664828159,\n  palegoldenrod: 4008225535,\n  palegreen: 2566625535,\n  paleturquoise: 2951671551,\n  palevioletred: 3681588223,\n  papayawhip: 4293907967,\n  peachpuff: 4292524543,\n  peru: 3448061951,\n  pink: 4290825215,\n  plum: 3718307327,\n  powderblue: 2967529215,\n  purple: 2147516671,\n  rebeccapurple: 1714657791,\n  red: 4278190335,\n  rosybrown: 3163525119,\n  royalblue: 1097458175,\n  saddlebrown: 2336560127,\n  salmon: 4202722047,\n  sandybrown: 4104413439,\n  seagreen: 780883967,\n  seashell: 4294307583,\n  sienna: 2689740287,\n  silver: 3233857791,\n  skyblue: 2278484991,\n  slateblue: 1784335871,\n  slategray: 1887473919,\n  slategrey: 1887473919,\n  snow: 4294638335,\n  springgreen: 16744447,\n  steelblue: 1182971135,\n  tan: 3535047935,\n  teal: 8421631,\n  thistle: 3636451583,\n  tomato: 4284696575,\n  turquoise: 1088475391,\n  violet: 4001558271,\n  wheat: 4125012991,\n  white: 4294967295,\n  whitesmoke: 4126537215,\n  yellow: 4294902015,\n  yellowgreen: 2597139199\n};\n\n// src/colorMatchers.ts\nvar NUMBER = \"[-+]?\\\\d*\\\\.?\\\\d+\";\nvar PERCENTAGE = NUMBER + \"%\";\nfunction call() {\n  for (var _len2 = arguments.length, parts = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    parts[_key2] = arguments[_key2];\n  }\n  return \"\\\\(\\\\s*(\" + parts.join(\")\\\\s*,\\\\s*(\") + \")\\\\s*\\\\)\";\n}\nvar rgb = new RegExp(\"rgb\" + call(NUMBER, NUMBER, NUMBER));\nvar rgba = new RegExp(\"rgba\" + call(NUMBER, NUMBER, NUMBER, NUMBER));\nvar hsl = new RegExp(\"hsl\" + call(NUMBER, PERCENTAGE, PERCENTAGE));\nvar hsla = new RegExp(\"hsla\" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER));\nvar hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex6 = /^#([0-9a-fA-F]{6})$/;\nvar hex8 = /^#([0-9a-fA-F]{8})$/;\n\n// src/normalizeColor.ts\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === \"number\") {\n    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;\n  }\n  if (match = hex6.exec(color)) return parseInt(match[1] + \"ff\", 16) >>> 0;\n  if (colors && colors[color] !== void 0) {\n    return colors[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 |\n    // r\n    parse255(match[2]) << 16 |\n    // g\n    parse255(match[3]) << 8 |\n    // b\n    255) >>>\n    // a\n    0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 |\n    // r\n    parse255(match[2]) << 16 |\n    // g\n    parse255(match[3]) << 8 |\n    // b\n    parse1(match[4])) >>>\n    // a\n    0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(match[1] + match[1] +\n    // r\n    match[2] + match[2] +\n    // g\n    match[3] + match[3] +\n    // b\n    \"ff\",\n    // a\n    16) >>> 0;\n  }\n  if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(match[1] + match[1] +\n    // r\n    match[2] + match[2] +\n    // g\n    match[3] + match[3] +\n    // b\n    match[4] + match[4],\n    // a\n    16) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(parse360(match[1]),\n    // h\n    parsePercentage(match[2]),\n    // s\n    parsePercentage(match[3])\n    // l\n    ) | 255) >>>\n    // a\n    0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(parse360(match[1]),\n    // h\n    parsePercentage(match[2]),\n    // s\n    parsePercentage(match[3])\n    // l\n    ) | parse1(match[4])) >>>\n    // a\n    0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) return 0;\n  if (int > 255) return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) return 0;\n  if (num > 1) return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0) return 0;\n  if (int > 100) return 1;\n  return int / 100;\n}\n\n// src/colorToRgba.ts\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null) return input;\n  int32Color = int32Color || 0;\n  const r = (int32Color & 4278190080) >>> 24;\n  const g = (int32Color & 16711680) >>> 16;\n  const b = (int32Color & 65280) >>> 8;\n  const a = (int32Color & 255) / 255;\n  return \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(a, \")\");\n}\n\n// src/createInterpolator.ts\nvar createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || \"extend\";\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || \"extend\";\n  const easing = config.easing || (t => t);\n  return input => {\n    const range2 = findRange(input, inputRange);\n    return interpolate(input, inputRange[range2], inputRange[range2 + 1], outputRange[range2], outputRange[range2 + 1], easing, extrapolateLeft, extrapolateRight, config.map);\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === \"identity\") return result;else if (extrapolateLeft === \"clamp\") result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === \"identity\") return result;else if (extrapolateRight === \"clamp\") result = inputMax;\n  }\n  if (outputMin === outputMax) return outputMin;\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity) result = -result;else if (inputMax === Infinity) result = result - inputMin;else result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity) result = -result;else if (outputMax === Infinity) result = result + outputMin;else result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i) if (inputRange[i] >= input) break;\n  return i - 1;\n}\n\n// src/easings.ts\nvar steps = function (steps2) {\n  let direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"end\";\n  return progress2 => {\n    progress2 = direction === \"end\" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);\n    const expanded = progress2 * steps2;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return clamp(0, 1, rounded / steps2);\n  };\n};\nvar c1 = 1.70158;\nvar c2 = c1 * 1.525;\nvar c3 = c1 + 1;\nvar c4 = 2 * Math.PI / 3;\nvar c5 = 2 * Math.PI / 4.5;\nvar bounceOut = x => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nvar easings = {\n  linear: x => x,\n  easeInQuad: x => x * x,\n  easeOutQuad: x => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: x => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: x => x * x * x,\n  easeOutCubic: x => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: x => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: x => x * x * x * x,\n  easeOutQuart: x => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: x => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: x => x * x * x * x * x,\n  easeOutQuint: x => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: x => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: x => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: x => Math.sin(x * Math.PI / 2),\n  easeInOutSine: x => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: x => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: x => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: x => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: x => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: x => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: x => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: x => c3 * x * x * x - c1 * x * x,\n  easeOutBack: x => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: x => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: x => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: x => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: x => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: x => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: x => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\n\n// src/fluids.ts\nvar $get = Symbol.for(\"FluidValue.get\");\nvar $observers = Symbol.for(\"FluidValue.observers\");\nvar hasFluidValue = arg => Boolean(arg && arg[$get]);\nvar getFluidValue = arg => arg && arg[$get] ? arg[$get]() : arg;\nvar getFluidObservers = target => target[$observers] || null;\nfunction callFluidObserver(observer2, event) {\n  if (observer2.eventObserved) {\n    observer2.eventObserved(event);\n  } else {\n    observer2(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  const observers = target[$observers];\n  if (observers) {\n    observers.forEach(observer2 => {\n      callFluidObserver(observer2, event);\n    });\n  }\n}\nvar FluidValue = class {\n  constructor(get) {\n    if (!get && !(get = this.get)) {\n      throw Error(\"Unknown getter\");\n    }\n    setFluidGetter(this, get);\n  }\n};\n$get, $observers;\nvar setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer2) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = /* @__PURE__ */new Set());\n    }\n    if (!observers.has(observer2)) {\n      observers.add(observer2);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer2);\n      }\n    }\n  }\n  return observer2;\n}\nfunction removeFluidObserver(target, observer2) {\n  const observers = target[$observers];\n  if (observers && observers.has(observer2)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer2);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer2);\n    }\n  }\n}\nvar setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\n\n// src/regexs.ts\nvar numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nvar colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nvar unitRegex = new RegExp(\"(\".concat(numberRegex.source, \")(%|[a-z]+)\"), \"i\");\nvar rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nvar cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\n\n// src/variableToRgba.ts\nvar variableToRgba = input => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith(\"--\")) {\n    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (value2) {\n      return value2;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nvar parseCSSVariable = current => {\n  const match = cssVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\n\n// src/stringInterpolation.ts\nvar namedColorRegex;\nvar rgbaRound = (_, p1, p2, p3, p4) => \"rgba(\".concat(Math.round(p1), \", \").concat(Math.round(p2), \", \").concat(Math.round(p3), \", \").concat(p4, \")\");\nvar createStringInterpolator2 = config => {\n  if (!namedColorRegex) namedColorRegex = colors ?\n  // match color names, ignore partial matches\n  new RegExp(\"(\".concat(Object.keys(colors).join(\"|\"), \")(?!\\\\w)\"), \"g\") :\n  // never match\n  /^\\b$/;\n  const output = config.output.map(value => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map(value => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map((_, i) => keyframes.map(values => {\n    if (!(i in values)) {\n      throw Error('The arity of each \"output\" value must be equal');\n    }\n    return values[i];\n  }));\n  const interpolators = outputRanges.map(output2 => createInterpolator(_objectSpread(_objectSpread({}, config), {}, {\n    output: output2\n  })));\n  return input => {\n    var _output$find;\n    const missingUnit = !unitRegex.test(output[0]) && ((_output$find = output.find(value => unitRegex.test(value))) === null || _output$find === void 0 ? void 0 : _output$find.replace(numberRegex, \"\"));\n    let i = 0;\n    return output[0].replace(numberRegex, () => \"\".concat(interpolators[i++](input)).concat(missingUnit || \"\")).replace(rgbaRegex, rgbaRound);\n  };\n};\n\n// src/deprecations.ts\nvar prefix = \"react-spring: \";\nvar once = fn => {\n  const func = fn;\n  let called = false;\n  if (typeof func != \"function\") {\n    throw new TypeError(\"\".concat(prefix, \"once requires a function parameter\"));\n  }\n  return function () {\n    if (!called) {\n      func(...arguments);\n      called = true;\n    }\n  };\n};\nvar warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(\"\".concat(prefix, \"The \\\"interpolate\\\" function is deprecated in v9 (use \\\"to\\\" instead)\"));\n}\nvar warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(\"\".concat(prefix, \"Directly calling start instead of using the api object is deprecated in v9 (use \\\".start\\\" instead), this will be removed in later 0.X.0 versions\"));\n}\n\n// src/isAnimatedString.ts\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == \"#\" || /\\d/.test(value) ||\n  // Do not identify a CSS variable as an AnimatedString if its SSR\n  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));\n}\n\n// src/dom-events/scroll/index.ts\nimport { raf as raf3 } from \"@react-spring/rafz\";\n\n// src/dom-events/resize/resizeElement.ts\nvar observer;\nvar resizeHandlers = /* @__PURE__ */new WeakMap();\nvar handleObservation = entries => entries.forEach(_ref => {\n  var _resizeHandlers$get;\n  let {\n    target,\n    contentRect\n  } = _ref;\n  return (_resizeHandlers$get = resizeHandlers.get(target)) === null || _resizeHandlers$get === void 0 ? void 0 : _resizeHandlers$get.forEach(handler => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== \"undefined\") {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = /* @__PURE__ */new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers2 = resizeHandlers.get(target);\n    if (!elementHandlers2) return;\n    elementHandlers2.delete(handler);\n    if (!elementHandlers2.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\n\n// src/dom-events/resize/resizeWindow.ts\nvar listeners = /* @__PURE__ */new Set();\nvar cleanupWindowResizeHandler;\nvar createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(callback => callback({\n      width: window.innerWidth,\n      height: window.innerHeight\n    }));\n  };\n  window.addEventListener(\"resize\", handleResize);\n  return () => {\n    window.removeEventListener(\"resize\", handleResize);\n  };\n};\nvar resizeWindow = callback => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = void 0;\n    }\n  };\n};\n\n// src/dom-events/resize/index.ts\nvar onResize = function (callback) {\n  let {\n    container = document.documentElement\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, container);\n  }\n};\n\n// src/progress.ts\nvar progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n// src/dom-events/scroll/ScrollHandler.ts\nvar SCROLL_KEYS = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nvar ScrollHandler = class {\n  constructor(callback, container) {\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = axisName => {\n      const axis = this.info[axisName];\n      const {\n        length,\n        position\n      } = SCROLL_KEYS[axisName];\n      axis.current = this.container[\"scroll\".concat(position)];\n      axis.scrollLength = this.container[\"scroll\".concat(length)] - this.container[\"client\".concat(length)];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis(\"x\");\n      this.updateAxis(\"y\");\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n};\n\n// src/dom-events/scroll/index.ts\nvar scrollListeners = /* @__PURE__ */new WeakMap();\nvar resizeListeners = /* @__PURE__ */new WeakMap();\nvar onScrollHandlers = /* @__PURE__ */new WeakMap();\nvar getTarget = container => container === document.documentElement ? window : container;\nvar onScroll = function (callback) {\n  let {\n    container = document.documentElement\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let containerHandlers = onScrollHandlers.get(container);\n  if (!containerHandlers) {\n    containerHandlers = /* @__PURE__ */new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      var _containerHandlers;\n      (_containerHandlers = containerHandlers) === null || _containerHandlers === void 0 || _containerHandlers.forEach(handler => handler.advance());\n      return true;\n    };\n    scrollListeners.set(container, listener);\n    const target = getTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, {\n        container\n      }));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n  }\n  const animateScroll = scrollListeners.get(container);\n  raf3(animateScroll);\n  return () => {\n    raf3.cancel(animateScroll);\n    const containerHandlers2 = onScrollHandlers.get(container);\n    if (!containerHandlers2) return;\n    containerHandlers2.delete(containerHandler);\n    if (containerHandlers2.size) return;\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      var _resizeListeners$get;\n      getTarget(container).removeEventListener(\"scroll\", listener);\n      window.removeEventListener(\"resize\", listener);\n      (_resizeListeners$get = resizeListeners.get(container)) === null || _resizeListeners$get === void 0 || _resizeListeners$get();\n    }\n  };\n};\n\n// src/hooks/useConstant.ts\nimport { useRef } from \"react\";\nfunction useConstant(init) {\n  const ref = useRef(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\n\n// src/hooks/useForceUpdate.ts\nimport { useState } from \"react\";\n\n// src/hooks/useIsMounted.ts\nimport { useRef as useRef2 } from \"react\";\n\n// src/hooks/useIsomorphicLayoutEffect.ts\nimport { useEffect, useLayoutEffect } from \"react\";\nvar useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect;\n\n// src/hooks/useIsMounted.ts\nvar useIsMounted = () => {\n  const isMounted = useRef2(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\n\n// src/hooks/useForceUpdate.ts\nfunction useForceUpdate() {\n  const update = useState()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\n\n// src/hooks/useMemoOne.ts\nimport { useEffect as useEffect2, useRef as useRef3, useState as useState2 } from \"react\";\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = useState2(() => ({\n    inputs,\n    result: getResult()\n  }));\n  const committed = useRef3();\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(inputs && cache.inputs && areInputsEqual(inputs, cache.inputs));\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  useEffect2(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = void 0;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/hooks/useOnce.ts\nimport { useEffect as useEffect3 } from \"react\";\nvar useOnce = effect => useEffect3(effect, emptyDeps);\nvar emptyDeps = [];\n\n// src/hooks/usePrev.ts\nimport { useEffect as useEffect4, useRef as useRef4 } from \"react\";\nfunction usePrev(value) {\n  const prevRef = useRef4();\n  useEffect4(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\n\n// src/hooks/useReducedMotion.ts\nimport { useState as useState3 } from \"react\";\nvar useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState3(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia(\"(prefers-reduced-motion)\");\n    const handleMediaChange = e => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    if (mql.addEventListener) {\n      mql.addEventListener(\"change\", handleMediaChange);\n    } else {\n      mql.addListener(handleMediaChange);\n    }\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener(\"change\", handleMediaChange);\n      } else {\n        mql.removeListener(handleMediaChange);\n      }\n    };\n  }, []);\n  return reducedMotion;\n};\n\n// src/index.ts\nimport { raf as raf4 } from \"@react-spring/rafz\";\nexport { FluidValue, globals_exports as Globals, addFluidObserver, callFluidObserver, callFluidObservers, clamp, colorToRgba, colors2 as colors, createInterpolator, createStringInterpolator2 as createStringInterpolator, defineHidden, deprecateDirectCall, deprecateInterpolate, each, eachProp, easings, flush, flushCalls, frameLoop, getFluidObservers, getFluidValue, hasFluidValue, hex3, hex4, hex6, hex8, hsl, hsla, is, isAnimatedString, isEqual, isSSR, noop, onResize, onScroll, once, prefix, raf4 as raf, removeFluidObserver, rgb, rgba, setFluidGetter, toArray, useConstant, useForceUpdate, useIsomorphicLayoutEffect, useMemoOne, useOnce, usePrev, useReducedMotion };", "map": {"version": 3, "names": ["globals_exports", "__export", "assign", "colors", "createStringInterpolator", "skipAnimation", "to", "willAdvance", "raf", "noop", "defineHidden", "obj", "key", "value", "Object", "defineProperty", "writable", "configurable", "is", "arr", "Array", "isArray", "a", "constructor", "name", "fun", "str", "num", "und", "isEqual", "b", "length", "i", "each", "fn", "for<PERSON>ach", "eachProp", "ctx", "call", "concat", "hasOwnProperty", "toArray", "flush", "queue", "iterator", "size", "items", "from", "clear", "flushCalls", "_len", "arguments", "args", "_key", "isSSR", "window", "navigator", "test", "userAgent", "globals", "now", "requestAnimationFrame", "use", "batchedUpdates", "frameLoop", "raf2", "startQueue", "Set", "currentFrame", "prevFrame", "priority", "idle", "start", "animation", "add", "onStart", "flushStartQueue", "startSafely", "advance", "sort", "onFrame", "prevIndex", "indexOf", "splice", "startUnsafely", "includes", "findIndex", "other", "dt", "next<PERSON><PERSON><PERSON>", "push", "index", "clamp", "min", "max", "v", "Math", "colors2", "transparent", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "<PERSON><PERSON><PERSON>", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "NUMBER", "PERCENTAGE", "_len2", "parts", "_key2", "join", "rgb", "RegExp", "rgba", "hsl", "hsla", "hex3", "hex4", "hex6", "hex8", "normalizeColor", "color", "match", "exec", "parseInt", "parse255", "parse1", "hslToRgb", "parse360", "parsePercentage", "hue2rgb", "p", "q", "t", "h", "s", "l", "r", "g", "round", "int", "parseFloat", "colorToRgba", "input", "int32Color", "createInterpolator", "range", "output", "extrapolate", "config", "outputRange", "inputRange", "extrapolateLeft", "extrapolateRight", "easing", "range2", "find<PERSON><PERSON><PERSON>", "interpolate", "map", "inputMin", "inputMax", "outputMin", "outputMax", "result", "Infinity", "steps", "steps2", "direction", "undefined", "progress2", "expanded", "rounded", "floor", "ceil", "c1", "c2", "c3", "c4", "PI", "c5", "bounceOut", "x", "n1", "d1", "easings", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "pow", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "sin", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "sqrt", "easeOutCirc", "easeInOutCirc", "easeInBack", "easeOutBack", "easeInOutBack", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBounce", "easeOutBounce", "easeInOutBounce", "$get", "Symbol", "for", "$observers", "hasFluidValue", "arg", "Boolean", "getFluidValue", "getFluidObservers", "target", "callFluidObserver", "observer2", "event", "eventObserved", "callFluidObservers", "observers", "FluidValue", "get", "Error", "setFluidGetter", "setHidden", "addFluidObserver", "has", "observerAdded", "removeFluidObserver", "count", "delete", "observerRemoved", "numberRegex", "colorRegex", "unitRegex", "source", "rgbaRegex", "cssVariableRegex", "variableToRgba", "token", "fallback", "parseCSSVariable", "getComputedStyle", "document", "documentElement", "getPropertyValue", "trim", "startsWith", "value2", "current", "namedColorRegex", "rgbaRound", "_", "p1", "p2", "p3", "p4", "createStringInterpolator2", "keys", "replace", "keyframes", "Number", "outputRanges", "values", "interpolators", "output2", "_objectSpread", "_output$find", "missing<PERSON><PERSON><PERSON>", "find", "prefix", "once", "func", "called", "TypeError", "warnInterpolate", "console", "warn", "deprecateInterpolate", "warnDirectCall", "deprecateDirectCall", "isAnimatedString", "raf3", "observer", "resizeHandlers", "WeakMap", "handleObservation", "entries", "_ref", "_resizeHandlers$get", "contentRect", "handler", "resizeElement", "ResizeObserver", "elementHandlers", "set", "observe", "elementHandlers2", "unobserve", "listeners", "cleanupWindowResizeHandler", "createResizeHandler", "handleResize", "callback", "width", "innerWidth", "height", "innerHeight", "addEventListener", "removeEventListener", "resizeWindow", "onResize", "container", "progress", "SCROLL_KEYS", "position", "y", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createAxis", "<PERSON><PERSON><PERSON><PERSON>", "updateAxis", "axisName", "axis", "info", "update", "sendEvent", "time", "scrollListeners", "resizeListeners", "onScrollHandlers", "get<PERSON><PERSON><PERSON>", "onScroll", "containerHandlers", "containerHandler", "listener", "_containerHandlers", "passive", "animateScroll", "cancel", "containerHandlers2", "_resizeListeners$get", "useRef", "useConstant", "init", "ref", "useState", "useRef2", "useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "useIsMounted", "isMounted", "useForceUpdate", "random", "useEffect2", "useRef3", "useState2", "useMemoOne", "getResult", "inputs", "initial", "committed", "prevCache", "cache", "useCache", "areInputsEqual", "next", "prev", "useEffect3", "useOnce", "effect", "emptyDeps", "useEffect4", "useRef4", "usePrev", "prevRef", "useState3", "useReducedMotion", "reducedMotion", "setReducedMotion", "mql", "matchMedia", "handleMediaChange", "e", "matches", "addListener", "removeListener", "raf4"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/globals.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/helpers.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/FrameLoop.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/clamp.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/colors.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/colorMatchers.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/normalizeColor.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/colorToRgba.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/createInterpolator.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/easings.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/fluids.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/regexs.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/variableToRgba.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/stringInterpolation.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/deprecations.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/isAnimatedString.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/dom-events/scroll/index.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/dom-events/resize/resizeElement.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/dom-events/resize/resizeWindow.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/dom-events/resize/index.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/progress.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/dom-events/scroll/ScrollHandler.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useConstant.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useForceUpdate.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useIsMounted.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useIsomorphicLayoutEffect.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useMemoOne.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useOnce.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/usePrev.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/hooks/useReducedMotion.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/shared/src/index.ts"], "sourcesContent": ["import { raf, <PERSON><PERSON><PERSON> } from '@react-spring/rafz'\nimport {\n  OneOrMore,\n  InterpolatorConfig,\n  InterpolatorArgs,\n} from '@react-spring/types'\n\nimport { FluidValue } from './fluids'\nimport type { OpaqueAnimation } from './FrameLoop'\nimport { noop } from './helpers'\n\n//\n// Required\n//\n\nexport let createStringInterpolator: (\n  config: InterpolatorConfig<string>\n) => (input: number) => string\n\n//\n// Optional\n//\n\nexport let to: <Input, Output>(\n  source: OneOrMore<FluidValue>,\n  args: InterpolatorArgs<Input, Output>\n) => FluidValue<Output>\n\nexport let colors = null as { [key: string]: number } | null\n\nexport let skipAnimation = false as boolean\n\nexport let willAdvance: (animation: OpaqueAnimation) => void = noop\n\n//\n// Configuration\n//\n\nexport interface AnimatedGlobals {\n  /** Returns a new `Interpolation` object */\n  to?: typeof to\n  /** Used to measure frame length. Read more [here](https://developer.mozilla.org/en-US/docs/Web/API/Performance/now) */\n  now?: typeof raf.now\n  /** Provide custom color names for interpolation */\n  colors?: typeof colors\n  /** Make all animations instant and skip the frameloop entirely */\n  skipAnimation?: typeof skipAnimation\n  /** Provide custom logic for string interpolation */\n  createStringInterpolator?: typeof createStringInterpolator\n  /** Schedule a function to run on the next frame */\n  requestAnimationFrame?: (cb: () => void) => void\n  /** Event props are called with `batchedUpdates` to reduce extraneous renders */\n  batchedUpdates?: typeof raf.batchedUpdates\n  /** @internal Exposed for testing purposes */\n  willAdvance?: typeof willAdvance\n  /** sets the global frameLoop setting for the global raf instance */\n  frameLoop?: Rafz['frameLoop']\n}\n\nexport const assign = (globals: AnimatedGlobals) => {\n  if (globals.to) to = globals.to\n  if (globals.now) raf.now = globals.now\n  if (globals.colors !== undefined) colors = globals.colors\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame)\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates\n  if (globals.willAdvance) willAdvance = globals.willAdvance\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop\n}\n", "import { Lookup, Arrify, AnyFn, Any } from '@react-spring/types'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\nexport const defineHidden = (obj: any, key: any, value: any) =>\n  Object.defineProperty(obj, key, { value, writable: true, configurable: true })\n\ntype IsType<U> = <T>(arg: T & any) => arg is Narrow<T, U>\ntype Narrow<T, U> = [T] extends [Any] ? U : [T] extends [U] ? Extract<T, U> : U\n\ntype PlainObject<T> = Exclude<T & Lookup, Function | readonly any[]>\n\nexport const is = {\n  arr: Array.isArray as IsType<readonly any[]>,\n  obj: <T>(a: T & any): a is PlainObject<T> =>\n    !!a && a.constructor.name === 'Object',\n  fun: ((a: unknown) => typeof a === 'function') as IsType<Function>,\n  str: (a: unknown): a is string => typeof a === 'string',\n  num: (a: unknown): a is number => typeof a === 'number',\n  und: (a: unknown): a is undefined => a === undefined,\n}\n\n/** Compare animatable values */\nexport function isEqual(a: any, b: any) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false\n    }\n    return true\n  }\n  return a === b\n}\n\ntype EachFn<Value, Key, This> = (this: This, value: Value, key: Key) => void\ntype Eachable<Value = any, Key = any, This = any> = {\n  forEach(cb: EachFn<Value, Key, This>, ctx?: This): void\n}\n\n/** Minifiable `.forEach` call */\nexport const each = <Value, Key, This>(\n  obj: Eachable<Value, Key, This>,\n  fn: EachFn<Value, Key, This>\n) => obj.forEach(fn)\n\n/** Iterate the properties of an object */\nexport function eachProp<T extends object, This>(\n  obj: T,\n  fn: (\n    this: This,\n    value: T extends any[] ? T[number] : T[keyof T],\n    key: string\n  ) => void,\n  ctx?: This\n) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx as any, obj[i] as any, `${i}`)\n    }\n    return\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx as any, obj[key] as any, key)\n    }\n  }\n}\n\nexport const toArray = <T>(a: T): Arrify<Exclude<T, void>> =>\n  is.und(a) ? [] : is.arr(a) ? (a as any) : [a]\n\n/** Copy the `queue`, then iterate it after the `queue` is cleared */\nexport function flush<P, T>(\n  queue: Map<P, T>,\n  iterator: (entry: [P, T]) => void\n): void\nexport function flush<T>(queue: Set<T>, iterator: (value: T) => void): void\nexport function flush(queue: any, iterator: any) {\n  if (queue.size) {\n    const items = Array.from(queue)\n    queue.clear()\n    each(items, iterator)\n  }\n}\n\n/** Call every function in the queue with the same arguments. */\nexport const flushCalls = <T extends AnyFn>(\n  queue: Set<T>,\n  ...args: Parameters<T>\n) => flush(queue, fn => fn(...args))\n\n// For server-side rendering: https://github.com/react-spring/zustand/pull/34\n// Deno support: https://github.com/pmndrs/zustand/issues/347\n\nexport const isSSR = () =>\n  typeof window === 'undefined' ||\n  !window.navigator ||\n  /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent)\n", "import { raf } from '@react-spring/rafz'\nimport * as G from './globals'\n\nexport interface OpaqueAnimation {\n  idle: boolean\n  priority: number\n  advance(dt: number): void\n}\n\n// Animations starting on the next frame\nconst startQueue = new Set<OpaqueAnimation>()\n\n// The animations being updated in the current frame, sorted by lowest\n// priority first. These two arrays are swapped at the end of each frame.\nlet currentFrame: OpaqueAnimation[] = []\nlet prevFrame: OpaqueAnimation[] = []\n\n// The priority of the currently advancing animation.\n// To protect against a race condition whenever a frame is being processed,\n// where the filtering of `animations` is corrupted with a shifting index,\n// causing animations to potentially advance 2x faster than intended.\nlet priority = 0\n\n/**\n * The frameloop executes its animations in order of lowest priority first.\n * Animations are retained until idle.\n */\nexport const frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length\n  },\n\n  /** Advance the given animation on every frame until idle. */\n  start(animation: OpaqueAnimation) {\n    // An animation can be added while a frame is being processed,\n    // unless its priority is lower than the animation last updated.\n    if (priority > animation.priority) {\n      startQueue.add(animation)\n      raf.onStart(flushStartQueue)\n    } else {\n      startSafely(animation)\n      raf(advance)\n    }\n  },\n\n  /** Advance all animations by the given time. */\n  advance,\n\n  /** Call this when an animation's priority changes. */\n  sort(animation: OpaqueAnimation) {\n    if (priority) {\n      raf.onFrame(() => frameLoop.sort(animation))\n    } else {\n      const prevIndex = currentFrame.indexOf(animation)\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1)\n        startUnsafely(animation)\n      }\n    }\n  },\n\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = []\n    startQueue.clear()\n  },\n}\n\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely)\n  startQueue.clear()\n  raf(advance)\n}\n\nfunction startSafely(animation: OpaqueAnimation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation)\n}\n\nfunction startUnsafely(animation: OpaqueAnimation) {\n  currentFrame.splice(\n    findIndex(currentFrame, other => other.priority > animation.priority),\n    0,\n    animation\n  )\n}\n\nfunction advance(dt: number) {\n  const nextFrame = prevFrame\n\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i]\n    priority = animation.priority\n\n    // Animations may go idle before advancing.\n    if (!animation.idle) {\n      G.willAdvance(animation)\n      animation.advance(dt)\n      if (!animation.idle) {\n        nextFrame.push(animation)\n      }\n    }\n  }\n  priority = 0\n\n  // Reuse the `currentFrame` array to avoid garbage collection.\n  prevFrame = currentFrame\n  prevFrame.length = 0\n\n  // Set `currentFrame` for next frame, so the `start` function\n  // adds new animations to the proper array.\n  currentFrame = nextFrame\n\n  return currentFrame.length > 0\n}\n\n/** Like `Array.prototype.findIndex` but returns `arr.length` instead of `-1` */\nfunction findIndex<T>(arr: T[], test: (value: T) => boolean) {\n  const index = arr.findIndex(test)\n  return index < 0 ? arr.length : index\n}\n", "export const clamp = (min: number, max: number, v: number) =>\n  Math.min(Math.max(v, min), max)\n", "export type ColorName = keyof typeof colors\n\n// http://www.w3.org/TR/css3-color/#svg-color\nexport const colors = {\n  transparent: 0x00000000,\n  aliceblue: 0xf0f8ffff,\n  antiquewhite: 0xfaebd7ff,\n  aqua: 0x00ffffff,\n  aquamarine: 0x7fffd4ff,\n  azure: 0xf0ffffff,\n  beige: 0xf5f5dcff,\n  bisque: 0xffe4c4ff,\n  black: 0x000000ff,\n  blanchedalmond: 0xffebcdff,\n  blue: 0x0000ffff,\n  blueviolet: 0x8a2be2ff,\n  brown: 0xa52a2aff,\n  burlywood: 0xdeb887ff,\n  burntsienna: 0xea7e5dff,\n  cadetblue: 0x5f9ea0ff,\n  chartreuse: 0x7fff00ff,\n  chocolate: 0xd2691eff,\n  coral: 0xff7f50ff,\n  cornflowerblue: 0x6495edff,\n  cornsilk: 0xfff8dcff,\n  crimson: 0xdc143cff,\n  cyan: 0x00ffffff,\n  darkblue: 0x00008bff,\n  darkcyan: 0x008b8bff,\n  darkgoldenrod: 0xb8860bff,\n  darkgray: 0xa9a9a9ff,\n  darkgreen: 0x006400ff,\n  darkgrey: 0xa9a9a9ff,\n  darkkhaki: 0xbdb76bff,\n  darkmagenta: 0x8b008bff,\n  darkolivegreen: 0x556b2fff,\n  darkorange: 0xff8c00ff,\n  darkorchid: 0x9932ccff,\n  darkred: 0x8b0000ff,\n  darksalmon: 0xe9967aff,\n  darkseagreen: 0x8fbc8fff,\n  darkslateblue: 0x483d8bff,\n  darkslategray: 0x2f4f4fff,\n  darkslategrey: 0x2f4f4fff,\n  darkturquoise: 0x00ced1ff,\n  darkviolet: 0x9400d3ff,\n  deeppink: 0xff1493ff,\n  deepskyblue: 0x00bfffff,\n  dimgray: 0x696969ff,\n  dimgrey: 0x696969ff,\n  dodgerblue: 0x1e90ffff,\n  firebrick: 0xb22222ff,\n  floralwhite: 0xfffaf0ff,\n  forestgreen: 0x228b22ff,\n  fuchsia: 0xff00ffff,\n  gainsboro: 0xdcdcdcff,\n  ghostwhite: 0xf8f8ffff,\n  gold: 0xffd700ff,\n  goldenrod: 0xdaa520ff,\n  gray: 0x808080ff,\n  green: 0x008000ff,\n  greenyellow: 0xadff2fff,\n  grey: 0x808080ff,\n  honeydew: 0xf0fff0ff,\n  hotpink: 0xff69b4ff,\n  indianred: 0xcd5c5cff,\n  indigo: 0x4b0082ff,\n  ivory: 0xfffff0ff,\n  khaki: 0xf0e68cff,\n  lavender: 0xe6e6faff,\n  lavenderblush: 0xfff0f5ff,\n  lawngreen: 0x7cfc00ff,\n  lemonchiffon: 0xfffacdff,\n  lightblue: 0xadd8e6ff,\n  lightcoral: 0xf08080ff,\n  lightcyan: 0xe0ffffff,\n  lightgoldenrodyellow: 0xfafad2ff,\n  lightgray: 0xd3d3d3ff,\n  lightgreen: 0x90ee90ff,\n  lightgrey: 0xd3d3d3ff,\n  lightpink: 0xffb6c1ff,\n  lightsalmon: 0xffa07aff,\n  lightseagreen: 0x20b2aaff,\n  lightskyblue: 0x87cefaff,\n  lightslategray: 0x778899ff,\n  lightslategrey: 0x778899ff,\n  lightsteelblue: 0xb0c4deff,\n  lightyellow: 0xffffe0ff,\n  lime: 0x00ff00ff,\n  limegreen: 0x32cd32ff,\n  linen: 0xfaf0e6ff,\n  magenta: 0xff00ffff,\n  maroon: 0x800000ff,\n  mediumaquamarine: 0x66cdaaff,\n  mediumblue: 0x0000cdff,\n  mediumorchid: 0xba55d3ff,\n  mediumpurple: 0x9370dbff,\n  mediumseagreen: 0x3cb371ff,\n  mediumslateblue: 0x7b68eeff,\n  mediumspringgreen: 0x00fa9aff,\n  mediumturquoise: 0x48d1ccff,\n  mediumvioletred: 0xc71585ff,\n  midnightblue: 0x191970ff,\n  mintcream: 0xf5fffaff,\n  mistyrose: 0xffe4e1ff,\n  moccasin: 0xffe4b5ff,\n  navajowhite: 0xffdeadff,\n  navy: 0x000080ff,\n  oldlace: 0xfdf5e6ff,\n  olive: 0x808000ff,\n  olivedrab: 0x6b8e23ff,\n  orange: 0xffa500ff,\n  orangered: 0xff4500ff,\n  orchid: 0xda70d6ff,\n  palegoldenrod: 0xeee8aaff,\n  palegreen: 0x98fb98ff,\n  paleturquoise: 0xafeeeeff,\n  palevioletred: 0xdb7093ff,\n  papayawhip: 0xffefd5ff,\n  peachpuff: 0xffdab9ff,\n  peru: 0xcd853fff,\n  pink: 0xffc0cbff,\n  plum: 0xdda0ddff,\n  powderblue: 0xb0e0e6ff,\n  purple: 0x800080ff,\n  rebeccapurple: 0x663399ff,\n  red: 0xff0000ff,\n  rosybrown: 0xbc8f8fff,\n  royalblue: 0x4169e1ff,\n  saddlebrown: 0x8b4513ff,\n  salmon: 0xfa8072ff,\n  sandybrown: 0xf4a460ff,\n  seagreen: 0x2e8b57ff,\n  seashell: 0xfff5eeff,\n  sienna: 0xa0522dff,\n  silver: 0xc0c0c0ff,\n  skyblue: 0x87ceebff,\n  slateblue: 0x6a5acdff,\n  slategray: 0x708090ff,\n  slategrey: 0x708090ff,\n  snow: 0xfffafaff,\n  springgreen: 0x00ff7fff,\n  steelblue: 0x4682b4ff,\n  tan: 0xd2b48cff,\n  teal: 0x008080ff,\n  thistle: 0xd8bfd8ff,\n  tomato: 0xff6347ff,\n  turquoise: 0x40e0d0ff,\n  violet: 0xee82eeff,\n  wheat: 0xf5deb3ff,\n  white: 0xffffffff,\n  whitesmoke: 0xf5f5f5ff,\n  yellow: 0xffff00ff,\n  yellowgreen: 0x9acd32ff,\n}\n", "// const INTEGER = '[-+]?\\\\d+';\nconst NUMBER = '[-+]?\\\\d*\\\\.?\\\\d+'\nconst PERCENTAGE = NUMBER + '%'\n\nfunction call(...parts: string[]) {\n  return '\\\\(\\\\s*(' + parts.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)'\n}\n\nexport const rgb = new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER))\nexport const rgba = new RegExp('rgba' + call(NUMBER, NUMBER, NUMBER, NUMBER))\nexport const hsl = new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE))\nexport const hsla = new RegExp(\n  'hsla' + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n)\nexport const hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex4 =\n  /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex6 = /^#([0-9a-fA-F]{6})$/\nexport const hex8 = /^#([0-9a-fA-F]{8})$/\n", "/*\nhttps://github.com/react-community/normalize-css-color\n\nBSD 3-Clause License\n\nCopyright (c) 2016, React Community\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n* Redistributions of source code must retain the above copyright notice, this\n  list of conditions and the following disclaimer.\n\n* Redistributions in binary form must reproduce the above copyright notice,\n  this list of conditions and the following disclaimer in the documentation\n  and/or other materials provided with the distribution.\n\n* Neither the name of the copyright holder nor the names of its\n  contributors may be used to endorse or promote products derived from\n  this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, <PERSON>XEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\nimport * as matchers from './colorMatchers'\nimport * as G from './globals'\n\nexport function normalizeColor(color: number | string) {\n  let match\n\n  if (typeof color === 'number') {\n    return color >>> 0 === color && color >= 0 && color <= 0xffffffff\n      ? color\n      : null\n  }\n\n  // Ordered based on occurrences on Facebook codebase\n  if ((match = matchers.hex6.exec(color)))\n    return parseInt(match[1] + 'ff', 16) >>> 0\n\n  if (G.colors && G.colors[color] !== undefined) {\n    return G.colors[color]\n  }\n\n  if ((match = matchers.rgb.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.rgba.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hex3.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          'ff', // a\n        16\n      ) >>> 0\n    )\n  }\n\n  // https://drafts.csswg.org/css-color-4/#hex-notation\n  if ((match = matchers.hex8.exec(color))) return parseInt(match[1], 16) >>> 0\n\n  if ((match = matchers.hex4.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          match[4] +\n          match[4], // a\n        16\n      ) >>> 0\n    )\n  }\n\n  if ((match = matchers.hsl.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hsla.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n  return null\n}\n\nfunction hue2rgb(p: number, q: number, t: number) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nfunction hslToRgb(h: number, s: number, l: number) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n  const p = 2 * l - q\n  const r = hue2rgb(p, q, h + 1 / 3)\n  const g = hue2rgb(p, q, h)\n  const b = hue2rgb(p, q, h - 1 / 3)\n  return (\n    (Math.round(r * 255) << 24) |\n    (Math.round(g * 255) << 16) |\n    (Math.round(b * 255) << 8)\n  )\n}\n\nfunction parse255(str: string) {\n  const int = parseInt(str, 10)\n  if (int < 0) return 0\n  if (int > 255) return 255\n  return int\n}\n\nfunction parse360(str: string) {\n  const int = parseFloat(str)\n  return (((int % 360) + 360) % 360) / 360\n}\n\nfunction parse1(str: string) {\n  const num = parseFloat(str)\n  if (num < 0) return 0\n  if (num > 1) return 255\n  return Math.round(num * 255)\n}\n\nfunction parsePercentage(str: string) {\n  // parseFloat conveniently ignores the final %\n  const int = parseFloat(str)\n  if (int < 0) return 0\n  if (int > 100) return 1\n  return int / 100\n}\n", "import { normalizeColor } from './normalizeColor'\n\nexport function colorToRgba(input: string) {\n  let int32Color = normalizeColor(input)\n  if (int32Color === null) return input\n  int32Color = int32Color || 0\n  const r = (int32Color & 0xff000000) >>> 24\n  const g = (int32Color & 0x00ff0000) >>> 16\n  const b = (int32Color & 0x0000ff00) >>> 8\n  const a = (int32Color & 0x000000ff) / 255\n  return `rgba(${r}, ${g}, ${b}, ${a})`\n}\n", "import * as G from './globals'\nimport { is } from './helpers'\nimport {\n  Animatable,\n  InterpolatorFn,\n  EasingFunction,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFactory,\n} from '@react-spring/types'\n\nexport const createInterpolator: InterpolatorFactory = (\n  range: readonly number[] | InterpolatorFn<any, any> | InterpolatorConfig<any>,\n  output?: readonly Animatable[],\n  extrapolate?: ExtrapolateType\n) => {\n  if (is.fun(range)) {\n    return range\n  }\n\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output: output!,\n      extrapolate,\n    })\n  }\n\n  if (is.str(range.output[0])) {\n    return G.createStringInterpolator(range as any) as any\n  }\n\n  const config = range as InterpolatorConfig<number>\n  const outputRange = config.output\n  const inputRange = config.range || [0, 1]\n\n  const extrapolateLeft =\n    config.extrapolateLeft || config.extrapolate || 'extend'\n  const extrapolateRight =\n    config.extrapolateRight || config.extrapolate || 'extend'\n  const easing = config.easing || (t => t)\n\n  return (input: number) => {\n    const range = findRange(input, inputRange)\n    return interpolate(\n      input,\n      inputRange[range],\n      inputRange[range + 1],\n      outputRange[range],\n      outputRange[range + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    )\n  }\n}\n\nfunction interpolate(\n  input: number,\n  inputMin: number,\n  inputMax: number,\n  outputMin: number,\n  outputMax: number,\n  easing: EasingFunction,\n  extrapolateLeft: ExtrapolateType,\n  extrapolateRight: ExtrapolateType,\n  map?: (x: number) => number\n) {\n  let result = map ? map(input) : input\n  // Extrapolate\n  if (result < inputMin) {\n    if (extrapolateLeft === 'identity') return result\n    else if (extrapolateLeft === 'clamp') result = inputMin\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === 'identity') return result\n    else if (extrapolateRight === 'clamp') result = inputMax\n  }\n  if (outputMin === outputMax) return outputMin\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax\n  // Input Range\n  if (inputMin === -Infinity) result = -result\n  else if (inputMax === Infinity) result = result - inputMin\n  else result = (result - inputMin) / (inputMax - inputMin)\n  // Easing\n  result = easing(result)\n  // Output Range\n  if (outputMin === -Infinity) result = -result\n  else if (outputMax === Infinity) result = result + outputMin\n  else result = result * (outputMax - outputMin) + outputMin\n  return result\n}\n\nfunction findRange(input: number, inputRange: readonly number[]) {\n  // eslint-disable-next-line no-var\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break\n  return i - 1\n}\n", "import { EasingFunction } from '@react-spring/types'\n\nimport { clamp } from './clamp'\n\nexport type Direction = 'start' | 'end'\n\ntype StepsEasing = (steps: number, direction?: Direction) => EasingFunction\n\nconst steps: StepsEasing =\n  (steps: number, direction: Direction = 'end') =>\n  (progress: number) => {\n    progress =\n      direction === 'end'\n        ? Math.min(progress, 0.999)\n        : Math.max(progress, 0.001)\n    const expanded = progress * steps\n    const rounded =\n      direction === 'end' ? Math.floor(expanded) : Math.ceil(expanded)\n\n    return clamp(0, 1, rounded / steps)\n  }\n\n/**\n * With thanks to ai easings.net\n * https://github.com/ai/easings.net/blob/master/src/easings/easingsFunctions.ts\n */\ninterface EasingDictionary {\n  linear: (t: number) => number\n  easeInQuad: (t: number) => number\n  easeOutQuad: (t: number) => number\n  easeInOutQuad: (t: number) => number\n  easeInCubic: (t: number) => number\n  easeOutCubic: (t: number) => number\n  easeInOutCubic: (t: number) => number\n  easeInQuart: (t: number) => number\n  easeOutQuart: (t: number) => number\n  easeInOutQuart: (t: number) => number\n  easeInQuint: (t: number) => number\n  easeOutQuint: (t: number) => number\n  easeInOutQuint: (t: number) => number\n  easeInSine: (t: number) => number\n  easeOutSine: (t: number) => number\n  easeInOutSine: (t: number) => number\n  easeInExpo: (t: number) => number\n  easeOutExpo: (t: number) => number\n  easeInOutExpo: (t: number) => number\n  easeInCirc: (t: number) => number\n  easeOutCirc: (t: number) => number\n  easeInOutCirc: (t: number) => number\n  easeInBack: (t: number) => number\n  easeOutBack: (t: number) => number\n  easeInOutBack: (t: number) => number\n  easeInElastic: (t: number) => number\n  easeOutElastic: (t: number) => number\n  easeInOutElastic: (t: number) => number\n  easeInBounce: (t: number) => number\n  easeOutBounce: (t: number) => number\n  easeInOutBounce: (t: number) => number\n  steps: StepsEasing\n}\n\nconst c1 = 1.70158\nconst c2 = c1 * 1.525\nconst c3 = c1 + 1\nconst c4 = (2 * Math.PI) / 3\nconst c5 = (2 * Math.PI) / 4.5\n\nconst bounceOut: EasingFunction = x => {\n  const n1 = 7.5625\n  const d1 = 2.75\n\n  if (x < 1 / d1) {\n    return n1 * x * x\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375\n  }\n}\n\nexport const easings: EasingDictionary = {\n  linear: x => x,\n  easeInQuad: x => x * x,\n  easeOutQuad: x => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: x => (x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2),\n  easeInCubic: x => x * x * x,\n  easeOutCubic: x => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: x =>\n    x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: x => x * x * x * x,\n  easeOutQuart: x => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: x =>\n    x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: x => x * x * x * x * x,\n  easeOutQuint: x => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: x =>\n    x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: x => 1 - Math.cos((x * Math.PI) / 2),\n  easeOutSine: x => Math.sin((x * Math.PI) / 2),\n  easeInOutSine: x => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: x => (x === 0 ? 0 : Math.pow(2, 10 * x - 10)),\n  easeOutExpo: x => (x === 1 ? 1 : 1 - Math.pow(2, -10 * x)),\n  easeInOutExpo: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? Math.pow(2, 20 * x - 10) / 2\n          : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: x => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: x => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: x =>\n    x < 0.5\n      ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2\n      : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: x => c3 * x * x * x - c1 * x * x,\n  easeOutBack: x => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: x =>\n    x < 0.5\n      ? (Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2)) / 2\n      : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2\n          : (Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5)) / 2 +\n            1,\n  easeInBounce: x => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: x =>\n    x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps,\n} as const\n", "/**\n * MIT License\n * Copyright (c) <PERSON>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\nconst $get = Symbol.for('FluidValue.get')\nconst $observers = Symbol.for('FluidValue.observers')\n\nexport {\n  FluidValue,\n  hasFluidValue,\n  getFluidValue,\n  getFluidObservers,\n  callFluidObserver,\n  callFluidObservers,\n  // Mutations\n  setFluidGetter,\n  addFluidObserver,\n  removeFluidObserver,\n}\n\n/** Returns true if `arg` can be observed. */\nconst hasFluidValue = (arg: any): arg is FluidValue => Boolean(arg && arg[$get])\n\n/**\n * Get the current value.\n * If `arg` is not observable, `arg` is returned.\n */\nconst getFluidValue: GetFluidValue = (arg: any) =>\n  arg && arg[$get] ? arg[$get]() : arg\n\n/** Get the current observer set. Never mutate it directly! */\nconst getFluidObservers: GetFluidObservers = (target: any) =>\n  target[$observers] || null\n\n/** Send an event to an observer. */\nfunction callFluidObserver<E extends FluidEvent>(\n  observer: FluidObserver<E>,\n  event: E\n): void\n\nfunction callFluidObserver(observer: any, event: FluidEvent) {\n  if (observer.eventObserved) {\n    observer.eventObserved(event)\n  } else {\n    observer(event)\n  }\n}\n\n/** Send an event to all observers. */\nfunction callFluidObservers<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  event: E\n): void\n\nfunction callFluidObservers(target: object, event: FluidEvent): void\n\nfunction callFluidObservers(target: any, event: FluidEvent) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers) {\n    observers.forEach(observer => {\n      callFluidObserver(observer, event)\n    })\n  }\n}\n\ntype GetFluidValue = {\n  <T, U = never>(target: T | FluidValue<U>): Exclude<T, FluidValue> | U\n}\n\ntype GetFluidObservers = {\n  <E extends FluidEvent>(\n    target: FluidValue<any, E>\n  ): ReadonlySet<FluidObserver<E>> | null\n  (target: object): ReadonlySet<FluidObserver> | null\n}\n\n/** An event sent to `FluidObserver` objects. */\nexport interface FluidEvent<T = any> {\n  type: string\n  parent: FluidValue<T>\n}\n\n/**\n * Extend this class for automatic TypeScript support when passing this\n * value to `fluids`-compatible libraries.\n */\nabstract class FluidValue<T = any, E extends FluidEvent<T> = any> {\n  // @ts-expect-error (TS 4.4)\n  private [$get]: () => T\n  // @ts-expect-error (TS 4.4)\n  private [$observers]?: Set<FluidObserver<E>>\n\n  constructor(get?: () => T) {\n    if (!get && !(get = this.get)) {\n      throw Error('Unknown getter')\n    }\n    setFluidGetter(this, get)\n  }\n\n  /** Get the current value. */\n  protected get?(): T\n  /** Called after an observer is added. */\n  protected observerAdded?(count: number, observer: FluidObserver<E>): void\n  /** Called after an observer is removed. */\n  protected observerRemoved?(count: number, observer: FluidObserver<E>): void\n}\n\n/** An observer of `FluidValue` objects. */\nexport type FluidObserver<E extends FluidEvent = any> =\n  | { eventObserved(event: E): void }\n  | { (event: E): void }\n\n/** Add the `FluidValue` type to every property. */\nexport type FluidProps<T> = T extends object\n  ? { [P in keyof T]: T[P] | FluidValue<Exclude<T[P], void>> }\n  : unknown\n\n/** Remove the `FluidValue` type from every property. */\nexport type StaticProps<T extends object> = {\n  [P in keyof T]: T[P] extends FluidValue<infer U> ? U : T[P]\n}\n\n/** Define the getter called by `getFluidValue`. */\nconst setFluidGetter = (target: object, get: () => any) =>\n  setHidden(target, $get, get)\n\n/** Observe a `fluids`-compatible object. */\nfunction addFluidObserver<T, E extends FluidEvent>(\n  target: FluidValue<T, E>,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver(target: any, observer: FluidObserver) {\n  if (target[$get]) {\n    let observers: Set<FluidObserver> = target[$observers]\n    if (!observers) {\n      setHidden(target, $observers, (observers = new Set()))\n    }\n    if (!observers.has(observer)) {\n      observers.add(observer)\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer)\n      }\n    }\n  }\n  return observer\n}\n\n/** Stop observing a `fluids`-compatible object. */\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver(target: any, observer: FluidObserver) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers && observers.has(observer)) {\n    const count = observers.size - 1\n    if (count) {\n      observers.delete(observer)\n    } else {\n      target[$observers] = null\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer)\n    }\n  }\n}\n\nconst setHidden = (target: any, key: any, value: any) =>\n  Object.defineProperty(target, key, {\n    value,\n    writable: true,\n    configurable: true,\n  })\n", "// Problem: https://github.com/animatedjs/animated/pull/102\n// Solution: https://stackoverflow.com/questions/638565/parsing-scientific-notation-sensibly/658662\nexport const numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g\n\n// Covers rgb, rgba, hsl, hsla\n// Taken from https://gist.github.com/olmokramer/82ccce673f86db7cda5e\nexport const colorRegex =\n  /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi\n\n// Gets numbers with units when specified\nexport const unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, 'i')\n\n// get values of rgba string\nexport const rgbaRegex =\n  /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi\n\n/**\n * Parse special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n */\nexport const cssVariableRegex =\n  /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/\n", "import { isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n/**\n * takes a CSS variable and attempts\n * to turn it into a RGBA value\n *\n * ```\n * 'var(--white)' => 'rgba(255,255,255,1)'\n * ```\n *\n * @param input string\n * @returns string\n */\nexport const variableToRgba = (input: string): string => {\n  const [token, fallback] = parseCSSVariable(input)\n\n  if (!token || isSSR()) {\n    return input\n  }\n\n  const value = window\n    .getComputedStyle(document.documentElement)\n    .getPropertyValue(token)\n\n  if (value) {\n    /**\n     * We have a valid variable returned\n     * trim and return\n     */\n    return value.trim()\n  } else if (fallback && fallback.startsWith('--')) {\n    /**\n     * fallback is something like --my-variable\n     * so we try get property value\n     */\n    const value = window\n      .getComputedStyle(document.documentElement)\n      .getPropertyValue(fallback)\n\n    /**\n     * if it exists, return else nothing was found so just return the input\n     */\n    if (value) {\n      return value\n    } else {\n      return input\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    /**\n     * We have a fallback and it's another CSS variable\n     */\n    return variableToRgba(fallback)\n  } else if (fallback) {\n    /**\n     * We have a fallback and it's not a CSS variable\n     */\n    return fallback\n  }\n\n  /**\n   * Nothing worked so just return the input\n   * like our other FluidValue replace functions do\n   */\n  return input\n}\n\nconst parseCSSVariable = (current: string) => {\n  const match = cssVariableRegex.exec(current)\n  if (!match) return [,]\n\n  const [, token, fallback] = match\n  return [token, fallback]\n}\n", "import { InterpolatorConfig } from '@react-spring/types'\n\nimport { getFluidValue } from './fluids'\nimport { createInterpolator } from './createInterpolator'\nimport { colorToRgba } from './colorToRgba'\nimport * as G from './globals'\nimport {\n  cssVariableRegex,\n  colorRegex,\n  unitRegex,\n  numberRegex,\n  rgbaRegex,\n} from './regexs'\nimport { variableToRgba } from './variableToRgba'\n\n// Covers color names (transparent, blue, etc.)\nlet namedColorRegex: RegExp\n\n// rgba requires that the r,g,b are integers.... so we want to round them,\n// but we *dont* want to round the opacity (4th column).\nconst rgbaRound = (_: any, p1: number, p2: number, p3: number, p4: number) =>\n  `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`\n\n/**\n * Supports string shapes by extracting numbers so new values can be computed,\n * and recombines those values into new strings of the same shape.  Supports\n * things like:\n *\n *     \"rgba(123, 42, 99, 0.36)\"           // colors\n *     \"-45deg\"                            // values with units\n *     \"0 2px 2px 0px rgba(0, 0, 0, 0.12)\" // CSS box-shadows\n *     \"rotate(0deg) translate(2px, 3px)\"  // CSS transforms\n */\nexport const createStringInterpolator = (\n  config: InterpolatorConfig<string>\n) => {\n  if (!namedColorRegex)\n    namedColorRegex = G.colors\n      ? // match color names, ignore partial matches\n        new RegExp(`(${Object.keys(G.colors).join('|')})(?!\\\\w)`, 'g')\n      : // never match\n        /^\\b$/\n\n  // Convert colors to rgba(...)\n  const output = config.output.map(value => {\n    return getFluidValue(value)\n      .replace(cssVariableRegex, variableToRgba)\n      .replace(colorRegex, colorToRgba)\n      .replace(namedColorRegex, colorToRgba)\n  })\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 2], [0, 0]]\n  const keyframes = output.map(value => value.match(numberRegex)!.map(Number))\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 0], [2, 0]]\n  const outputRanges = keyframes[0].map((_, i) =>\n    keyframes.map(values => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal')\n      }\n      return values[i]\n    })\n  )\n\n  // Create an interpolator for each animated number\n  const interpolators = outputRanges.map(output =>\n    createInterpolator({ ...config, output })\n  )\n\n  // Use the first `output` as a template for each call\n  return (input: number) => {\n    // Convert numbers to units if available (allows for [\"0\", \"100%\"])\n    const missingUnit =\n      !unitRegex.test(output[0]) &&\n      output.find(value => unitRegex.test(value))?.replace(numberRegex, '')\n\n    let i = 0\n    return output[0]\n      .replace(\n        numberRegex,\n        () => `${interpolators[i++](input)}${missingUnit || ''}`\n      )\n      .replace(rgbaRegex, rgbaRound)\n  }\n}\n", "declare const console: any\n\nexport const prefix = 'react-spring: '\n\nexport const once = <TFunc extends (...args: any) => any>(fn: TFunc) => {\n  const func = fn\n  let called = false\n\n  if (typeof func != 'function') {\n    throw new TypeError(`${prefix}once requires a function parameter`)\n  }\n\n  return (...args: any) => {\n    if (!called) {\n      func(...args)\n      called = true\n    }\n  }\n}\n\nconst warnInterpolate = once(console.warn)\nexport function deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  )\n}\n\nconst warnDirectCall = once(console.warn)\nexport function deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  )\n}\n", "import * as G from './globals'\nimport { is, isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n// Not all strings can be animated (eg: {display: \"none\"})\nexport function isAnimatedString(value: unknown): value is string {\n  return (\n    is.str(value) &&\n    (value[0] == '#' ||\n      /\\d/.test(value) ||\n      // Do not identify a CSS variable as an AnimatedString if its SSR\n      (!isSSR() && cssVariableRegex.test(value)) ||\n      value in (G.colors || {}))\n  )\n}\n", "import { raf } from '@react-spring/rafz'\nimport { onResize } from '../resize'\n\nimport { ScrollInfo, ScrollHandler } from './ScrollHandler'\n\nexport type OnScrollCallback = (info: ScrollInfo) => void\n\nexport type OnScrollOptions = {\n  /**\n   * The root container to measure against\n   */\n  container?: HTMLElement\n}\n\nconst scrollListeners = new WeakMap<Element, () => boolean>()\nconst resizeListeners = new WeakMap<Element, VoidFunction>()\nconst onScrollHandlers = new WeakMap<Element, Set<ScrollHandler>>()\n\nconst getTarget = (container: HTMLElement) =>\n  container === document.documentElement ? window : container\n\nexport const onScroll = (\n  callback: OnScrollCallback,\n  { container = document.documentElement }: OnScrollOptions = {}\n) => {\n  /**\n   * get the current handlers for the target\n   */\n  let containerHandlers = onScrollHandlers.get(container)\n\n  /**\n   * If there aren't any handlers then create a new set.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set()\n    onScrollHandlers.set(container, containerHandlers)\n  }\n\n  /**\n   * Create a new ScrollHandler class and add it to the set.\n   */\n  const containerHandler = new ScrollHandler(callback, container)\n  containerHandlers.add(containerHandler)\n\n  /**\n   * If there are no scrollListeners then we need to make them\n   */\n  if (!scrollListeners.has(container)) {\n    /**\n     * Return true so RAFZ continues to run it\n     */\n    const listener = () => {\n      containerHandlers?.forEach(handler => handler.advance())\n      return true\n    }\n\n    scrollListeners.set(container, listener)\n\n    const target = getTarget(container)\n\n    /**\n     * Add resize handlers so we can correctly calculate the\n     * scroll position on changes\n     */\n    window.addEventListener('resize', listener, { passive: true })\n\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }))\n    }\n\n    /**\n     * Add the actual scroll listener\n     */\n    target.addEventListener('scroll', listener, { passive: true })\n  }\n\n  /**\n   * Start animation loop\n   */\n  const animateScroll = scrollListeners.get(container)!\n  raf(animateScroll)\n\n  return () => {\n    /**\n     * Clear it on cleanup\n     */\n    raf.cancel(animateScroll)\n\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const containerHandlers = onScrollHandlers.get(container)\n    if (!containerHandlers) return\n\n    containerHandlers.delete(containerHandler)\n\n    if (containerHandlers.size) return\n\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const listener = scrollListeners.get(container)\n    scrollListeners.delete(container)\n\n    if (listener) {\n      getTarget(container).removeEventListener('scroll', listener)\n      window.removeEventListener('resize', listener)\n\n      resizeListeners.get(container)?.()\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nlet observer: ResizeObserver | undefined\n\nconst resizeHandlers = new WeakMap<Element, Set<OnResizeCallback>>()\n\nconst handleObservation = (entries: ResizeObserverEntry[]) =>\n  entries.forEach(({ target, contentRect }) => {\n    return resizeHandlers.get(target)?.forEach(handler => handler(contentRect))\n  })\n\nexport function resizeElement(handler: OnResizeCallback, target: HTMLElement) {\n  /**\n   * If there's a resize observer in the ENV then use that too.\n   */\n  if (!observer) {\n    if (typeof ResizeObserver !== 'undefined') {\n      observer = new ResizeObserver(handleObservation)\n    }\n  }\n\n  /**\n   * Fetch the handlers for the target\n   */\n  let elementHandlers = resizeHandlers.get(target)\n\n  /**\n   * If there are no handlers create a new set for the target\n   * and then add to the map\n   */\n  if (!elementHandlers) {\n    elementHandlers = new Set()\n    resizeHandlers.set(target, elementHandlers)\n  }\n\n  /**\n   * Add the handler to the target's set\n   * and observe the target if possible.\n   */\n  elementHandlers.add(handler)\n\n  if (observer) {\n    observer.observe(target)\n  }\n\n  /**\n   * Cleanup the event handlers and potential observers.\n   */\n  return () => {\n    const elementHandlers = resizeHandlers.get(target)\n\n    if (!elementHandlers) return\n\n    elementHandlers.delete(handler)\n\n    if (!elementHandlers.size && observer) {\n      observer.unobserve(target)\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nconst listeners = new Set<OnResizeCallback>()\n\nlet cleanupWindowResizeHandler: VoidFunction | undefined\n\nconst createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(callback =>\n      callback({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      })\n    )\n  }\n\n  window.addEventListener('resize', handleResize)\n\n  return () => {\n    window.removeEventListener('resize', handleResize)\n  }\n}\n\nexport const resizeWindow = (callback: OnResizeCallback) => {\n  listeners.add(callback)\n\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler()\n  }\n\n  return () => {\n    listeners.delete(callback)\n\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler()\n      cleanupWindowResizeHandler = undefined\n    }\n  }\n}\n", "import { resizeElement } from './resizeElement'\nimport { resizeWindow } from './resizeWindow'\n\nexport interface OnResizeOptions {\n  container?: HTMLElement\n}\n\nexport type OnResizeCallback = (\n  rect: Pick<DOMRectReadOnly, 'width' | 'height'> &\n    Partial<Omit<DOMRectReadOnly, 'width' | 'height'>>\n) => void\n\nexport const onResize = (\n  callback: OnResizeCallback,\n  { container = document.documentElement }: OnResizeOptions = {}\n): (() => void) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback)\n  } else {\n    return resizeElement(callback, container)\n  }\n}\n", "export const progress = (min: number, max: number, value: number) =>\n  max - min === 0 ? 1 : (value - min) / (max - min)\n", "import { progress } from '../../progress'\n\nimport type { OnScrollCallback } from './index'\n\nconst SCROLL_KEYS = {\n  x: {\n    length: 'Width',\n    position: 'Left',\n  },\n  y: {\n    length: 'Height',\n    position: 'Top',\n  },\n} as const\n\n/**\n * Whilst user's may not need the scrollLength, it's easier to return\n * the whole state we're storing and let them pick what they want.\n */\nexport interface ScrollAxis {\n  current: number\n  progress: number\n  scrollLength: number\n}\n\nexport interface ScrollInfo {\n  time: number\n  x: ScrollAxis\n  y: ScrollAxis\n}\n\n/**\n * Why use a class? More extensible in the future.\n */\nexport class ScrollHandler {\n  protected callback: OnScrollCallback\n  protected container: HTMLElement\n  protected info: ScrollInfo\n\n  constructor(callback: OnScrollCallback, container: HTMLElement) {\n    this.callback = callback\n    this.container = container\n\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis(),\n    }\n  }\n\n  private createAxis = (): ScrollAxis => ({\n    current: 0,\n    progress: 0,\n    scrollLength: 0,\n  })\n\n  private updateAxis = (axisName: keyof Pick<ScrollInfo, 'x' | 'y'>) => {\n    const axis = this.info[axisName]\n    const { length, position } = SCROLL_KEYS[axisName]\n\n    axis.current = this.container[`scroll${position}`]\n    axis.scrollLength =\n      this.container[`scroll${length}`] - this.container[`client${length}`]\n\n    axis.progress = progress(0, axis.scrollLength, axis.current)\n  }\n\n  private update = () => {\n    this.updateAxis('x')\n    this.updateAxis('y')\n  }\n\n  private sendEvent = () => {\n    this.callback(this.info)\n  }\n\n  advance = () => {\n    this.update()\n    this.sendEvent()\n  }\n}\n", "import { useRef } from 'react'\n\ntype Init<T> = () => T\n\n/**\n * Creates a constant value over the lifecycle of a component.\n */\nexport function useConstant<T>(init: Init<T>) {\n  const ref = useRef<T | null>(null)\n\n  if (ref.current === null) {\n    ref.current = init()\n  }\n\n  return ref.current\n}\n", "import { useState } from 'react'\nimport { useIsMounted } from './useIsMounted'\n\n/** Return a function that re-renders this component, if still mounted */\nexport function useForceUpdate() {\n  const update = useState<any>()[1]\n  const isMounted = useIsMounted()\n  return () => {\n    if (isMounted.current) {\n      update(Math.random())\n    }\n  }\n}\n", "import { useRef } from 'react'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\nexport const useIsMounted = () => {\n  const isMounted = useRef(false)\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true\n\n    return () => {\n      isMounted.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n", "import { useEffect, useLayoutEffect } from 'react'\n\nimport { isSSR } from '../helpers'\n\n/**\n * Use this to read layout from the DOM and synchronously\n * re-render if the isSSR returns true. Updates scheduled\n * inside `useIsomorphicLayoutEffect` will be flushed\n * synchronously in the browser, before the browser has\n * a chance to paint.\n */\nexport const useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect\n", "import { useEffect, useRef, useState } from 'react'\n\ntype Cache<T> = {\n  inputs?: any[]\n  result?: T\n}\n\n// TODO: remove once merged (https://github.com/alexreardon/use-memo-one/pull/10)\nexport function useMemoOne<T>(getResult: () => T, inputs?: any[]): T {\n  const [initial] = useState(\n    (): Cache<T> => ({\n      inputs,\n      result: getResult(),\n    })\n  )\n\n  const committed = useRef<Cache<T>>()\n  const prevCache = committed.current\n\n  let cache = prevCache\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    )\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult(),\n      }\n    }\n  } else {\n    cache = initial\n  }\n\n  useEffect(() => {\n    committed.current = cache\n    if (prevCache == initial) {\n      initial.inputs = initial.result = undefined\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [cache])\n\n  return cache.result!\n}\n\nfunction areInputsEqual(next: any[], prev: any[]) {\n  if (next.length !== prev.length) {\n    return false\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false\n    }\n  }\n  return true\n}\n", "/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, EffectCallback } from 'react'\n\nexport const useOnce = (effect: EffectCallback) => useEffect(effect, emptyDeps)\n\nconst emptyDeps: any[] = []\n", "import { useEffect, useRef } from 'react'\n\n/** Use a value from the previous render */\nexport function usePrev<T>(value: T): T | undefined {\n  const prevRef = useRef<any>()\n  useEffect(() => {\n    prevRef.current = value\n  })\n  return prevRef.current\n}\n", "import { useState } from 'react'\n\nimport { assign } from '../globals'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\n/**\n * Returns `boolean` or `null`, used to automatically\n * set skipAnimations to the value of the user's\n * `prefers-reduced-motion` query.\n *\n * The return value, post-effect, is the value of their prefered setting\n */\nexport const useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState<boolean | null>(null)\n\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia('(prefers-reduced-motion)')\n\n    const handleMediaChange = (e: MediaQueryListEvent | MediaQueryList) => {\n      setReducedMotion(e.matches)\n\n      assign({\n        skipAnimation: e.matches,\n      })\n    }\n\n    handleMediaChange(mql)\n\n    if (mql.addEventListener) {\n      mql.addEventListener('change', handleMediaChange)\n    } else {\n      mql.addListener(handleMediaChange)\n    }\n\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener('change', handleMediaChange)\n      } else {\n        mql.removeListener(handleMediaChange)\n      }\n    }\n  }, [])\n\n  return reducedMotion\n}\n", "import * as Globals from './globals'\nexport { Globals }\n\nexport * from './FrameLoop'\nexport * from './clamp'\nexport * from './colors'\nexport * from './colorToRgba'\nexport * from './colorMatchers'\nexport * from './createInterpolator'\nexport * from './easings'\nexport * from './stringInterpolation'\nexport * from './deprecations'\nexport * from './helpers'\nexport * from './isAnimatedString'\n/**\n * Should these be moved to a DOM only\n * package to avoid native issues?\n */\nexport * from './dom-events/scroll'\nexport * from './dom-events/resize'\n\nexport * from './hooks/useConstant'\nexport * from './hooks/useForceUpdate'\nexport * from './hooks/useMemoOne'\nexport * from './hooks/useOnce'\nexport * from './hooks/usePrev'\nexport * from './hooks/useIsomorphicLayoutEffect'\nexport * from './hooks/useReducedMotion'\n\nexport * from './fluids'\n\nexport { raf } from '@react-spring/rafz'\nexport type { Timeout } from '@react-spring/rafz'\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,eAAA;AAAAC,QAAA,CAAAD,eAAA;EAAAE,MAAA,EAAAA,CAAA,KAAAA,MAAA;EAAAC,MAAA,EAAAA,CAAA,KAAAA,MAAA;EAAAC,wBAAA,EAAAA,CAAA,KAAAA,wBAAA;EAAAC,aAAA,EAAAA,CAAA,KAAAA,aAAA;EAAAC,EAAA,EAAAA,CAAA,KAAAA,EAAA;EAAAC,WAAA,EAAAA,CAAA,KAAAA;AAAA;AAAA,SAASC,GAAA,QAAiB;;;ACGnB,SAASC,KAAA,EAAO,CAAC;AAEjB,IAAMC,YAAA,GAAeA,CAACC,GAAA,EAAUC,GAAA,EAAUC,KAAA,KAC/CC,MAAA,CAAOC,cAAA,CAAeJ,GAAA,EAAKC,GAAA,EAAK;EAAEC,KAAA;EAAOG,QAAA,EAAU;EAAMC,YAAA,EAAc;AAAK,CAAC;AAOxE,IAAMC,EAAA,GAAK;EAChBC,GAAA,EAAKC,KAAA,CAAMC,OAAA;EACXV,GAAA,EAASW,CAAA,IACP,CAAC,CAACA,CAAA,IAAKA,CAAA,CAAEC,WAAA,CAAYC,IAAA,KAAS;EAChCC,GAAA,EAAOH,CAAA,IAAe,OAAOA,CAAA,KAAM;EACnCI,GAAA,EAAMJ,CAAA,IAA4B,OAAOA,CAAA,KAAM;EAC/CK,GAAA,EAAML,CAAA,IAA4B,OAAOA,CAAA,KAAM;EAC/CM,GAAA,EAAMN,CAAA,IAA+BA,CAAA,KAAM;AAC7C;AAGO,SAASO,QAAQP,CAAA,EAAQQ,CAAA,EAAQ;EACtC,IAAIZ,EAAA,CAAGC,GAAA,CAAIG,CAAC,GAAG;IACb,IAAI,CAACJ,EAAA,CAAGC,GAAA,CAAIW,CAAC,KAAKR,CAAA,CAAES,MAAA,KAAWD,CAAA,CAAEC,MAAA,EAAQ,OAAO;IAChD,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIV,CAAA,CAAES,MAAA,EAAQC,CAAA,IAAK;MACjC,IAAIV,CAAA,CAAEU,CAAC,MAAMF,CAAA,CAAEE,CAAC,GAAG,OAAO;IAC5B;IACA,OAAO;EACT;EACA,OAAOV,CAAA,KAAMQ,CAAA;AACf;AAQO,IAAMG,IAAA,GAAOA,CAClBtB,GAAA,EACAuB,EAAA,KACGvB,GAAA,CAAIwB,OAAA,CAAQD,EAAE;AAGZ,SAASE,SACdzB,GAAA,EACAuB,EAAA,EAKAG,GAAA,EACA;EACA,IAAInB,EAAA,CAAGC,GAAA,CAAIR,GAAG,GAAG;IACf,SAASqB,CAAA,GAAI,GAAGA,CAAA,GAAIrB,GAAA,CAAIoB,MAAA,EAAQC,CAAA,IAAK;MACnCE,EAAA,CAAGI,IAAA,CAAKD,GAAA,EAAY1B,GAAA,CAAIqB,CAAC,MAAAO,MAAA,CAAaP,CAAA,CAAG;IAC3C;IACA;EACF;EACA,WAAWpB,GAAA,IAAOD,GAAA,EAAK;IACrB,IAAIA,GAAA,CAAI6B,cAAA,CAAe5B,GAAG,GAAG;MAC3BsB,EAAA,CAAGI,IAAA,CAAKD,GAAA,EAAY1B,GAAA,CAAIC,GAAG,GAAUA,GAAG;IAC1C;EACF;AACF;AAEO,IAAM6B,OAAA,GAAcnB,CAAA,IACzBJ,EAAA,CAAGU,GAAA,CAAIN,CAAC,IAAI,EAAC,GAAIJ,EAAA,CAAGC,GAAA,CAAIG,CAAC,IAAKA,CAAA,GAAY,CAACA,CAAC;AAQvC,SAASoB,MAAMC,KAAA,EAAYC,QAAA,EAAe;EAC/C,IAAID,KAAA,CAAME,IAAA,EAAM;IACd,MAAMC,KAAA,GAAQ1B,KAAA,CAAM2B,IAAA,CAAKJ,KAAK;IAC9BA,KAAA,CAAMK,KAAA,CAAM;IACZf,IAAA,CAAKa,KAAA,EAAOF,QAAQ;EACtB;AACF;AAGO,IAAMK,UAAA,GAAa,SAAAA,CACxBN,KAAA;EAAA,SAAAO,IAAA,GAAAC,SAAA,CAAApB,MAAA,EACGqB,IAAA,OAAAhC,KAAA,CAAA8B,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAAD,IAAA,CAAAC,IAAA,QAAAF,SAAA,CAAAE,IAAA;EAAA;EAAA,OACAX,KAAA,CAAMC,KAAA,EAAOT,EAAA,IAAMA,EAAA,CAAG,GAAGkB,IAAI,CAAC;AAAA;AAK5B,IAAME,KAAA,GAAQA,CAAA,KACnB,OAAOC,MAAA,KAAW,eAClB,CAACA,MAAA,CAAOC,SAAA,IACR,8BAA8BC,IAAA,CAAKF,MAAA,CAAOC,SAAA,CAAUE,SAAS;;;ADnFxD,IAAItD,wBAAA;AAQJ,IAAIE,EAAA;AAKJ,IAAIH,MAAA,GAAS;AAEb,IAAIE,aAAA,GAAgB;AAEpB,IAAIE,WAAA,GAAoDE,IAAA;AA2BxD,IAAMP,MAAA,GAAUyD,OAAA,IAA6B;EAClD,IAAIA,OAAA,CAAQrD,EAAA,EAAIA,EAAA,GAAKqD,OAAA,CAAQrD,EAAA;EAC7B,IAAIqD,OAAA,CAAQC,GAAA,EAAKpD,GAAA,CAAIoD,GAAA,GAAMD,OAAA,CAAQC,GAAA;EACnC,IAAID,OAAA,CAAQxD,MAAA,KAAW,QAAWA,MAAA,GAASwD,OAAA,CAAQxD,MAAA;EACnD,IAAIwD,OAAA,CAAQtD,aAAA,IAAiB,MAAMA,aAAA,GAAgBsD,OAAA,CAAQtD,aAAA;EAC3D,IAAIsD,OAAA,CAAQvD,wBAAA,EACVA,wBAAA,GAA2BuD,OAAA,CAAQvD,wBAAA;EACrC,IAAIuD,OAAA,CAAQE,qBAAA,EAAuBrD,GAAA,CAAIsD,GAAA,CAAIH,OAAA,CAAQE,qBAAqB;EACxE,IAAIF,OAAA,CAAQI,cAAA,EAAgBvD,GAAA,CAAIuD,cAAA,GAAiBJ,OAAA,CAAQI,cAAA;EACzD,IAAIJ,OAAA,CAAQpD,WAAA,EAAaA,WAAA,GAAcoD,OAAA,CAAQpD,WAAA;EAC/C,IAAIoD,OAAA,CAAQK,SAAA,EAAWxD,GAAA,CAAIwD,SAAA,GAAYL,OAAA,CAAQK,SAAA;AACjD;;;AEtEA,SAASxD,GAAA,IAAAyD,IAAA,QAAW;AAUpB,IAAMC,UAAA,GAAa,mBAAIC,GAAA,CAAqB;AAI5C,IAAIC,YAAA,GAAkC,EAAC;AACvC,IAAIC,SAAA,GAA+B,EAAC;AAMpC,IAAIC,QAAA,GAAW;AAMR,IAAMN,SAAA,GAAY;EACvB,IAAIO,KAAA,EAAO;IACT,OAAO,CAACL,UAAA,CAAWrB,IAAA,IAAQ,CAACuB,YAAA,CAAarC,MAAA;EAC3C;EAAA;EAGAyC,MAAMC,SAAA,EAA4B;IAGhC,IAAIH,QAAA,GAAWG,SAAA,CAAUH,QAAA,EAAU;MACjCJ,UAAA,CAAWQ,GAAA,CAAID,SAAS;MACxBR,IAAA,CAAIU,OAAA,CAAQC,eAAe;IAC7B,OAAO;MACLC,WAAA,CAAYJ,SAAS;MACrBR,IAAA,CAAIa,OAAO;IACb;EACF;EAAA;EAGAA,OAAA;EAAA;EAGAC,KAAKN,SAAA,EAA4B;IAC/B,IAAIH,QAAA,EAAU;MACZL,IAAA,CAAIe,OAAA,CAAQ,MAAMhB,SAAA,CAAUe,IAAA,CAAKN,SAAS,CAAC;IAC7C,OAAO;MACL,MAAMQ,SAAA,GAAYb,YAAA,CAAac,OAAA,CAAQT,SAAS;MAChD,IAAI,CAACQ,SAAA,EAAW;QACdb,YAAA,CAAae,MAAA,CAAOF,SAAA,EAAW,CAAC;QAChCG,aAAA,CAAcX,SAAS;MACzB;IACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAzB,MAAA,EAAQ;IACNoB,YAAA,GAAe,EAAC;IAChBF,UAAA,CAAWlB,KAAA,CAAM;EACnB;AACF;AAEA,SAAS4B,gBAAA,EAAkB;EACzBV,UAAA,CAAW/B,OAAA,CAAQ0C,WAAW;EAC9BX,UAAA,CAAWlB,KAAA,CAAM;EACjBiB,IAAA,CAAIa,OAAO;AACb;AAEA,SAASD,YAAYJ,SAAA,EAA4B;EAC/C,IAAI,CAACL,YAAA,CAAaiB,QAAA,CAASZ,SAAS,GAAGW,aAAA,CAAcX,SAAS;AAChE;AAEA,SAASW,cAAcX,SAAA,EAA4B;EACjDL,YAAA,CAAae,MAAA,CACXG,SAAA,CAAUlB,YAAA,EAAcmB,KAAA,IAASA,KAAA,CAAMjB,QAAA,GAAWG,SAAA,CAAUH,QAAQ,GACpE,GACAG,SACF;AACF;AAEA,SAASK,QAAQU,EAAA,EAAY;EAC3B,MAAMC,SAAA,GAAYpB,SAAA;EAElB,SAASrC,CAAA,GAAI,GAAGA,CAAA,GAAIoC,YAAA,CAAarC,MAAA,EAAQC,CAAA,IAAK;IAC5C,MAAMyC,SAAA,GAAYL,YAAA,CAAapC,CAAC;IAChCsC,QAAA,GAAWG,SAAA,CAAUH,QAAA;IAGrB,IAAI,CAACG,SAAA,CAAUF,IAAA,EAAM;MACjBhE,WAAA,CAAYkE,SAAS;MACvBA,SAAA,CAAUK,OAAA,CAAQU,EAAE;MACpB,IAAI,CAACf,SAAA,CAAUF,IAAA,EAAM;QACnBkB,SAAA,CAAUC,IAAA,CAAKjB,SAAS;MAC1B;IACF;EACF;EACAH,QAAA,GAAW;EAGXD,SAAA,GAAYD,YAAA;EACZC,SAAA,CAAUtC,MAAA,GAAS;EAInBqC,YAAA,GAAeqB,SAAA;EAEf,OAAOrB,YAAA,CAAarC,MAAA,GAAS;AAC/B;AAGA,SAASuD,UAAanE,GAAA,EAAUsC,IAAA,EAA6B;EAC3D,MAAMkC,KAAA,GAAQxE,GAAA,CAAImE,SAAA,CAAU7B,IAAI;EAChC,OAAOkC,KAAA,GAAQ,IAAIxE,GAAA,CAAIY,MAAA,GAAS4D,KAAA;AAClC;;;AC3HO,IAAMC,KAAA,GAAQA,CAACC,GAAA,EAAaC,GAAA,EAAaC,CAAA,KAC9CC,IAAA,CAAKH,GAAA,CAAIG,IAAA,CAAKF,GAAA,CAAIC,CAAA,EAAGF,GAAG,GAAGC,GAAG;;;ACEzB,IAAMG,OAAA,GAAS;EACpBC,WAAA,EAAa;EACbC,SAAA,EAAW;EACXC,YAAA,EAAc;EACdC,IAAA,EAAM;EACNC,UAAA,EAAY;EACZC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,cAAA,EAAgB;EAChBC,IAAA,EAAM;EACNC,UAAA,EAAY;EACZC,KAAA,EAAO;EACPC,SAAA,EAAW;EACXC,WAAA,EAAa;EACbC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,KAAA,EAAO;EACPC,cAAA,EAAgB;EAChBC,QAAA,EAAU;EACVC,OAAA,EAAS;EACTC,IAAA,EAAM;EACNC,QAAA,EAAU;EACVC,QAAA,EAAU;EACVC,aAAA,EAAe;EACfC,QAAA,EAAU;EACVC,SAAA,EAAW;EACXC,QAAA,EAAU;EACVC,SAAA,EAAW;EACXC,WAAA,EAAa;EACbC,cAAA,EAAgB;EAChBC,UAAA,EAAY;EACZC,UAAA,EAAY;EACZC,OAAA,EAAS;EACTC,UAAA,EAAY;EACZC,YAAA,EAAc;EACdC,aAAA,EAAe;EACfC,aAAA,EAAe;EACfC,aAAA,EAAe;EACfC,aAAA,EAAe;EACfC,UAAA,EAAY;EACZC,QAAA,EAAU;EACVC,WAAA,EAAa;EACbC,OAAA,EAAS;EACTC,OAAA,EAAS;EACTC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,WAAA,EAAa;EACbC,WAAA,EAAa;EACbC,OAAA,EAAS;EACTC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZC,IAAA,EAAM;EACNC,SAAA,EAAW;EACXC,IAAA,EAAM;EACNC,KAAA,EAAO;EACPC,WAAA,EAAa;EACbC,IAAA,EAAM;EACNC,QAAA,EAAU;EACVC,OAAA,EAAS;EACTC,SAAA,EAAW;EACXC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,QAAA,EAAU;EACVC,aAAA,EAAe;EACfC,SAAA,EAAW;EACXC,YAAA,EAAc;EACdC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,oBAAA,EAAsB;EACtBC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,SAAA,EAAW;EACXC,WAAA,EAAa;EACbC,aAAA,EAAe;EACfC,YAAA,EAAc;EACdC,cAAA,EAAgB;EAChBC,cAAA,EAAgB;EAChBC,cAAA,EAAgB;EAChBC,WAAA,EAAa;EACbC,IAAA,EAAM;EACNC,SAAA,EAAW;EACXC,KAAA,EAAO;EACPC,OAAA,EAAS;EACTC,MAAA,EAAQ;EACRC,gBAAA,EAAkB;EAClBC,UAAA,EAAY;EACZC,YAAA,EAAc;EACdC,YAAA,EAAc;EACdC,cAAA,EAAgB;EAChBC,eAAA,EAAiB;EACjBC,iBAAA,EAAmB;EACnBC,eAAA,EAAiB;EACjBC,eAAA,EAAiB;EACjBC,YAAA,EAAc;EACdC,SAAA,EAAW;EACXC,SAAA,EAAW;EACXC,QAAA,EAAU;EACVC,WAAA,EAAa;EACbC,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,KAAA,EAAO;EACPC,SAAA,EAAW;EACXC,MAAA,EAAQ;EACRC,SAAA,EAAW;EACXC,MAAA,EAAQ;EACRC,aAAA,EAAe;EACfC,SAAA,EAAW;EACXC,aAAA,EAAe;EACfC,aAAA,EAAe;EACfC,UAAA,EAAY;EACZC,SAAA,EAAW;EACXC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,UAAA,EAAY;EACZC,MAAA,EAAQ;EACRC,aAAA,EAAe;EACfC,GAAA,EAAK;EACLC,SAAA,EAAW;EACXC,SAAA,EAAW;EACXC,WAAA,EAAa;EACbC,MAAA,EAAQ;EACRC,UAAA,EAAY;EACZC,QAAA,EAAU;EACVC,QAAA,EAAU;EACVC,MAAA,EAAQ;EACRC,MAAA,EAAQ;EACRC,OAAA,EAAS;EACTC,SAAA,EAAW;EACXC,SAAA,EAAW;EACXC,SAAA,EAAW;EACXC,IAAA,EAAM;EACNC,WAAA,EAAa;EACbC,SAAA,EAAW;EACXC,GAAA,EAAK;EACLC,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,MAAA,EAAQ;EACRC,SAAA,EAAW;EACXC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,UAAA,EAAY;EACZC,MAAA,EAAQ;EACRC,WAAA,EAAa;AACf;;;ACzJA,IAAMC,MAAA,GAAS;AACf,IAAMC,UAAA,GAAaD,MAAA,GAAS;AAE5B,SAASlN,KAAA,EAAyB;EAAA,SAAAoN,KAAA,GAAAvM,SAAA,CAAApB,MAAA,EAAjB4N,KAAA,OAAAvO,KAAA,CAAAsO,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAAD,KAAA,CAAAC,KAAA,IAAAzM,SAAA,CAAAyM,KAAA;EAAA;EACf,OAAO,aAAaD,KAAA,CAAME,IAAA,CAAK,aAAa,IAAI;AAClD;AAEO,IAAMC,GAAA,GAAM,IAAIC,MAAA,CAAO,QAAQzN,IAAA,CAAKkN,MAAA,EAAQA,MAAA,EAAQA,MAAM,CAAC;AAC3D,IAAMQ,IAAA,GAAO,IAAID,MAAA,CAAO,SAASzN,IAAA,CAAKkN,MAAA,EAAQA,MAAA,EAAQA,MAAA,EAAQA,MAAM,CAAC;AACrE,IAAMS,GAAA,GAAM,IAAIF,MAAA,CAAO,QAAQzN,IAAA,CAAKkN,MAAA,EAAQC,UAAA,EAAYA,UAAU,CAAC;AACnE,IAAMS,IAAA,GAAO,IAAIH,MAAA,CACtB,SAASzN,IAAA,CAAKkN,MAAA,EAAQC,UAAA,EAAYA,UAAA,EAAYD,MAAM,CACtD;AACO,IAAMW,IAAA,GAAO;AACb,IAAMC,IAAA,GACX;AACK,IAAMC,IAAA,GAAO;AACb,IAAMC,IAAA,GAAO;;;ACmBb,SAASC,eAAeC,KAAA,EAAwB;EACrD,IAAIC,KAAA;EAEJ,IAAI,OAAOD,KAAA,KAAU,UAAU;IAC7B,OAAOA,KAAA,KAAU,MAAMA,KAAA,IAASA,KAAA,IAAS,KAAKA,KAAA,IAAS,aACnDA,KAAA,GACA;EACN;EAGA,IAAKC,KAAA,GAAiBJ,IAAA,CAAKK,IAAA,CAAKF,KAAK,GACnC,OAAOG,QAAA,CAASF,KAAA,CAAM,CAAC,IAAI,MAAM,EAAE,MAAM;EAE3C,IAAMtQ,MAAA,IAAYA,MAAA,CAAOqQ,KAAK,MAAM,QAAW;IAC7C,OAASrQ,MAAA,CAAOqQ,KAAK;EACvB;EAEA,IAAKC,KAAA,GAAiBX,GAAA,CAAIY,IAAA,CAAKF,KAAK,GAAI;IACtC,QACII,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACrBG,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACtBG,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACvB;IAAA;IACF;EAEJ;EAEA,IAAKA,KAAA,GAAiBT,IAAA,CAAKU,IAAA,CAAKF,KAAK,GAAI;IACvC,QACII,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACrBG,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACtBG,QAAA,CAASH,KAAA,CAAM,CAAC,CAAC,KAAK;IAAA;IACvBI,MAAA,CAAOJ,KAAA,CAAM,CAAC,CAAC;IAAA;IACjB;EAEJ;EAEA,IAAKA,KAAA,GAAiBN,IAAA,CAAKO,IAAA,CAAKF,KAAK,GAAI;IACvC,OACEG,QAAA,CACEF,KAAA,CAAM,CAAC,IACLA,KAAA,CAAM,CAAC;IAAA;IACPA,KAAA,CAAM,CAAC,IACPA,KAAA,CAAM,CAAC;IAAA;IACPA,KAAA,CAAM,CAAC,IACPA,KAAA,CAAM,CAAC;IAAA;IACP;IAAA;IACF,EACF,MAAM;EAEV;EAGA,IAAKA,KAAA,GAAiBH,IAAA,CAAKI,IAAA,CAAKF,KAAK,GAAI,OAAOG,QAAA,CAASF,KAAA,CAAM,CAAC,GAAG,EAAE,MAAM;EAE3E,IAAKA,KAAA,GAAiBL,IAAA,CAAKM,IAAA,CAAKF,KAAK,GAAI;IACvC,OACEG,QAAA,CACEF,KAAA,CAAM,CAAC,IACLA,KAAA,CAAM,CAAC;IAAA;IACPA,KAAA,CAAM,CAAC,IACPA,KAAA,CAAM,CAAC;IAAA;IACPA,KAAA,CAAM,CAAC,IACPA,KAAA,CAAM,CAAC;IAAA;IACPA,KAAA,CAAM,CAAC,IACPA,KAAA,CAAM,CAAC;IAAA;IACT,EACF,MAAM;EAEV;EAEA,IAAKA,KAAA,GAAiBR,GAAA,CAAIS,IAAA,CAAKF,KAAK,GAAI;IACtC,QACGM,QAAA,CACCC,QAAA,CAASN,KAAA,CAAM,CAAC,CAAC;IAAA;IACjBO,eAAA,CAAgBP,KAAA,CAAM,CAAC,CAAC;IAAA;IACxBO,eAAA,CAAgBP,KAAA,CAAM,CAAC,CAAC;IAAA;IAC1B,IACE;IAAA;IACF;EAEJ;EAEA,IAAKA,KAAA,GAAiBP,IAAA,CAAKQ,IAAA,CAAKF,KAAK,GAAI;IACvC,QACGM,QAAA,CACCC,QAAA,CAASN,KAAA,CAAM,CAAC,CAAC;IAAA;IACjBO,eAAA,CAAgBP,KAAA,CAAM,CAAC,CAAC;IAAA;IACxBO,eAAA,CAAgBP,KAAA,CAAM,CAAC,CAAC;IAAA;IAC1B,IACEI,MAAA,CAAOJ,KAAA,CAAM,CAAC,CAAC;IAAA;IACjB;EAEJ;EACA,OAAO;AACT;AAEA,SAASQ,QAAQC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAW;EAChD,IAAIA,CAAA,GAAI,GAAGA,CAAA,IAAK;EAChB,IAAIA,CAAA,GAAI,GAAGA,CAAA,IAAK;EAChB,IAAIA,CAAA,GAAI,IAAI,GAAG,OAAOF,CAAA,IAAKC,CAAA,GAAID,CAAA,IAAK,IAAIE,CAAA;EACxC,IAAIA,CAAA,GAAI,IAAI,GAAG,OAAOD,CAAA;EACtB,IAAIC,CAAA,GAAI,IAAI,GAAG,OAAOF,CAAA,IAAKC,CAAA,GAAID,CAAA,KAAM,IAAI,IAAIE,CAAA,IAAK;EAClD,OAAOF,CAAA;AACT;AAEA,SAASJ,SAASO,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAW;EACjD,MAAMJ,CAAA,GAAII,CAAA,GAAI,MAAMA,CAAA,IAAK,IAAID,CAAA,IAAKC,CAAA,GAAID,CAAA,GAAIC,CAAA,GAAID,CAAA;EAC9C,MAAMJ,CAAA,GAAI,IAAIK,CAAA,GAAIJ,CAAA;EAClB,MAAMK,CAAA,GAAIP,OAAA,CAAQC,CAAA,EAAGC,CAAA,EAAGE,CAAA,GAAI,IAAI,CAAC;EACjC,MAAMI,CAAA,GAAIR,OAAA,CAAQC,CAAA,EAAGC,CAAA,EAAGE,CAAC;EACzB,MAAMvP,CAAA,GAAImP,OAAA,CAAQC,CAAA,EAAGC,CAAA,EAAGE,CAAA,GAAI,IAAI,CAAC;EACjC,OACGrL,IAAA,CAAK0L,KAAA,CAAMF,CAAA,GAAI,GAAG,KAAK,KACvBxL,IAAA,CAAK0L,KAAA,CAAMD,CAAA,GAAI,GAAG,KAAK,KACvBzL,IAAA,CAAK0L,KAAA,CAAM5P,CAAA,GAAI,GAAG,KAAK;AAE5B;AAEA,SAAS8O,SAASlP,GAAA,EAAa;EAC7B,MAAMiQ,GAAA,GAAMhB,QAAA,CAASjP,GAAA,EAAK,EAAE;EAC5B,IAAIiQ,GAAA,GAAM,GAAG,OAAO;EACpB,IAAIA,GAAA,GAAM,KAAK,OAAO;EACtB,OAAOA,GAAA;AACT;AAEA,SAASZ,SAASrP,GAAA,EAAa;EAC7B,MAAMiQ,GAAA,GAAMC,UAAA,CAAWlQ,GAAG;EAC1B,QAAUiQ,GAAA,GAAM,MAAO,OAAO,MAAO;AACvC;AAEA,SAASd,OAAOnP,GAAA,EAAa;EAC3B,MAAMC,GAAA,GAAMiQ,UAAA,CAAWlQ,GAAG;EAC1B,IAAIC,GAAA,GAAM,GAAG,OAAO;EACpB,IAAIA,GAAA,GAAM,GAAG,OAAO;EACpB,OAAOqE,IAAA,CAAK0L,KAAA,CAAM/P,GAAA,GAAM,GAAG;AAC7B;AAEA,SAASqP,gBAAgBtP,GAAA,EAAa;EAEpC,MAAMiQ,GAAA,GAAMC,UAAA,CAAWlQ,GAAG;EAC1B,IAAIiQ,GAAA,GAAM,GAAG,OAAO;EACpB,IAAIA,GAAA,GAAM,KAAK,OAAO;EACtB,OAAOA,GAAA,GAAM;AACf;;;ACnLO,SAASE,YAAYC,KAAA,EAAe;EACzC,IAAIC,UAAA,GAAaxB,cAAA,CAAeuB,KAAK;EACrC,IAAIC,UAAA,KAAe,MAAM,OAAOD,KAAA;EAChCC,UAAA,GAAaA,UAAA,IAAc;EAC3B,MAAMP,CAAA,IAAKO,UAAA,GAAa,gBAAgB;EACxC,MAAMN,CAAA,IAAKM,UAAA,GAAa,cAAgB;EACxC,MAAMjQ,CAAA,IAAKiQ,UAAA,GAAa,WAAgB;EACxC,MAAMzQ,CAAA,IAAKyQ,UAAA,GAAa,OAAc;EACtC,eAAAxP,MAAA,CAAeiP,CAAA,QAAAjP,MAAA,CAAMkP,CAAA,QAAAlP,MAAA,CAAMT,CAAA,QAAAS,MAAA,CAAMjB,CAAA;AACnC;;;ACAO,IAAM0Q,kBAAA,GAA0CA,CACrDC,KAAA,EACAC,MAAA,EACAC,WAAA,KACG;EACH,IAAIjR,EAAA,CAAGO,GAAA,CAAIwQ,KAAK,GAAG;IACjB,OAAOA,KAAA;EACT;EAEA,IAAI/Q,EAAA,CAAGC,GAAA,CAAI8Q,KAAK,GAAG;IACjB,OAAOD,kBAAA,CAAmB;MACxBC,KAAA;MACAC,MAAA;MACAC;IACF,CAAC;EACH;EAEA,IAAIjR,EAAA,CAAGQ,GAAA,CAAIuQ,KAAA,CAAMC,MAAA,CAAO,CAAC,CAAC,GAAG;IAC3B,OAAS9R,wBAAA,CAAyB6R,KAAY;EAChD;EAEA,MAAMG,MAAA,GAASH,KAAA;EACf,MAAMI,WAAA,GAAcD,MAAA,CAAOF,MAAA;EAC3B,MAAMI,UAAA,GAAaF,MAAA,CAAOH,KAAA,IAAS,CAAC,GAAG,CAAC;EAExC,MAAMM,eAAA,GACJH,MAAA,CAAOG,eAAA,IAAmBH,MAAA,CAAOD,WAAA,IAAe;EAClD,MAAMK,gBAAA,GACJJ,MAAA,CAAOI,gBAAA,IAAoBJ,MAAA,CAAOD,WAAA,IAAe;EACnD,MAAMM,MAAA,GAASL,MAAA,CAAOK,MAAA,KAAWrB,CAAA,IAAKA,CAAA;EAEtC,OAAQU,KAAA,IAAkB;IACxB,MAAMY,MAAA,GAAQC,SAAA,CAAUb,KAAA,EAAOQ,UAAU;IACzC,OAAOM,WAAA,CACLd,KAAA,EACAQ,UAAA,CAAWI,MAAK,GAChBJ,UAAA,CAAWI,MAAA,GAAQ,CAAC,GACpBL,WAAA,CAAYK,MAAK,GACjBL,WAAA,CAAYK,MAAA,GAAQ,CAAC,GACrBD,MAAA,EACAF,eAAA,EACAC,gBAAA,EACAJ,MAAA,CAAOS,GACT;EACF;AACF;AAEA,SAASD,YACPd,KAAA,EACAgB,QAAA,EACAC,QAAA,EACAC,SAAA,EACAC,SAAA,EACAR,MAAA,EACAF,eAAA,EACAC,gBAAA,EACAK,GAAA,EACA;EACA,IAAIK,MAAA,GAASL,GAAA,GAAMA,GAAA,CAAIf,KAAK,IAAIA,KAAA;EAEhC,IAAIoB,MAAA,GAASJ,QAAA,EAAU;IACrB,IAAIP,eAAA,KAAoB,YAAY,OAAOW,MAAA,UAClCX,eAAA,KAAoB,SAASW,MAAA,GAASJ,QAAA;EACjD;EACA,IAAII,MAAA,GAASH,QAAA,EAAU;IACrB,IAAIP,gBAAA,KAAqB,YAAY,OAAOU,MAAA,UACnCV,gBAAA,KAAqB,SAASU,MAAA,GAASH,QAAA;EAClD;EACA,IAAIC,SAAA,KAAcC,SAAA,EAAW,OAAOD,SAAA;EACpC,IAAIF,QAAA,KAAaC,QAAA,EAAU,OAAOjB,KAAA,IAASgB,QAAA,GAAWE,SAAA,GAAYC,SAAA;EAElE,IAAIH,QAAA,KAAa,CAAAK,QAAA,EAAWD,MAAA,GAAS,CAACA,MAAA,UAC7BH,QAAA,KAAaI,QAAA,EAAUD,MAAA,GAASA,MAAA,GAASJ,QAAA,MAC7CI,MAAA,IAAUA,MAAA,GAASJ,QAAA,KAAaC,QAAA,GAAWD,QAAA;EAEhDI,MAAA,GAAST,MAAA,CAAOS,MAAM;EAEtB,IAAIF,SAAA,KAAc,CAAAG,QAAA,EAAWD,MAAA,GAAS,CAACA,MAAA,UAC9BD,SAAA,KAAcE,QAAA,EAAUD,MAAA,GAASA,MAAA,GAASF,SAAA,MAC9CE,MAAA,GAASA,MAAA,IAAUD,SAAA,GAAYD,SAAA,IAAaA,SAAA;EACjD,OAAOE,MAAA;AACT;AAEA,SAASP,UAAUb,KAAA,EAAeQ,UAAA,EAA+B;EAE/D,SAAStQ,CAAA,GAAI,GAAGA,CAAA,GAAIsQ,UAAA,CAAWvQ,MAAA,GAAS,GAAG,EAAEC,CAAA,EAC3C,IAAIsQ,UAAA,CAAWtQ,CAAC,KAAK8P,KAAA,EAAO;EAC9B,OAAO9P,CAAA,GAAI;AACb;;;AC3FA,IAAMoR,KAAA,GACJ,SAAAA,CAACC,MAAA;EAAA,IAAeC,SAAA,GAAAnQ,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAoQ,SAAA,GAAApQ,SAAA,MAAuB;EAAA,OACtCqQ,SAAA,IAAqB;IACpBA,SAAA,GACEF,SAAA,KAAc,QACVtN,IAAA,CAAKH,GAAA,CAAI2N,SAAA,EAAU,KAAK,IACxBxN,IAAA,CAAKF,GAAA,CAAI0N,SAAA,EAAU,IAAK;IAC9B,MAAMC,QAAA,GAAWD,SAAA,GAAWH,MAAA;IAC5B,MAAMK,OAAA,GACJJ,SAAA,KAAc,QAAQtN,IAAA,CAAK2N,KAAA,CAAMF,QAAQ,IAAIzN,IAAA,CAAK4N,IAAA,CAAKH,QAAQ;IAEjE,OAAO7N,KAAA,CAAM,GAAG,GAAG8N,OAAA,GAAUL,MAAK;EACpC;AAAA;AAyCF,IAAMQ,EAAA,GAAK;AACX,IAAMC,EAAA,GAAKD,EAAA,GAAK;AAChB,IAAME,EAAA,GAAKF,EAAA,GAAK;AAChB,IAAMG,EAAA,GAAM,IAAIhO,IAAA,CAAKiO,EAAA,GAAM;AAC3B,IAAMC,EAAA,GAAM,IAAIlO,IAAA,CAAKiO,EAAA,GAAM;AAE3B,IAAME,SAAA,GAA4BC,CAAA,IAAK;EACrC,MAAMC,EAAA,GAAK;EACX,MAAMC,EAAA,GAAK;EAEX,IAAIF,CAAA,GAAI,IAAIE,EAAA,EAAI;IACd,OAAOD,EAAA,GAAKD,CAAA,GAAIA,CAAA;EAClB,WAAWA,CAAA,GAAI,IAAIE,EAAA,EAAI;IACrB,OAAOD,EAAA,IAAMD,CAAA,IAAK,MAAME,EAAA,IAAMF,CAAA,GAAI;EACpC,WAAWA,CAAA,GAAI,MAAME,EAAA,EAAI;IACvB,OAAOD,EAAA,IAAMD,CAAA,IAAK,OAAOE,EAAA,IAAMF,CAAA,GAAI;EACrC,OAAO;IACL,OAAOC,EAAA,IAAMD,CAAA,IAAK,QAAQE,EAAA,IAAMF,CAAA,GAAI;EACtC;AACF;AAEO,IAAMG,OAAA,GAA4B;EACvCC,MAAA,EAAQJ,CAAA,IAAKA,CAAA;EACbK,UAAA,EAAYL,CAAA,IAAKA,CAAA,GAAIA,CAAA;EACrBM,WAAA,EAAaN,CAAA,IAAK,KAAK,IAAIA,CAAA,KAAM,IAAIA,CAAA;EACrCO,aAAA,EAAeP,CAAA,IAAMA,CAAA,GAAI,MAAM,IAAIA,CAAA,GAAIA,CAAA,GAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,KAAKR,CAAA,GAAI,GAAG,CAAC,IAAI;EACzES,WAAA,EAAaT,CAAA,IAAKA,CAAA,GAAIA,CAAA,GAAIA,CAAA;EAC1BU,YAAA,EAAcV,CAAA,IAAK,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,EAAG,CAAC;EACxCW,cAAA,EAAgBX,CAAA,IACdA,CAAA,GAAI,MAAM,IAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,KAAKR,CAAA,GAAI,GAAG,CAAC,IAAI;EAC1DY,WAAA,EAAaZ,CAAA,IAAKA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA;EAC9Ba,YAAA,EAAcb,CAAA,IAAK,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,EAAG,CAAC;EACxCc,cAAA,EAAgBd,CAAA,IACdA,CAAA,GAAI,MAAM,IAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,KAAKR,CAAA,GAAI,GAAG,CAAC,IAAI;EAC9De,WAAA,EAAaf,CAAA,IAAKA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA;EAClCgB,YAAA,EAAchB,CAAA,IAAK,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,EAAG,CAAC;EACxCiB,cAAA,EAAgBjB,CAAA,IACdA,CAAA,GAAI,MAAM,KAAKA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,KAAKR,CAAA,GAAI,GAAG,CAAC,IAAI;EACnEkB,UAAA,EAAYlB,CAAA,IAAK,IAAIpO,IAAA,CAAKuP,GAAA,CAAKnB,CAAA,GAAIpO,IAAA,CAAKiO,EAAA,GAAM,CAAC;EAC/CuB,WAAA,EAAapB,CAAA,IAAKpO,IAAA,CAAKyP,GAAA,CAAKrB,CAAA,GAAIpO,IAAA,CAAKiO,EAAA,GAAM,CAAC;EAC5CyB,aAAA,EAAetB,CAAA,IAAK,EAAEpO,IAAA,CAAKuP,GAAA,CAAIvP,IAAA,CAAKiO,EAAA,GAAKG,CAAC,IAAI,KAAK;EACnDuB,UAAA,EAAYvB,CAAA,IAAMA,CAAA,KAAM,IAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,KAAKR,CAAA,GAAI,EAAE;EACvDwB,WAAA,EAAaxB,CAAA,IAAMA,CAAA,KAAM,IAAI,IAAI,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,MAAMR,CAAC;EACxDyB,aAAA,EAAezB,CAAA,IACbA,CAAA,KAAM,IACF,IACAA,CAAA,KAAM,IACJ,IACAA,CAAA,GAAI,MACFpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,KAAKR,CAAA,GAAI,EAAE,IAAI,KAC1B,IAAIpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,MAAMR,CAAA,GAAI,EAAE,KAAK;EAC5C0B,UAAA,EAAY1B,CAAA,IAAK,IAAIpO,IAAA,CAAK+P,IAAA,CAAK,IAAI/P,IAAA,CAAK4O,GAAA,CAAIR,CAAA,EAAG,CAAC,CAAC;EACjD4B,WAAA,EAAa5B,CAAA,IAAKpO,IAAA,CAAK+P,IAAA,CAAK,IAAI/P,IAAA,CAAK4O,GAAA,CAAIR,CAAA,GAAI,GAAG,CAAC,CAAC;EAClD6B,aAAA,EAAe7B,CAAA,IACbA,CAAA,GAAI,OACC,IAAIpO,IAAA,CAAK+P,IAAA,CAAK,IAAI/P,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,EAAG,CAAC,CAAC,KAAK,KACzCpO,IAAA,CAAK+P,IAAA,CAAK,IAAI/P,IAAA,CAAK4O,GAAA,CAAI,KAAKR,CAAA,GAAI,GAAG,CAAC,CAAC,IAAI,KAAK;EACrD8B,UAAA,EAAY9B,CAAA,IAAKL,EAAA,GAAKK,CAAA,GAAIA,CAAA,GAAIA,CAAA,GAAIP,EAAA,GAAKO,CAAA,GAAIA,CAAA;EAC3C+B,WAAA,EAAa/B,CAAA,IAAK,IAAIL,EAAA,GAAK/N,IAAA,CAAK4O,GAAA,CAAIR,CAAA,GAAI,GAAG,CAAC,IAAIP,EAAA,GAAK7N,IAAA,CAAK4O,GAAA,CAAIR,CAAA,GAAI,GAAG,CAAC;EACtEgC,aAAA,EAAehC,CAAA,IACbA,CAAA,GAAI,MACCpO,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,EAAG,CAAC,MAAMN,EAAA,GAAK,KAAK,IAAIM,CAAA,GAAIN,EAAA,IAAO,KAChD9N,IAAA,CAAK4O,GAAA,CAAI,IAAIR,CAAA,GAAI,GAAG,CAAC,MAAMN,EAAA,GAAK,MAAMM,CAAA,GAAI,IAAI,KAAKN,EAAA,IAAM,KAAK;EACrEuC,aAAA,EAAejC,CAAA,IACbA,CAAA,KAAM,IACF,IACAA,CAAA,KAAM,IACJ,IACA,CAACpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,KAAKR,CAAA,GAAI,EAAE,IAAIpO,IAAA,CAAKyP,GAAA,EAAKrB,CAAA,GAAI,KAAK,SAASJ,EAAE;EAClEsC,cAAA,EAAgBlC,CAAA,IACdA,CAAA,KAAM,IACF,IACAA,CAAA,KAAM,IACJ,IACApO,IAAA,CAAK4O,GAAA,CAAI,GAAG,MAAMR,CAAC,IAAIpO,IAAA,CAAKyP,GAAA,EAAKrB,CAAA,GAAI,KAAK,QAAQJ,EAAE,IAAI;EAChEuC,gBAAA,EAAkBnC,CAAA,IAChBA,CAAA,KAAM,IACF,IACAA,CAAA,KAAM,IACJ,IACAA,CAAA,GAAI,MACF,EAAEpO,IAAA,CAAK4O,GAAA,CAAI,GAAG,KAAKR,CAAA,GAAI,EAAE,IAAIpO,IAAA,CAAKyP,GAAA,EAAK,KAAKrB,CAAA,GAAI,UAAUF,EAAE,KAAK,IAChElO,IAAA,CAAK4O,GAAA,CAAI,GAAG,MAAMR,CAAA,GAAI,EAAE,IAAIpO,IAAA,CAAKyP,GAAA,EAAK,KAAKrB,CAAA,GAAI,UAAUF,EAAE,IAAK,IACjE;EACVsC,YAAA,EAAcpC,CAAA,IAAK,IAAID,SAAA,CAAU,IAAIC,CAAC;EACtCqC,aAAA,EAAetC,SAAA;EACfuC,eAAA,EAAiBtC,CAAA,IACfA,CAAA,GAAI,OAAO,IAAID,SAAA,CAAU,IAAI,IAAIC,CAAC,KAAK,KAAK,IAAID,SAAA,CAAU,IAAIC,CAAA,GAAI,CAAC,KAAK;EAC1EhB;AACF;;;AChIA,IAAMuD,IAAA,GAAOC,MAAA,CAAOC,GAAA,CAAI,gBAAgB;AACxC,IAAMC,UAAA,GAAaF,MAAA,CAAOC,GAAA,CAAI,sBAAsB;AAgBpD,IAAME,aAAA,GAAiBC,GAAA,IAAgCC,OAAA,CAAQD,GAAA,IAAOA,GAAA,CAAIL,IAAI,CAAC;AAM/E,IAAMO,aAAA,GAAgCF,GAAA,IACpCA,GAAA,IAAOA,GAAA,CAAIL,IAAI,IAAIK,GAAA,CAAIL,IAAI,EAAE,IAAIK,GAAA;AAGnC,IAAMG,iBAAA,GAAwCC,MAAA,IAC5CA,MAAA,CAAON,UAAU,KAAK;AAQxB,SAASO,kBAAkBC,SAAA,EAAeC,KAAA,EAAmB;EAC3D,IAAID,SAAA,CAASE,aAAA,EAAe;IAC1BF,SAAA,CAASE,aAAA,CAAcD,KAAK;EAC9B,OAAO;IACLD,SAAA,CAASC,KAAK;EAChB;AACF;AAUA,SAASE,mBAAmBL,MAAA,EAAaG,KAAA,EAAmB;EAC1D,MAAMG,SAAA,GAAgCN,MAAA,CAAON,UAAU;EACvD,IAAIY,SAAA,EAAW;IACbA,SAAA,CAAUvV,OAAA,CAAQmV,SAAA,IAAY;MAC5BD,iBAAA,CAAkBC,SAAA,EAAUC,KAAK;IACnC,CAAC;EACH;AACF;AAuBA,IAAeI,UAAA,GAAf,MAAkE;EAMhEpW,YAAYqW,GAAA,EAAe;IACzB,IAAI,CAACA,GAAA,IAAO,EAAEA,GAAA,GAAM,KAAKA,GAAA,GAAM;MAC7B,MAAMC,KAAA,CAAM,gBAAgB;IAC9B;IACAC,cAAA,CAAe,MAAMF,GAAG;EAC1B;AAQF;AAjBWjB,IAAA,EAEAG,UAAA;AAiCX,IAAMgB,cAAA,GAAiBA,CAACV,MAAA,EAAgBQ,GAAA,KACtCG,SAAA,CAAUX,MAAA,EAAQT,IAAA,EAAMiB,GAAG;AAa7B,SAASI,iBAAiBZ,MAAA,EAAaE,SAAA,EAAyB;EAC9D,IAAIF,MAAA,CAAOT,IAAI,GAAG;IAChB,IAAIe,SAAA,GAAgCN,MAAA,CAAON,UAAU;IACrD,IAAI,CAACY,SAAA,EAAW;MACdK,SAAA,CAAUX,MAAA,EAAQN,UAAA,EAAaY,SAAA,GAAY,mBAAIvT,GAAA,CAAI,CAAE;IACvD;IACA,IAAI,CAACuT,SAAA,CAAUO,GAAA,CAAIX,SAAQ,GAAG;MAC5BI,SAAA,CAAUhT,GAAA,CAAI4S,SAAQ;MACtB,IAAIF,MAAA,CAAOc,aAAA,EAAe;QACxBd,MAAA,CAAOc,aAAA,CAAcR,SAAA,CAAU7U,IAAA,EAAMyU,SAAQ;MAC/C;IACF;EACF;EACA,OAAOA,SAAA;AACT;AAaA,SAASa,oBAAoBf,MAAA,EAAaE,SAAA,EAAyB;EACjE,MAAMI,SAAA,GAAgCN,MAAA,CAAON,UAAU;EACvD,IAAIY,SAAA,IAAaA,SAAA,CAAUO,GAAA,CAAIX,SAAQ,GAAG;IACxC,MAAMc,KAAA,GAAQV,SAAA,CAAU7U,IAAA,GAAO;IAC/B,IAAIuV,KAAA,EAAO;MACTV,SAAA,CAAUW,MAAA,CAAOf,SAAQ;IAC3B,OAAO;MACLF,MAAA,CAAON,UAAU,IAAI;IACvB;IACA,IAAIM,MAAA,CAAOkB,eAAA,EAAiB;MAC1BlB,MAAA,CAAOkB,eAAA,CAAgBF,KAAA,EAAOd,SAAQ;IACxC;EACF;AACF;AAEA,IAAMS,SAAA,GAAYA,CAACX,MAAA,EAAaxW,GAAA,EAAUC,KAAA,KACxCC,MAAA,CAAOC,cAAA,CAAeqW,MAAA,EAAQxW,GAAA,EAAK;EACjCC,KAAA;EACAG,QAAA,EAAU;EACVC,YAAA,EAAc;AAChB,CAAC;;;ACxMI,IAAMsX,WAAA,GAAc;AAIpB,IAAMC,UAAA,GACX;AAGK,IAAMC,SAAA,GAAY,IAAI1I,MAAA,KAAAxN,MAAA,CAAWgW,WAAA,CAAYG,MAAA,kBAAqB,GAAG;AAGrE,IAAMC,SAAA,GACX;AAUK,IAAMC,gBAAA,GACX;;;ACXK,IAAMC,cAAA,GAAkB/G,KAAA,IAA0B;EACvD,MAAM,CAACgH,KAAA,EAAOC,QAAQ,IAAIC,gBAAA,CAAiBlH,KAAK;EAEhD,IAAI,CAACgH,KAAA,IAASxV,KAAA,CAAM,GAAG;IACrB,OAAOwO,KAAA;EACT;EAEA,MAAMjR,KAAA,GAAQ0C,MAAA,CACX0V,gBAAA,CAAiBC,QAAA,CAASC,eAAe,EACzCC,gBAAA,CAAiBN,KAAK;EAEzB,IAAIjY,KAAA,EAAO;IAKT,OAAOA,KAAA,CAAMwY,IAAA,CAAK;EACpB,WAAWN,QAAA,IAAYA,QAAA,CAASO,UAAA,CAAW,IAAI,GAAG;IAKhD,MAAMC,MAAA,GAAQhW,MAAA,CACX0V,gBAAA,CAAiBC,QAAA,CAASC,eAAe,EACzCC,gBAAA,CAAiBL,QAAQ;IAK5B,IAAIQ,MAAA,EAAO;MACT,OAAOA,MAAA;IACT,OAAO;MACL,OAAOzH,KAAA;IACT;EACF,WAAWiH,QAAA,IAAYH,gBAAA,CAAiBnV,IAAA,CAAKsV,QAAQ,GAAG;IAItD,OAAOF,cAAA,CAAeE,QAAQ;EAChC,WAAWA,QAAA,EAAU;IAInB,OAAOA,QAAA;EACT;EAMA,OAAOjH,KAAA;AACT;AAEA,IAAMkH,gBAAA,GAAoBQ,OAAA,IAAoB;EAC5C,MAAM/I,KAAA,GAAQmI,gBAAA,CAAiBlI,IAAA,CAAK8I,OAAO;EAC3C,IAAI,CAAC/I,KAAA,EAAO,OAAO,GAAE;EAErB,MAAM,GAAGqI,KAAA,EAAOC,QAAQ,IAAItI,KAAA;EAC5B,OAAO,CAACqI,KAAA,EAAOC,QAAQ;AACzB;;;ACzDA,IAAIU,eAAA;AAIJ,IAAMC,SAAA,GAAYA,CAACC,CAAA,EAAQC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,EAAA,aAAAxX,MAAA,CACrDyD,IAAA,CAAK0L,KAAA,CAAMkI,EAAE,SAAArX,MAAA,CAAMyD,IAAA,CAAK0L,KAAA,CAAMmI,EAAE,SAAAtX,MAAA,CAAMyD,IAAA,CAAK0L,KAAA,CAAMoI,EAAE,SAAAvX,MAAA,CAAMwX,EAAA;AAY5D,IAAMC,yBAAA,GACX5H,MAAA,IACG;EACH,IAAI,CAACqH,eAAA,EACHA,eAAA,GAAoBtZ,MAAA;EAAA;EAEhB,IAAI4P,MAAA,KAAAxN,MAAA,CAAWzB,MAAA,CAAOmZ,IAAA,CAAO9Z,MAAM,EAAE0P,IAAA,CAAK,GAAG,gBAAa,GAAG;EAAA;EAE7D;EAGN,MAAMqC,MAAA,GAASE,MAAA,CAAOF,MAAA,CAAOW,GAAA,CAAIhS,KAAA,IAAS;IACxC,OAAOqW,aAAA,CAAcrW,KAAK,EACvBqZ,OAAA,CAAQtB,gBAAA,EAAkBC,cAAc,EACxCqB,OAAA,CAAQ1B,UAAA,EAAY3G,WAAW,EAC/BqI,OAAA,CAAQT,eAAA,EAAiB5H,WAAW;EACzC,CAAC;EAGD,MAAMsI,SAAA,GAAYjI,MAAA,CAAOW,GAAA,CAAIhS,KAAA,IAASA,KAAA,CAAM4P,KAAA,CAAM8H,WAAW,EAAG1F,GAAA,CAAIuH,MAAM,CAAC;EAG3E,MAAMC,YAAA,GAAeF,SAAA,CAAU,CAAC,EAAEtH,GAAA,CAAI,CAAC8G,CAAA,EAAG3X,CAAA,KACxCmY,SAAA,CAAUtH,GAAA,CAAIyH,MAAA,IAAU;IACtB,IAAI,EAAEtY,CAAA,IAAKsY,MAAA,GAAS;MAClB,MAAMzC,KAAA,CAAM,gDAAgD;IAC9D;IACA,OAAOyC,MAAA,CAAOtY,CAAC;EACjB,CAAC,CACH;EAGA,MAAMuY,aAAA,GAAgBF,YAAA,CAAaxH,GAAA,CAAI2H,OAAA,IACrCxI,kBAAA,CAAAyI,aAAA,CAAAA,aAAA,KAAwBrI,MAAA;IAAQF,MAAA,EAAAsI;EAAA,EAAQ,CAC1C;EAGA,OAAQ1I,KAAA,IAAkB;IAAA,IAAA4I,YAAA;IAExB,MAAMC,WAAA,GACJ,CAAClC,SAAA,CAAUhV,IAAA,CAAKyO,MAAA,CAAO,CAAC,CAAC,OAAAwI,YAAA,GACzBxI,MAAA,CAAO0I,IAAA,CAAK/Z,KAAA,IAAS4X,SAAA,CAAUhV,IAAA,CAAK5C,KAAK,CAAC,eAAA6Z,YAAA,uBAA1CA,YAAA,CAA6CR,OAAA,CAAQ3B,WAAA,EAAa,EAAE;IAEtE,IAAIvW,CAAA,GAAI;IACR,OAAOkQ,MAAA,CAAO,CAAC,EACZgI,OAAA,CACC3B,WAAA,EACA,SAAAhW,MAAA,CAASgY,aAAA,CAAcvY,CAAA,EAAG,EAAE8P,KAAK,GAAAvP,MAAA,CAAIoY,WAAA,IAAe,GACtD,EACCT,OAAA,CAAQvB,SAAA,EAAWe,SAAS;EACjC;AACF;;;AClFO,IAAMmB,MAAA,GAAS;AAEf,IAAMC,IAAA,GAA6C5Y,EAAA,IAAc;EACtE,MAAM6Y,IAAA,GAAO7Y,EAAA;EACb,IAAI8Y,MAAA,GAAS;EAEb,IAAI,OAAOD,IAAA,IAAQ,YAAY;IAC7B,MAAM,IAAIE,SAAA,IAAA1Y,MAAA,CAAasY,MAAA,uCAA0C;EACnE;EAEA,OAAO,YAAkB;IACvB,IAAI,CAACG,MAAA,EAAQ;MACXD,IAAA,CAAK,GAAA5X,SAAO;MACZ6X,MAAA,GAAS;IACX;EACF;AACF;AAEA,IAAME,eAAA,GAAkBJ,IAAA,CAAKK,OAAA,CAAQC,IAAI;AAClC,SAASC,qBAAA,EAAuB;EACrCH,eAAA,IAAA3Y,MAAA,CACKsY,MAAA,0EACL;AACF;AAEA,IAAMS,cAAA,GAAiBR,IAAA,CAAKK,OAAA,CAAQC,IAAI;AACjC,SAASG,oBAAA,EAAsB;EACpCD,cAAA,IAAA/Y,MAAA,CACKsY,MAAA,sJACL;AACF;;;AC3BO,SAASW,iBAAiB3a,KAAA,EAAiC;EAChE,OACEK,EAAA,CAAGQ,GAAA,CAAIb,KAAK,MACXA,KAAA,CAAM,CAAC,KAAK,OACX,KAAK4C,IAAA,CAAK5C,KAAK;EAAA;EAEd,CAACyC,KAAA,CAAM,KAAKsV,gBAAA,CAAiBnV,IAAA,CAAK5C,KAAK,KACxCA,KAAA,KAAYV,MAAA,IAAU,CAAC;AAE7B;;;ACdA,SAASK,GAAA,IAAAib,IAAA,QAAW;;;ACEpB,IAAIC,QAAA;AAEJ,IAAMC,cAAA,GAAiB,mBAAIC,OAAA,CAAwC;AAEnE,IAAMC,iBAAA,GAAqBC,OAAA,IACzBA,OAAA,CAAQ3Z,OAAA,CAAQ4Z,IAAA,IAA6B;EAAA,IAAAC,mBAAA;EAAA,IAA5B;IAAE5E,MAAA;IAAQ6E;EAAY,IAAAF,IAAA;EACrC,QAAAC,mBAAA,GAAOL,cAAA,CAAe/D,GAAA,CAAIR,MAAM,eAAA4E,mBAAA,uBAAzBA,mBAAA,CAA4B7Z,OAAA,CAAQ+Z,OAAA,IAAWA,OAAA,CAAQD,WAAW,CAAC;AAC5E,CAAC;AAEI,SAASE,cAAcD,OAAA,EAA2B9E,MAAA,EAAqB;EAI5E,IAAI,CAACsE,QAAA,EAAU;IACb,IAAI,OAAOU,cAAA,KAAmB,aAAa;MACzCV,QAAA,GAAW,IAAIU,cAAA,CAAeP,iBAAiB;IACjD;EACF;EAKA,IAAIQ,eAAA,GAAkBV,cAAA,CAAe/D,GAAA,CAAIR,MAAM;EAM/C,IAAI,CAACiF,eAAA,EAAiB;IACpBA,eAAA,GAAkB,mBAAIlY,GAAA,CAAI;IAC1BwX,cAAA,CAAeW,GAAA,CAAIlF,MAAA,EAAQiF,eAAe;EAC5C;EAMAA,eAAA,CAAgB3X,GAAA,CAAIwX,OAAO;EAE3B,IAAIR,QAAA,EAAU;IACZA,QAAA,CAASa,OAAA,CAAQnF,MAAM;EACzB;EAKA,OAAO,MAAM;IACX,MAAMoF,gBAAA,GAAkBb,cAAA,CAAe/D,GAAA,CAAIR,MAAM;IAEjD,IAAI,CAACoF,gBAAA,EAAiB;IAEtBA,gBAAA,CAAgBnE,MAAA,CAAO6D,OAAO;IAE9B,IAAI,CAACM,gBAAA,CAAgB3Z,IAAA,IAAQ6Y,QAAA,EAAU;MACrCA,QAAA,CAASe,SAAA,CAAUrF,MAAM;IAC3B;EACF;AACF;;;ACzDA,IAAMsF,SAAA,GAAY,mBAAIvY,GAAA,CAAsB;AAE5C,IAAIwY,0BAAA;AAEJ,IAAMC,mBAAA,GAAsBA,CAAA,KAAM;EAChC,MAAMC,YAAA,GAAeA,CAAA,KAAM;IACzBH,SAAA,CAAUva,OAAA,CAAQ2a,QAAA,IAChBA,QAAA,CAAS;MACPC,KAAA,EAAOxZ,MAAA,CAAOyZ,UAAA;MACdC,MAAA,EAAQ1Z,MAAA,CAAO2Z;IACjB,CAAC,CACH;EACF;EAEA3Z,MAAA,CAAO4Z,gBAAA,CAAiB,UAAUN,YAAY;EAE9C,OAAO,MAAM;IACXtZ,MAAA,CAAO6Z,mBAAA,CAAoB,UAAUP,YAAY;EACnD;AACF;AAEO,IAAMQ,YAAA,GAAgBP,QAAA,IAA+B;EAC1DJ,SAAA,CAAUhY,GAAA,CAAIoY,QAAQ;EAEtB,IAAI,CAACH,0BAAA,EAA4B;IAC/BA,0BAAA,GAA6BC,mBAAA,CAAoB;EACnD;EAEA,OAAO,MAAM;IACXF,SAAA,CAAUrE,MAAA,CAAOyE,QAAQ;IAEzB,IAAI,CAACJ,SAAA,CAAU7Z,IAAA,IAAQ8Z,0BAAA,EAA4B;MACjDA,0BAAA,CAA2B;MAC3BA,0BAAA,GAA6B;IAC/B;EACF;AACF;;;AC1BO,IAAMW,QAAA,GAAW,SAAAA,CACtBR,QAAA,EAEiB;EAAA,IADjB;IAAES,SAAA,GAAYrE,QAAA,CAASC;EAAgB,IAAAhW,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAoQ,SAAA,GAAApQ,SAAA,MAAqB,CAAC;EAE7D,IAAIoa,SAAA,KAAcrE,QAAA,CAASC,eAAA,EAAiB;IAC1C,OAAOkE,YAAA,CAAaP,QAAQ;EAC9B,OAAO;IACL,OAAOX,aAAA,CAAcW,QAAA,EAAUS,SAAS;EAC1C;AACF;;;ACrBO,IAAMC,QAAA,GAAWA,CAAC3X,GAAA,EAAaC,GAAA,EAAajF,KAAA,KACjDiF,GAAA,GAAMD,GAAA,KAAQ,IAAI,KAAKhF,KAAA,GAAQgF,GAAA,KAAQC,GAAA,GAAMD,GAAA;;;ACG/C,IAAM4X,WAAA,GAAc;EAClBrJ,CAAA,EAAG;IACDrS,MAAA,EAAQ;IACR2b,QAAA,EAAU;EACZ;EACAC,CAAA,EAAG;IACD5b,MAAA,EAAQ;IACR2b,QAAA,EAAU;EACZ;AACF;AAqBO,IAAME,aAAA,GAAN,MAAoB;EAKzBrc,YAAYub,QAAA,EAA4BS,SAAA,EAAwB;IAWhE,KAAQM,UAAA,GAAa,OAAmB;MACtCrE,OAAA,EAAS;MACTgE,QAAA,EAAU;MACVM,YAAA,EAAc;IAChB;IAEA,KAAQC,UAAA,GAAcC,QAAA,IAAgD;MACpE,MAAMC,IAAA,GAAO,KAAKC,IAAA,CAAKF,QAAQ;MAC/B,MAAM;QAAEjc,MAAA;QAAQ2b;MAAS,IAAID,WAAA,CAAYO,QAAQ;MAEjDC,IAAA,CAAKzE,OAAA,GAAU,KAAK+D,SAAA,UAAAhb,MAAA,CAAmBmb,QAAA,EAAU;MACjDO,IAAA,CAAKH,YAAA,GACH,KAAKP,SAAA,UAAAhb,MAAA,CAAmBR,MAAA,EAAQ,GAAI,KAAKwb,SAAA,UAAAhb,MAAA,CAAmBR,MAAA,EAAQ;MAEtEkc,IAAA,CAAKT,QAAA,GAAWA,QAAA,CAAS,GAAGS,IAAA,CAAKH,YAAA,EAAcG,IAAA,CAAKzE,OAAO;IAC7D;IAEA,KAAQ2E,MAAA,GAAS,MAAM;MACrB,KAAKJ,UAAA,CAAW,GAAG;MACnB,KAAKA,UAAA,CAAW,GAAG;IACrB;IAEA,KAAQK,SAAA,GAAY,MAAM;MACxB,KAAKtB,QAAA,CAAS,KAAKoB,IAAI;IACzB;IAEA,KAAApZ,OAAA,GAAU,MAAM;MACd,KAAKqZ,MAAA,CAAO;MACZ,KAAKC,SAAA,CAAU;IACjB;IAvCE,KAAKtB,QAAA,GAAWA,QAAA;IAChB,KAAKS,SAAA,GAAYA,SAAA;IAEjB,KAAKW,IAAA,GAAO;MACVG,IAAA,EAAM;MACNjK,CAAA,EAAG,KAAKyJ,UAAA,CAAW;MACnBF,CAAA,EAAG,KAAKE,UAAA,CAAW;IACrB;EACF;AAgCF;;;ALlEA,IAAMS,eAAA,GAAkB,mBAAI1C,OAAA,CAAgC;AAC5D,IAAM2C,eAAA,GAAkB,mBAAI3C,OAAA,CAA+B;AAC3D,IAAM4C,gBAAA,GAAmB,mBAAI5C,OAAA,CAAqC;AAElE,IAAM6C,SAAA,GAAalB,SAAA,IACjBA,SAAA,KAAcrE,QAAA,CAASC,eAAA,GAAkB5V,MAAA,GAASga,SAAA;AAE7C,IAAMmB,QAAA,GAAW,SAAAA,CACtB5B,QAAA,EAEG;EAAA,IADH;IAAES,SAAA,GAAYrE,QAAA,CAASC;EAAgB,IAAAhW,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAoQ,SAAA,GAAApQ,SAAA,MAAqB,CAAC;EAK7D,IAAIwb,iBAAA,GAAoBH,gBAAA,CAAiB5G,GAAA,CAAI2F,SAAS;EAKtD,IAAI,CAACoB,iBAAA,EAAmB;IACtBA,iBAAA,GAAoB,mBAAIxa,GAAA,CAAI;IAC5Bqa,gBAAA,CAAiBlC,GAAA,CAAIiB,SAAA,EAAWoB,iBAAiB;EACnD;EAKA,MAAMC,gBAAA,GAAmB,IAAIhB,aAAA,CAAcd,QAAA,EAAUS,SAAS;EAC9DoB,iBAAA,CAAkBja,GAAA,CAAIka,gBAAgB;EAKtC,IAAI,CAACN,eAAA,CAAgBrG,GAAA,CAAIsF,SAAS,GAAG;IAInC,MAAMsB,QAAA,GAAWA,CAAA,KAAM;MAAA,IAAAC,kBAAA;MACrB,CAAAA,kBAAA,GAAAH,iBAAA,cAAAG,kBAAA,eAAAA,kBAAA,CAAmB3c,OAAA,CAAQ+Z,OAAA,IAAWA,OAAA,CAAQpX,OAAA,CAAQ,CAAC;MACvD,OAAO;IACT;IAEAwZ,eAAA,CAAgBhC,GAAA,CAAIiB,SAAA,EAAWsB,QAAQ;IAEvC,MAAMzH,MAAA,GAASqH,SAAA,CAAUlB,SAAS;IAMlCha,MAAA,CAAO4Z,gBAAA,CAAiB,UAAU0B,QAAA,EAAU;MAAEE,OAAA,EAAS;IAAK,CAAC;IAE7D,IAAIxB,SAAA,KAAcrE,QAAA,CAASC,eAAA,EAAiB;MAC1CoF,eAAA,CAAgBjC,GAAA,CAAIiB,SAAA,EAAWD,QAAA,CAASuB,QAAA,EAAU;QAAEtB;MAAU,CAAC,CAAC;IAClE;IAKAnG,MAAA,CAAO+F,gBAAA,CAAiB,UAAU0B,QAAA,EAAU;MAAEE,OAAA,EAAS;IAAK,CAAC;EAC/D;EAKA,MAAMC,aAAA,GAAgBV,eAAA,CAAgB1G,GAAA,CAAI2F,SAAS;EACnD9B,IAAA,CAAIuD,aAAa;EAEjB,OAAO,MAAM;IAIXvD,IAAA,CAAIwD,MAAA,CAAOD,aAAa;IAKxB,MAAME,kBAAA,GAAoBV,gBAAA,CAAiB5G,GAAA,CAAI2F,SAAS;IACxD,IAAI,CAAC2B,kBAAA,EAAmB;IAExBA,kBAAA,CAAkB7G,MAAA,CAAOuG,gBAAgB;IAEzC,IAAIM,kBAAA,CAAkBrc,IAAA,EAAM;IAK5B,MAAMgc,QAAA,GAAWP,eAAA,CAAgB1G,GAAA,CAAI2F,SAAS;IAC9Ce,eAAA,CAAgBjG,MAAA,CAAOkF,SAAS;IAEhC,IAAIsB,QAAA,EAAU;MAAA,IAAAM,oBAAA;MACZV,SAAA,CAAUlB,SAAS,EAAEH,mBAAA,CAAoB,UAAUyB,QAAQ;MAC3Dtb,MAAA,CAAO6Z,mBAAA,CAAoB,UAAUyB,QAAQ;MAE7C,CAAAM,oBAAA,GAAAZ,eAAA,CAAgB3G,GAAA,CAAI2F,SAAS,eAAA4B,oBAAA,eAA7BA,oBAAA,CAAiC;IACnC;EACF;AACF;;;AM/GA,SAASC,MAAA,QAAc;AAOhB,SAASC,YAAeC,IAAA,EAAe;EAC5C,MAAMC,GAAA,GAAMH,MAAA,CAAiB,IAAI;EAEjC,IAAIG,GAAA,CAAI/F,OAAA,KAAY,MAAM;IACxB+F,GAAA,CAAI/F,OAAA,GAAU8F,IAAA,CAAK;EACrB;EAEA,OAAOC,GAAA,CAAI/F,OAAA;AACb;;;ACfA,SAASgG,QAAA,QAAgB;;;ACAzB,SAASJ,MAAA,IAAAK,OAAA,QAAc;;;ACAvB,SAASC,SAAA,EAAWC,eAAA,QAAuB;AAWpC,IAAMC,yBAAA,GAA4Btc,KAAA,CAAM,IAAIoc,SAAA,GAAYC,eAAA;;;ADRxD,IAAME,YAAA,GAAeA,CAAA,KAAM;EAChC,MAAMC,SAAA,GAAYL,OAAA,CAAO,KAAK;EAC9BG,yBAAA,CAA0B,MAAM;IAC9BE,SAAA,CAAUtG,OAAA,GAAU;IAEpB,OAAO,MAAM;MACXsG,SAAA,CAAUtG,OAAA,GAAU;IACtB;EACF,GAAG,EAAE;EAEL,OAAOsG,SAAA;AACT;;;ADVO,SAASC,eAAA,EAAiB;EAC/B,MAAM5B,MAAA,GAASqB,QAAA,CAAc,EAAE,CAAC;EAChC,MAAMM,SAAA,GAAYD,YAAA,CAAa;EAC/B,OAAO,MAAM;IACX,IAAIC,SAAA,CAAUtG,OAAA,EAAS;MACrB2E,MAAA,CAAOnY,IAAA,CAAKga,MAAA,CAAO,CAAC;IACtB;EACF;AACF;;;AGZA,SAASN,SAAA,IAAAO,UAAA,EAAWb,MAAA,IAAAc,OAAA,EAAQV,QAAA,IAAAW,SAAA,QAAgB;AAQrC,SAASC,WAAcC,SAAA,EAAoBC,MAAA,EAAmB;EACnE,MAAM,CAACC,OAAO,IAAIJ,SAAA,CAChB,OAAiB;IACfG,MAAA;IACApN,MAAA,EAAQmN,SAAA,CAAU;EACpB,EACF;EAEA,MAAMG,SAAA,GAAYN,OAAA,CAAiB;EACnC,MAAMO,SAAA,GAAYD,SAAA,CAAUhH,OAAA;EAE5B,IAAIkH,KAAA,GAAQD,SAAA;EACZ,IAAIC,KAAA,EAAO;IACT,MAAMC,QAAA,GAAW1J,OAAA,CACfqJ,MAAA,IAAUI,KAAA,CAAMJ,MAAA,IAAUM,cAAA,CAAeN,MAAA,EAAQI,KAAA,CAAMJ,MAAM,CAC/D;IACA,IAAI,CAACK,QAAA,EAAU;MACbD,KAAA,GAAQ;QACNJ,MAAA;QACApN,MAAA,EAAQmN,SAAA,CAAU;MACpB;IACF;EACF,OAAO;IACLK,KAAA,GAAQH,OAAA;EACV;EAEAN,UAAA,CAAU,MAAM;IACdO,SAAA,CAAUhH,OAAA,GAAUkH,KAAA;IACpB,IAAID,SAAA,IAAaF,OAAA,EAAS;MACxBA,OAAA,CAAQD,MAAA,GAASC,OAAA,CAAQrN,MAAA,GAAS;IACpC;EAEF,GAAG,CAACwN,KAAK,CAAC;EAEV,OAAOA,KAAA,CAAMxN,MAAA;AACf;AAEA,SAAS0N,eAAeC,IAAA,EAAaC,IAAA,EAAa;EAChD,IAAID,IAAA,CAAK9e,MAAA,KAAW+e,IAAA,CAAK/e,MAAA,EAAQ;IAC/B,OAAO;EACT;EACA,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI6e,IAAA,CAAK9e,MAAA,EAAQC,CAAA,IAAK;IACpC,IAAI6e,IAAA,CAAK7e,CAAC,MAAM8e,IAAA,CAAK9e,CAAC,GAAG;MACvB,OAAO;IACT;EACF;EACA,OAAO;AACT;;;ACtDA,SAAS0d,SAAA,IAAAqB,UAAA,QAAiC;AAEnC,IAAMC,OAAA,GAAWC,MAAA,IAA2BF,UAAA,CAAUE,MAAA,EAAQC,SAAS;AAE9E,IAAMA,SAAA,GAAmB,EAAC;;;ACL1B,SAASxB,SAAA,IAAAyB,UAAA,EAAW/B,MAAA,IAAAgC,OAAA,QAAc;AAG3B,SAASC,QAAWxgB,KAAA,EAAyB;EAClD,MAAMygB,OAAA,GAAUF,OAAA,CAAY;EAC5BD,UAAA,CAAU,MAAM;IACdG,OAAA,CAAQ9H,OAAA,GAAU3Y,KAAA;EACpB,CAAC;EACD,OAAOygB,OAAA,CAAQ9H,OAAA;AACjB;;;ACTA,SAASgG,QAAA,IAAA+B,SAAA,QAAgB;AAYlB,IAAMC,gBAAA,GAAmBA,CAAA,KAAM;EACpC,MAAM,CAACC,aAAA,EAAeC,gBAAgB,IAAIH,SAAA,CAAyB,IAAI;EAEvE3B,yBAAA,CAA0B,MAAM;IAC9B,MAAM+B,GAAA,GAAMpe,MAAA,CAAOqe,UAAA,CAAW,0BAA0B;IAExD,MAAMC,iBAAA,GAAqBC,CAAA,IAA4C;MACrEJ,gBAAA,CAAiBI,CAAA,CAAEC,OAAO;MAE1B7hB,MAAA,CAAO;QACLG,aAAA,EAAeyhB,CAAA,CAAEC;MACnB,CAAC;IACH;IAEAF,iBAAA,CAAkBF,GAAG;IAErB,IAAIA,GAAA,CAAIxE,gBAAA,EAAkB;MACxBwE,GAAA,CAAIxE,gBAAA,CAAiB,UAAU0E,iBAAiB;IAClD,OAAO;MACLF,GAAA,CAAIK,WAAA,CAAYH,iBAAiB;IACnC;IAEA,OAAO,MAAM;MACX,IAAIF,GAAA,CAAIvE,mBAAA,EAAqB;QAC3BuE,GAAA,CAAIvE,mBAAA,CAAoB,UAAUyE,iBAAiB;MACrD,OAAO;QACLF,GAAA,CAAIM,cAAA,CAAeJ,iBAAiB;MACtC;IACF;EACF,GAAG,EAAE;EAEL,OAAOJ,aAAA;AACT;;;ACbA,SAASjhB,GAAA,IAAA0hB,IAAA,QAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}