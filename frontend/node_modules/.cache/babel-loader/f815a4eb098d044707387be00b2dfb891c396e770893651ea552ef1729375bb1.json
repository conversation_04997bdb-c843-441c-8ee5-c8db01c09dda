{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Slider, Grid, Button, CircularProgress, Chip, Divider, useTheme, IconButton, Tooltip } from '@mui/material';\nimport { useSpring, animated } from 'react-spring';\nimport { Line } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip as ChartTooltip, Legend, Filler } from 'chart.js';\nimport CalendarTodayIcon from '@mui/icons-material/CalendarToday';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport InfoIcon from '@mui/icons-material/Info';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\n\n// Register ChartJS components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, ChartTooltip, Legend, Filler);\nconst TimelineRiskPredictor = ({\n  formData,\n  onForecastGenerated\n}) => {\n  _s();\n  const theme = useTheme();\n  const [loading, setLoading] = useState(false);\n  const [forecastData, setForecastData] = useState(null);\n  const [timeRange, setTimeRange] = useState(48); // Default 48 hours\n  const [selectedTimeIndex, setSelectedTimeIndex] = useState(null);\n\n  // Animation for the component\n  const fadeIn = useSpring({\n    from: {\n      opacity: 0,\n      transform: 'translateY(20px)'\n    },\n    to: {\n      opacity: 1,\n      transform: 'translateY(0)'\n    },\n    config: {\n      tension: 280,\n      friction: 20\n    },\n    delay: 200\n  });\n\n  // Generate mock forecast data based on the form data\n  // In a real implementation, this would call an API endpoint\n  const generateForecast = () => {\n    setLoading(true);\n\n    // Simulate API call delay\n    setTimeout(() => {\n      const hours = Array.from({\n        length: timeRange\n      }, (_, i) => i);\n\n      // Base values from form data\n      const baseRainfall = parseFloat(formData.rainfall);\n      const baseWaterLevel = parseFloat(formData.water_level);\n      const baseDischarge = parseFloat(formData.discharge);\n\n      // Generate realistic variations over time\n      const rainfallData = hours.map(hour => {\n        // Create a realistic rainfall pattern with peaks\n        const timeOfDay = hour % 24;\n        const dayNumber = Math.floor(hour / 24);\n\n        // More rain in afternoon/evening (random pattern with some peaks)\n        const hourlyVariation = timeOfDay > 12 && timeOfDay < 20 ? Math.random() * 15 : Math.random() * 5;\n\n        // Add some random storm peaks\n        const stormPeak = Math.random() > 0.9 ? Math.random() * 30 : 0;\n        return Math.max(0, baseRainfall * 0.1 + hourlyVariation + stormPeak);\n      });\n\n      // Water level rises after rainfall with some delay\n      const waterLevelData = hours.map(hour => {\n        let level = baseWaterLevel;\n\n        // Calculate cumulative effect of previous rainfall\n        for (let i = Math.max(0, hour - 12); i < hour; i++) {\n          level += rainfallData[i] * 0.01 * Math.exp(-(hour - i) / 6);\n        }\n        return level + Math.random() * 0.3; // Add small random variations\n      });\n\n      // Discharge follows water level with some delay\n      const dischargeData = hours.map(hour => {\n        if (hour < 2) return baseDischarge;\n        return baseDischarge + (waterLevelData[hour] - baseWaterLevel) * 100 + Math.random() * 50;\n      });\n\n      // Calculate risk score based on all factors\n      const riskScoreData = hours.map((hour, index) => {\n        const rainfall = rainfallData[index];\n        const waterLevel = waterLevelData[index];\n        const discharge = dischargeData[index];\n\n        // Simple risk calculation algorithm\n        let risk = 0;\n\n        // Rainfall contribution\n        if (rainfall > 20) risk += (rainfall - 20) * 0.5;\n\n        // Water level contribution\n        if (waterLevel > 7) risk += (waterLevel - 7) * 15;\n\n        // Discharge contribution\n        if (discharge > 1000) risk += (discharge - 1000) * 0.02;\n\n        // Add other factors from form data\n        if (formData.historical_floods === 'Yes') risk += 10;\n        if (parseInt(formData.elevation) < 100) risk += (100 - parseInt(formData.elevation)) * 0.2;\n\n        // Cap risk score at 100\n        return Math.min(100, Math.max(0, risk));\n      });\n\n      // Generate timestamps\n      const now = new Date();\n      const timestamps = hours.map(hour => {\n        const date = new Date(now.getTime() + hour * 60 * 60 * 1000);\n        return date.toLocaleString('en-US', {\n          month: 'short',\n          day: 'numeric',\n          hour: 'numeric',\n          minute: '2-digit'\n        });\n      });\n      setForecastData({\n        timestamps,\n        rainfallData,\n        waterLevelData,\n        dischargeData,\n        riskScoreData\n      });\n      setLoading(false);\n\n      // Notify parent component\n      if (onForecastGenerated) {\n        onForecastGenerated({\n          maxRiskScore: Math.max(...riskScoreData),\n          maxRiskTime: timestamps[riskScoreData.indexOf(Math.max(...riskScoreData))],\n          riskTrend: riskScoreData[riskScoreData.length - 1] > riskScoreData[0] ? 'increasing' : 'decreasing'\n        });\n      }\n    }, 2000);\n  };\n\n  // Handle time range change\n  const handleTimeRangeChange = (event, newValue) => {\n    setTimeRange(newValue);\n  };\n\n  // Handle chart click to select a specific time\n  const handleChartClick = (_, elements) => {\n    if (elements.length > 0) {\n      setSelectedTimeIndex(elements[0].index);\n    }\n  };\n\n  // Prepare chart data\n  const chartData = forecastData ? {\n    labels: forecastData.timestamps,\n    datasets: [{\n      label: 'Flood Risk Score',\n      data: forecastData.riskScoreData,\n      borderColor: theme.palette.primary.main,\n      backgroundColor: 'rgba(58, 134, 255, 0.2)',\n      fill: true,\n      tension: 0.4,\n      pointRadius: ctx => ctx.dataIndex === selectedTimeIndex ? 6 : 3,\n      pointBackgroundColor: ctx => {\n        const value = ctx.raw;\n        if (value > 70) return theme.palette.error.main;\n        if (value > 40) return theme.palette.warning.main;\n        return theme.palette.success.main;\n      },\n      pointBorderColor: '#fff',\n      pointBorderWidth: 2\n    }]\n  } : null;\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        callbacks: {\n          label: function (context) {\n            const value = context.raw;\n            let riskLevel = 'Low';\n            if (value > 70) riskLevel = 'High';else if (value > 40) riskLevel = 'Medium';\n            return `Risk Score: ${value.toFixed(1)} (${riskLevel})`;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Risk Score'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Time'\n        },\n        ticks: {\n          maxRotation: 45,\n          minRotation: 45\n        }\n      }\n    },\n    onClick: handleChartClick\n  };\n\n  // Generate forecast when component mounts or when form data changes significantly\n  useEffect(() => {\n    if (forecastData) {\n      // Reset forecast when form data changes significantly\n      generateForecast();\n    }\n  }, [formData.rainfall, formData.water_level, formData.discharge, formData.elevation, formData.historical_floods]);\n  return /*#__PURE__*/_jsxDEV(animated.div, {\n    style: fadeIn,\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 3,\n        mb: 4,\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        sx: {\n          fontWeight: 600,\n          color: theme.palette.primary.dark\n        },\n        children: [\"Temporal Flood Risk Prediction\", /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            sx: {\n              ml: 1,\n              mb: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"See how flood risk may evolve over the next hours and days based on your input parameters and weather forecasts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 8,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            gutterBottom: true,\n            children: \"Forecast Time Range (hours)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Slider, {\n            value: timeRange,\n            onChange: handleTimeRangeChange,\n            \"aria-labelledby\": \"time-range-slider\",\n            valueLabelDisplay: \"auto\",\n            step: 12,\n            marks: [{\n              value: 12,\n              label: '12h'\n            }, {\n              value: 24,\n              label: '24h'\n            }, {\n              value: 48,\n              label: '48h'\n            }, {\n              value: 72,\n              label: '72h'\n            }],\n            min: 12,\n            max: 72\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'flex-end'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: generateForecast,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 85\n            }, this),\n            children: forecastData ? 'Refresh Forecast' : 'Generate Forecast'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: 300\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this) : forecastData ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: 300,\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: chartData,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), selectedTimeIndex !== null && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2,\n              borderColor: 'rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: [/*#__PURE__*/_jsxDEV(CalendarTodayIcon, {\n                    fontSize: \"small\",\n                    sx: {\n                      mr: 1,\n                      verticalAlign: 'middle'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this), forecastData.timestamps[selectedTimeIndex]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Risk Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? theme.palette.error.main : forecastData.riskScoreData[selectedTimeIndex] > 40 ? theme.palette.warning.main : theme.palette.success.main\n                  },\n                  children: forecastData.riskScoreData[selectedTimeIndex].toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'High Risk' : forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'Medium Risk' : 'Low Risk',\n                  size: \"small\",\n                  color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'error' : forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'warning' : 'success',\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [/*#__PURE__*/_jsxDEV(ThunderstormIcon, {\n                    fontSize: \"small\",\n                    sx: {\n                      mr: 0.5,\n                      verticalAlign: 'middle'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 25\n                  }, this), \"Rainfall\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [forecastData.rainfallData[selectedTimeIndex].toFixed(1), \" mm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [/*#__PURE__*/_jsxDEV(WaterDropIcon, {\n                    fontSize: \"small\",\n                    sx: {\n                      mr: 0.5,\n                      verticalAlign: 'middle'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this), \"Water Level\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: [forecastData.waterLevelData[selectedTimeIndex].toFixed(2), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n              fontSize: \"small\",\n              sx: {\n                mr: 0.5,\n                verticalAlign: 'middle'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), \"This forecast is based on your input parameters and simulated weather patterns. Click on any point in the chart to see detailed predictions for that time.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: 300,\n          backgroundColor: 'rgba(0, 0, 0, 0.02)',\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n          sx: {\n            fontSize: 60,\n            color: 'rgba(0, 0, 0, 0.2)',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          align: \"center\",\n          children: \"Click \\\"Generate Forecast\\\" to see how flood risk may change over time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(TimelineRiskPredictor, \"AotrpAIo5MVet8vIJ0iMCA4L3uI=\", false, function () {\n  return [useTheme, useSpring];\n});\n_c = TimelineRiskPredictor;\nexport default TimelineRiskPredictor;\nvar _c;\n$RefreshReg$(_c, \"TimelineRiskPredictor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Slide<PERSON>", "Grid", "<PERSON><PERSON>", "CircularProgress", "Chip", "Divider", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "useSpring", "animated", "Line", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "ChartTooltip", "Legend", "Filler", "CalendarTodayIcon", "AccessTimeIcon", "RefreshIcon", "InfoIcon", "WaterDropIcon", "ThunderstormIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "TimelineRiskPredictor", "formData", "onForecastGenerated", "_s", "theme", "loading", "setLoading", "forecastData", "setForecastData", "timeRange", "setTimeRange", "selectedTimeIndex", "setSelectedTimeIndex", "fadeIn", "from", "opacity", "transform", "to", "config", "tension", "friction", "delay", "generateForecast", "setTimeout", "hours", "Array", "length", "_", "i", "baseRainfall", "parseFloat", "rainfall", "baseWaterLevel", "water_level", "baseDischarge", "discharge", "rainfallData", "map", "hour", "timeOfDay", "dayNumber", "Math", "floor", "hourlyVariation", "random", "stormPeak", "max", "waterLevelData", "level", "exp", "dischargeData", "riskScoreData", "index", "waterLevel", "risk", "historical_floods", "parseInt", "elevation", "min", "now", "Date", "timestamps", "date", "getTime", "toLocaleString", "month", "day", "minute", "maxRiskScore", "maxRiskTime", "indexOf", "riskTrend", "handleTimeRangeChange", "event", "newValue", "handleChartClick", "elements", "chartData", "labels", "datasets", "label", "data", "borderColor", "palette", "primary", "main", "backgroundColor", "fill", "pointRadius", "ctx", "dataIndex", "pointBackgroundColor", "value", "raw", "error", "warning", "success", "pointBorderColor", "pointBorderWidth", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "tooltip", "callbacks", "context", "riskLevel", "toFixed", "scales", "y", "beginAtZero", "title", "display", "text", "x", "ticks", "maxRotation", "minRotation", "onClick", "div", "style", "children", "sx", "p", "mb", "variant", "gutterBottom", "fontWeight", "color", "dark", "size", "ml", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paragraph", "container", "spacing", "item", "xs", "sm", "onChange", "valueLabelDisplay", "step", "marks", "alignItems", "justifyContent", "disabled", "startIcon", "height", "options", "mt", "mr", "verticalAlign", "my", "flexDirection", "borderRadius", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Box, \n  Typography, \n  Paper, \n  Slider, \n  Grid, \n  Button, \n  CircularProgress,\n  Chip,\n  Divider,\n  useTheme,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport { useSpring, animated } from 'react-spring';\nimport { Line } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler\n} from 'chart.js';\nimport CalendarTodayIcon from '@mui/icons-material/CalendarToday';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport InfoIcon from '@mui/icons-material/Info';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\nconst TimelineRiskPredictor = ({ formData, onForecastGenerated }) => {\n  const theme = useTheme();\n  const [loading, setLoading] = useState(false);\n  const [forecastData, setForecastData] = useState(null);\n  const [timeRange, setTimeRange] = useState(48); // Default 48 hours\n  const [selectedTimeIndex, setSelectedTimeIndex] = useState(null);\n  \n  // Animation for the component\n  const fadeIn = useSpring({\n    from: { opacity: 0, transform: 'translateY(20px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { tension: 280, friction: 20 },\n    delay: 200\n  });\n\n  // Generate mock forecast data based on the form data\n  // In a real implementation, this would call an API endpoint\n  const generateForecast = () => {\n    setLoading(true);\n    \n    // Simulate API call delay\n    setTimeout(() => {\n      const hours = Array.from({ length: timeRange }, (_, i) => i);\n      \n      // Base values from form data\n      const baseRainfall = parseFloat(formData.rainfall);\n      const baseWaterLevel = parseFloat(formData.water_level);\n      const baseDischarge = parseFloat(formData.discharge);\n      \n      // Generate realistic variations over time\n      const rainfallData = hours.map(hour => {\n        // Create a realistic rainfall pattern with peaks\n        const timeOfDay = hour % 24;\n        const dayNumber = Math.floor(hour / 24);\n        \n        // More rain in afternoon/evening (random pattern with some peaks)\n        const hourlyVariation = timeOfDay > 12 && timeOfDay < 20 \n          ? Math.random() * 15 \n          : Math.random() * 5;\n          \n        // Add some random storm peaks\n        const stormPeak = Math.random() > 0.9 ? Math.random() * 30 : 0;\n        \n        return Math.max(0, baseRainfall * 0.1 + hourlyVariation + stormPeak);\n      });\n      \n      // Water level rises after rainfall with some delay\n      const waterLevelData = hours.map(hour => {\n        let level = baseWaterLevel;\n        \n        // Calculate cumulative effect of previous rainfall\n        for (let i = Math.max(0, hour - 12); i < hour; i++) {\n          level += rainfallData[i] * 0.01 * Math.exp(-(hour - i) / 6);\n        }\n        \n        return level + Math.random() * 0.3; // Add small random variations\n      });\n      \n      // Discharge follows water level with some delay\n      const dischargeData = hours.map(hour => {\n        if (hour < 2) return baseDischarge;\n        return baseDischarge + (waterLevelData[hour] - baseWaterLevel) * 100 + Math.random() * 50;\n      });\n      \n      // Calculate risk score based on all factors\n      const riskScoreData = hours.map((hour, index) => {\n        const rainfall = rainfallData[index];\n        const waterLevel = waterLevelData[index];\n        const discharge = dischargeData[index];\n        \n        // Simple risk calculation algorithm\n        let risk = 0;\n        \n        // Rainfall contribution\n        if (rainfall > 20) risk += (rainfall - 20) * 0.5;\n        \n        // Water level contribution\n        if (waterLevel > 7) risk += (waterLevel - 7) * 15;\n        \n        // Discharge contribution\n        if (discharge > 1000) risk += (discharge - 1000) * 0.02;\n        \n        // Add other factors from form data\n        if (formData.historical_floods === 'Yes') risk += 10;\n        if (parseInt(formData.elevation) < 100) risk += (100 - parseInt(formData.elevation)) * 0.2;\n        \n        // Cap risk score at 100\n        return Math.min(100, Math.max(0, risk));\n      });\n      \n      // Generate timestamps\n      const now = new Date();\n      const timestamps = hours.map(hour => {\n        const date = new Date(now.getTime() + hour * 60 * 60 * 1000);\n        return date.toLocaleString('en-US', { \n          month: 'short', \n          day: 'numeric', \n          hour: 'numeric', \n          minute: '2-digit'\n        });\n      });\n      \n      setForecastData({\n        timestamps,\n        rainfallData,\n        waterLevelData,\n        dischargeData,\n        riskScoreData\n      });\n      \n      setLoading(false);\n      \n      // Notify parent component\n      if (onForecastGenerated) {\n        onForecastGenerated({\n          maxRiskScore: Math.max(...riskScoreData),\n          maxRiskTime: timestamps[riskScoreData.indexOf(Math.max(...riskScoreData))],\n          riskTrend: riskScoreData[riskScoreData.length - 1] > riskScoreData[0] ? 'increasing' : 'decreasing'\n        });\n      }\n    }, 2000);\n  };\n  \n  // Handle time range change\n  const handleTimeRangeChange = (event, newValue) => {\n    setTimeRange(newValue);\n  };\n  \n  // Handle chart click to select a specific time\n  const handleChartClick = (_, elements) => {\n    if (elements.length > 0) {\n      setSelectedTimeIndex(elements[0].index);\n    }\n  };\n  \n  // Prepare chart data\n  const chartData = forecastData ? {\n    labels: forecastData.timestamps,\n    datasets: [\n      {\n        label: 'Flood Risk Score',\n        data: forecastData.riskScoreData,\n        borderColor: theme.palette.primary.main,\n        backgroundColor: 'rgba(58, 134, 255, 0.2)',\n        fill: true,\n        tension: 0.4,\n        pointRadius: (ctx) => ctx.dataIndex === selectedTimeIndex ? 6 : 3,\n        pointBackgroundColor: (ctx) => {\n          const value = ctx.raw;\n          if (value > 70) return theme.palette.error.main;\n          if (value > 40) return theme.palette.warning.main;\n          return theme.palette.success.main;\n        },\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n      }\n    ]\n  } : null;\n  \n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context) {\n            const value = context.raw;\n            let riskLevel = 'Low';\n            if (value > 70) riskLevel = 'High';\n            else if (value > 40) riskLevel = 'Medium';\n            \n            return `Risk Score: ${value.toFixed(1)} (${riskLevel})`;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Risk Score'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Time'\n        },\n        ticks: {\n          maxRotation: 45,\n          minRotation: 45\n        }\n      }\n    },\n    onClick: handleChartClick\n  };\n  \n  // Generate forecast when component mounts or when form data changes significantly\n  useEffect(() => {\n    if (forecastData) {\n      // Reset forecast when form data changes significantly\n      generateForecast();\n    }\n  }, [formData.rainfall, formData.water_level, formData.discharge, formData.elevation, formData.historical_floods]);\n  \n  return (\n    <animated.div style={fadeIn}>\n      <Paper elevation={3} sx={{ p: 3, mb: 4, position: 'relative' }}>\n        <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.dark }}>\n          Temporal Flood Risk Prediction\n          <Tooltip title=\"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\">\n            <IconButton size=\"small\" sx={{ ml: 1, mb: 1 }}>\n              <InfoIcon fontSize=\"small\" />\n            </IconButton>\n          </Tooltip>\n        </Typography>\n        \n        <Typography variant=\"body1\" paragraph>\n          See how flood risk may evolve over the next hours and days based on your input parameters and weather forecasts.\n        </Typography>\n        \n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12} sm={8}>\n            <Typography gutterBottom>\n              Forecast Time Range (hours)\n            </Typography>\n            <Slider\n              value={timeRange}\n              onChange={handleTimeRangeChange}\n              aria-labelledby=\"time-range-slider\"\n              valueLabelDisplay=\"auto\"\n              step={12}\n              marks={[\n                { value: 12, label: '12h' },\n                { value: 24, label: '24h' },\n                { value: 48, label: '48h' },\n                { value: 72, label: '72h' }\n              ]}\n              min={12}\n              max={72}\n            />\n          </Grid>\n          <Grid item xs={12} sm={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={generateForecast}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <RefreshIcon />}\n            >\n              {forecastData ? 'Refresh Forecast' : 'Generate Forecast'}\n            </Button>\n          </Grid>\n        </Grid>\n        \n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>\n            <CircularProgress />\n          </Box>\n        ) : forecastData ? (\n          <>\n            <Box sx={{ height: 300, mb: 3 }}>\n              <Line data={chartData} options={chartOptions} />\n            </Box>\n            \n            {selectedTimeIndex !== null && (\n              <Box sx={{ mt: 2, mb: 3 }}>\n                <Paper variant=\"outlined\" sx={{ p: 2, borderColor: 'rgba(0, 0, 0, 0.1)' }}>\n                  <Grid container spacing={2}>\n                    <Grid item xs={12}>\n                      <Typography variant=\"h6\" gutterBottom>\n                        <CalendarTodayIcon fontSize=\"small\" sx={{ mr: 1, verticalAlign: 'middle' }} />\n                        {forecastData.timestamps[selectedTimeIndex]}\n                      </Typography>\n                      <Divider sx={{ my: 1 }} />\n                    </Grid>\n                    <Grid item xs={12} sm={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">Risk Score</Typography>\n                      <Typography variant=\"h5\" sx={{ \n                        fontWeight: 'bold',\n                        color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? theme.palette.error.main :\n                               forecastData.riskScoreData[selectedTimeIndex] > 40 ? theme.palette.warning.main :\n                               theme.palette.success.main\n                      }}>\n                        {forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}\n                      </Typography>\n                      <Chip \n                        label={\n                          forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'High Risk' :\n                          forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'Medium Risk' : 'Low Risk'\n                        }\n                        size=\"small\"\n                        color={\n                          forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'error' :\n                          forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'warning' : 'success'\n                        }\n                        sx={{ mt: 1 }}\n                      />\n                    </Grid>\n                    <Grid item xs={12} sm={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        <ThunderstormIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                        Rainfall\n                      </Typography>\n                      <Typography variant=\"h6\">\n                        {forecastData.rainfallData[selectedTimeIndex].toFixed(1)} mm\n                      </Typography>\n                    </Grid>\n                    <Grid item xs={12} sm={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        <WaterDropIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                        Water Level\n                      </Typography>\n                      <Typography variant=\"h6\">\n                        {forecastData.waterLevelData[selectedTimeIndex].toFixed(2)} m\n                      </Typography>\n                    </Grid>\n                  </Grid>\n                </Paper>\n              </Box>\n            )}\n            \n            <Box sx={{ mt: 3 }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                <InfoIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                This forecast is based on your input parameters and simulated weather patterns. Click on any point in the chart to see detailed predictions for that time.\n              </Typography>\n            </Box>\n          </>\n        ) : (\n          <Box sx={{ \n            display: 'flex', \n            flexDirection: 'column', \n            justifyContent: 'center', \n            alignItems: 'center', \n            height: 300,\n            backgroundColor: 'rgba(0, 0, 0, 0.02)',\n            borderRadius: 2\n          }}>\n            <AccessTimeIcon sx={{ fontSize: 60, color: 'rgba(0, 0, 0, 0.2)', mb: 2 }} />\n            <Typography variant=\"body1\" color=\"text.secondary\" align=\"center\">\n              Click \"Generate Forecast\" to see how flood risk may change over time\n            </Typography>\n          </Box>\n        )}\n      </Paper>\n    </animated.div>\n  );\n};\n\nexport default TimelineRiskPredictor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAASC,SAAS,EAAEC,QAAQ,QAAQ,cAAc;AAClD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLV,OAAO,IAAIW,YAAY,EACvBC,MAAM,EACNC,MAAM,QACD,UAAU;AACjB,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,gBAAgB,MAAM,kCAAkC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAlB,OAAO,CAACmB,QAAQ,CACdlB,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,YAAY,EACZC,MAAM,EACNC,MACF,CAAC;AAED,MAAMY,qBAAqB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAMC,KAAK,GAAG/B,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAMmD,MAAM,GAAGrC,SAAS,CAAC;IACvBsC,IAAI,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAmB,CAAC;IACnDC,EAAE,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAgB,CAAC;IAC9CE,MAAM,EAAE;MAAEC,OAAO,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACtCC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACAiB,UAAU,CAAC,MAAM;MACf,MAAMC,KAAK,GAAGC,KAAK,CAACX,IAAI,CAAC;QAAEY,MAAM,EAAEjB;MAAU,CAAC,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;MAE5D;MACA,MAAMC,YAAY,GAAGC,UAAU,CAAC7B,QAAQ,CAAC8B,QAAQ,CAAC;MAClD,MAAMC,cAAc,GAAGF,UAAU,CAAC7B,QAAQ,CAACgC,WAAW,CAAC;MACvD,MAAMC,aAAa,GAAGJ,UAAU,CAAC7B,QAAQ,CAACkC,SAAS,CAAC;;MAEpD;MACA,MAAMC,YAAY,GAAGZ,KAAK,CAACa,GAAG,CAACC,IAAI,IAAI;QACrC;QACA,MAAMC,SAAS,GAAGD,IAAI,GAAG,EAAE;QAC3B,MAAME,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC;;QAEvC;QACA,MAAMK,eAAe,GAAGJ,SAAS,GAAG,EAAE,IAAIA,SAAS,GAAG,EAAE,GACpDE,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,GAClBH,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC;;QAErB;QACA,MAAMC,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGH,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAE9D,OAAOH,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEjB,YAAY,GAAG,GAAG,GAAGc,eAAe,GAAGE,SAAS,CAAC;MACtE,CAAC,CAAC;;MAEF;MACA,MAAME,cAAc,GAAGvB,KAAK,CAACa,GAAG,CAACC,IAAI,IAAI;QACvC,IAAIU,KAAK,GAAGhB,cAAc;;QAE1B;QACA,KAAK,IAAIJ,CAAC,GAAGa,IAAI,CAACK,GAAG,CAAC,CAAC,EAAER,IAAI,GAAG,EAAE,CAAC,EAAEV,CAAC,GAAGU,IAAI,EAAEV,CAAC,EAAE,EAAE;UAClDoB,KAAK,IAAIZ,YAAY,CAACR,CAAC,CAAC,GAAG,IAAI,GAAGa,IAAI,CAACQ,GAAG,CAAC,EAAEX,IAAI,GAAGV,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7D;QAEA,OAAOoB,KAAK,GAAGP,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,CAAC;;MAEF;MACA,MAAMM,aAAa,GAAG1B,KAAK,CAACa,GAAG,CAACC,IAAI,IAAI;QACtC,IAAIA,IAAI,GAAG,CAAC,EAAE,OAAOJ,aAAa;QAClC,OAAOA,aAAa,GAAG,CAACa,cAAc,CAACT,IAAI,CAAC,GAAGN,cAAc,IAAI,GAAG,GAAGS,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE;MAC3F,CAAC,CAAC;;MAEF;MACA,MAAMO,aAAa,GAAG3B,KAAK,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEc,KAAK,KAAK;QAC/C,MAAMrB,QAAQ,GAAGK,YAAY,CAACgB,KAAK,CAAC;QACpC,MAAMC,UAAU,GAAGN,cAAc,CAACK,KAAK,CAAC;QACxC,MAAMjB,SAAS,GAAGe,aAAa,CAACE,KAAK,CAAC;;QAEtC;QACA,IAAIE,IAAI,GAAG,CAAC;;QAEZ;QACA,IAAIvB,QAAQ,GAAG,EAAE,EAAEuB,IAAI,IAAI,CAACvB,QAAQ,GAAG,EAAE,IAAI,GAAG;;QAEhD;QACA,IAAIsB,UAAU,GAAG,CAAC,EAAEC,IAAI,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,EAAE;;QAEjD;QACA,IAAIlB,SAAS,GAAG,IAAI,EAAEmB,IAAI,IAAI,CAACnB,SAAS,GAAG,IAAI,IAAI,IAAI;;QAEvD;QACA,IAAIlC,QAAQ,CAACsD,iBAAiB,KAAK,KAAK,EAAED,IAAI,IAAI,EAAE;QACpD,IAAIE,QAAQ,CAACvD,QAAQ,CAACwD,SAAS,CAAC,GAAG,GAAG,EAAEH,IAAI,IAAI,CAAC,GAAG,GAAGE,QAAQ,CAACvD,QAAQ,CAACwD,SAAS,CAAC,IAAI,GAAG;;QAE1F;QACA,OAAOhB,IAAI,CAACiB,GAAG,CAAC,GAAG,EAAEjB,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEQ,IAAI,CAAC,CAAC;MACzC,CAAC,CAAC;;MAEF;MACA,MAAMK,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,UAAU,GAAGrC,KAAK,CAACa,GAAG,CAACC,IAAI,IAAI;QACnC,MAAMwB,IAAI,GAAG,IAAIF,IAAI,CAACD,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGzB,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC5D,OAAOwB,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;UAClCC,KAAK,EAAE,OAAO;UACdC,GAAG,EAAE,SAAS;UACd5B,IAAI,EAAE,SAAS;UACf6B,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF3D,eAAe,CAAC;QACdqD,UAAU;QACVzB,YAAY;QACZW,cAAc;QACdG,aAAa;QACbC;MACF,CAAC,CAAC;MAEF7C,UAAU,CAAC,KAAK,CAAC;;MAEjB;MACA,IAAIJ,mBAAmB,EAAE;QACvBA,mBAAmB,CAAC;UAClBkE,YAAY,EAAE3B,IAAI,CAACK,GAAG,CAAC,GAAGK,aAAa,CAAC;UACxCkB,WAAW,EAAER,UAAU,CAACV,aAAa,CAACmB,OAAO,CAAC7B,IAAI,CAACK,GAAG,CAAC,GAAGK,aAAa,CAAC,CAAC,CAAC;UAC1EoB,SAAS,EAAEpB,aAAa,CAACA,aAAa,CAACzB,MAAM,GAAG,CAAC,CAAC,GAAGyB,aAAa,CAAC,CAAC,CAAC,GAAG,YAAY,GAAG;QACzF,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMqB,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACjDhE,YAAY,CAACgE,QAAQ,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAChD,CAAC,EAAEiD,QAAQ,KAAK;IACxC,IAAIA,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAE;MACvBd,oBAAoB,CAACgE,QAAQ,CAAC,CAAC,CAAC,CAACxB,KAAK,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMyB,SAAS,GAAGtE,YAAY,GAAG;IAC/BuE,MAAM,EAAEvE,YAAY,CAACsD,UAAU;IAC/BkB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE1E,YAAY,CAAC4C,aAAa;MAChC+B,WAAW,EAAE9E,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAACC,IAAI;MACvCC,eAAe,EAAE,yBAAyB;MAC1CC,IAAI,EAAE,IAAI;MACVpE,OAAO,EAAE,GAAG;MACZqE,WAAW,EAAGC,GAAG,IAAKA,GAAG,CAACC,SAAS,KAAK/E,iBAAiB,GAAG,CAAC,GAAG,CAAC;MACjEgF,oBAAoB,EAAGF,GAAG,IAAK;QAC7B,MAAMG,KAAK,GAAGH,GAAG,CAACI,GAAG;QACrB,IAAID,KAAK,GAAG,EAAE,EAAE,OAAOxF,KAAK,CAAC+E,OAAO,CAACW,KAAK,CAACT,IAAI;QAC/C,IAAIO,KAAK,GAAG,EAAE,EAAE,OAAOxF,KAAK,CAAC+E,OAAO,CAACY,OAAO,CAACV,IAAI;QACjD,OAAOjF,KAAK,CAAC+E,OAAO,CAACa,OAAO,CAACX,IAAI;MACnC,CAAC;MACDY,gBAAgB,EAAE,MAAM;MACxBC,gBAAgB,EAAE;IACpB,CAAC;EAEL,CAAC,GAAG,IAAI;;EAER;EACA,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE;UACT1B,KAAK,EAAE,SAAAA,CAAS2B,OAAO,EAAE;YACvB,MAAMf,KAAK,GAAGe,OAAO,CAACd,GAAG;YACzB,IAAIe,SAAS,GAAG,KAAK;YACrB,IAAIhB,KAAK,GAAG,EAAE,EAAEgB,SAAS,GAAG,MAAM,CAAC,KAC9B,IAAIhB,KAAK,GAAG,EAAE,EAAEgB,SAAS,GAAG,QAAQ;YAEzC,OAAO,eAAehB,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC,KAAKD,SAAS,GAAG;UACzD;QACF;MACF;IACF,CAAC;IACDE,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBlE,GAAG,EAAE,GAAG;QACRmE,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDC,CAAC,EAAE;QACDH,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR,CAAC;QACDE,KAAK,EAAE;UACLC,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE;QACf;MACF;IACF,CAAC;IACDC,OAAO,EAAE7C;EACX,CAAC;;EAED;EACAhH,SAAS,CAAC,MAAM;IACd,IAAI4C,YAAY,EAAE;MAChB;MACAe,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACrB,QAAQ,CAAC8B,QAAQ,EAAE9B,QAAQ,CAACgC,WAAW,EAAEhC,QAAQ,CAACkC,SAAS,EAAElC,QAAQ,CAACwD,SAAS,EAAExD,QAAQ,CAACsD,iBAAiB,CAAC,CAAC;EAEjH,oBACE3D,OAAA,CAACnB,QAAQ,CAACgJ,GAAG;IAACC,KAAK,EAAE7G,MAAO;IAAA8G,QAAA,eAC1B/H,OAAA,CAAC9B,KAAK;MAAC2F,SAAS,EAAE,CAAE;MAACmE,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEtB,QAAQ,EAAE;MAAW,CAAE;MAAAmB,QAAA,gBAC7D/H,OAAA,CAAC/B,UAAU;QAACkK,OAAO,EAAC,IAAI;QAACC,YAAY;QAACJ,EAAE,EAAE;UAAEK,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE9H,KAAK,CAAC+E,OAAO,CAACC,OAAO,CAAC+C;QAAK,CAAE;QAAAR,QAAA,GAAC,gCAEhG,eAAA/H,OAAA,CAACrB,OAAO;UAAC0I,KAAK,EAAC,8GAA8G;UAAAU,QAAA,eAC3H/H,OAAA,CAACtB,UAAU;YAAC8J,IAAI,EAAC,OAAO;YAACR,EAAE,EAAE;cAAES,EAAE,EAAE,CAAC;cAAEP,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,eAC5C/H,OAAA,CAACJ,QAAQ;cAAC8I,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEb9I,OAAA,CAAC/B,UAAU;QAACkK,OAAO,EAAC,OAAO;QAACY,SAAS;QAAAhB,QAAA,EAAC;MAEtC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9I,OAAA,CAAC5B,IAAI;QAAC4K,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACxC/H,OAAA,CAAC5B,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,gBACvB/H,OAAA,CAAC/B,UAAU;YAACmK,YAAY;YAAAL,QAAA,EAAC;UAEzB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9I,OAAA,CAAC7B,MAAM;YACL6H,KAAK,EAAEnF,SAAU;YACjBwI,QAAQ,EAAEzE,qBAAsB;YAChC,mBAAgB,mBAAmB;YACnC0E,iBAAiB,EAAC,MAAM;YACxBC,IAAI,EAAE,EAAG;YACTC,KAAK,EAAE,CACL;cAAExD,KAAK,EAAE,EAAE;cAAEZ,KAAK,EAAE;YAAM,CAAC,EAC3B;cAAEY,KAAK,EAAE,EAAE;cAAEZ,KAAK,EAAE;YAAM,CAAC,EAC3B;cAAEY,KAAK,EAAE,EAAE;cAAEZ,KAAK,EAAE;YAAM,CAAC,EAC3B;cAAEY,KAAK,EAAE,EAAE;cAAEZ,KAAK,EAAE;YAAM,CAAC,CAC3B;YACFtB,GAAG,EAAE,EAAG;YACRZ,GAAG,EAAE;UAAG;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP9I,OAAA,CAAC5B,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACpB,EAAE,EAAE;YAAEV,OAAO,EAAE,MAAM;YAAEmC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAW,CAAE;UAAA3B,QAAA,eAClG/H,OAAA,CAAC3B,MAAM;YACL8J,OAAO,EAAC,WAAW;YACnBG,KAAK,EAAC,SAAS;YACfV,OAAO,EAAElG,gBAAiB;YAC1BiI,QAAQ,EAAElJ,OAAQ;YAClBmJ,SAAS,EAAEnJ,OAAO,gBAAGT,OAAA,CAAC1B,gBAAgB;cAACkK,IAAI,EAAE,EAAG;cAACF,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9I,OAAA,CAACL,WAAW;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAf,QAAA,EAErFpH,YAAY,GAAG,kBAAkB,GAAG;UAAmB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAENrI,OAAO,gBACNT,OAAA,CAAChC,GAAG;QAACgK,EAAE,EAAE;UAAEV,OAAO,EAAE,MAAM;UAAEoC,cAAc,EAAE,QAAQ;UAAED,UAAU,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAI,CAAE;QAAA9B,QAAA,eACxF/H,OAAA,CAAC1B,gBAAgB;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJnI,YAAY,gBACdX,OAAA,CAAAE,SAAA;QAAA6H,QAAA,gBACE/H,OAAA,CAAChC,GAAG;UAACgK,EAAE,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAE3B,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,eAC9B/H,OAAA,CAAClB,IAAI;YAACuG,IAAI,EAAEJ,SAAU;YAAC6E,OAAO,EAAEvD;UAAa;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EAEL/H,iBAAiB,KAAK,IAAI,iBACzBf,OAAA,CAAChC,GAAG;UAACgK,EAAE,EAAE;YAAE+B,EAAE,EAAE,CAAC;YAAE7B,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,eACxB/H,OAAA,CAAC9B,KAAK;YAACiK,OAAO,EAAC,UAAU;YAACH,EAAE,EAAE;cAAEC,CAAC,EAAE,CAAC;cAAE3C,WAAW,EAAE;YAAqB,CAAE;YAAAyC,QAAA,eACxE/H,OAAA,CAAC5B,IAAI;cAAC4K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAlB,QAAA,gBACzB/H,OAAA,CAAC5B,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAApB,QAAA,gBAChB/H,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAL,QAAA,gBACnC/H,OAAA,CAACP,iBAAiB;oBAACiJ,QAAQ,EAAC,OAAO;oBAACV,EAAE,EAAE;sBAAEgC,EAAE,EAAE,CAAC;sBAAEC,aAAa,EAAE;oBAAS;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC7EnI,YAAY,CAACsD,UAAU,CAAClD,iBAAiB,CAAC;gBAAA;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACb9I,OAAA,CAACxB,OAAO;kBAACwJ,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACP9I,OAAA,CAAC5B,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAArB,QAAA,gBACvB/H,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,EAAC;gBAAU;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1E9I,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,IAAI;kBAACH,EAAE,EAAE;oBAC3BK,UAAU,EAAE,MAAM;oBAClBC,KAAK,EAAE3H,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAGP,KAAK,CAAC+E,OAAO,CAACW,KAAK,CAACT,IAAI,GAC7E9E,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAGP,KAAK,CAAC+E,OAAO,CAACY,OAAO,CAACV,IAAI,GAC/EjF,KAAK,CAAC+E,OAAO,CAACa,OAAO,CAACX;kBAC/B,CAAE;kBAAAsC,QAAA,EACCpH,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,CAACkG,OAAO,CAAC,CAAC;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACb9I,OAAA,CAACzB,IAAI;kBACH6G,KAAK,EACHzE,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAG,WAAW,GAChEJ,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAG,aAAa,GAAG,UACtE;kBACDyH,IAAI,EAAC,OAAO;kBACZF,KAAK,EACH3H,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAG,OAAO,GAC5DJ,YAAY,CAAC4C,aAAa,CAACxC,iBAAiB,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,SAClE;kBACDiH,EAAE,EAAE;oBAAE+B,EAAE,EAAE;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP9I,OAAA,CAAC5B,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAArB,QAAA,gBACvB/H,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,gBAChD/H,OAAA,CAACF,gBAAgB;oBAAC4I,QAAQ,EAAC,OAAO;oBAACV,EAAE,EAAE;sBAAEgC,EAAE,EAAE,GAAG;sBAAEC,aAAa,EAAE;oBAAS;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9I,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,IAAI;kBAAAJ,QAAA,GACrBpH,YAAY,CAAC6B,YAAY,CAACzB,iBAAiB,CAAC,CAACkG,OAAO,CAAC,CAAC,CAAC,EAAC,KAC3D;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACP9I,OAAA,CAAC5B,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAArB,QAAA,gBACvB/H,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAP,QAAA,gBAChD/H,OAAA,CAACH,aAAa;oBAAC6I,QAAQ,EAAC,OAAO;oBAACV,EAAE,EAAE;sBAAEgC,EAAE,EAAE,GAAG;sBAAEC,aAAa,EAAE;oBAAS;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE9E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9I,OAAA,CAAC/B,UAAU;kBAACkK,OAAO,EAAC,IAAI;kBAAAJ,QAAA,GACrBpH,YAAY,CAACwC,cAAc,CAACpC,iBAAiB,CAAC,CAACkG,OAAO,CAAC,CAAC,CAAC,EAAC,IAC7D;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAED9I,OAAA,CAAChC,GAAG;UAACgK,EAAE,EAAE;YAAE+B,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,eACjB/H,OAAA,CAAC/B,UAAU;YAACkK,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAAAP,QAAA,gBAChD/H,OAAA,CAACJ,QAAQ;cAAC8I,QAAQ,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAEgC,EAAE,EAAE,GAAG;gBAAEC,aAAa,EAAE;cAAS;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8JAEzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA,eACN,CAAC,gBAEH9I,OAAA,CAAChC,GAAG;QAACgK,EAAE,EAAE;UACPV,OAAO,EAAE,MAAM;UACf6C,aAAa,EAAE,QAAQ;UACvBT,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpBI,MAAM,EAAE,GAAG;UACXnE,eAAe,EAAE,qBAAqB;UACtC0E,YAAY,EAAE;QAChB,CAAE;QAAArC,QAAA,gBACA/H,OAAA,CAACN,cAAc;UAACsI,EAAE,EAAE;YAAEU,QAAQ,EAAE,EAAE;YAAEJ,KAAK,EAAE,oBAAoB;YAAEJ,EAAE,EAAE;UAAE;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5E9I,OAAA,CAAC/B,UAAU;UAACkK,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAC+B,KAAK,EAAC,QAAQ;UAAAtC,QAAA,EAAC;QAElE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEnB,CAAC;AAACvI,EAAA,CAlWIH,qBAAqB;EAAA,QACX3B,QAAQ,EAOPG,SAAS;AAAA;AAAA0L,EAAA,GARpBlK,qBAAqB;AAoW3B,eAAeA,qBAAqB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}