{"ast": null, "code": "import _objectWithoutProperties from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectDestructuringEmpty from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectDestructuringEmpty.js\";\nimport _objectSpread from \"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"children\"],\n  _excluded2 = [\"container\"],\n  _excluded3 = [\"container\"],\n  _excluded4 = [\"to\", \"from\"],\n  _excluded5 = [\"root\", \"once\", \"amount\"],\n  _excluded6 = [\"children\"],\n  _excluded7 = [\"items\", \"children\"],\n  _excluded8 = [\"items\", \"children\"];\n// src/hooks/useChain.ts\nimport { each, useIsomorphicLayoutEffect } from \"@react-spring/shared\";\n\n// src/helpers.ts\nimport { is, toArray, eachProp, getFluidValue, isAnimatedString, Globals as G } from \"@react-spring/shared\";\nfunction callProp(value) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  return is.fun(value) ? value(...args) : value;\n}\nvar matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));\nvar resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;\nvar getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;\nvar noopTransform = value => value;\nvar getDefaultProps = function (props) {\n  let transform = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noopTransform;\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults2 = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!is.und(value)) {\n      defaults2[key] = value;\n    }\n  }\n  return defaults2;\n};\nvar DEFAULT_PROPS = [\"config\", \"onProps\", \"onStart\", \"onChange\", \"onPause\", \"onResume\", \"onRest\"];\nvar RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to2 = getForwardProps(props);\n  if (to2) {\n    const out = {\n      to: to2\n    };\n    eachProp(props, (val, key) => key in to2 || (out[key] = val));\n    return out;\n  }\n  return _objectSpread({}, props);\n}\nfunction computeGoal(value) {\n  value = getFluidValue(value);\n  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? G.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to2) {\n  return is.fun(to2) || is.arr(to2) && is.obj(to2[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  var _ctrl$ref;\n  (_ctrl$ref = ctrl.ref) === null || _ctrl$ref === void 0 || _ctrl$ref.delete(ctrl);\n  ref === null || ref === void 0 || ref.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    var _ctrl$ref2;\n    (_ctrl$ref2 = ctrl.ref) === null || _ctrl$ref2 === void 0 || _ctrl$ref2.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\n// src/hooks/useChain.ts\nfunction useChain(refs, timeSteps) {\n  let timeFrame = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1e3;\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      each(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;else prevDelay = delay;\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              const memoizedDelayProp = props.delay;\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      each(refs, ref => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            each(controllers, (ctrl, i) => each(queues[i] || [], update2 => ctrl.queue.push(update2)));\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\n// src/hooks/useSpring.ts\nimport { is as is9 } from \"@react-spring/shared\";\n\n// src/hooks/useSprings.ts\nimport { useContext as useContext2, useMemo, useRef } from \"react\";\nimport { is as is8, each as each5, usePrev, useOnce, useForceUpdate, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect2 } from \"@react-spring/shared\";\n\n// src/SpringValue.ts\nimport { is as is5, raf as raf3, each as each2, isEqual, toArray as toArray2, eachProp as eachProp3, frameLoop as frameLoop2, flushCalls, getFluidValue as getFluidValue2, isAnimatedString as isAnimatedString2, Globals as G5, callFluidObservers as callFluidObservers2, hasFluidValue, addFluidObserver, removeFluidObserver, getFluidObservers } from \"@react-spring/shared\";\nimport { AnimatedValue, AnimatedString, getPayload, getAnimated as getAnimated2, setAnimated, getAnimatedType } from \"@react-spring/animated\";\n\n// src/AnimationConfig.ts\nimport { is as is2, easings } from \"@react-spring/shared\";\n\n// src/constants.ts\nvar config = {\n  default: {\n    tension: 170,\n    friction: 26\n  },\n  gentle: {\n    tension: 120,\n    friction: 14\n  },\n  wobbly: {\n    tension: 180,\n    friction: 12\n  },\n  stiff: {\n    tension: 210,\n    friction: 20\n  },\n  slow: {\n    tension: 280,\n    friction: 60\n  },\n  molasses: {\n    tension: 280,\n    friction: 120\n  }\n};\n\n// src/AnimationConfig.ts\nvar defaults = _objectSpread(_objectSpread({}, config.default), {}, {\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false\n});\nvar AnimationConfig = class {\n  constructor() {\n    /**\n     * The initial velocity of one or more values.\n     *\n     * @default 0\n     */\n    this.velocity = 0;\n    Object.assign(this, defaults);\n  }\n};\nfunction mergeConfig(config2, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = _objectSpread({}, defaultConfig);\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = _objectSpread(_objectSpread({}, defaultConfig), newConfig);\n  }\n  sanitizeConfig(config2, newConfig);\n  Object.assign(config2, newConfig);\n  for (const key in defaults) {\n    if (config2[key] == null) {\n      config2[key] = defaults[key];\n    }\n  }\n  let {\n    frequency,\n    damping\n  } = config2;\n  const {\n    mass\n  } = config2;\n  if (!is2.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config2.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config2;\n}\nfunction sanitizeConfig(config2, props) {\n  if (!is2.und(props.decay)) {\n    config2.duration = void 0;\n  } else {\n    const isTensionConfig = !is2.und(props.tension) || !is2.und(props.friction);\n    if (isTensionConfig || !is2.und(props.frequency) || !is2.und(props.damping) || !is2.und(props.mass)) {\n      config2.duration = void 0;\n      config2.decay = void 0;\n    }\n    if (isTensionConfig) {\n      config2.frequency = void 0;\n    }\n  }\n}\n\n// src/Animation.ts\nvar emptyArray = [];\nvar Animation = class {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n};\n\n// src/scheduleProps.ts\nimport { is as is3, raf, Globals as G2 } from \"@react-spring/shared\";\nfunction scheduleProps(callId, _ref) {\n  let {\n    key,\n    props,\n    defaultProps,\n    state,\n    actions\n  } = _ref;\n  return new Promise((resolve, reject) => {\n    var _props$cancel;\n    let delay;\n    let timeout;\n    let cancel = matchProp((_props$cancel = props.cancel) !== null && _props$cancel !== void 0 ? _props$cancel : defaultProps === null || defaultProps === void 0 ? void 0 : defaultProps.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!is3.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps === null || defaultProps === void 0 ? void 0 : defaultProps.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !G2.skipAnimation) {\n        state.delayed = true;\n        timeout = raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start(_objectSpread(_objectSpread({}, props), {}, {\n          callId,\n          cancel\n        }), resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\n// src/runAsync.ts\nimport { is as is4, raf as raf2, flush, eachProp as eachProp2, Globals as G3 } from \"@react-spring/shared\";\n\n// src/AnimationResult.ts\nvar getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some(result => result.cancelled) ? getCancelledResult(target.get()) : results.every(result => result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every(result => result.finished));\nvar getNoopResult = value => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nvar getFinishedResult = function (value, finished) {\n  let cancelled = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  return {\n    value,\n    finished,\n    cancelled\n  };\n};\nvar getCancelledResult = value => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\n// src/runAsync.ts\nfunction runAsync(to2, props, state, target) {\n  const {\n    callId,\n    parentId,\n    onRest\n  } = props;\n  const {\n    asyncTo: prevTo,\n    promise: prevPromise\n  } = state;\n  if (!parentId && to2 === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to2;\n    const defaultProps = getDefaultProps(props, (value, key) =>\n    // The `onRest` prop is only called when the `runAsync` promise is resolved.\n    key === \"onRest\" ? void 0 : value);\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise((resolve, reject) => (preventBail = resolve, bail = reject));\n    const bailIfEnded = bailSignal => {\n      const bailResult =\n      // The `cancel` prop or `stop` method was used.\n      callId <= (state.cancelId || 0) && getCancelledResult(target) ||\n      // The async `to` prop was replaced.\n      callId !== state.asyncId && getFinishedResult(target, false);\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (G3.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props2 = is4.obj(arg1) ? _objectSpread({}, arg1) : _objectSpread(_objectSpread({}, arg2), {}, {\n          to: arg1\n        });\n        props2.parentId = callId;\n        eachProp2(defaultProps, (value, key) => {\n          if (is4.und(props2[key])) {\n            props2[key] = value;\n          }\n        });\n        const result2 = await target.start(props2);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise(resume => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result2;\n      })();\n    };\n    let result;\n    if (G3.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (is4.arr(to2)) {\n        animating = (async queue => {\n          for (const props2 of queue) {\n            await animate(props2);\n          }\n        })(to2);\n      } else {\n        animating = Promise.resolve(to2(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : void 0;\n        state.promise = parentId ? prevPromise : void 0;\n      }\n    }\n    if (is4.fun(onRest)) {\n      raf2.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  flush(state.timeouts, t => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = void 0;\n  if (cancelId) state.cancelId = cancelId;\n}\nvar BailSignal = class extends Error {\n  constructor() {\n    super(\"An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.\");\n  }\n};\nvar SkipAnimationSignal = class extends Error {\n  constructor() {\n    super(\"SkipAnimationSignal\");\n  }\n};\n\n// src/FrameValue.ts\nimport { deprecateInterpolate, frameLoop, FluidValue as FluidValue2, Globals as G4, callFluidObservers } from \"@react-spring/shared\";\nimport { getAnimated } from \"@react-spring/animated\";\nvar isFrameValue = value => value instanceof FrameValue;\nvar nextId = 1;\nvar FrameValue = class extends FluidValue2 {\n  constructor() {\n    super(...arguments);\n    this.id = nextId++;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  /** Get the current value */\n  get() {\n    const node = getAnimated(this);\n    return node && node.getValue();\n  }\n  /** Create a spring that maps our value to another value */\n  to() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return G4.to(this, args);\n  }\n  /** @deprecated Use the `to` method instead. */\n  interpolate() {\n    deprecateInterpolate();\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return G4.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  /** Called when the first child is added. */\n  _attach() {}\n  /** Called when the last child is removed. */\n  _detach() {}\n  /** Tell our children about our new value */\n  _onChange(value) {\n    let idle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    callFluidObservers(this, {\n      type: \"change\",\n      parent: this,\n      value,\n      idle\n    });\n  }\n  /** Tell our children about our new priority */\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      frameLoop.sort(this);\n    }\n    callFluidObservers(this, {\n      type: \"priority\",\n      parent: this,\n      priority\n    });\n  }\n};\n\n// src/SpringPhase.ts\nvar $P = Symbol.for(\"SpringPhase\");\nvar HAS_ANIMATED = 1;\nvar IS_ANIMATING = 2;\nvar IS_PAUSED = 4;\nvar hasAnimated = target => (target[$P] & HAS_ANIMATED) > 0;\nvar isAnimating = target => (target[$P] & IS_ANIMATING) > 0;\nvar isPaused = target => (target[$P] & IS_PAUSED) > 0;\nvar setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nvar setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\n// src/SpringValue.ts\nvar SpringValue = class extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    /** The animation state */\n    this.animation = new Animation();\n    /** Some props have customizable default values */\n    this.defaultProps = {};\n    /** The state for `runAsync` calls */\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: /* @__PURE__ */new Set(),\n      resumeQueue: /* @__PURE__ */new Set(),\n      timeouts: /* @__PURE__ */new Set()\n    };\n    /** The promise resolvers of pending `start` calls */\n    this._pendingCalls = /* @__PURE__ */new Set();\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastCallId = 0;\n    /** The last `scheduleProps` call that changed the `to` prop */\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!is5.und(arg1) || !is5.und(arg2)) {\n      const props = is5.obj(arg1) ? _objectSpread({}, arg1) : _objectSpread(_objectSpread({}, arg2), {}, {\n        from: arg1\n      });\n      if (is5.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return getFluidValue2(this.animation.to);\n  }\n  get velocity() {\n    const node = getAnimated2(this);\n    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map(node2 => node2.lastVelocity || 0);\n  }\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this);\n  }\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let {\n      toValues\n    } = anim;\n    const {\n      config: config2\n    } = anim;\n    const payload = getPayload(anim.to);\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray2(getFluidValue2(anim.to));\n    }\n    anim.values.forEach((node2, i) => {\n      if (node2.done) return;\n      const to2 =\n      // Animated strings always go from 0 to 1.\n      node2.constructor == AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];\n      let finished = anim.immediate;\n      let position = to2;\n      if (!finished) {\n        position = node2.lastPosition;\n        if (config2.tension <= 0) {\n          node2.done = true;\n          return;\n        }\n        let elapsed = node2.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = is5.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;\n        let velocity;\n        const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));\n        if (!is5.und(config2.duration)) {\n          let p = 1;\n          if (config2.duration > 0) {\n            if (this._memoizedDuration !== config2.duration) {\n              this._memoizedDuration = config2.duration;\n              if (node2.durationProgress > 0) {\n                node2.elapsedTime = config2.duration * node2.durationProgress;\n                elapsed = node2.elapsedTime += dt;\n              }\n            }\n            p = (config2.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node2.durationProgress = p;\n          }\n          position = from + config2.easing(p) * (to2 - from);\n          velocity = (position - node2.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config2.decay) {\n          const decay = config2.decay === true ? 0.998 : config2.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node2.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;\n          const restVelocity = config2.restVelocity || precision / 10;\n          const bounceFactor = config2.clamp ? 0 : config2.bounce;\n          const canBounce = !is5.und(bounceFactor);\n          const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to2 - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to2 || position > to2 == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to2;\n              }\n            }\n            const springForce = -config2.tension * 1e-6 * (position - to2);\n            const dampingForce = -config2.friction * 1e-3 * velocity;\n            const acceleration = (springForce + dampingForce) / config2.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node2.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(\"Got NaN while animating:\", this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node2.done = true;\n      } else {\n        idle = false;\n      }\n      if (node2.setValue(position, config2.round)) {\n        changed = true;\n      }\n    });\n    const node = getAnimated2(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = getFluidValue2(anim.to);\n      if ((currVal !== finalVal || changed) && !config2.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config2.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  /** Set the current value, while stopping the current animation */\n  set(value) {\n    raf3.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({\n      pause: true\n    });\n  }\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({\n      pause: false\n    });\n  }\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const {\n        to: to2,\n        config: config2\n      } = this.animation;\n      raf3.batchedUpdates(() => {\n        this._onStart();\n        if (!config2.decay) {\n          this._set(to2, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  /** Push props into the pending queue. */\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to2, arg2) {\n    let queue;\n    if (!is5.und(to2)) {\n      queue = [is5.obj(to2) ? to2 : _objectSpread(_objectSpread({}, arg2), {}, {\n        to: to2\n      })];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(queue.map(props => {\n      const up = this._update(props);\n      return up;\n    })).then(results => getCombinedResult(this, results));\n  }\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel) {\n    const {\n      to: to2\n    } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    raf3.batchedUpdates(() => this._stop(to2, cancel));\n    return this;\n  }\n  /** Restart the animation. */\n  reset() {\n    this._update({\n      reset: true\n    });\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._start();\n    } else if (event.type == \"priority\") {\n      this.priority = event.priority + 1;\n    }\n  }\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  _prepareNode(props) {\n    const key = this.key || \"\";\n    let {\n      to: to2,\n      from\n    } = props;\n    to2 = is5.obj(to2) ? to2[key] : to2;\n    if (to2 == null || isAsyncTo(to2)) {\n      to2 = void 0;\n    }\n    from = is5.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = void 0;\n    }\n    const range = {\n      to: to2,\n      from\n    };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to2, from] = [from, to2];\n      from = getFluidValue2(from);\n      if (!is5.und(from)) {\n        this._set(from);\n      } else if (!getAnimated2(this)) {\n        this._set(to2);\n      }\n    }\n    return range;\n  }\n  /** Every update is processed by this method before merging. */\n  _update(_ref2, isLoop) {\n    let props = Object.assign({}, (_objectDestructuringEmpty(_ref2), _ref2));\n    const {\n      key,\n      defaultProps\n    } = this;\n    if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value));\n    mergeActiveFn(this, props, \"onProps\");\n    sendEvent(this, \"onProps\", props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error(\"Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?\");\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            flushCalls(state.pauseQueue);\n            sendEvent(this, \"onPause\", getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            flushCalls(state.resumeQueue);\n            sendEvent(this, \"onResume\", getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  /** Merge props into the current animation */\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !is5.und(range.to);\n    const hasFromProp = !is5.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const {\n      key,\n      defaultProps,\n      animation: anim\n    } = this;\n    const {\n      to: prevTo,\n      from: prevFrom\n    } = anim;\n    let {\n      to: to2 = prevTo,\n      from = prevFrom\n    } = range;\n    if (hasFromProp && !hasToProp && (!props.default || is5.und(to2))) {\n      to2 = from;\n    }\n    if (props.reverse) [to2, from] = [from, to2];\n    const hasFromChanged = !isEqual(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = getFluidValue2(from);\n    const hasToChanged = !isEqual(to2, prevTo);\n    if (hasToChanged) {\n      this._focus(to2);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const {\n      config: config2\n    } = anim;\n    const {\n      decay,\n      velocity\n    } = config2;\n    if (hasToProp || hasFromProp) {\n      config2.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(config2, callProp(props.config, key),\n      // Avoid calling the same \"config\" prop twice.\n      props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);\n    }\n    let node = getAnimated2(this);\n    if (!node || is5.und(to2)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset =\n    // When `reset` is undefined, the `from` prop implies `reset: true`,\n    // except for declarative updates. When `reset` is defined, there\n    // must exist a value to animate from.\n    is5.und(props.reset) ? hasFromProp && !props.default : !is5.und(from) && matchProp(props.reset, key);\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to2);\n    const isAnimatable = is5.num(goal) || is5.arr(goal) || isAnimatedString2(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to2);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else throw Error(\"Cannot animate between \".concat(node.constructor.name, \" and \").concat(nodeType.name, \", as the \\\"to\\\" prop suggests\"));\n      }\n    }\n    const goalType = node.constructor;\n    let started = hasFluidValue(to2);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config2.decay, decay) || !isEqual(config2.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = hasFluidValue(to2) ? null : goalType == AnimatedString ? [1] : toArray2(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const {\n          onRest\n        } = anim;\n        each2(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        flushCalls(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed) raf3.batchedUpdates(() => {\n          anim.changed = !reset;\n          onRest === null || onRest === void 0 || onRest(result, this);\n          if (reset) {\n            callProp(defaultProps.onRest, result);\n          } else {\n            var _anim$onStart;\n            (_anim$onStart = anim.onStart) === null || _anim$onStart === void 0 || _anim$onStart.call(anim, result, this);\n          }\n        });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if (getFluidObservers(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const {\n      to: to2\n    } = this.animation;\n    if (hasFluidValue(to2)) {\n      addFluidObserver(to2, this);\n      if (isFrameValue(to2)) {\n        priority = to2.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const {\n      to: to2\n    } = this.animation;\n    if (hasFluidValue(to2)) {\n      removeFluidObserver(to2, this);\n    }\n  }\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  _set(arg) {\n    let idle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    const value = getFluidValue2(arg);\n    if (!is5.und(value)) {\n      const oldNode = getAnimated2(this);\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        const nodeType = getAnimatedType(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          raf3.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return getAnimated2(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(this, \"onStart\", getFinishedResult(this, checkFinished(this, anim.to)), this);\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  _start() {\n    const anim = this.animation;\n    getAnimated2(this).reset(getFluidValue2(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (G5.skipAnimation) {\n      this.finish();\n    } else {\n      frameLoop2.start(this);\n    }\n  }\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      each2(anim.values, node => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = void 0;\n      }\n      callFluidObservers2(this, {\n        type: \"idle\",\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal !== null && goal !== void 0 ? goal : anim.to));\n      flushCalls(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, \"onRest\", result, this);\n      }\n    }\n  }\n};\nfunction checkFinished(target, to2) {\n  const goal = computeGoal(to2);\n  const value = computeGoal(target.get());\n  return isEqual(value, goal);\n}\nfunction createLoopUpdate(props) {\n  let loop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : props.loop;\n  let to2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : props.to;\n  const loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate(_objectSpread(_objectSpread({}, props), {}, {\n      loop,\n      // Avoid updating default props when looping.\n      default: false,\n      // Never loop the `pause` prop.\n      pause: void 0,\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to2) ? to2 : void 0,\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : void 0,\n      reset\n    }, overrides));\n  }\n}\nfunction createUpdate(props) {\n  const {\n    to: to2,\n    from\n  } = props = inferTo(props);\n  const keys = /* @__PURE__ */new Set();\n  if (is5.obj(to2)) findDefined(to2, keys);\n  if (is5.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update2 = createUpdate(props);\n  if (is5.und(update2.default)) {\n    update2.default = getDefaultProps(update2);\n  }\n  return update2;\n}\nfunction findDefined(values, keys) {\n  eachProp3(values, (value, key) => value != null && keys.add(key));\n}\nvar ACTIVE_EVENTS = [\"onStart\", \"onRest\", \"onChange\", \"onPause\", \"onResume\"];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;\n}\nfunction sendEvent(target, type) {\n  var _target$animation$typ, _target$animation, _target$defaultProps$, _target$defaultProps;\n  for (var _len4 = arguments.length, args = new Array(_len4 > 2 ? _len4 - 2 : 0), _key4 = 2; _key4 < _len4; _key4++) {\n    args[_key4 - 2] = arguments[_key4];\n  }\n  (_target$animation$typ = (_target$animation = target.animation)[type]) === null || _target$animation$typ === void 0 || _target$animation$typ.call(_target$animation, ...args);\n  (_target$defaultProps$ = (_target$defaultProps = target.defaultProps)[type]) === null || _target$defaultProps$ === void 0 || _target$defaultProps$.call(_target$defaultProps, ...args);\n}\n\n// src/Controller.ts\nimport { is as is6, raf as raf4, each as each3, noop, flush as flush2, toArray as toArray3, eachProp as eachProp4, flushCalls as flushCalls2, addFluidObserver as addFluidObserver2 } from \"@react-spring/shared\";\nvar BATCHED_EVENTS = [\"onStart\", \"onChange\", \"onRest\"];\nvar nextId2 = 1;\nvar Controller = class {\n  constructor(props, flush3) {\n    this.id = nextId2++;\n    /** The animated values */\n    this.springs = {};\n    /** The queue of props passed to the `update` method. */\n    this.queue = [];\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastAsyncId = 0;\n    /** The values currently being animated */\n    this._active = /* @__PURE__ */new Set();\n    /** The values that changed recently */\n    this._changed = /* @__PURE__ */new Set();\n    /** Equals false when `onStart` listeners can be called */\n    this._started = false;\n    /** State used by the `runAsync` function */\n    this._state = {\n      paused: false,\n      pauseQueue: /* @__PURE__ */new Set(),\n      resumeQueue: /* @__PURE__ */new Set(),\n      timeouts: /* @__PURE__ */new Set()\n    };\n    /** The event queues that are flushed once per frame maximum */\n    this._events = {\n      onStart: /* @__PURE__ */new Map(),\n      onChange: /* @__PURE__ */new Map(),\n      onRest: /* @__PURE__ */new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush3) {\n      this._flush = flush3;\n    }\n    if (props) {\n      this.start(_objectSpread({\n        default: true\n      }, props));\n    }\n  }\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every(spring => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  /** Get the current values of our springs */\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  /** Set the current values without animating. */\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!is6.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  /** Push an update onto the queue of each value. */\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props) {\n    let {\n      queue\n    } = this;\n    if (props) {\n      queue = toArray3(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  /** @internal */\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each(spring => spring.stop(!!arg));\n    }\n    return this;\n  }\n  /** Freeze the active animation in time */\n  pause(keys) {\n    if (is6.und(keys)) {\n      this.start({\n        pause: true\n      });\n    } else {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].pause());\n    }\n    return this;\n  }\n  /** Resume the animation if paused. */\n  resume(keys) {\n    if (is6.und(keys)) {\n      this.start({\n        pause: false\n      });\n    } else {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].resume());\n    }\n    return this;\n  }\n  /** Call a function once per spring value */\n  each(iterator) {\n    eachProp4(this.springs, iterator);\n  }\n  /** @internal Called at the end of every animation frame */\n  _onFrame() {\n    const {\n      onStart,\n      onChange,\n      onRest\n    } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      flush2(onStart, _ref3 => {\n        let [onStart2, result] = _ref3;\n        result.value = this.get();\n        onStart2(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      flush2(onChange, _ref4 => {\n        let [onChange2, result] = _ref4;\n        result.value = values;\n        onChange2(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      flush2(onRest, _ref5 => {\n        let [onRest2, result] = _ref5;\n        result.value = values;\n        onRest2(result, this, this._item);\n      });\n    }\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else return;\n    raf4.onFrame(this._onFrame);\n  }\n};\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(results => getCombinedResult(ctrl, results));\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const {\n    keys,\n    to: to2,\n    from,\n    loop,\n    onRest,\n    onResolve\n  } = props;\n  const defaults2 = is6.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to2 === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = is6.arr(to2) || is6.fun(to2) ? to2 : void 0;\n  if (asyncTo) {\n    props.to = void 0;\n    props.onRest = void 0;\n    if (defaults2) {\n      defaults2.onRest = void 0;\n    }\n  } else {\n    each3(BATCHED_EVENTS, key => {\n      const handler = props[key];\n      if (is6.fun(handler)) {\n        const queue = ctrl[\"_events\"][key];\n        props[key] = _ref6 => {\n          let {\n            finished,\n            cancelled\n          } = _ref6;\n          const result2 = queue.get(handler);\n          if (result2) {\n            if (!finished) result2.finished = false;\n            if (cancelled) result2.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults2) {\n          defaults2[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl[\"_state\"];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    flushCalls2(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(key => ctrl.springs[key].start(props));\n  const cancel = props.cancel === true || getDefaultProp(props, \"cancel\") === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(scheduleProps(++ctrl[\"_lastAsyncId\"], {\n      props,\n      state,\n      actions: {\n        pause: noop,\n        resume: noop,\n        start(props2, resolve) {\n          if (cancel) {\n            stopAsync(state, ctrl[\"_lastAsyncId\"]);\n            resolve(getCancelledResult(ctrl));\n          } else {\n            props2.onRest = onRest;\n            resolve(runAsync(asyncTo, props2, state, ctrl));\n          }\n        }\n      }\n    }));\n  }\n  if (state.paused) {\n    await new Promise(resume => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to2);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    raf4.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = _objectSpread({}, ctrl.springs);\n  if (props) {\n    each3(toArray3(props), props2 => {\n      if (is6.und(props2.keys)) {\n        props2 = createUpdate(props2);\n      }\n      if (!is6.obj(props2.to)) {\n        props2 = _objectSpread(_objectSpread({}, props2), {}, {\n          to: void 0\n        });\n      }\n      prepareSprings(springs, props2, key => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  eachProp4(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      addFluidObserver2(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    addFluidObserver2(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    each3(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring[\"_prepareNode\"](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  each3(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\n// src/SpringContext.tsx\nimport * as React from \"react\";\nimport { useContext } from \"react\";\nimport { useMemoOne } from \"@react-spring/shared\";\nvar SpringContext = _ref7 => {\n  let {\n      children\n    } = _ref7,\n    props = _objectWithoutProperties(_ref7, _excluded);\n  const inherited = useContext(ctx);\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate;\n  props = useMemoOne(() => ({\n    pause,\n    immediate\n  }), [pause, immediate]);\n  const {\n    Provider\n  } = ctx;\n  return /* @__PURE__ */React.createElement(Provider, {\n    value: props\n  }, children);\n};\nvar ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\nfunction makeContext(target, init) {\n  Object.assign(target, React.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\n\n// src/SpringRef.ts\nimport { each as each4, is as is7, deprecateDirectCall } from \"@react-spring/shared\";\nvar SpringRef = () => {\n  const current = [];\n  const SpringRef2 = function (props) {\n    deprecateDirectCall();\n    const results = [];\n    each4(current, (ctrl, i) => {\n      if (is7.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = _getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.current = current;\n  SpringRef2.add = function (ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef2.delete = function (ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef2.pause = function () {\n    each4(current, ctrl => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef2.resume = function () {\n    each4(current, ctrl => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef2.set = function (values) {\n    each4(current, (ctrl, i) => {\n      const update2 = is7.fun(values) ? values(i, ctrl) : values;\n      if (update2) {\n        ctrl.set(update2);\n      }\n    });\n  };\n  SpringRef2.start = function (props) {\n    const results = [];\n    each4(current, (ctrl, i) => {\n      if (is7.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = this._getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.stop = function () {\n    each4(current, ctrl => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef2.update = function (props) {\n    each4(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function (arg, ctrl, index) {\n    return is7.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef2._getProps = _getProps;\n  return SpringRef2;\n};\n\n// src/hooks/useSprings.ts\nfunction useSprings(length, props, deps) {\n  const propsFn = is8.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const layoutId = useRef(0);\n  const forceUpdate = useForceUpdate();\n  const state = useMemo(() => ({\n    ctrls: [],\n    queue: [],\n    flush(ctrl, updates2) {\n      const springs2 = getSprings(ctrl, updates2);\n      const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some(key => !ctrl.springs[key]);\n      return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise(resolve => {\n        setSprings(ctrl, springs2);\n        state.queue.push(() => {\n          resolve(flushUpdateQueue(ctrl, updates2));\n        });\n        forceUpdate();\n      });\n    }\n  }), []);\n  const ctrls = useRef([...state.ctrls]);\n  const updates = [];\n  const prevLength = usePrev(length) || 0;\n  useMemo(() => {\n    each5(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update2 = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update2) {\n        updates[i] = declareUpdate(update2);\n      }\n    }\n  }\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = useContext2(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect2(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const {\n      queue\n    } = state;\n    if (queue.length) {\n      state.queue = [];\n      each5(queue, cb => cb());\n    }\n    each5(ctrls.current, (ctrl, i) => {\n      ref === null || ref === void 0 || ref.add(ctrl);\n      if (hasContext) {\n        ctrl.start({\n          default: context\n        });\n      }\n      const update2 = updates[i];\n      if (update2) {\n        replaceRef(ctrl, update2.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update2);\n        } else {\n          ctrl.start(update2);\n        }\n      }\n    });\n  });\n  useOnce(() => () => {\n    each5(state.ctrls, ctrl => ctrl.stop(true));\n  });\n  const values = springs.map(x => _objectSpread({}, x));\n  return ref ? [values, ref] : values;\n}\n\n// src/hooks/useSpring.ts\nfunction useSpring(props, deps) {\n  const isFn = is9.fun(props);\n  const [[values], ref] = useSprings(1, isFn ? props : [props], isFn ? deps || [] : deps);\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\n// src/hooks/useSpringRef.ts\nimport { useState } from \"react\";\nvar initSpringRef = () => SpringRef();\nvar useSpringRef = () => useState(initSpringRef)[0];\n\n// src/hooks/useSpringValue.ts\nimport { useConstant, useOnce as useOnce2 } from \"@react-spring/shared\";\nvar useSpringValue = (initial, props) => {\n  const springValue = useConstant(() => new SpringValue(initial, props));\n  useOnce2(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\n// src/hooks/useTrail.ts\nimport { each as each6, is as is10, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect3 } from \"@react-spring/shared\";\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = is10.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = void 0;\n  const result = useSprings(length, (i, ctrl) => {\n    const props = propsFn ? propsFn(i, ctrl) : propsArg;\n    passedRef = props.ref;\n    reverse = reverse && props.reverse;\n    return props;\n  },\n  // Ensure the props function is called when no deps exist.\n  // This works around the 3 argument rule.\n  deps || [{}]);\n  useIsomorphicLayoutEffect3(() => {\n    each6(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({\n            to: parent.springs\n          });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({\n          to: parent.springs\n        });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef !== null && passedRef !== void 0 ? passedRef : result[1];\n    ref[\"_getProps\"] = (propsArg2, ctrl, i) => {\n      const props = is10.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\n\n// src/hooks/useTransition.tsx\nimport * as React2 from \"react\";\nimport { useContext as useContext3, useRef as useRef2, useMemo as useMemo2 } from \"react\";\nimport { is as is11, toArray as toArray4, useForceUpdate as useForceUpdate2, useOnce as useOnce3, usePrev as usePrev2, each as each7, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect4 } from \"@react-spring/shared\";\nfunction useTransition(data, props, deps) {\n  const propsFn = is11.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = useMemo2(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const items = toArray4(data);\n  const transitions = [];\n  const usedTransitions = useRef2(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  useIsomorphicLayoutEffect4(() => {\n    usedTransitions.current = transitions;\n  });\n  useOnce3(() => {\n    each7(transitions, t => {\n      ref === null || ref === void 0 || ref.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      each7(usedTransitions.current, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  useIsomorphicLayoutEffect4(() => each7(expired, _ref8 => {\n    let {\n      ctrl,\n      item,\n      key\n    } = _ref8;\n    detachRefs(ctrl, ref);\n    callProp(onDestroyed, item, key);\n  }));\n  const reused = [];\n  if (prevTransitions) each7(prevTransitions, (t, i) => {\n    if (t.expired) {\n      clearTimeout(t.expirationId);\n      expired.push(t);\n    } else {\n      i = reused[i] = keys.indexOf(t.key);\n      if (~i) transitions[i] = t;\n    }\n  });\n  each7(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: \"mount\" /* MOUNT */,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const {\n      leave\n    } = propsFn ? propsFn() : props;\n    each7(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = _objectSpread(_objectSpread({}, t), {}, {\n          item: items[keyIndex]\n        });\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (is11.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = useForceUpdate2();\n  const defaultProps = getDefaultProps(props);\n  const changes = /* @__PURE__ */new Map();\n  const exitingTransitions = useRef2(/* @__PURE__ */new Map());\n  const forceChange = useRef2(false);\n  each7(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to2;\n    let phase;\n    const propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == \"mount\" /* MOUNT */) {\n      to2 = p.enter;\n      phase = \"enter\" /* ENTER */;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != \"leave\" /* LEAVE */) {\n        if (isLeave) {\n          to2 = p.leave;\n          phase = \"leave\" /* LEAVE */;\n        } else if (to2 = p.update) {\n          phase = \"update\" /* UPDATE */;\n        } else return;\n      } else if (!isLeave) {\n        to2 = p.enter;\n        phase = \"enter\" /* ENTER */;\n      } else return;\n    }\n    to2 = callProp(to2, t.item, i);\n    to2 = is11.obj(to2) ? inferTo(to2) : {\n      to: to2\n    };\n    if (!to2.config) {\n      const config2 = propsConfig || defaultProps.config;\n      to2.config = callProp(config2, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = _objectSpread(_objectSpread({}, defaultProps), {}, {\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false\n    }, to2);\n    if (phase == \"enter\" /* ENTER */ && is11.und(payload.from)) {\n      const p2 = propsFn ? propsFn() : props;\n      const from = is11.und(p2.initial) || prevTransitions ? p2.from : p2.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const {\n      onResolve\n    } = payload;\n    payload.onResolve = result => {\n      callProp(onResolve, result);\n      const transitions2 = usedTransitions.current;\n      const t2 = transitions2.find(t3 => t3.key === key);\n      if (!t2) return;\n      if (result.cancelled && t2.phase != \"update\" /* UPDATE */) {\n        return;\n      }\n      if (t2.ctrl.idle) {\n        const idle = transitions2.every(t3 => t3.ctrl.idle);\n        if (t2.phase == \"leave\" /* LEAVE */) {\n          const expiry = callProp(expires, t2.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t2.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 2147483647) t2.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions2.some(t3 => t3.expired)) {\n          exitingTransitions.current.delete(t2);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === \"leave\" /* LEAVE */ && exitBeforeEnter) {\n      exitingTransitions.current.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    } else {\n      changes.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    }\n  });\n  const context = useContext3(SpringContext);\n  const prevContext = usePrev2(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect4(() => {\n    if (hasContext) {\n      each7(transitions, t => {\n        t.ctrl.start({\n          default: context\n        });\n      });\n    }\n  }, [context]);\n  each7(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  useIsomorphicLayoutEffect4(() => {\n    each7(exitingTransitions.current.size ? exitingTransitions.current : changes, (_ref9, t) => {\n      let {\n        phase,\n        payload\n      } = _ref9;\n      const {\n        ctrl\n      } = t;\n      t.phase = phase;\n      ref === null || ref === void 0 || ref.add(ctrl);\n      if (hasContext && phase == \"enter\" /* ENTER */) {\n        ctrl.start({\n          default: context\n        });\n      }\n      if (payload) {\n        replaceRef(ctrl, payload.ref);\n        if ((ctrl.ref || ref) && !forceChange.current) {\n          ctrl.update(payload);\n        } else {\n          ctrl.start(payload);\n          if (forceChange.current) {\n            forceChange.current = false;\n          }\n        }\n      }\n    });\n  }, reset ? void 0 : deps);\n  const renderTransitions = render => /* @__PURE__ */React2.createElement(React2.Fragment, null, transitions.map((t, i) => {\n    const {\n      springs\n    } = changes.get(t) || t.ctrl;\n    const elem = render(_objectSpread({}, springs), t.item, t, i);\n    return elem && elem.type ? /* @__PURE__ */React2.createElement(elem.type, _objectSpread(_objectSpread({}, elem.props), {}, {\n      key: is11.str(t.key) || is11.num(t.key) ? t.key : t.ctrl.id,\n      ref: elem.ref\n    })) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nvar nextKey = 1;\nfunction getKeys(items, _ref0, prevTransitions) {\n  let {\n    key,\n    keys = key\n  } = _ref0;\n  if (keys === null) {\n    const reused = /* @__PURE__ */new Set();\n    return items.map(item => {\n      const t = prevTransitions && prevTransitions.find(t2 => t2.item === item && t2.phase !== \"leave\" /* LEAVE */ && !reused.has(t2));\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return is11.und(keys) ? items : is11.fun(keys) ? items.map(keys) : toArray4(keys);\n}\n\n// src/hooks/useScroll.ts\nimport { each as each8, onScroll, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect5 } from \"@react-spring/shared\";\nvar useScroll = function () {\n  let _ref1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    {\n      container\n    } = _ref1,\n    springOptions = _objectWithoutProperties(_ref1, _excluded2);\n  const [scrollValues, api] = useSpring(() => _objectSpread({\n    scrollX: 0,\n    scrollY: 0,\n    scrollXProgress: 0,\n    scrollYProgress: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect5(() => {\n    const cleanupScroll = onScroll(_ref10 => {\n      let {\n        x,\n        y\n      } = _ref10;\n      api.start({\n        scrollX: x.current,\n        scrollXProgress: x.progress,\n        scrollY: y.current,\n        scrollYProgress: y.progress\n      });\n    }, {\n      container: (container === null || container === void 0 ? void 0 : container.current) || void 0\n    });\n    return () => {\n      each8(Object.values(scrollValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\n// src/hooks/useResize.ts\nimport { onResize, each as each9, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect6 } from \"@react-spring/shared\";\nvar useResize = _ref11 => {\n  let {\n      container\n    } = _ref11,\n    springOptions = _objectWithoutProperties(_ref11, _excluded3);\n  const [sizeValues, api] = useSpring(() => _objectSpread({\n    width: 0,\n    height: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect6(() => {\n    const cleanupScroll = onResize(_ref12 => {\n      let {\n        width,\n        height\n      } = _ref12;\n      api.start({\n        width,\n        height,\n        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n      });\n    }, {\n      container: (container === null || container === void 0 ? void 0 : container.current) || void 0\n    });\n    return () => {\n      each9(Object.values(sizeValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\n// src/hooks/useInView.ts\nimport { useRef as useRef3, useState as useState2 } from \"react\";\nimport { is as is12, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect7 } from \"@react-spring/shared\";\nvar defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = useState2(false);\n  const ref = useRef3();\n  const propsFn = is12.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const {\n      to: to2 = {},\n      from = {}\n    } = springsProps,\n    restSpringProps = _objectWithoutProperties(springsProps, _excluded4);\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => _objectSpread({\n    from\n  }, restSpringProps), []);\n  useIsomorphicLayoutEffect7(() => {\n    const element = ref.current;\n    const _ref13 = intersectionArguments !== null && intersectionArguments !== void 0 ? intersectionArguments : {},\n      {\n        root,\n        once,\n        amount = \"any\"\n      } = _ref13,\n      restArgs = _objectWithoutProperties(_ref13, _excluded5);\n    if (!element || once && isInView || typeof IntersectionObserver === \"undefined\") return;\n    const activeIntersections = /* @__PURE__ */new WeakMap();\n    const onEnter = () => {\n      if (to2) {\n        api.start(to2);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? void 0 : cleanup;\n    };\n    const handleIntersection = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (is12.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, _objectSpread({\n      root: root && root.current || void 0,\n      threshold: typeof amount === \"number\" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount]\n    }, restArgs));\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\n\n// src/components/Spring.tsx\nfunction Spring(_ref14) {\n  let {\n      children\n    } = _ref14,\n    props = _objectWithoutProperties(_ref14, _excluded6);\n  return children(useSpring(props));\n}\n\n// src/components/Trail.tsx\nimport { is as is13 } from \"@react-spring/shared\";\nfunction Trail(_ref15) {\n  let {\n      items,\n      children\n    } = _ref15,\n    props = _objectWithoutProperties(_ref15, _excluded7);\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return is13.fun(result) ? result(trails[index]) : result;\n  });\n}\n\n// src/components/Transition.tsx\nfunction Transition(_ref16) {\n  let {\n      items,\n      children\n    } = _ref16,\n    props = _objectWithoutProperties(_ref16, _excluded8);\n  return useTransition(items, props)(children);\n}\n\n// src/interpolate.ts\nimport { deprecateInterpolate as deprecateInterpolate2 } from \"@react-spring/shared\";\n\n// src/Interpolation.ts\nimport { is as is14, raf as raf5, each as each10, isEqual as isEqual2, toArray as toArray5, frameLoop as frameLoop3, getFluidValue as getFluidValue3, createInterpolator, Globals as G6, callFluidObservers as callFluidObservers3, addFluidObserver as addFluidObserver3, removeFluidObserver as removeFluidObserver2, hasFluidValue as hasFluidValue2 } from \"@react-spring/shared\";\nimport { getAnimated as getAnimated3, setAnimated as setAnimated2, getAnimatedType as getAnimatedType2, getPayload as getPayload2 } from \"@react-spring/animated\";\nvar Interpolation = class extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.source = source;\n    /** Equals false when in the frameloop */\n    this.idle = true;\n    /** The inputs which are currently animating */\n    this._active = /* @__PURE__ */new Set();\n    this.calc = createInterpolator(...args);\n    const value = this._get();\n    const nodeType = getAnimatedType2(value);\n    setAnimated2(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!isEqual2(value, oldValue)) {\n      getAnimated3(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = is14.arr(this.source) ? this.source.map(getFluidValue3) : toArray5(getFluidValue3(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      each10(getPayload2(this), node => {\n        node.done = false;\n      });\n      if (G6.skipAnimation) {\n        raf5.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        frameLoop3.start(this);\n      }\n    }\n  }\n  // Observe our sources only when we're observed.\n  _attach() {\n    let priority = 1;\n    each10(toArray5(this.source), source => {\n      if (hasFluidValue2(source)) {\n        addFluidObserver3(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  // Stop observing our sources once we have no observers.\n  _detach() {\n    each10(toArray5(this.source), source => {\n      if (hasFluidValue2(source)) {\n        removeFluidObserver2(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else if (event.type == \"priority\") {\n      this.priority = toArray5(this.source).reduce((highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);\n    }\n  }\n};\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    each10(getPayload2(self), node => {\n      node.done = true;\n    });\n    callFluidObservers3(self, {\n      type: \"idle\",\n      parent: self\n    });\n  }\n}\n\n// src/interpolate.ts\nvar to = function (source) {\n  for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n    args[_key5 - 1] = arguments[_key5];\n  }\n  return new Interpolation(source, args);\n};\nvar interpolate = function (source) {\n  for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n    args[_key6 - 1] = arguments[_key6];\n  }\n  return deprecateInterpolate2(), new Interpolation(source, args);\n};\n\n// src/globals.ts\nimport { Globals, frameLoop as frameLoop4, createStringInterpolator } from \"@react-spring/shared\";\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nvar update = frameLoop4.advance;\n\n// src/index.ts\nimport { createInterpolator as createInterpolator2, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect8, useReducedMotion, easings as easings2 } from \"@react-spring/shared\";\nexport * from \"@react-spring/types\";\nexport { BailSignal, Controller, FrameValue, Globals, Interpolation, Spring, SpringContext, SpringRef, SpringValue, Trail, Transition, config, createInterpolator2 as createInterpolator, easings2 as easings, inferTo, interpolate, to, update, useChain, useInView, useIsomorphicLayoutEffect8 as useIsomorphicLayoutEffect, useReducedMotion, useResize, useScroll, useSpring, useSpringRef, useSpringValue, useSprings, useTrail, useTransition };", "map": {"version": 3, "names": ["each", "useIsomorphicLayoutEffect", "is", "toArray", "eachProp", "getFluidValue", "isAnimatedString", "Globals", "G", "callProp", "value", "_len", "arguments", "length", "args", "Array", "_key", "fun", "matchProp", "key", "includes", "resolveProp", "prop", "obj", "getDefaultProp", "props", "default", "noopTransform", "getDefaultProps", "transform", "undefined", "keys", "DEFAULT_PROPS", "Object", "defaults2", "und", "RESERVED_PROPS", "config", "from", "to", "ref", "loop", "reset", "pause", "cancel", "reverse", "immediate", "delay", "onProps", "onStart", "onChange", "onPause", "onResume", "onRest", "onResolve", "items", "trail", "sort", "expires", "initial", "enter", "update", "leave", "children", "onDestroyed", "callId", "parentId", "getForwardProps", "forward", "count", "inferTo", "to2", "out", "val", "_objectSpread", "computeGoal", "arr", "map", "createStringInterpolator", "range", "output", "hasProps", "_", "isAsyncTo", "detachRefs", "ctrl", "_ctrl$ref", "delete", "replaceRef", "_ctrl$ref2", "add", "<PERSON><PERSON><PERSON>n", "refs", "timeSteps", "timeFrame", "prev<PERSON><PERSON><PERSON>", "i", "controllers", "current", "isNaN", "queue", "memoizedDelayProp", "start", "p", "Promise", "resolve", "queues", "q", "then", "update2", "push", "all", "is9", "useContext", "useContext2", "useMemo", "useRef", "is8", "each5", "usePrev", "useOnce", "useForceUpdate", "useIsomorphicLayoutEffect2", "is5", "raf", "raf3", "each2", "isEqual", "toArray2", "eachProp3", "frameLoop", "frameLoop2", "flushCalls", "getFluidValue2", "isAnimatedString2", "G5", "callFluidObservers", "callFluidObservers2", "hasFluidValue", "addFluidObserver", "removeFluidObserver", "getFluidObservers", "AnimatedValue", "AnimatedString", "getPayload", "getAnimated", "getAnimated2", "setAnimated", "getAnimatedType", "is2", "easings", "tension", "friction", "gentle", "wobbly", "stiff", "slow", "molasses", "defaults", "mass", "damping", "easing", "linear", "clamp", "AnimationConfig", "constructor", "velocity", "assign", "mergeConfig", "config2", "newConfig", "defaultConfig", "sanitizeConfig", "frequency", "Math", "pow", "PI", "decay", "duration", "isTensionConfig", "emptyArray", "Animation", "changed", "values", "to<PERSON><PERSON><PERSON>", "fromValues", "is3", "G2", "scheduleProps", "_ref", "defaultProps", "state", "actions", "reject", "_props$cancel", "timeout", "paused", "resumeQueue", "resume", "timeouts", "time", "now", "skipAnimation", "delayed", "setTimeout", "pauseQueue", "cancelId", "err", "is4", "raf2", "flush", "eachProp2", "G3", "getCombinedResult", "target", "results", "some", "result", "cancelled", "getCancelledResult", "get", "every", "noop", "getNoopResult", "getFinishedResult", "finished", "runAsync", "asyncTo", "prevTo", "promise", "prevPromise", "asyncId", "preventBail", "bail", "bailPromise", "bailIfEnded", "bailSignal", "bailResult", "animate", "arg1", "arg2", "BailSignal", "skipAnimationSignal", "SkipAnimationSignal", "stopAsync", "props2", "result2", "animating", "stop", "bind", "batchedUpdates", "item", "t", "clear", "Error", "deprecateInterpolate", "FluidValue", "FluidValue2", "G4", "isFrameValue", "FrameValue", "nextId", "id", "_priority", "priority", "_onPriorityChange", "node", "getValue", "_len2", "_key2", "interpolate", "_len3", "_key3", "toJSON", "observerAdded", "_attach", "observerRemoved", "_detach", "_onChange", "idle", "type", "parent", "$P", "Symbol", "for", "HAS_ANIMATED", "IS_ANIMATING", "IS_PAUSED", "hasAnimated", "isAnimating", "isPaused", "setActiveBit", "active", "setPausedBit", "SpringValue", "animation", "_state", "Set", "_pendingCalls", "_lastCallId", "_lastToId", "_memoizedDuration", "goal", "lastVelocity", "node2", "<PERSON><PERSON><PERSON><PERSON>", "advance", "dt", "anim", "payload", "for<PERSON>ach", "done", "lastPosition", "position", "elapsed", "elapsedTime", "v0", "precision", "min", "abs", "durationProgress", "progress", "e", "exp", "restVelocity", "bounceFactor", "bounce", "canBounce", "isGrowing", "isMoving", "isBouncing", "step", "numSteps", "ceil", "n", "springForce", "dampingForce", "acceleration", "Number", "console", "warn", "setValue", "round", "currVal", "finalVal", "_stop", "set", "_focus", "_set", "_update", "finish", "_onStart", "up", "eventObserved", "event", "_start", "_prepareNode", "_ref2", "isLoop", "_objectDestructuringEmpty", "test", "mergeActiveFn", "sendEvent", "isFrozen", "checkFinished", "_resume", "_merge", "nextProps", "createLoopUpdate", "hasToProp", "hasFromProp", "prevFrom", "has<PERSON>romC<PERSON>ed", "hasToChanged", "hasAsyncTo", "isAnimatable", "num", "nodeType", "concat", "name", "goalType", "started", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ACTIVE_EVENTS", "_anim$onStart", "call", "arg", "oldNode", "create", "loopRet", "overrides", "createUpdate", "findDefined", "size", "declareUpdate", "_target$animation$typ", "_target$animation", "_target$defaultProps$", "_target$defaultProps", "_len4", "_key4", "is6", "raf4", "each3", "flush2", "toArray3", "eachProp4", "flushCalls2", "addFluidObserver2", "BATCHED_EVENTS", "nextId2", "Controller", "flush3", "springs", "_lastAsyncId", "_active", "_changed", "_started", "_events", "Map", "_onFrame", "_flush", "spring", "_item", "prepare<PERSON>eys", "flushUpdateQueue", "iterator", "_ref3", "onStart2", "_ref4", "onChange2", "_ref5", "onRest2", "onFrame", "flushUpdate", "handler", "_ref6", "promises", "getSprings", "prepareSprings", "createSpring", "setSprings", "observer", "React", "useMemoOne", "SpringContext", "_ref7", "_objectWithoutProperties", "_excluded", "inherited", "ctx", "Provider", "createElement", "makeContext", "Consumer", "init", "createContext", "_context", "each4", "is7", "deprecateDirectCall", "SpringRef", "SpringRef2", "_getProps", "indexOf", "splice", "index", "useSprings", "deps", "propsFn", "layoutId", "forceUpdate", "ctrls", "updates2", "springs2", "canFlushSync", "updates", "prevLength", "slice", "declareUpdates", "startIndex", "endIndex", "context", "prevContext", "hasContext", "cb", "x", "useSpring", "isFn", "useState", "initSpringRef", "useSpringRef", "useConstant", "useOnce2", "useSpringValue", "springValue", "each6", "is10", "useIsomorphicLayoutEffect3", "useTrail", "propsArg", "passedRef", "propsArg2", "React2", "useContext3", "useRef2", "useMemo2", "is11", "toArray4", "useForceUpdate2", "useOnce3", "usePrev2", "each7", "useIsomorphicLayoutEffect4", "useTransition", "data", "exitBeforeEnter", "propsRef", "propsConfig", "transitions", "usedTransitions", "prevTransitions", "expired", "clearTimeout", "expirationId", "get<PERSON><PERSON><PERSON>", "_ref8", "reused", "phase", "keyIndex", "prevIndex", "a", "b", "changes", "exitingTransitions", "forceChange", "prevPhase", "props<PERSON><PERSON><PERSON>", "isLeave", "p2", "transitions2", "t2", "find", "t3", "expiry", "expiryMs", "ind", "findIndex", "_ref9", "renderTransitions", "render", "Fragment", "elem", "str", "<PERSON><PERSON><PERSON>", "_ref0", "has", "each8", "onScroll", "useIsomorphicLayoutEffect5", "useScroll", "_ref1", "container", "springOptions", "_excluded2", "scrollValues", "api", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "cleanupScroll", "_ref10", "y", "onResize", "each9", "useIsomorphicLayoutEffect6", "useResize", "_ref11", "_excluded3", "sizeValues", "width", "height", "_ref12", "useRef3", "useState2", "is12", "useIsomorphicLayoutEffect7", "defaultThresholdOptions", "any", "useInView", "isInView", "setIsInView", "springsProps", "restSpringProps", "_excluded4", "intersectionArguments", "element", "_ref13", "root", "once", "amount", "restArgs", "_excluded5", "IntersectionObserver", "activeIntersections", "WeakMap", "onEnter", "cleanup", "handleIntersection", "entries", "entry", "onLeave", "isIntersecting", "Boolean", "newOnLeave", "unobserve", "threshold", "isArray", "observe", "Spring", "_ref14", "_excluded6", "is13", "Trail", "_ref15", "_excluded7", "trails", "Transition", "_ref16", "_excluded8", "deprecateInterpolate2", "is14", "raf5", "each10", "isEqual2", "toArray5", "frameLoop3", "getFluidValue3", "createInterpolator", "G6", "callFluidObservers3", "addFluidObserver3", "removeFluidObserver2", "hasFluidValue2", "getAnimated3", "setAnimated2", "getAnimatedType2", "getPayload2", "Interpolation", "source", "calc", "_get", "_dt", "oldValue", "checkIdle", "becomeIdle", "inputs", "max", "reduce", "highest", "isIdle", "self", "_len5", "_key5", "_len6", "_key6", "frameLoop4", "createInterpolator2", "useIsomorphicLayoutEffect8", "useReducedMotion", "easings2"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useChain.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/helpers.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpring.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSprings.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/AnimationConfig.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/constants.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Animation.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/scheduleProps.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/runAsync.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/AnimationResult.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/FrameValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringPhase.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Controller.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringContext.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringRef.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpringRef.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpringValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useTrail.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useTransition.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useScroll.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useResize.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useInView.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Spring.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Trail.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Transition.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/interpolate.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Interpolation.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/globals.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/index.ts"], "sourcesContent": ["import { each, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { SpringRef } from '../SpringRef'\nimport { callProp } from '../helpers'\n\n/**\n * Used to orchestrate animation hooks in sequence with one another.\n * This is best used when you specifically want to orchestrate different\n * types of animation hook e.g. `useSpring` & `useTransition` in\n * sequence as opposed to multiple `useSpring` hooks.\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *  //...\n *  useChain([springRef, transitionRef])\n *  //...\n * }\n * ```\n *\n * @param refs – An array of `SpringRef`s.\n * @param timeSteps – Optional array of numbers that define the\n * delay between each animation from 0-1. The length should correlate\n * to the length of `refs`.\n * @param timeFrame – Optional number that defines the total duration\n *\n * @public\n */\nexport function useChain(\n  refs: ReadonlyArray<SpringRef>,\n  timeSteps?: number[],\n  timeFrame = 1000\n) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0\n      each(refs, (ref, i) => {\n        const controllers = ref.current\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i]\n\n          // Use the previous delay if none exists.\n          if (isNaN(delay)) delay = prevDelay\n          else prevDelay = delay\n\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              // memoizing stops recursion https://github.com/pmndrs/react-spring/issues/1367\n              const memoizedDelayProp = props.delay\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key)\n            })\n          })\n\n          ref.start()\n        }\n      })\n    } else {\n      let p: Promise<any> = Promise.resolve()\n      each(refs, ref => {\n        const controllers = ref.current\n        if (controllers.length) {\n          // Take the queue of each controller\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue\n            ctrl.queue = []\n            return q\n          })\n\n          // Apply the queue when the previous ref stops animating\n          p = p.then(() => {\n            each(controllers, (ctrl, i) =>\n              each(queues[i] || [], update => ctrl.queue.push(update))\n            )\n            return Promise.all(ref.start())\n          })\n        }\n      })\n    }\n  })\n}\n", "import {\n  is,\n  toArray,\n  eachProp,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n} from '@react-spring/shared'\nimport { AnyFn, OneOrMore, Lookup } from '@react-spring/types'\nimport { ReservedProps, ForwardProps, InferTo } from './types'\nimport type { Controller } from './Controller'\nimport type { SpringRef } from './SpringRef'\n\nexport function callProp<T>(\n  value: T,\n  ...args: T extends AnyFn ? Parameters<T> : unknown[]\n): T extends AnyFn<any, infer U> ? U : T {\n  return is.fun(value) ? value(...args) : value\n}\n\n/** Try to coerce the given value into a boolean using the given key */\nexport const matchProp = (\n  value: boolean | OneOrMore<string> | ((key: any) => boolean) | undefined,\n  key: string | undefined\n) =>\n  value === true ||\n  !!(\n    key &&\n    value &&\n    (is.fun(value) ? value(key) : toArray(value).includes(key))\n  )\n\nexport const resolveProp = <T>(\n  prop: T | Lookup<T> | undefined,\n  key: string | undefined\n) => (is.obj(prop) ? key && (prop as any)[key] : prop)\n\nexport const concatFn = <T extends AnyFn>(first: T | undefined, last: T) =>\n  first ? (...args: Parameters<T>) => (first(...args), last(...args)) : last\n\n/** Returns `true` if the given prop is having its default value set. */\nexport const hasDefaultProp = <T extends Lookup>(props: T, key: keyof T) =>\n  !is.und(getDefaultProp(props, key))\n\n/** Get the default value being set for the given `key` */\nexport const getDefaultProp = <T extends Lookup, P extends keyof T>(\n  props: T,\n  key: P\n): T[P] =>\n  props.default === true\n    ? props[key]\n    : props.default\n      ? props.default[key]\n      : undefined\n\nconst noopTransform = (value: any) => value\n\n/**\n * Extract the default props from an update.\n *\n * When the `default` prop is falsy, this function still behaves as if\n * `default: true` was used. The `default` prop is always respected when\n * truthy.\n */\nexport const getDefaultProps = <T extends Lookup>(\n  props: Lookup,\n  transform: (value: any, key: string) => any = noopTransform\n): T => {\n  let keys: readonly string[] = DEFAULT_PROPS\n  if (props.default && props.default !== true) {\n    props = props.default\n    keys = Object.keys(props)\n  }\n  const defaults: any = {}\n  for (const key of keys) {\n    const value = transform(props[key], key)\n    if (!is.und(value)) {\n      defaults[key] = value\n    }\n  }\n  return defaults\n}\n\n/**\n * These props are implicitly used as defaults when defined in a\n * declarative update (eg: render-based) or any update with `default: true`.\n *\n * Use `default: {}` or `default: false` to opt-out of these implicit defaults\n * for any given update.\n *\n * Note: These are not the only props with default values. For example, the\n * `pause`, `cancel`, and `immediate` props. But those must be updated with\n * the object syntax (eg: `default: { immediate: true }`).\n */\nexport const DEFAULT_PROPS = [\n  'config',\n  'onProps',\n  'onStart',\n  'onChange',\n  'onPause',\n  'onResume',\n  'onRest',\n] as const\n\nconst RESERVED_PROPS: {\n  [key: string]: 1 | undefined\n} = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1,\n}\n\n/**\n * Extract any properties whose keys are *not* reserved for customizing your\n * animations. All hooks use this function, which means `useTransition` props\n * are reserved for `useSpring` calls, etc.\n */\nfunction getForwardProps<Props extends ReservedProps>(\n  props: Props\n): ForwardProps<Props> | undefined {\n  const forward: any = {}\n\n  let count = 0\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value\n      count++\n    }\n  })\n\n  if (count) {\n    return forward\n  }\n}\n\n/**\n * Clone the given `props` and move all non-reserved props\n * into the `to` prop.\n */\nexport function inferTo<T extends object>(props: T): InferTo<T> {\n  const to = getForwardProps(props)\n  if (to) {\n    const out: any = { to }\n    eachProp(props, (val, key) => key in to || (out[key] = val))\n    return out\n  }\n  return { ...props } as any\n}\n\n// Compute the goal value, converting \"red\" to \"rgba(255, 0, 0, 1)\" in the process\nexport function computeGoal<T>(value: T | FluidValue<T>): T {\n  value = getFluidValue(value)\n  return is.arr(value)\n    ? value.map(computeGoal)\n    : isAnimatedString(value)\n      ? (G.createStringInterpolator({\n          range: [0, 1],\n          output: [value, value] as any,\n        })(1) as any)\n      : value\n}\n\nexport function hasProps(props: object) {\n  for (const _ in props) return true\n  return false\n}\n\nexport function isAsyncTo(to: any) {\n  return is.fun(to) || (is.arr(to) && is.obj(to[0]))\n}\n\n/** Detach `ctrl` from `ctrl.ref` and (optionally) the given `ref` */\nexport function detachRefs(ctrl: Controller, ref?: SpringRef) {\n  ctrl.ref?.delete(ctrl)\n  ref?.delete(ctrl)\n}\n\n/** Replace `ctrl.ref` with the given `ref` (if defined) */\nexport function replaceRef(ctrl: Controller, ref?: SpringRef) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl)\n    ref.add(ctrl)\n    ctrl.ref = ref\n  }\n}\n", "import { Lookup, Remap } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { ControllerUpdate, PickAnimated, SpringValues } from '../types'\nimport { Valid } from '../types/common'\nimport { SpringRef } from '../SpringRef'\nimport { useSprings } from './useSprings'\n\n/**\n * The props that `useSpring` recognizes.\n */\nexport type UseSpringProps<Props extends object = any> = unknown &\n  PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? Remap<\n        ControllerUpdate<State> & {\n          /**\n           * Used to access the imperative API.\n           *\n           * When defined, the render animation won't auto-start.\n           */\n          ref?: SpringRef<State>\n        }\n      >\n    : never\n  : never\n\n/**\n * The `props` function is only called on the first render, unless\n * `deps` change (when defined). State is inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props:\n    | Function\n    | (() => (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps),\n  deps?: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/**\n * Updated on every render, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps\n): SpringValues<PickAnimated<Props>>\n\n/**\n * Updated only when `deps` change, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps,\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSpring(props: any, deps?: readonly any[]) {\n  const isFn = is.fun(props)\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  )\n  return isFn || arguments.length == 2 ? [values, ref] : values\n}\n", "import { useContext, useMemo, useRef } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport {\n  is,\n  each,\n  usePrev,\n  useOnce,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  ControllerFlushFn,\n  ControllerUpdate,\n  PickAnimated,\n  SpringValues,\n} from '../types'\nimport { UseSpringProps } from './useSpring'\nimport { declareUpdate } from '../SpringValue'\nimport {\n  Controller,\n  getSprings,\n  flushUpdateQueue,\n  setSprings,\n} from '../Controller'\nimport { hasProps, detachRefs, replaceRef } from '../helpers'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nexport type UseSpringsProps<State extends Lookup = Lookup> = unknown &\n  ControllerUpdate<State> & {\n    ref?: SpringRefType<State>\n  }\n\n/**\n * When the `deps` argument exists, the `props` function is called whenever\n * the `deps` change on re-render.\n *\n * Without the `deps` argument, the `props` function is only called once.\n */\nexport function useSprings<Props extends UseSpringProps>(\n  length: number,\n  props: (i: number, ctrl: Controller) => Props,\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/**\n * Animations are updated on re-render.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[]\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * When the `deps` argument exists, you get the `update` and `stop` function.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[],\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSprings(\n  length: number,\n  props: any[] | ((i: number, ctrl: Controller) => any),\n  deps?: readonly any[]\n): any {\n  const propsFn = is.fun(props) && props\n  if (propsFn && !deps) deps = []\n\n  // Create a local ref if a props function or deps array is ever passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  interface State {\n    // The controllers used for applying updates.\n    ctrls: Controller[]\n    // The queue of changes to make on commit.\n    queue: Array<() => void>\n    // The flush function used by controllers.\n    flush: ControllerFlushFn\n  }\n\n  // Set to 0 to prevent sync flush.\n  const layoutId = useRef(0)\n  const forceUpdate = useForceUpdate()\n\n  // State is updated on commit.\n  const state = useMemo(\n    (): State => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates) {\n        const springs = getSprings(ctrl, updates)\n\n        // Flushing is postponed until the component's commit phase\n        // if a spring was created since the last commit.\n        const canFlushSync =\n          layoutId.current > 0 &&\n          !state.queue.length &&\n          !Object.keys(springs).some(key => !ctrl.springs[key])\n\n        return canFlushSync\n          ? flushUpdateQueue(ctrl, updates)\n          : new Promise<any>(resolve => {\n              setSprings(ctrl, springs)\n              state.queue.push(() => {\n                resolve(flushUpdateQueue(ctrl, updates))\n              })\n              forceUpdate()\n            })\n      },\n    }),\n    []\n  )\n\n  const ctrls = useRef([...state.ctrls])\n  const updates: any[] = []\n\n  // Cache old controllers to dispose in the commit phase.\n  const prevLength = usePrev(length) || 0\n\n  // Create new controllers when \"length\" increases, and destroy\n  // the affected controllers when \"length\" decreases.\n  useMemo(() => {\n    // Clean up any unused controllers\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref)\n      ctrl.stop(true)\n    })\n    ctrls.current.length = length\n\n    declareUpdates(prevLength, length)\n  }, [length])\n\n  // Update existing controllers when \"deps\" are changed.\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length))\n  }, deps)\n\n  /** Fill the `updates` array with declarative updates for the given index range. */\n  function declareUpdates(startIndex: number, endIndex: number) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl =\n        ctrls.current[i] ||\n        (ctrls.current[i] = new Controller(null, state.flush))\n\n      const update: UseSpringProps<any> = propsFn\n        ? propsFn(i, ctrl)\n        : (props as any)[i]\n\n      if (update) {\n        updates[i] = declareUpdate(update)\n      }\n    }\n  }\n\n  // New springs are created during render so users can pass them to\n  // their animated components, but new springs aren't cached until the\n  // commit phase (see the `useIsomorphicLayoutEffect` callback below).\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]))\n\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++\n\n    // Replace the cached controllers.\n    state.ctrls = ctrls.current\n\n    // Flush the commit queue.\n    const { queue } = state\n    if (queue.length) {\n      state.queue = []\n      each(queue, cb => cb())\n    }\n\n    // Update existing controllers.\n    each(ctrls.current, (ctrl, i) => {\n      // Attach the controller to the local ref.\n      ref?.add(ctrl)\n\n      // Update the default props.\n      if (hasContext) {\n        ctrl.start({ default: context })\n      }\n\n      // Apply updates created during render.\n      const update = updates[i]\n      if (update) {\n        // Update the injected ref if needed.\n        replaceRef(ctrl, update.ref)\n\n        // When an injected ref exists, the update is postponed\n        // until the ref has its `start` method called.\n        if (ctrl.ref) {\n          ctrl.queue.push(update)\n        } else {\n          ctrl.start(update)\n        }\n      }\n    })\n  })\n\n  // Cancel the animations of all controllers on unmount.\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true))\n  })\n\n  // Return a deep copy of the `springs` array so the caller can\n  // safely mutate it during render.\n  const values = springs.map(x => ({ ...x }))\n\n  return ref ? [values, ref] : values\n}\n", "import {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  eachProp,\n  frameLoop,\n  flushCalls,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  getFluidObservers,\n} from '@react-spring/shared'\nimport {\n  Animated,\n  AnimatedValue,\n  AnimatedString,\n  getPayload,\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n} from '@react-spring/animated'\nimport { Lookup } from '@react-spring/types'\n\nimport { Animation } from './Animation'\nimport { mergeConfig } from './AnimationConfig'\nimport { scheduleProps } from './scheduleProps'\nimport { runAsync, RunAsyncState, RunAsyncProps, stopAsync } from './runAsync'\nimport {\n  callProp,\n  computeGoal,\n  matchProp,\n  inferTo,\n  getDefaultProps,\n  getDefaultProp,\n  isAsyncTo,\n  resolveProp,\n} from './helpers'\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  isAnimating,\n  isPaused,\n  setPausedBit,\n  hasAnimated,\n  setActiveBit,\n} from './SpringPhase'\nimport {\n  AnimationRange,\n  AnimationResolver,\n  EventKey,\n  PickEventFns,\n} from './types/internal'\nimport { AsyncResult, SpringUpdate, VelocityProp, SpringProps } from './types'\nimport {\n  getCombinedResult,\n  getCancelledResult,\n  getFinishedResult,\n  getNoopResult,\n} from './AnimationResult'\n\ndeclare const console: any\n\ninterface DefaultSpringProps<T>\n  extends Pick<SpringProps<T>, 'pause' | 'cancel' | 'immediate' | 'config'>,\n    PickEventFns<SpringProps<T>> {}\n\n/**\n * Only numbers, strings, and arrays of numbers/strings are supported.\n * Non-animatable strings are also supported.\n */\nexport class SpringValue<T = any> extends FrameValue<T> {\n  /** The property name used when `to` or `from` is an object. Useful when debugging too. */\n  key?: string\n\n  /** The animation state */\n  animation = new Animation<T>()\n\n  /** The queue of pending props */\n  queue?: SpringUpdate<T>[]\n\n  /** Some props have customizable default values */\n  defaultProps: DefaultSpringProps<T> = {}\n\n  /** The state for `runAsync` calls */\n  protected _state: RunAsyncState<SpringValue<T>> = {\n    paused: false,\n    delayed: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The promise resolvers of pending `start` calls */\n  protected _pendingCalls = new Set<AnimationResolver<this>>()\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastCallId = 0\n\n  /** The last `scheduleProps` call that changed the `to` prop */\n  protected _lastToId = 0\n\n  protected _memoizedDuration = 0\n\n  constructor(from: Exclude<T, object>, props?: SpringUpdate<T>)\n  constructor(props?: SpringUpdate<T>)\n  constructor(arg1?: any, arg2?: any) {\n    super()\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 }\n      if (is.und(props.default)) {\n        props.default = true\n      }\n      this.start(props)\n    }\n  }\n\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this)\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to) as T\n  }\n\n  get velocity(): VelocityProp<T> {\n    const node = getAnimated(this)!\n    return (\n      node instanceof AnimatedValue\n        ? node.lastVelocity || 0\n        : node.getPayload().map(node => node.lastVelocity || 0)\n    ) as any\n  }\n\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this)\n  }\n\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this)\n  }\n\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this)\n  }\n\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed\n  }\n\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt: number) {\n    let idle = true\n    let changed = false\n\n    const anim = this.animation\n    let { toValues } = anim\n    const { config } = anim\n\n    const payload = getPayload(anim.to)\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to)) as any\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return\n\n      const to =\n        // Animated strings always go from 0 to 1.\n        node.constructor == AnimatedString\n          ? 1\n          : payload\n            ? payload[i].lastPosition\n            : toValues![i]\n\n      let finished = anim.immediate\n      let position = to\n\n      if (!finished) {\n        position = node.lastPosition\n\n        // Loose springs never move.\n        if (config.tension <= 0) {\n          node.done = true\n          return\n        }\n\n        let elapsed = (node.elapsedTime += dt)\n        const from = anim.fromValues[i]\n\n        const v0 =\n          node.v0 != null\n            ? node.v0\n            : (node.v0 = is.arr(config.velocity)\n                ? config.velocity[i]\n                : config.velocity)\n\n        let velocity: number\n\n        /** The smallest distance from a value before being treated like said value. */\n        /**\n         * TODO: make this value ~0.0001 by default in next breaking change\n         * for more info see – https://github.com/pmndrs/react-spring/issues/1389\n         */\n        const precision =\n          config.precision ||\n          (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001))\n\n        // Duration easing\n        if (!is.und(config.duration)) {\n          let p = 1\n          if (config.duration > 0) {\n            /**\n             * Here we check if the duration has changed in the config\n             * and if so update the elapsed time to the percentage\n             * of completition so there is no jank in the animation\n             * https://github.com/pmndrs/react-spring/issues/1163\n             */\n            if (this._memoizedDuration !== config.duration) {\n              // update the memoized version to the new duration\n              this._memoizedDuration = config.duration\n\n              // if the value has started animating we need to update it\n              if (node.durationProgress > 0) {\n                // set elapsed time to be the same percentage of progress as the previous duration\n                node.elapsedTime = config.duration * node.durationProgress\n                // add the delta so the below updates work as expected\n                elapsed = node.elapsedTime += dt\n              }\n            }\n\n            // calculate the new progress\n            p = (config.progress || 0) + elapsed / this._memoizedDuration\n            // p is clamped between 0-1\n            p = p > 1 ? 1 : p < 0 ? 0 : p\n            // store our new progress\n            node.durationProgress = p\n          }\n\n          position = from + config.easing(p) * (to - from)\n          velocity = (position - node.lastPosition) / dt\n\n          finished = p == 1\n        }\n\n        // Decay easing\n        else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay\n          const e = Math.exp(-(1 - decay) * elapsed)\n\n          position = from + (v0 / (1 - decay)) * (1 - e)\n          finished = Math.abs(node.lastPosition - position) <= precision\n\n          // derivative of position\n          velocity = v0 * e\n        }\n\n        // Spring easing\n        else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity\n\n          /** The velocity at which movement is essentially none */\n          const restVelocity = config.restVelocity || precision / 10\n\n          // Bouncing is opt-in (not to be confused with overshooting)\n          const bounceFactor = config.clamp ? 0 : config.bounce!\n          const canBounce = !is.und(bounceFactor)\n\n          /** When `true`, the value is increasing over time */\n          const isGrowing = from == to ? node.v0 > 0 : from < to\n\n          /** When `true`, the velocity is considered moving */\n          let isMoving!: boolean\n\n          /** When `true`, the velocity is being deflected or clamped */\n          let isBouncing = false\n\n          const step = 1 // 1ms\n          const numSteps = Math.ceil(dt / step)\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision\n              if (finished) {\n                break\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing\n\n              // Invert the velocity with a magnitude, or clamp it.\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor\n                position = to\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to)\n            const dampingForce = -config.friction * 0.001 * velocity\n            const acceleration = (springForce + dampingForce) / config.mass // pt/ms^2\n\n            velocity = velocity + acceleration * step // pt/ms\n            position = position + velocity * step\n          }\n        }\n\n        node.lastVelocity = velocity\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this)\n          finished = true\n        }\n      }\n\n      // Parent springs must finish before their children can.\n      if (payload && !payload[i].done) {\n        finished = false\n      }\n\n      if (finished) {\n        node.done = true\n      } else {\n        idle = false\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true\n      }\n    })\n\n    const node = getAnimated(this)!\n    /**\n     * Get the node's current value, this will be different\n     * to anim.to when config.decay is true\n     */\n    const currVal = node.getValue()\n    if (idle) {\n      // get our final fluid val from the anim.to\n      const finalVal = getFluidValue(anim.to)\n      /**\n       * check if they're not equal, or if they're\n       * change and if there's no config.decay set\n       */\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        // set the value to anim.to\n        node.setValue(finalVal)\n        this._onChange(finalVal)\n      } else if (changed && config.decay) {\n        /**\n         * if it's changed but there is a config.decay,\n         * just call _onChange with currrent value\n         */\n        this._onChange(currVal)\n      }\n      // call stop because the spring has stopped.\n      this._stop()\n    } else if (changed) {\n      /**\n       * if the spring has changed, but is not idle,\n       * just call the _onChange handler\n       */\n      this._onChange(currVal)\n    }\n  }\n\n  /** Set the current value, while stopping the current animation */\n  set(value: T | FluidValue<T>) {\n    raf.batchedUpdates(() => {\n      this._stop()\n\n      // These override the current value and goal value that may have\n      // been updated by `onRest` handlers in the `_stop` call above.\n      this._focus(value)\n      this._set(value)\n    })\n    return this\n  }\n\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true })\n  }\n\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false })\n  }\n\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to, config } = this.animation\n      raf.batchedUpdates(() => {\n        // Ensure the \"onStart\" and \"onRest\" props are called.\n        this._onStart()\n\n        // Jump to the goal value, except for decay animations\n        // which have an undefined goal value.\n        if (!config.decay) {\n          this._set(to, false)\n        }\n\n        this._stop()\n      })\n    }\n    return this\n  }\n\n  /** Push props into the pending queue. */\n  update(props: SpringUpdate<T>) {\n    const queue = this.queue || (this.queue = [])\n    queue.push(props)\n    return this\n  }\n\n  /**\n   * Update this value's animation using the queue of pending props,\n   * and unpause the current animation (if one is frozen).\n   *\n   * When arguments are passed, a new animation is created, and the\n   * queued animations are left alone.\n   */\n  start(): AsyncResult<this>\n\n  start(props: SpringUpdate<T>): AsyncResult<this>\n\n  start(to: T, props?: SpringProps<T>): AsyncResult<this>\n\n  start(to?: any, arg2?: any) {\n    let queue: SpringUpdate<T>[]\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : { ...arg2, to }]\n    } else {\n      queue = this.queue || []\n      this.queue = []\n    }\n\n    return Promise.all(\n      queue.map(props => {\n        const up = this._update(props)\n        return up\n      })\n    ).then(results => getCombinedResult(this, results))\n  }\n\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel?: boolean) {\n    const { to } = this.animation\n\n    // The current value becomes the goal value.\n    this._focus(this.get())\n\n    stopAsync(this._state, cancel && this._lastCallId)\n    raf.batchedUpdates(() => this._stop(to, cancel))\n\n    return this\n  }\n\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true })\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._start()\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1\n    }\n  }\n\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  protected _prepareNode(props: {\n    to?: any\n    from?: any\n    reverse?: boolean\n    default?: any\n  }) {\n    const key = this.key || ''\n\n    let { to, from } = props\n\n    to = is.obj(to) ? to[key] : to\n    if (to == null || isAsyncTo(to)) {\n      to = undefined\n    }\n\n    from = is.obj(from) ? from[key] : from\n    if (from == null) {\n      from = undefined\n    }\n\n    // Create the range now to avoid \"reverse\" logic.\n    const range = { to, from }\n\n    // Before ever animating, this method ensures an `Animated` node\n    // exists and keeps its value in sync with the \"from\" prop.\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to]\n\n      from = getFluidValue(from)\n      if (!is.und(from)) {\n        this._set(from)\n      }\n      // Use the \"to\" value if our node is undefined.\n      else if (!getAnimated(this)) {\n        this._set(to)\n      }\n    }\n\n    return range\n  }\n\n  /** Every update is processed by this method before merging. */\n  protected _update(\n    { ...props }: SpringProps<T>,\n    isLoop?: boolean\n  ): AsyncResult<SpringValue<T>> {\n    const { key, defaultProps } = this\n\n    // Update the default props immediately.\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(props, (value, prop) =>\n          /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      )\n\n    mergeActiveFn(this, props, 'onProps')\n    sendEvent(this, 'onProps', props, this)\n\n    // Ensure the initial value can be accessed by animated components.\n    const range = this._prepareNode(props)\n\n    if (Object.isFrozen(this)) {\n      throw Error(\n        'Cannot animate a `SpringValue` object that is frozen. ' +\n          'Did you forget to pass your component to `animated(...)` before animating its props?'\n      )\n    }\n\n    const state = this._state\n\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true)\n            flushCalls(state.pauseQueue)\n            sendEvent(\n              this,\n              'onPause',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false)\n            if (isAnimating(this)) {\n              this._resume()\n            }\n            flushCalls(state.resumeQueue)\n            sendEvent(\n              this,\n              'onResume',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        start: this._merge.bind(this, range),\n      },\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props)\n        if (nextProps) {\n          return this._update(nextProps, true)\n        }\n      }\n      return result\n    })\n  }\n\n  /** Merge props into the current animation */\n  protected _merge(\n    range: AnimationRange<T>,\n    props: RunAsyncProps<SpringValue<T>>,\n    resolve: AnimationResolver<SpringValue<T>>\n  ): void {\n    // The \"cancel\" prop cancels all pending delays and it forces the\n    // active animation to stop where it is.\n    if (props.cancel) {\n      this.stop(true)\n      return resolve(getCancelledResult(this))\n    }\n\n    /** The \"to\" prop is defined. */\n    const hasToProp = !is.und(range.to)\n\n    /** The \"from\" prop is defined. */\n    const hasFromProp = !is.und(range.from)\n\n    // Avoid merging other props if implicitly prevented, except\n    // when both the \"to\" and \"from\" props are undefined.\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId\n      } else {\n        return resolve(getCancelledResult(this))\n      }\n    }\n\n    const { key, defaultProps, animation: anim } = this\n    const { to: prevTo, from: prevFrom } = anim\n    let { to = prevTo, from = prevFrom } = range\n\n    // Focus the \"from\" value if changing without a \"to\" value.\n    // For default updates, do this only if no \"to\" value exists.\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from\n    }\n\n    // Flip the current range if \"reverse\" is true.\n    if (props.reverse) [to, from] = [from, to]\n\n    /** The \"from\" value is changing. */\n    const hasFromChanged = !isEqual(from, prevFrom)\n\n    if (hasFromChanged) {\n      anim.from = from\n    }\n\n    // Coerce \"from\" into a static value.\n    from = getFluidValue(from)\n\n    /** The \"to\" value is changing. */\n    const hasToChanged = !isEqual(to, prevTo)\n\n    if (hasToChanged) {\n      this._focus(to)\n    }\n\n    /** The \"to\" prop is async. */\n    const hasAsyncTo = isAsyncTo(props.to)\n\n    const { config } = anim\n    const { decay, velocity } = config\n\n    // Reset to default velocity when goal values are defined.\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0\n    }\n\n    // The \"runAsync\" function treats the \"config\" prop as a default,\n    // so we must avoid merging it when the \"to\" prop is async.\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config,\n        callProp(props.config, key!),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config\n          ? callProp(defaultProps.config, key!)\n          : void 0\n      )\n    }\n\n    // This instance might not have its Animated node yet. For example,\n    // the constructor can be given props without a \"to\" or \"from\" value.\n    let node = getAnimated(this)\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true))\n    }\n\n    /** When true, start at the \"from\" value. */\n    const reset =\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      is.und(props.reset)\n        ? hasFromProp && !props.default\n        : !is.und(from) && matchProp(props.reset, key)\n\n    // The current value, where the animation starts from.\n    const value = reset ? (from as T) : this.get()\n\n    // The animation ends at this value, unless \"to\" is fluid.\n    const goal = computeGoal<any>(to)\n\n    // Only specific types can be animated to/from.\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal)\n\n    // When true, the value changes instantly on the next frame.\n    const immediate =\n      !hasAsyncTo &&\n      (!isAnimatable ||\n        matchProp(defaultProps.immediate || props.immediate, key))\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to)\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal)!\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          )\n      }\n    }\n\n    // The type of Animated node for the goal value.\n    const goalType = node.constructor\n\n    // When the goal value is fluid, we don't know if its value\n    // will change before the next animation frame, so it always\n    // starts the animation to be safe.\n    let started = hasFluidValue(to)\n    let finished = false\n\n    if (!started) {\n      // When true, the current value has probably changed.\n      const hasValueChanged = reset || (!hasAnimated(this) && hasFromChanged)\n\n      // When the \"to\" value or current value are changed,\n      // start animating if not already finished.\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal)\n        started = !finished\n      }\n\n      // Changing \"decay\" or \"velocity\" starts the animation.\n      if (\n        (!isEqual(anim.immediate, immediate) && !immediate) ||\n        !isEqual(config.decay, decay) ||\n        !isEqual(config.velocity, velocity)\n      ) {\n        started = true\n      }\n    }\n\n    // Was the goal value set to the current value while animating?\n    if (finished && isAnimating(this)) {\n      // If the first frame has passed, allow the animation to\n      // overshoot instead of stopping abruptly.\n      if (anim.changed && !reset) {\n        started = true\n      }\n      // Stop the animation before its first frame.\n      else if (!started) {\n        this._stop(prevTo)\n      }\n    }\n\n    if (!hasAsyncTo) {\n      // Make sure our \"toValues\" are updated even if our previous\n      // \"to\" prop is a fluid value whose current value is also ours.\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload()\n        anim.toValues = hasFluidValue(to)\n          ? null\n          : goalType == AnimatedString\n            ? [1]\n            : toArray(goal)\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate\n\n        // Ensure the immediate goal is used as from value.\n        if (!immediate && !reset) {\n          this._set(prevTo)\n        }\n      }\n\n      if (started) {\n        const { onRest } = anim\n\n        // Set the active handlers when an animation starts.\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type))\n\n        const result = getFinishedResult(this, checkFinished(this, prevTo))\n        flushCalls(this._pendingCalls, result)\n        this._pendingCalls.add(resolve)\n\n        if (anim.changed)\n          raf.batchedUpdates(() => {\n            // Ensure `onStart` can be called after a reset.\n            anim.changed = !reset\n\n            // Call the active `onRest` handler from the interrupted animation.\n            onRest?.(result, this)\n\n            // Notify the default `onRest` of the reset, but wait for the\n            // first frame to pass before sending an `onStart` event.\n            if (reset) {\n              callProp(defaultProps.onRest, result)\n            }\n            // Call the active `onStart` handler here since the first frame\n            // has already passed, which means this is a goal update and not\n            // an entirely new animation.\n            else {\n              anim.onStart?.(result, this)\n            }\n          })\n      }\n    }\n\n    if (reset) {\n      this._set(value)\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this))\n    }\n\n    // Start an animation\n    else if (started) {\n      this._start()\n    }\n\n    // Postpone promise resolution until the animation is finished,\n    // so that no-op updates still resolve at the expected time.\n    else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve)\n    }\n\n    // Resolve our promise immediately.\n    else {\n      resolve(getNoopResult(value))\n    }\n  }\n\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  protected _focus(value: T | FluidValue<T>) {\n    const anim = this.animation\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach()\n      }\n      anim.to = value\n      if (getFluidObservers(this)) {\n        this._attach()\n      }\n    }\n  }\n\n  protected _attach() {\n    let priority = 0\n\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this)\n      if (isFrameValue(to)) {\n        priority = to.priority + 1\n      }\n    }\n\n    this.priority = priority\n  }\n\n  protected _detach() {\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this)\n    }\n  }\n\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  protected _set(arg: T | FluidValue<T>, idle = true): Animated | undefined {\n    const value = getFluidValue(arg)\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this)\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        // Create a new node or update the existing node.\n        const nodeType = getAnimatedType(value)\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value))\n        } else {\n          oldNode.setValue(value)\n        }\n        // Never emit a \"change\" event for the initial value.\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle)\n          })\n        }\n      }\n    }\n    return getAnimated(this)\n  }\n\n  protected _onStart() {\n    const anim = this.animation\n    if (!anim.changed) {\n      anim.changed = true\n      sendEvent(\n        this,\n        'onStart',\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      )\n    }\n  }\n\n  protected _onChange(value: T, idle?: boolean) {\n    if (!idle) {\n      this._onStart()\n      callProp(this.animation.onChange, value, this)\n    }\n    callProp(this.defaultProps.onChange, value, this)\n    super._onChange(value, idle)\n  }\n\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  protected _start() {\n    const anim = this.animation\n\n    // Reset the state of each Animated node.\n    getAnimated(this)!.reset(getFluidValue(anim.to))\n\n    // Use the current values as the from values.\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition)\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true)\n      if (!isPaused(this)) {\n        this._resume()\n      }\n    }\n  }\n\n  protected _resume() {\n    // The \"skipAnimation\" global avoids the frameloop.\n    if (G.skipAnimation) {\n      this.finish()\n    } else {\n      frameLoop.start(this)\n    }\n  }\n\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  protected _stop(goal?: any, cancel?: boolean) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false)\n\n      const anim = this.animation\n      each(anim.values, node => {\n        node.done = true\n      })\n\n      // These active handlers must be reset to undefined or else\n      // they could be called while idle. But keep them defined\n      // when the goal value is dynamic.\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this,\n      })\n\n      const result = cancel\n        ? getCancelledResult(this.get())\n        : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to))\n\n      flushCalls(this._pendingCalls, result)\n      if (anim.changed) {\n        anim.changed = false\n        sendEvent(this, 'onRest', result, this)\n      }\n    }\n  }\n}\n\n/** Returns true when the current value and goal value are equal. */\nfunction checkFinished<T>(target: SpringValue<T>, to: T | FluidValue<T>) {\n  const goal = computeGoal(to)\n  const value = computeGoal(target.get())\n  return isEqual(value, goal)\n}\n\nexport function createLoopUpdate<T>(\n  props: T & { loop?: any; to?: any; from?: any; reverse?: any },\n  loop = props.loop,\n  to = props.to\n): T | undefined {\n  const loopRet = callProp(loop)\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet)\n    const reverse = (overrides || props).reverse\n    const reset = !overrides || overrides.reset\n    return createUpdate({\n      ...props,\n      loop,\n\n      // Avoid updating default props when looping.\n      default: false,\n\n      // Never loop the `pause` prop.\n      pause: undefined,\n\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : undefined,\n      reset,\n\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides,\n    })\n  }\n}\n\n/**\n * Return a new object based on the given `props`.\n *\n * - All non-reserved props are moved into the `to` prop object.\n * - The `keys` prop is set to an array of affected keys,\n *   or `null` if all keys are affected.\n */\nexport function createUpdate(props: any) {\n  const { to, from } = (props = inferTo(props))\n\n  // Collect the keys affected by this update.\n  const keys = new Set<string>()\n\n  if (is.obj(to)) findDefined(to, keys)\n  if (is.obj(from)) findDefined(from, keys)\n\n  // The \"keys\" prop helps in applying updates to affected keys only.\n  props.keys = keys.size ? Array.from(keys) : null\n\n  return props\n}\n\n/**\n * A modified version of `createUpdate` meant for declarative APIs.\n */\nexport function declareUpdate(props: any) {\n  const update = createUpdate(props)\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update)\n  }\n  return update\n}\n\n/** Find keys with defined values */\nfunction findDefined(values: Lookup, keys: Set<string>) {\n  eachProp(values, (value, key) => value != null && keys.add(key as any))\n}\n\n/** Event props with \"active handler\" support */\nconst ACTIVE_EVENTS = [\n  'onStart',\n  'onRest',\n  'onChange',\n  'onPause',\n  'onResume',\n] as const\n\nfunction mergeActiveFn<T, P extends EventKey>(\n  target: SpringValue<T>,\n  props: SpringProps<T>,\n  type: P\n) {\n  target.animation[type] =\n    props[type] !== getDefaultProp(props, type)\n      ? resolveProp<any>(props[type], target.key)\n      : undefined\n}\n\ntype EventArgs<T, P extends EventKey> = Parameters<\n  Extract<SpringProps<T>[P], Function>\n>\n\n/** Call the active handler first, then the default handler. */\nfunction sendEvent<T, P extends EventKey>(\n  target: SpringValue<T>,\n  type: P,\n  ...args: EventArgs<T, P>\n) {\n  target.animation[type]?.(...(args as [any, any]))\n  target.defaultProps[type]?.(...(args as [any, any]))\n}\n", "import { is, easings } from '@react-spring/shared'\nimport { EasingFunction } from '@react-spring/types'\nimport { config as configs } from './constants'\n\nconst defaults: any = {\n  ...configs.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false,\n}\n\nexport class AnimationConfig {\n  /**\n   * With higher tension, the spring will resist bouncing and try harder to stop at its end value.\n   *\n   * When tension is zero, no animation occurs.\n   *\n   * @default 170\n   */\n  tension!: number\n\n  /**\n   * The damping ratio coefficient, or just the damping ratio when `speed` is defined.\n   *\n   * When `speed` is defined, this value should be between 0 and 1.\n   *\n   * Higher friction means the spring will slow down faster.\n   *\n   * @default 26\n   */\n  friction!: number\n\n  /**\n   * The natural frequency (in seconds), which dictates the number of bounces\n   * per second when no damping exists.\n   *\n   * When defined, `tension` is derived from this, and `friction` is derived\n   * from `tension` and `damping`.\n   */\n  frequency?: number\n\n  /**\n   * The damping ratio, which dictates how the spring slows down.\n   *\n   * Set to `0` to never slow down. Set to `1` to slow down without bouncing.\n   * Between `0` and `1` is for you to explore.\n   *\n   * Only works when `frequency` is defined.\n   *\n   * @default 1\n   */\n  damping!: number\n\n  /**\n   * Higher mass means more friction is required to slow down.\n   *\n   * Defaults to 1, which works fine most of the time.\n   *\n   * @default 1\n   */\n  mass!: number\n\n  /**\n   * The initial velocity of one or more values.\n   *\n   * @default 0\n   */\n  velocity: number | number[] = 0\n\n  /**\n   * The smallest velocity before the animation is considered \"not moving\".\n   *\n   * When undefined, `precision` is used instead.\n   */\n  restVelocity?: number\n\n  /**\n   * The smallest distance from a value before that distance is essentially zero.\n   *\n   * This helps in deciding when a spring is \"at rest\". The spring must be within\n   * this distance from its final value, and its velocity must be lower than this\n   * value too (unless `restVelocity` is defined).\n   *\n   * @default 0.01\n   */\n  precision?: number\n\n  /**\n   * For `duration` animations only. Note: The `duration` is not affected\n   * by this property.\n   *\n   * Defaults to `0`, which means \"start from the beginning\".\n   *\n   * Setting to `1+` makes an immediate animation.\n   *\n   * Setting to `0.5` means \"start from the middle of the easing function\".\n   *\n   * Any number `>= 0` and `<= 1` makes sense here.\n   */\n  progress?: number\n\n  /**\n   * Animation length in number of milliseconds.\n   */\n  duration?: number\n\n  /**\n   * The animation curve. Only used when `duration` is defined.\n   *\n   * Defaults to quadratic ease-in-out.\n   */\n  easing!: EasingFunction\n\n  /**\n   * Avoid overshooting by ending abruptly at the goal value.\n   *\n   * @default false\n   */\n  clamp!: boolean\n\n  /**\n   * When above zero, the spring will bounce instead of overshooting when\n   * exceeding its goal value. Its velocity is multiplied by `-1 + bounce`\n   * whenever its current value equals or exceeds its goal. For example,\n   * setting `bounce` to `0.5` chops the velocity in half on each bounce,\n   * in addition to any friction.\n   */\n  bounce?: number\n\n  /**\n   * \"Decay animations\" decelerate without an explicit goal value.\n   * Useful for scrolling animations.\n   *\n   * Use `true` for the default exponential decay factor (`0.998`).\n   *\n   * When a `number` between `0` and `1` is given, a lower number makes the\n   * animation slow down faster. And setting to `1` would make an unending\n   * animation.\n   *\n   * @default false\n   */\n  decay?: boolean | number\n\n  /**\n   * While animating, round to the nearest multiple of this number.\n   * The `from` and `to` values are never rounded, as well as any value\n   * passed to the `set` method of an animated value.\n   */\n  round?: number\n\n  constructor() {\n    Object.assign(this, defaults)\n  }\n}\n\nexport function mergeConfig(\n  config: AnimationConfig,\n  newConfig: Partial<AnimationConfig>,\n  defaultConfig?: Partial<AnimationConfig>\n): typeof config\n\nexport function mergeConfig(\n  config: any,\n  newConfig: object,\n  defaultConfig?: object\n) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig }\n    sanitizeConfig(defaultConfig, newConfig)\n    newConfig = { ...defaultConfig, ...newConfig }\n  }\n\n  sanitizeConfig(config, newConfig)\n  Object.assign(config, newConfig)\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key]\n    }\n  }\n\n  let { frequency, damping } = config\n  const { mass } = config\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01\n    if (damping < 0) damping = 0\n    config.tension = Math.pow((2 * Math.PI) / frequency, 2) * mass\n    config.friction = (4 * Math.PI * damping * mass) / frequency\n  }\n\n  return config\n}\n\n// Prevent a config from accidentally overriding new props.\n// This depends on which \"config\" props take precedence when defined.\nfunction sanitizeConfig(\n  config: Partial<AnimationConfig>,\n  props: Partial<AnimationConfig>\n) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction)\n    if (\n      isTensionConfig ||\n      !is.und(props.frequency) ||\n      !is.und(props.damping) ||\n      !is.und(props.mass)\n    ) {\n      config.duration = undefined\n      config.decay = undefined\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined\n    }\n  }\n}\n", "// The `mass` prop defaults to 1\nexport const config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 },\n} as const\n", "import { AnimatedValue } from '@react-spring/animated'\nimport { FluidValue } from '@react-spring/shared'\nimport { AnimationConfig } from './AnimationConfig'\nimport { PickEventFns } from './types/internal'\nimport { SpringProps } from './types'\n\nconst emptyArray: readonly any[] = []\n\n/** An animation being executed by the frameloop */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nexport class Animation<T = any> {\n  changed = false\n  values: readonly AnimatedValue[] = emptyArray\n  toValues: readonly number[] | null = null\n  fromValues: readonly number[] = emptyArray\n\n  to!: T | FluidValue<T>\n  from!: T | FluidValue<T>\n  config = new AnimationConfig()\n  immediate = false\n}\n\nexport interface Animation<T> extends PickEventFns<SpringProps<T>> {}\n", "import { Timeout, is, raf, Globals as G } from '@react-spring/shared'\nimport { matchProp, callProp } from './helpers'\nimport { AsyncResult, MatchProp } from './types'\nimport { RunAsyncState, RunAsyncProps } from './runAsync'\nimport {\n  AnimationResolver,\n  AnimationTarget,\n  InferProps,\n  InferState,\n} from './types/internal'\n\n// The `scheduleProps` function only handles these defaults.\ntype DefaultProps<T> = { cancel?: MatchProp<T>; pause?: MatchProp<T> }\n\ninterface ScheduledProps<T extends AnimationTarget> {\n  key?: string\n  props: InferProps<T>\n  defaultProps?: DefaultProps<InferState<T>>\n  state: RunAsyncState<T>\n  actions: {\n    pause: () => void\n    resume: () => void\n    start: (props: RunAsyncProps<T>, resolve: AnimationResolver<T>) => void\n  }\n}\n\n/**\n * This function sets a timeout if both the `delay` prop exists and\n * the `cancel` prop is not `true`.\n *\n * The `actions.start` function must handle the `cancel` prop itself,\n * but the `pause` prop is taken care of.\n */\nexport function scheduleProps<T extends AnimationTarget>(\n  callId: number,\n  { key, props, defaultProps, state, actions }: ScheduledProps<T>\n): AsyncResult<T> {\n  return new Promise((resolve, reject) => {\n    let delay: number\n    let timeout: Timeout\n\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key)\n    if (cancel) {\n      onStart()\n    } else {\n      // The `pause` prop updates the paused flag.\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key)\n      }\n      // The default `pause` takes precedence when true,\n      // which allows `SpringContext` to work as expected.\n      let pause = defaultProps?.pause\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key)\n      }\n\n      delay = callProp(props.delay || 0, key)\n      if (pause) {\n        state.resumeQueue.add(onResume)\n        actions.pause()\n      } else {\n        actions.resume()\n        onResume()\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume)\n      state.timeouts.delete(timeout)\n      timeout.cancel()\n      // Cache the remaining delay.\n      delay = timeout.time - raf.now()\n    }\n\n    function onResume() {\n      if (delay > 0 && !G.skipAnimation) {\n        state.delayed = true\n        timeout = raf.setTimeout(onStart, delay)\n        state.pauseQueue.add(onPause)\n        state.timeouts.add(timeout)\n      } else {\n        onStart()\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false\n      }\n\n      state.pauseQueue.delete(onPause)\n      state.timeouts.delete(timeout)\n\n      // Maybe cancelled during its delay.\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true\n      }\n\n      try {\n        actions.start({ ...props, callId, cancel }, resolve)\n      } catch (err) {\n        reject(err)\n      }\n    }\n  })\n}\n", "import {\n  is,\n  raf,\n  flush,\n  eachProp,\n  Timeout,\n  Globals as G,\n} from '@react-spring/shared'\nimport { Falsy } from '@react-spring/types'\n\nimport { getDefaultProps } from './helpers'\nimport { AnimationTarget, InferState, InferProps } from './types/internal'\nimport { AnimationResult, AsyncResult, SpringChain, SpringToFn } from './types'\nimport { getCancelledResult, getFinishedResult } from './AnimationResult'\n\ntype AsyncTo<T> = SpringChain<T> | SpringToFn<T>\n\n/** @internal */\nexport type RunAsyncProps<T extends AnimationTarget = any> = InferProps<T> & {\n  callId: number\n  parentId?: number\n  cancel: boolean\n  to?: any\n}\n\n/** @internal */\nexport interface RunAsyncState<T extends AnimationTarget = any> {\n  paused: boolean\n  pauseQueue: Set<() => void>\n  resumeQueue: Set<() => void>\n  timeouts: Set<Timeout>\n  delayed?: boolean\n  asyncId?: number\n  asyncTo?: AsyncTo<InferState<T>>\n  promise?: AsyncResult<T>\n  cancelId?: number\n}\n\n/**\n * Start an async chain or an async script.\n *\n * Always call `runAsync` in the action callback of a `scheduleProps` call.\n *\n * The `T` parameter can be a set of animated values (as an object type)\n * or a primitive type for a single animated value.\n */\nexport function runAsync<T extends AnimationTarget>(\n  to: AsyncTo<InferState<T>>,\n  props: RunAsyncProps<T>,\n  state: RunAsyncState<T>,\n  target: T\n): AsyncResult<T> {\n  const { callId, parentId, onRest } = props\n  const { asyncTo: prevTo, promise: prevPromise } = state\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise!\n  }\n\n  return (state.promise = (async () => {\n    state.asyncId = callId\n    state.asyncTo = to\n\n    // The default props of any `animate` calls.\n    const defaultProps = getDefaultProps<InferProps<T>>(props, (value, key) =>\n      // The `onRest` prop is only called when the `runAsync` promise is resolved.\n      key === 'onRest' ? undefined : value\n    )\n\n    let preventBail!: () => void\n    let bail: (error: any) => void\n\n    // This promise is rejected when the animation is interrupted.\n    const bailPromise = new Promise<void>(\n      (resolve, reject) => ((preventBail = resolve), (bail = reject))\n    )\n\n    const bailIfEnded = (bailSignal: BailSignal) => {\n      const bailResult =\n        // The `cancel` prop or `stop` method was used.\n        (callId <= (state.cancelId || 0) && getCancelledResult(target)) ||\n        // The async `to` prop was replaced.\n        (callId !== state.asyncId && getFinishedResult(target, false))\n\n      if (bailResult) {\n        bailSignal.result = bailResult\n\n        // Reject the `bailPromise` to ensure the `runAsync` promise\n        // is not relying on the caller to rethrow the error for us.\n        bail(bailSignal)\n        throw bailSignal\n      }\n    }\n\n    const animate: any = (arg1: any, arg2?: any) => {\n      // Create the bail signal outside the returned promise,\n      // so the generated stack trace is relevant.\n      const bailSignal = new BailSignal()\n      const skipAnimationSignal = new SkipAnimationSignal()\n\n      return (async () => {\n        if (G.skipAnimation) {\n          /**\n           * We need to stop animations if `skipAnimation`\n           * is set in the Globals\n           *\n           */\n          stopAsync(state)\n\n          // create the rejection error that's handled gracefully\n          skipAnimationSignal.result = getFinishedResult(target, false)\n          bail(skipAnimationSignal)\n          throw skipAnimationSignal\n        }\n\n        bailIfEnded(bailSignal)\n\n        const props: any = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 }\n        props.parentId = callId\n\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value\n          }\n        })\n\n        const result = await target.start(props)\n        bailIfEnded(bailSignal)\n\n        if (state.paused) {\n          await new Promise<void>(resume => {\n            state.resumeQueue.add(resume)\n          })\n        }\n\n        return result\n      })()\n    }\n\n    let result!: AnimationResult<T>\n\n    if (G.skipAnimation) {\n      /**\n       * We need to stop animations if `skipAnimation`\n       * is set in the Globals\n       */\n      stopAsync(state)\n      return getFinishedResult(target, false)\n    }\n\n    try {\n      let animating!: Promise<void>\n\n      // Async sequence\n      if (is.arr(to)) {\n        animating = (async (queue: any[]) => {\n          for (const props of queue) {\n            await animate(props)\n          }\n        })(to)\n      }\n\n      // Async script\n      else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)))\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise])\n      result = getFinishedResult(target.get(), true, false)\n\n      // Bail handling\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result\n      } else {\n        throw err\n      }\n\n      // Reset the async state.\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId\n        state.asyncTo = parentId ? prevTo : undefined\n        state.promise = parentId ? prevPromise : undefined\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item)\n      })\n    }\n\n    return result\n  })())\n}\n\n/** Stop the current `runAsync` call with `finished: false` (or with `cancelled: true` when `cancelId` is defined) */\nexport function stopAsync(state: RunAsyncState, cancelId?: number | Falsy) {\n  flush(state.timeouts, t => t.cancel())\n  state.pauseQueue.clear()\n  state.resumeQueue.clear()\n  state.asyncId = state.asyncTo = state.promise = undefined\n  if (cancelId) state.cancelId = cancelId\n}\n\n/** This error is thrown to signal an interrupted async animation. */\nexport class BailSignal extends Error {\n  result!: AnimationResult\n  constructor() {\n    super(\n      'An async animation has been interrupted. You see this error because you ' +\n        'forgot to use `await` or `.catch(...)` on its returned promise.'\n    )\n  }\n}\n\nexport class SkipAnimationSignal extends Error {\n  result!: AnimationResult\n\n  constructor() {\n    super('SkipAnimationSignal')\n  }\n}\n", "import { AnimationResult } from './types'\nimport { Readable } from './types/internal'\n\n/** @internal */\nexport const getCombinedResult = <T extends Readable>(\n  target: T,\n  results: AnimationResult<T>[]\n): AnimationResult<T> =>\n  results.length == 1\n    ? results[0]\n    : results.some(result => result.cancelled)\n      ? getCancelledResult(target.get())\n      : results.every(result => result.noop)\n        ? getNoopResult(target.get())\n        : getFinishedResult(\n            target.get(),\n            results.every(result => result.finished)\n          )\n\n/** No-op results are for updates that never start an animation. */\nexport const getNoopResult = (value: any) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false,\n})\n\nexport const getFinishedResult = (\n  value: any,\n  finished: boolean,\n  cancelled = false\n) => ({\n  value,\n  finished,\n  cancelled,\n})\n\nexport const getCancelledResult = (value: any) => ({\n  value,\n  cancelled: true,\n  finished: false,\n})\n", "import {\n  deprecateInterpolate,\n  frameLoop,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n} from '@react-spring/shared'\nimport { InterpolatorArgs } from '@react-spring/types'\nimport { getAnimated } from '@react-spring/animated'\n\nimport { Interpolation } from './Interpolation'\n\nexport const isFrameValue = (value: any): value is FrameValue =>\n  value instanceof FrameValue\n\nlet nextId = 1\n\n/**\n * A kind of `FluidValue` that manages an `AnimatedValue` node.\n *\n * Its underlying value can be accessed and even observed.\n */\nexport abstract class FrameValue<T = any> extends FluidValue<\n  T,\n  FrameValue.Event<T>\n> {\n  readonly id = nextId++\n\n  abstract key?: string\n  abstract get idle(): boolean\n\n  protected _priority = 0\n\n  get priority() {\n    return this._priority\n  }\n  set priority(priority: number) {\n    if (this._priority != priority) {\n      this._priority = priority\n      this._onPriorityChange(priority)\n    }\n  }\n\n  /** Get the current value */\n  get(): T {\n    const node = getAnimated(this)\n    return node && node.getValue()\n  }\n\n  /** Create a spring that maps our value to another value */\n  to<Out>(...args: InterpolatorArgs<T, Out>) {\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  /** @deprecated Use the `to` method instead. */\n  interpolate<Out>(...args: InterpolatorArgs<T, Out>) {\n    deprecateInterpolate()\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  toJSON() {\n    return this.get()\n  }\n\n  protected observerAdded(count: number) {\n    if (count == 1) this._attach()\n  }\n\n  protected observerRemoved(count: number) {\n    if (count == 0) this._detach()\n  }\n\n  /** @internal */\n  abstract advance(dt: number): void\n\n  /** @internal */\n  abstract eventObserved(_event: FrameValue.Event): void\n\n  /** Called when the first child is added. */\n  protected _attach() {}\n\n  /** Called when the last child is removed. */\n  protected _detach() {}\n\n  /** Tell our children about our new value */\n  protected _onChange(value: T, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle,\n    })\n  }\n\n  /** Tell our children about our new priority */\n  protected _onPriorityChange(priority: number) {\n    if (!this.idle) {\n      frameLoop.sort(this)\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority,\n    })\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport declare namespace FrameValue {\n  /** A parent changed its value */\n  interface ChangeEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'change'\n    value: T\n    idle: boolean\n  }\n\n  /** A parent changed its priority */\n  interface PriorityEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'priority'\n    priority: number\n  }\n\n  /** A parent is done animating */\n  interface IdleEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'idle'\n  }\n\n  /** Events sent to children of `FrameValue` objects */\n  export type Event<T = any> = ChangeEvent<T> | PriorityEvent<T> | IdleEvent<T>\n}\n", "/** The property symbol of the current animation phase. */\nconst $P = Symbol.for('SpringPhase')\n\nconst HAS_ANIMATED = 1\nconst IS_ANIMATING = 2\nconst IS_PAUSED = 4\n\n/** Returns true if the `target` has ever animated. */\nexport const hasAnimated = (target: any) => (target[$P] & HAS_ANIMATED) > 0\n\n/** Returns true if the `target` is animating (even if paused). */\nexport const isAnimating = (target: any) => (target[$P] & IS_ANIMATING) > 0\n\n/** Returns true if the `target` is paused (even if idle). */\nexport const isPaused = (target: any) => (target[$P] & IS_PAUSED) > 0\n\n/** Set the active bit of the `target` phase. */\nexport const setActiveBit = (target: any, active: boolean) =>\n  active\n    ? (target[$P] |= IS_ANIMATING | HAS_ANIMATED)\n    : (target[$P] &= ~IS_ANIMATING)\n\nexport const setPausedBit = (target: any, paused: boolean) =>\n  paused ? (target[$P] |= IS_PAUSED) : (target[$P] &= ~IS_PAUSED)\n", "import { <PERSON><PERSON>r<PERSON>ore, UnknownProps, Lookup, Falsy } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  noop,\n  flush,\n  toArray,\n  eachProp,\n  flushCalls,\n  addFluidObserver,\n  FluidObserver,\n} from '@react-spring/shared'\n\nimport { getDefaultProp } from './helpers'\nimport { FrameValue } from './FrameValue'\nimport type { SpringRef } from './SpringRef'\nimport { SpringValue, createLoopUpdate, createUpdate } from './SpringValue'\nimport { getCancelledResult, getCombinedResult } from './AnimationResult'\nimport { runAsync, RunAsyncState, stopAsync } from './runAsync'\nimport { scheduleProps } from './scheduleProps'\nimport {\n  AnimationResult,\n  AsyncResult,\n  ControllerFlushFn,\n  ControllerUpdate,\n  OnChange,\n  OnRest,\n  OnStart,\n  SpringChain,\n  SpringToFn,\n  SpringValues,\n} from './types'\n\n/** Events batched by the `Controller` class */\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'] as const\n\nlet nextId = 1\n\n/** Queue of pending updates for a `Controller` instance. */\nexport interface ControllerQueue<State extends Lookup = Lookup>\n  extends Array<\n    ControllerUpdate<State, any> & {\n      /** The keys affected by this update. When null, all keys are affected. */\n      keys: string[] | null\n    }\n  > {}\n\nexport class Controller<State extends Lookup = Lookup> {\n  readonly id = nextId++\n\n  /** The animated values */\n  springs: SpringValues<State> = {} as any\n\n  /** The queue of props passed to the `update` method. */\n  queue: ControllerQueue<State> = []\n\n  /**\n   * The injected ref. When defined, render-based updates are pushed\n   * onto the `queue` instead of being auto-started.\n   */\n  ref?: SpringRef<State>\n\n  /** Custom handler for flushing update queues */\n  protected _flush?: ControllerFlushFn<this>\n\n  /** These props are used by all future spring values */\n  protected _initialProps?: Lookup\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastAsyncId = 0\n\n  /** The values currently being animated */\n  protected _active = new Set<FrameValue>()\n\n  /** The values that changed recently */\n  protected _changed = new Set<FrameValue>()\n\n  /** Equals false when `onStart` listeners can be called */\n  protected _started = false\n\n  private _item?: any\n\n  /** State used by the `runAsync` function */\n  protected _state: RunAsyncState<this> = {\n    paused: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The event queues that are flushed once per frame maximum */\n  protected _events = {\n    onStart: new Map<\n      OnStart<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onChange: new Map<\n      OnChange<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onRest: new Map<\n      OnRest<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n  }\n\n  constructor(\n    props?: ControllerUpdate<State> | null,\n    flush?: ControllerFlushFn<any>\n  ) {\n    this._onFrame = this._onFrame.bind(this)\n    if (flush) {\n      this._flush = flush\n    }\n    if (props) {\n      this.start({ default: true, ...props })\n    }\n  }\n\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return (\n      !this._state.asyncTo &&\n      Object.values(this.springs as Lookup<SpringValue>).every(spring => {\n        return spring.idle && !spring.isDelayed && !spring.isPaused\n      })\n    )\n  }\n\n  get item() {\n    return this._item\n  }\n\n  set item(item) {\n    this._item = item\n  }\n\n  /** Get the current values of our springs */\n  get(): State & UnknownProps {\n    const values: any = {}\n    this.each((spring, key) => (values[key] = spring.get()))\n    return values\n  }\n\n  /** Set the current values without animating. */\n  set(values: Partial<State>) {\n    for (const key in values) {\n      const value = values[key]\n      if (!is.und(value)) {\n        this.springs[key].set(value)\n      }\n    }\n  }\n\n  /** Push an update onto the queue of each value. */\n  update(props: ControllerUpdate<State> | Falsy) {\n    if (props) {\n      this.queue.push(createUpdate(props))\n    }\n    return this\n  }\n\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props?: OneOrMore<ControllerUpdate<State>> | null): AsyncResult<this> {\n    let { queue } = this as any\n    if (props) {\n      queue = toArray<any>(props).map(createUpdate)\n    } else {\n      this.queue = []\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue)\n    }\n\n    prepareKeys(this, queue)\n    return flushUpdateQueue(this, queue)\n  }\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n  /** @internal */\n  stop(arg?: boolean | OneOrMore<string>, keys?: OneOrMore<string>) {\n    if (arg !== !!arg) {\n      keys = arg as OneOrMore<string>\n    }\n    if (keys) {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].stop(!!arg))\n    } else {\n      stopAsync(this._state, this._lastAsyncId)\n      this.each(spring => spring.stop(!!arg))\n    }\n    return this\n  }\n\n  /** Freeze the active animation in time */\n  pause(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: true })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].pause())\n    }\n    return this\n  }\n\n  /** Resume the animation if paused. */\n  resume(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: false })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].resume())\n    }\n    return this\n  }\n\n  /** Call a function once per spring value */\n  each(iterator: (spring: SpringValue, key: string) => void) {\n    eachProp(this.springs, iterator as any)\n  }\n\n  /** @internal Called at the end of every animation frame */\n  protected _onFrame() {\n    const { onStart, onChange, onRest } = this._events\n\n    const active = this._active.size > 0\n    const changed = this._changed.size > 0\n\n    if ((active && !this._started) || (changed && !this._started)) {\n      this._started = true\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get()\n        onStart(result, this, this._item)\n      })\n    }\n\n    const idle = !active && this._started\n    const values = changed || (idle && onRest.size) ? this.get() : null\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values\n        onChange(result, this, this._item)\n      })\n    }\n\n    // The \"onRest\" queue is only flushed when all springs are idle.\n    if (idle) {\n      this._started = false\n      flush(onRest, ([onRest, result]) => {\n        result.value = values\n        onRest(result, this, this._item)\n      })\n    }\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent)\n      if (!event.idle) {\n        this._active.add(event.parent)\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // The `onFrame` handler runs when a parent is changed or idle.\n    else return\n    raf.onFrame(this._onFrame)\n  }\n}\n\n/**\n * Warning: Props might be mutated.\n */\nexport function flushUpdateQueue(\n  ctrl: Controller<any>,\n  queue: ControllerQueue\n) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(\n    results => getCombinedResult(ctrl, results)\n  )\n}\n\n/**\n * Warning: Props might be mutated.\n *\n * Process a single set of props using the given controller.\n *\n * The returned promise resolves to `true` once the update is\n * applied and any animations it starts are finished without being\n * stopped or cancelled.\n */\nexport async function flushUpdate(\n  ctrl: Controller<any>,\n  props: ControllerQueue[number],\n  isLoop?: boolean\n): AsyncResult {\n  const { keys, to, from, loop, onRest, onResolve } = props\n  const defaults = is.obj(props.default) && props.default\n\n  // Looping must be handled in this function, or else the values\n  // would end up looping out-of-sync in many common cases.\n  if (loop) {\n    props.loop = false\n  }\n\n  // Treat false like null, which gets ignored.\n  if (to === false) props.to = null\n  if (from === false) props.from = null\n\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined\n  if (asyncTo) {\n    props.to = undefined\n    props.onRest = undefined\n    if (defaults) {\n      defaults.onRest = undefined\n    }\n  }\n  // For certain events, use batching to prevent multiple calls per frame.\n  // However, batching is avoided when the `to` prop is async, because any\n  // event props are used as default props instead.\n  else {\n    each(BATCHED_EVENTS, key => {\n      const handler: any = props[key]\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key]\n        props[key] = (({ finished, cancelled }: AnimationResult) => {\n          const result = queue.get(handler)\n          if (result) {\n            if (!finished) result.finished = false\n            if (cancelled) result.cancelled = true\n          } else {\n            // The \"value\" is set before the \"handler\" is called.\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false,\n            })\n          }\n        }) as any\n\n        // Avoid using a batched `handler` as a default prop.\n        if (defaults) {\n          defaults[key] = props[key] as any\n        }\n      }\n    })\n  }\n\n  const state = ctrl['_state']\n\n  // Pause/resume the `asyncTo` when `props.pause` is true/false.\n  if (props.pause === !state.paused) {\n    state.paused = props.pause\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue)\n  }\n  // When a controller is paused, its values are also paused.\n  else if (state.paused) {\n    props.pause = true\n  }\n\n  const promises: AsyncResult[] = (keys || Object.keys(ctrl.springs)).map(key =>\n    ctrl.springs[key]!.start(props as any)\n  )\n\n  const cancel =\n    props.cancel === true || getDefaultProp(props, 'cancel') === true\n\n  if (asyncTo || (cancel && state.asyncId)) {\n    promises.push(\n      scheduleProps(++ctrl['_lastAsyncId'], {\n        props,\n        state,\n        actions: {\n          pause: noop,\n          resume: noop,\n          start(props, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl['_lastAsyncId'])\n              resolve(getCancelledResult(ctrl))\n            } else {\n              props.onRest = onRest\n              resolve(\n                runAsync(\n                  asyncTo as SpringChain | SpringToFn,\n                  props,\n                  state,\n                  ctrl\n                )\n              )\n            }\n          },\n        },\n      })\n    )\n  }\n\n  // Pause after updating each spring, so they can be resumed separately\n  // and so their default `pause` and `cancel` props are updated.\n  if (state.paused) {\n    // Ensure `this` must be resumed before the returned promise\n    // is resolved and before starting the next `loop` repetition.\n    await new Promise<void>(resume => {\n      state.resumeQueue.add(resume)\n    })\n  }\n\n  const result = getCombinedResult<any>(ctrl, await Promise.all(promises))\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to)\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps])\n      return flushUpdate(ctrl, nextProps, true)\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item))\n  }\n  return result\n}\n\n/**\n * From an array of updates, get the map of `SpringValue` objects\n * by their keys. Springs are created when any update wants to\n * animate a new key.\n *\n * Springs created by `getSprings` are neither cached nor observed\n * until they're given to `setSprings`.\n */\nexport function getSprings<State extends Lookup>(\n  ctrl: Controller<Lookup<any>>,\n  props?: OneOrMore<ControllerUpdate<State>>\n) {\n  const springs = { ...ctrl.springs }\n  if (props) {\n    each(toArray(props), (props: any) => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props)\n      }\n      if (!is.obj(props.to)) {\n        // Avoid passing array/function to each spring.\n        props = { ...props, to: undefined }\n      }\n      prepareSprings(springs as any, props, key => {\n        return createSpring(key)\n      })\n    })\n  }\n  setSprings(ctrl, springs)\n  return springs\n}\n\n/**\n * Tell a controller to manage the given `SpringValue` objects\n * whose key is not already in use.\n */\nexport function setSprings(\n  ctrl: Controller<Lookup<any>>,\n  springs: SpringValues<UnknownProps>\n) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring\n      addFluidObserver(spring, ctrl)\n    }\n  })\n}\n\nfunction createSpring(key: string, observer?: FluidObserver<FrameValue.Event>) {\n  const spring = new SpringValue()\n  spring.key = key\n  if (observer) {\n    addFluidObserver(spring, observer)\n  }\n  return spring\n}\n\n/**\n * Ensure spring objects exist for each defined key.\n *\n * Using the `props`, the `Animated` node of each `SpringValue` may\n * be created or updated.\n */\nfunction prepareSprings(\n  springs: SpringValues,\n  props: ControllerQueue[number],\n  create: (key: string) => SpringValue\n) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key))\n      spring['_prepareNode'](props)\n    })\n  }\n}\n\n/**\n * Ensure spring objects exist for each defined key, and attach the\n * `ctrl` to them for observation.\n *\n * The queue is expected to contain `createUpdate` results.\n */\nfunction prepareKeys(ctrl: Controller<any>, queue: ControllerQueue[number][]) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl)\n    })\n  })\n}\n", "import * as React from 'react'\nimport { useContext, PropsWithChildren } from 'react'\nimport { useMemoOne } from '@react-spring/shared'\n\n/**\n * This context affects all new and existing `SpringValue` objects\n * created with the hook API or the renderprops API.\n */\nexport interface SpringContext {\n  /** Pause all new and existing animations. */\n  pause?: boolean\n  /** Force all new and existing animations to be immediate. */\n  immediate?: boolean\n}\n\nexport const SpringContext = ({\n  children,\n  ...props\n}: PropsWithChildren<SpringContext>) => {\n  const inherited = useContext(ctx)\n\n  // Inherited values are dominant when truthy.\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate\n\n  // Memoize the context to avoid unwanted renders.\n  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate])\n\n  const { Provider } = ctx\n  return <Provider value={props}>{children}</Provider>\n}\n\nconst ctx = makeContext(SpringContext, {} as SpringContext)\n\n// Allow `useContext(SpringContext)` in TypeScript.\nSpringContext.Provider = ctx.Provider\nSpringContext.Consumer = ctx.Consumer\n\n/** Make the `target` compatible with `useContext` */\nfunction makeContext<T>(target: any, init: T): React.Context<T> {\n  Object.assign(target, React.createContext(init))\n  target.Provider._context = target\n  target.Consumer._context = target\n  return target\n}\n", "import { each, is, deprecateDirect<PERSON>all } from '@react-spring/shared'\nimport { Lookup, Falsy, OneOrMore } from '@react-spring/types'\nimport { AsyncResult, ControllerUpdate } from './types'\nimport { Controller } from './Controller'\n\nexport interface ControllerUpdateFn<State extends Lookup = Lookup> {\n  (i: number, ctrl: Controller<State>): ControllerUpdate<State> | Falsy\n}\n\nexport interface SpringRef<State extends Lookup = Lookup> {\n  (\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n  current: Controller<State>[]\n\n  /** Add a controller to this ref */\n  add(ctrl: Controller<State>): void\n\n  /** Remove a controller from this ref */\n  delete(ctrl: Controller<State>): void\n\n  /** Pause all animations. */\n  pause(): this\n  /** Pause animations for the given keys. */\n  pause(keys: OneOrMore<string>): this\n  /** Pause some or all animations. */\n  pause(keys?: OneOrMore<string>): this\n\n  /** Resume all animations. */\n  resume(): this\n  /** Resume animations for the given keys. */\n  resume(keys: OneOrMore<string>): this\n  /** Resume some or all animations. */\n  resume(keys?: OneOrMore<string>): this\n\n  /** Update the state of each controller without animating. */\n  set(values: Partial<State>): void\n  /** Update the state of each controller without animating based on their passed state. */\n  set(values: (index: number, ctrl: Controller<State>) => Partial<State>): void\n\n  /** Start the queued animations of each controller. */\n  start(): AsyncResult<Controller<State>>[]\n  /** Update every controller with the same props. */\n  start(props: ControllerUpdate<State>): AsyncResult<Controller<State>>[]\n  /** Update controllers based on their state. */\n  start(props: ControllerUpdateFn<State>): AsyncResult<Controller<State>>[]\n  /** Start animating each controller. */\n  start(\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n\n  /** Add the same props to each controller's update queue. */\n  update(props: ControllerUpdate<State>): this\n  /** Generate separate props for each controller's update queue. */\n  update(props: ControllerUpdateFn<State>): this\n  /** Add props to each controller's update queue. */\n  update(props: ControllerUpdate<State> | ControllerUpdateFn<State>): this\n\n  _getProps(\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ): ControllerUpdate<State> | Falsy\n}\n\nexport const SpringRef = <\n  State extends Lookup = Lookup,\n>(): SpringRef<State> => {\n  const current: Controller<State>[] = []\n\n  const SpringRef: SpringRef<State> = function (props) {\n    deprecateDirectCall()\n\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = _getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  SpringRef.current = current\n\n  /** Add a controller to this ref */\n  SpringRef.add = function (ctrl: Controller<State>) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl)\n    }\n  }\n\n  /** Remove a controller from this ref */\n  SpringRef.delete = function (ctrl: Controller<State>) {\n    const i = current.indexOf(ctrl)\n    if (~i) current.splice(i, 1)\n  }\n\n  /** Pause all animations. */\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments))\n    return this\n  }\n\n  /** Resume all animations. */\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments))\n    return this\n  }\n\n  /** Update the state of each controller without animating. */\n  SpringRef.set = function (\n    values:\n      | Partial<State>\n      | ((i: number, ctrl: Controller<State>) => Partial<State>)\n  ) {\n    each(current, (ctrl, i) => {\n      const update = is.fun(values) ? values(i, ctrl) : values\n      if (update) {\n        ctrl.set(update)\n      }\n    })\n  }\n\n  SpringRef.start = function (props?: object | ControllerUpdateFn<State>) {\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = this._getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  /** Stop all animations. */\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments))\n    return this\n  }\n\n  SpringRef.update = function (props: object | ControllerUpdateFn<State>) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)))\n    return this\n  }\n\n  /** Overridden by `useTrail` to manipulate props */\n  const _getProps = function (\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ) {\n    return is.fun(arg) ? arg(index, ctrl) : arg\n  }\n\n  SpringRef._getProps = _getProps\n\n  return SpringRef\n}\n", "import { useState } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nconst initSpringRef = () => SpringRef<any>()\n\nexport const useSpringRef = <State extends Lookup = Lookup>() =>\n  useState(initSpringRef)[0] as SpringRefType<State>\n", "import { useConstant, useOnce } from '@react-spring/shared'\n\nimport { SpringValue } from '../SpringValue'\nimport { SpringUpdate } from '../types'\n\n/**\n * Creates a constant single `SpringValue` that can be interacted\n * with imperatively. This is an advanced API and does not react\n * to updates from the parent component e.g. passing a new initial value\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const opacity = useSpringValue(1)\n *\n *   return <animated.div style={{ opacity }} />\n * }\n * ```\n *\n * @param initial – The initial value of the `SpringValue`.\n * @param props – Typically the same props as `useSpring` e.g. `config`, `loop` etc.\n *\n * @public\n */\nexport const useSpringValue = <T>(\n  initial: Exclude<T, object>,\n  props?: SpringUpdate<T>\n) => {\n  const springValue = useConstant(() => new SpringValue(initial, props))\n\n  useOnce(() => () => {\n    springValue.stop()\n  })\n\n  return springValue\n}\n", "import { each, is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\n\nimport { SpringRef } from '../SpringRef'\nimport { Controller } from '../Controller'\n\nimport { UseSpringProps } from './useSpring'\nimport { useSprings } from './useSprings'\nimport { replaceRef } from '../helpers'\n\nexport type UseTrailProps<Props extends object = any> = UseSpringProps<Props>\n\nexport function useTrail<Props extends object>(\n  length: number,\n  props: (\n    i: number,\n    ctrl: Controller\n  ) => UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0})\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>)\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0}, [])\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n * @param deps – The optional array of dependencies to pass to the internal\n * `useSprings` hook, therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\nexport function useTrail(\n  length: number,\n  propsArg: unknown,\n  deps?: readonly any[]\n) {\n  const propsFn = is.fun(propsArg) && propsArg\n  if (propsFn && !deps) deps = []\n\n  // The trail is reversed when every render-based update is reversed.\n  let reverse = true\n  let passedRef: SpringRef | undefined = undefined\n\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg\n      passedRef = props.ref\n      reverse = reverse && props.reverse\n\n      return props\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    /**\n     * Run through the ref passed by the `useSprings` hook.\n     */\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)]\n\n      /**\n       * If there's a passed ref then we replace the ctrl ref with it\n       */\n      replaceRef(ctrl, passedRef)\n\n      /**\n       * And if there's a ctrl ref then we update instead of start\n       * which means nothing is fired until the start method\n       * of said passedRef is called.\n       */\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs })\n        }\n\n        return\n      }\n\n      if (parent) {\n        ctrl.start({ to: parent.springs })\n      } else {\n        ctrl.start()\n      }\n    })\n  }, deps)\n\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1]\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)]\n        if (parent) props.to = parent.springs\n        return props\n      }\n    }\n    return result\n  }\n\n  return result[0]\n}\n", "import * as React from 'react'\nimport { useContext, useRef, useMemo } from 'react'\nimport { Lookup, OneOrMore, UnknownProps } from '@react-spring/types'\nimport {\n  is,\n  toArray,\n  useForceUpdate,\n  useOnce,\n  usePrev,\n  each,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  Change,\n  ControllerUpdate,\n  ItemKeys,\n  PickAnimated,\n  TransitionFn,\n  TransitionState,\n  TransitionTo,\n  UseTransitionProps,\n} from '../types'\nimport { Valid } from '../types/common'\nimport {\n  callProp,\n  detachRefs,\n  getDefaultProps,\n  hasProps,\n  inferTo,\n  replaceRef,\n} from '../helpers'\nimport { Controller, getSprings } from '../Controller'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\nimport { TransitionPhase } from '../TransitionPhase'\n\ndeclare function setTimeout(handler: Function, timeout?: number): number\ndeclare function clearTimeout(timeoutId: number): void\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props: () =>\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps?: any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, PickAnimated<Props>>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>)\n): TransitionFn<Item, PickAnimated<Props>>\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps: any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, State>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition(\n  data: unknown,\n  props: UseTransitionProps | (() => any),\n  deps?: any[]\n): any {\n  const propsFn = is.fun(props) && props\n\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig,\n  }: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n  // Return a `SpringRef` if a deps array was passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  // Every item has its own transition.\n  const items = toArray(data)\n  const transitions: TransitionState[] = []\n\n  // The \"onRest\" callbacks need a ref to the latest transitions.\n  const usedTransitions = useRef<TransitionState[] | null>(null)\n  const prevTransitions = reset ? null : usedTransitions.current\n\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions\n  })\n\n  useOnce(() => {\n    /**\n     * If transitions exist on mount of the component\n     * then reattach their refs on-mount, this was required\n     * for react18 strict mode to work properly.\n     *\n     * See https://github.com/pmndrs/react-spring/issues/1890\n     */\n\n    each(transitions, t => {\n      ref?.add(t.ctrl)\n      t.ctrl.ref = ref\n    })\n\n    // Destroy all transitions on dismount.\n    return () => {\n      each(usedTransitions.current!, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId!)\n        }\n        detachRefs(t.ctrl, ref)\n        t.ctrl.stop(true)\n      })\n    }\n  })\n\n  // Keys help with reusing transitions between renders.\n  // The `key` prop can be undefined (which means the items themselves are used\n  // as keys), or a function (which maps each item to its key), or an array of\n  // keys (which are assigned to each item by index).\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions)\n\n  // Expired transitions that need clean up.\n  const expired = (reset && usedTransitions.current) || []\n  useIsomorphicLayoutEffect(() =>\n    each(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref)\n      callProp(onDestroyed, item, key)\n    })\n  )\n\n  // Map old indices to new indices.\n  const reused: number[] = []\n  if (prevTransitions)\n    each(prevTransitions, (t, i) => {\n      // Expired transitions are not rendered.\n      if (t.expired) {\n        clearTimeout(t.expirationId!)\n        expired.push(t)\n      } else {\n        i = reused[i] = keys.indexOf(t.key)\n        if (~i) transitions[i] = t\n      }\n    })\n\n  // Mount new items with fresh transitions.\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller(),\n      }\n\n      transitions[i].ctrl.item = item\n    }\n  })\n\n  // Update the item of any transition whose key still exists,\n  // and ensure leaving transitions are rendered until they finish.\n  if (reused.length) {\n    let i = -1\n    const { leave }: UseTransitionProps<any> = propsFn ? propsFn() : props\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions![prevIndex]\n      if (~keyIndex) {\n        i = transitions.indexOf(t)\n        transitions[i] = { ...t, item: items[keyIndex] }\n      } else if (leave) {\n        transitions.splice(++i, 0, t)\n      }\n    })\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item))\n  }\n\n  // Track cumulative delay for the \"trail\" prop.\n  let delay = -trail\n\n  // Expired transitions use this to dismount.\n  const forceUpdate = useForceUpdate()\n\n  // These props are inherited by every phase change.\n  const defaultProps = getDefaultProps<UseTransitionProps>(props)\n  // Generate changes to apply in useEffect.\n  const changes = new Map<TransitionState, Change>()\n  const exitingTransitions = useRef(new Map<TransitionState, Change>())\n\n  const forceChange = useRef(false)\n  each(transitions, (t, i) => {\n    const key = t.key\n    const prevPhase = t.phase\n\n    const p: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n    let to: TransitionTo<any>\n    let phase: TransitionPhase\n\n    const propsDelay = callProp(p.delay || 0, key)\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter\n      phase = TransitionPhase.ENTER\n    } else {\n      const isLeave = keys.indexOf(key) < 0\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave\n          phase = TransitionPhase.LEAVE\n        } else if ((to = p.update)) {\n          phase = TransitionPhase.UPDATE\n        } else return\n      } else if (!isLeave) {\n        to = p.enter\n        phase = TransitionPhase.ENTER\n      } else return\n    }\n\n    // When \"to\" is a function, it can return (1) an array of \"useSpring\" props,\n    // (2) an async function, or (3) an object with any \"useSpring\" props.\n    to = callProp(to, t.item, i)\n    to = is.obj(to) ? inferTo(to) : { to }\n\n    /**\n     * This would allow us to give different delays for phases.\n     * If we were to do this, we'd have to suffle the prop\n     * spreading below to set delay last.\n     * But if we were going to do that, we should consider letting\n     * the prop trail also be part of a phase.\n     */\n    // if (to.delay) {\n    //   phaseDelay = callProp(to.delay, key)\n    // }\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config\n      to.config = callProp(config, t.item, i, phase)\n    }\n\n    delay += trail\n\n    // The payload is used to update the spring props once the current render is committed.\n    const payload: ControllerUpdate<UnknownProps> = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...(to as any),\n    }\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const p = propsFn ? propsFn() : props\n      // The `initial` prop is used on the first render of our parent component,\n      // as well as when `reset: true` is passed. It overrides the `from` prop\n      // when defined, and it makes `enter` instant when null.\n      const from = is.und(p.initial) || prevTransitions ? p.from : p.initial\n\n      payload.from = callProp(from, t.item, i)\n    }\n\n    const { onResolve } = payload\n    payload.onResolve = result => {\n      callProp(onResolve, result)\n\n      const transitions = usedTransitions.current!\n      const t = transitions.find(t => t.key === key)\n      if (!t) return\n\n      // Reset the phase of a cancelled enter/leave transition, so it can\n      // retry the animation on the next render.\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        /**\n         * @legacy Reset the phase of a cancelled enter/leave transition, so it can\n         * retry the animation on the next render.\n         *\n         * Note: leaving this here made the transitioned item respawn.\n         */\n        // t.phase = prevPhase\n        return\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle)\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item)\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry\n            t.expired = true\n\n            // Force update once the expiration delay ends.\n            if (!idle && expiryMs > 0) {\n              // The maximum timeout is 2^31-1\n              if (expiryMs <= 0x7fffffff)\n                t.expirationId = setTimeout(forceUpdate, expiryMs)\n              return\n            }\n          }\n        }\n        // Force update once idle and expired items exist.\n        if (idle && transitions.some(t => t.expired)) {\n          /**\n           * Remove the exited transition from the list\n           * this may not exist but we'll try anyway.\n           */\n          exitingTransitions.current.delete(t)\n\n          if (exitBeforeEnter) {\n            /**\n             * If we have exitBeforeEnter == true\n             * we need to force the animation to start\n             */\n            forceChange.current = true\n          }\n\n          forceUpdate()\n        }\n      }\n    }\n\n    const springs = getSprings(t.ctrl, payload)\n\n    /**\n     * Make a separate map for the exiting changes and \"regular\" changes\n     */\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload })\n    } else {\n      changes.set(t, { phase, springs, payload })\n    }\n  })\n\n  // The prop overrides from an ancestor.\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  // Merge the context into each transition.\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({ default: context })\n      })\n    }\n  }, [context])\n\n  each(changes, (_, t) => {\n    /**\n     * If we have children to exit because exitBeforeEnter is\n     * set to true, we remove the transitions so they go to back\n     * to their initial state.\n     */\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key)\n      transitions.splice(ind, 1)\n    }\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      /*\n       * if exitingTransitions.current has a size it means we're exiting before enter\n       * so we want to map through those and fire those first.\n       */\n      each(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t\n\n          t.phase = phase\n\n          // Attach the controller to our local ref.\n          ref?.add(ctrl)\n\n          // Merge the context into new items.\n          if (hasContext && phase == TransitionPhase.ENTER) {\n            ctrl.start({ default: context })\n          }\n\n          if (payload) {\n            // Update the injected ref if needed.\n            replaceRef(ctrl, payload.ref)\n\n            /**\n             * When an injected ref exists, the update is postponed\n             * until the ref has its `start` method called.\n             * Unless we have exitBeforeEnter in which case will skip\n             * to enter the new animation straight away as if they \"overlapped\"\n             */\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload)\n            } else {\n              ctrl.start(payload)\n\n              if (forceChange.current) {\n                forceChange.current = false\n              }\n            }\n          }\n        }\n      )\n    },\n    reset ? void 0 : deps\n  )\n\n  const renderTransitions: TransitionFn = render => (\n    <>\n      {transitions.map((t, i) => {\n        const { springs } = changes.get(t) || t.ctrl\n        const elem: any = render({ ...springs }, t.item, t, i)\n        return elem && elem.type ? (\n          <elem.type\n            {...elem.props}\n            key={is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id}\n            ref={elem.ref}\n          />\n        ) : (\n          elem\n        )\n      })}\n    </>\n  )\n\n  return ref ? [renderTransitions, ref] : renderTransitions\n}\n\n/** Local state for auto-generated item keys */\nlet nextKey = 1\n\nfunction getKeys(\n  items: readonly any[],\n  { key, keys = key }: { key?: ItemKeys; keys?: ItemKeys },\n  prevTransitions: TransitionState[] | null\n): readonly any[] {\n  if (keys === null) {\n    const reused = new Set()\n    return items.map(item => {\n      const t =\n        prevTransitions &&\n        prevTransitions.find(\n          t =>\n            t.item === item &&\n            t.phase !== TransitionPhase.LEAVE &&\n            !reused.has(t)\n        )\n      if (t) {\n        reused.add(t)\n        return t.key\n      }\n      return nextKey++\n    })\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys)\n}\n", "import { MutableRefObject } from 'react'\nimport { each, onScroll, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseScrollOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement>\n}\n\n/**\n * A small utility abstraction around our signature useSpring hook. It's a great way to create\n * a scroll-linked animation. With either the raw value of distance or a 0-1 progress value.\n * You can either use the scroll values of the whole document, or just a specific element.\n *\n * \n ```jsx\n    import { useScroll, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { scrollYProgress } = useScroll()\n\n      return (\n        <animated.div style={{ opacity: scrollYProgress }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseScrollOptions} useScrollOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} useScrollOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{scrollX: number; scrollY: number; scrollXProgress: number; scrollYProgress: number}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useScroll = ({\n  container,\n  ...springOptions\n}: UseScrollOptions = {}): SpringValues<{\n  scrollX: number\n  scrollY: number\n  scrollXProgress: number\n  scrollYProgress: number\n}> => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(scrollValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return scrollValues\n}\n", "import { MutableRefObject } from 'react'\nimport { onResize, each, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseResizeOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement | null | undefined>\n}\n\n/**\n * A small abstraction around the `useSpring` hook. It returns a `SpringValues` \n * object with the `width` and `height` of the element it's attached to & doesn't \n * necessarily have to be attached to the window, by passing a `container` you \n * can observe that element's size instead.\n * \n ```jsx\n    import { useResize, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { width } = useResize()\n\n      return (\n        <animated.div style={{ width }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseResizeOptions} UseResizeOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} UseResizeOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{width: number; height: number;}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useResize = ({\n  container,\n  ...springOptions\n}: UseResizeOptions): SpringValues<{\n  width: number\n  height: number\n}> => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate:\n            sizeValues.width.get() === 0 || sizeValues.height.get() === 0,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(sizeValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return sizeValues\n}\n", "import { RefObject, useRef, useState } from 'react'\nimport { is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { PickAnimated, SpringValues } from '../types'\nimport { useSpring, UseSpringProps } from './useSpring'\nimport { Valid } from '../types/common'\n\nexport interface IntersectionArgs\n  extends Omit<IntersectionObserverInit, 'root' | 'threshold'> {\n  root?: React.MutableRefObject<HTMLElement>\n  once?: boolean\n  amount?: 'any' | 'all' | number | number[]\n}\n\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1,\n}\n\nexport function useInView(args?: IntersectionArgs): [RefObject<any>, boolean]\nexport function useInView<Props extends object>(\n  /**\n   * TODO: make this narrower to only accept reserved props.\n   */\n  props: () => Props & Valid<Props, UseSpringProps<Props>>,\n  args?: IntersectionArgs\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [RefObject<any>, SpringValues<State>]\n    : never\n  : never\nexport function useInView<TElement extends HTMLElement>(\n  props?: (() => UseSpringProps<any>) | IntersectionArgs,\n  args?: IntersectionArgs\n) {\n  const [isInView, setIsInView] = useState(false)\n  const ref = useRef<TElement>()\n\n  const propsFn = is.fun(props) && props\n\n  const springsProps = propsFn ? propsFn() : {}\n  const { to = {}, from = {}, ...restSpringProps } = springsProps\n\n  const intersectionArguments = propsFn ? args : props\n\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), [])\n\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current\n    const {\n      root,\n      once,\n      amount = 'any',\n      ...restArgs\n    } = intersectionArguments ?? {}\n\n    if (\n      !element ||\n      (once && isInView) ||\n      typeof IntersectionObserver === 'undefined'\n    )\n      return\n\n    const activeIntersections = new WeakMap<Element, VoidFunction>()\n\n    const onEnter = () => {\n      if (to) {\n        // @ts-expect-error – TODO: fix this type error\n        api.start(to)\n      }\n\n      setIsInView(true)\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from)\n        }\n        setIsInView(false)\n      }\n\n      return once ? undefined : cleanup\n    }\n\n    const handleIntersection: IntersectionObserverCallback = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target)\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter()\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave)\n          } else {\n            observer.unobserve(entry.target)\n          }\n        } else if (onLeave) {\n          onLeave()\n          activeIntersections.delete(entry.target)\n        }\n      })\n    }\n\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: (root && root.current) || undefined,\n      threshold:\n        typeof amount === 'number' || Array.isArray(amount)\n          ? amount\n          : defaultThresholdOptions[amount],\n      ...restArgs,\n    })\n\n    observer.observe(element)\n\n    return () => observer.unobserve(element)\n  }, [intersectionArguments])\n\n  if (propsFn) {\n    return [ref, springs]\n  }\n\n  return [ref, isInView]\n}\n", "import { NoInfer, UnknownProps } from '@react-spring/types'\nimport { useSpring, UseSpringProps } from '../hooks/useSpring'\nimport { SpringValues, SpringToFn, SpringChain } from '../types'\n\nexport type SpringComponentProps<State extends object = UnknownProps> =\n  unknown &\n    UseSpringProps<State> & {\n      children: (values: SpringValues<State>) => JSX.Element | null\n    }\n\n// Infer state from \"from\" object prop.\nexport function Spring<State extends object>(\n  props: {\n    from: State\n    to?: SpringChain<NoInfer<State>> | SpringToFn<NoInfer<State>>\n  } & Omit<SpringComponentProps<NoInfer<State>>, 'from' | 'to'>\n): JSX.Element | null\n\n// Infer state from \"to\" object prop.\nexport function Spring<State extends object>(\n  props: { to: State } & Omit<SpringComponentProps<NoInfer<State>>, 'to'>\n): JSX.Element | null\n\nexport function Spring({ children, ...props }: any) {\n  return children(useSpring(props))\n}\n", "import { ReactNode } from 'react'\nimport { NoInfer, Falsy } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\nimport { UseSpringProps } from '../hooks/useSpring'\nimport { useTrail } from '../hooks/useTrail'\n\nexport type TrailComponentProps<Item, Props extends object = any> = unknown &\n  UseSpringProps<Props> & {\n    items: readonly Item[]\n    children: (\n      item: NoInfer<Item>,\n      index: number\n    ) => ((values: SpringValues<PickAnimated<Props>>) => ReactNode) | Falsy\n  }\n\nexport function Trail<Item, Props extends TrailComponentProps<Item>>({\n  items,\n  children,\n  ...props\n}: Props & Valid<Props, TrailComponentProps<Item, Props>>) {\n  const trails: any[] = useTrail(items.length, props)\n  return items.map((item, index) => {\n    const result = children(item, index)\n    return is.fun(result) ? result(trails[index]) : result\n  })\n}\n", "import { Valid } from '../types/common'\nimport { TransitionComponentProps } from '../types'\nimport { useTransition } from '../hooks'\n\nexport function Transition<Item, Props extends TransitionComponentProps<Item>>(\n  props:\n    | TransitionComponentProps<Item>\n    | (Props & Valid<Props, TransitionComponentProps<Item, Props>>)\n): JSX.Element\n\nexport function Transition({\n  items,\n  children,\n  ...props\n}: TransitionComponentProps<any>) {\n  return useTransition(items, props)(children)\n}\n", "import { FluidValue, deprecateInterpolate } from '@react-spring/shared'\nimport {\n  Constrain,\n  OneOrMore,\n  Animatable,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFn,\n} from '@react-spring/types'\nimport { Interpolation } from './Interpolation'\n\n/** Map the value of one or more dependencies */\nexport const to: Interpolator = (source: any, ...args: [any]) =>\n  new Interpolation(source, args)\n\n/** @deprecated Use the `to` export instead */\nexport const interpolate: Interpolator = (source: any, ...args: [any]) => (\n  deprecateInterpolate(), new Interpolation(source, args)\n)\n\n/** Extract the raw value types that are being interpolated */\nexport type Interpolated<T extends ReadonlyArray<any>> = {\n  [P in keyof T]: T[P] extends infer Element\n    ? Element extends FluidValue<infer U>\n      ? U\n      : Element\n    : never\n}\n\n/**\n * This interpolates one or more `FluidValue` objects.\n * The exported `interpolate` function uses this type.\n */\nexport interface Interpolator {\n  // Tuple of parent values\n  <Input extends ReadonlyArray<any>, Output>(\n    parents: Input,\n    interpolator: (...args: Interpolated<Input>) => Output\n  ): Interpolation<Output>\n\n  // Single parent value\n  <Input, Output>(\n    parent: FluidValue<Input> | Input,\n    interpolator: InterpolatorFn<Input, Output>\n  ): Interpolation<Output>\n\n  // Interpolation config\n  <Out>(\n    parents: OneOrMore<FluidValue>,\n    config: InterpolatorConfig<Out>\n  ): Interpolation<Animatable<Out>>\n\n  // Range shortcuts\n  <Out>(\n    parents: OneOrMore<FluidValue<number>> | FluidValue<number[]>,\n    range: readonly number[],\n    output: readonly Constrain<Out, Animatable>[],\n    extrapolate?: ExtrapolateType\n  ): Interpolation<Animatable<Out>>\n}\n", "import { Arrify, InterpolatorArgs, InterpolatorFn } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  frameLoop,\n  FluidValue,\n  getFluidValue,\n  createInterpolator,\n  Globals as G,\n  callFluidObservers,\n  addFluidObserver,\n  removeFluidObserver,\n  hasFluidValue,\n} from '@react-spring/shared'\n\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n  getPayload,\n} from '@react-spring/animated'\n\n/**\n * An `Interpolation` is a memoized value that's computed whenever one of its\n * `FluidValue` dependencies has its value changed.\n *\n * Other `FrameValue` objects can depend on this. For example, passing an\n * `Interpolation` as the `to` prop of a `useSpring` call will trigger an\n * animation toward the memoized value.\n */\nexport class Interpolation<\n  Input = any,\n  Output = any,\n> extends FrameValue<Output> {\n  /** Useful for debugging. */\n  key?: string\n\n  /** Equals false when in the frameloop */\n  idle = true\n\n  /** The function that maps inputs values to output */\n  readonly calc: InterpolatorFn<Input, Output>\n\n  /** The inputs which are currently animating */\n  protected _active = new Set<FluidValue>()\n\n  constructor(\n    /** The source of input values */\n    readonly source: unknown,\n    args: InterpolatorArgs<Input, Output>\n  ) {\n    super()\n    this.calc = createInterpolator(...args)\n\n    const value = this._get()\n    const nodeType = getAnimatedType(value)\n\n    // Assume the computed value never changes type.\n    setAnimated(this, nodeType.create(value))\n  }\n\n  advance(_dt?: number) {\n    const value = this._get()\n    const oldValue = this.get()\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this)!.setValue(value)\n      this._onChange(value, this.idle)\n    }\n    // Become idle when all parents are idle or paused.\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this)\n    }\n  }\n\n  protected _get() {\n    const inputs: Arrify<Input> = is.arr(this.source)\n      ? this.source.map(getFluidValue)\n      : (toArray(getFluidValue(this.source)) as any)\n\n    return this.calc(...inputs)\n  }\n\n  protected _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false\n\n      each(getPayload(this)!, node => {\n        node.done = false\n      })\n\n      if (G.skipAnimation) {\n        raf.batchedUpdates(() => this.advance())\n        becomeIdle(this)\n      } else {\n        frameLoop.start(this)\n      }\n    }\n  }\n\n  // Observe our sources only when we're observed.\n  protected _attach() {\n    let priority = 1\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this)\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source)\n        }\n        priority = Math.max(priority, source.priority + 1)\n      }\n    })\n    this.priority = priority\n    this._start()\n  }\n\n  // Stop observing our sources once we have no observers.\n  protected _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this)\n      }\n    })\n    this._active.clear()\n    becomeIdle(this)\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    // Update our value when an idle parent is changed,\n    // and enter the frameloop when a parent is resumed.\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance()\n      } else {\n        this._active.add(event.parent)\n        this._start()\n      }\n    }\n    // Once all parents are idle, the `advance` method runs one more time,\n    // so we should avoid updating the `idle` status here.\n    else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // Ensure our priority is greater than all parents, which means\n    // our value won't be updated until our parents have updated.\n    else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce(\n        (highest: number, parent) =>\n          Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      )\n    }\n  }\n}\n\n/** Returns true for an idle source. */\nfunction isIdle(source: any) {\n  return source.idle !== false\n}\n\n/** Return true if all values in the given set are idle or paused. */\nfunction checkIdle(active: Set<FluidValue>) {\n  // Parents can be active even when paused, so the `.every` check\n  // removes us from the frameloop if all active parents are paused.\n  return !active.size || Array.from(active).every(isIdle)\n}\n\n/** Become idle if not already idle. */\nfunction becomeIdle(self: Interpolation) {\n  if (!self.idle) {\n    self.idle = true\n\n    each(getPayload(self)!, node => {\n      node.done = true\n    })\n\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self,\n    })\n  }\n}\n", "import {\n  Globals,\n  frameLoop,\n  createStringInterpolator,\n} from '@react-spring/shared'\nimport { Interpolation } from './Interpolation'\n\n// Sane defaults\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args),\n})\n\nexport { Globals }\n\n/** Advance all animations by the given time */\nexport const update = frameLoop.advance\n", "export * from './hooks'\nexport * from './components'\nexport * from './interpolate'\nexport * from './constants'\nexport * from './globals'\n\nexport { Controller } from './Controller'\nexport { SpringValue } from './SpringValue'\nexport { SpringContext } from './SpringContext'\nexport { SpringRef } from './SpringRef'\n\nexport { FrameValue } from './FrameValue'\nexport { Interpolation } from './Interpolation'\nexport { BailSignal } from './runAsync'\nexport {\n  createInterpolator,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  easings,\n} from '@react-spring/shared'\nexport { inferTo } from './helpers'\n\nexport * from './types'\nexport * from '@react-spring/types'\n"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,IAAA,EAAMC,yBAAA,QAAiC;;;ACAhD,SACEC,EAAA,EACAC,OAAA,EACAC,QAAA,EACAC,aAAA,EACAC,gBAAA,EAEAC,OAAA,IAAWC,CAAA,QACN;AAMA,SAASC,SACdC,KAAA,EAEuC;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADpCC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAAF,IAAA,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEH,OAAOd,EAAA,CAAGe,GAAA,CAAIP,KAAK,IAAIA,KAAA,CAAM,GAAGI,IAAI,IAAIJ,KAAA;AAC1C;AAGO,IAAMQ,SAAA,GAAYA,CACvBR,KAAA,EACAS,GAAA,KAEAT,KAAA,KAAU,QACV,CAAC,EACCS,GAAA,IACAT,KAAA,KACCR,EAAA,CAAGe,GAAA,CAAIP,KAAK,IAAIA,KAAA,CAAMS,GAAG,IAAIhB,OAAA,CAAQO,KAAK,EAAEU,QAAA,CAASD,GAAG;AAGtD,IAAME,WAAA,GAAcA,CACzBC,IAAA,EACAH,GAAA,KACIjB,EAAA,CAAGqB,GAAA,CAAID,IAAI,IAAIH,GAAA,IAAQG,IAAA,CAAaH,GAAG,IAAIG,IAAA;AAU1C,IAAME,cAAA,GAAiBA,CAC5BC,KAAA,EACAN,GAAA,KAEAM,KAAA,CAAMC,OAAA,KAAY,OACdD,KAAA,CAAMN,GAAG,IACTM,KAAA,CAAMC,OAAA,GACJD,KAAA,CAAMC,OAAA,CAAQP,GAAG,IACjB;AAER,IAAMQ,aAAA,GAAiBjB,KAAA,IAAeA,KAAA;AAS/B,IAAMkB,eAAA,GAAkB,SAAAA,CAC7BH,KAAA,EAEM;EAAA,IADNI,SAAA,GAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAA8Ce,aAAA;EAE9C,IAAII,IAAA,GAA0BC,aAAA;EAC9B,IAAIP,KAAA,CAAMC,OAAA,IAAWD,KAAA,CAAMC,OAAA,KAAY,MAAM;IAC3CD,KAAA,GAAQA,KAAA,CAAMC,OAAA;IACdK,IAAA,GAAOE,MAAA,CAAOF,IAAA,CAAKN,KAAK;EAC1B;EACA,MAAMS,SAAA,GAAgB,CAAC;EACvB,WAAWf,GAAA,IAAOY,IAAA,EAAM;IACtB,MAAMrB,KAAA,GAAQmB,SAAA,CAAUJ,KAAA,CAAMN,GAAG,GAAGA,GAAG;IACvC,IAAI,CAACjB,EAAA,CAAGiC,GAAA,CAAIzB,KAAK,GAAG;MAClBwB,SAAA,CAASf,GAAG,IAAIT,KAAA;IAClB;EACF;EACA,OAAOwB,SAAA;AACT;AAaO,IAAMF,aAAA,GAAgB,CAC3B,UACA,WACA,WACA,YACA,WACA,YACA,SACF;AAEA,IAAMI,cAAA,GAEF;EACFC,MAAA,EAAQ;EACRC,IAAA,EAAM;EACNC,EAAA,EAAI;EACJC,GAAA,EAAK;EACLC,IAAA,EAAM;EACNC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,OAAA,EAAS;EACTC,SAAA,EAAW;EACXpB,OAAA,EAAS;EACTqB,KAAA,EAAO;EACPC,OAAA,EAAS;EACTC,OAAA,EAAS;EACTC,QAAA,EAAU;EACVC,OAAA,EAAS;EACTC,QAAA,EAAU;EACVC,MAAA,EAAQ;EACRC,SAAA,EAAW;EAAA;EAGXC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,OAAA,EAAS;EACTC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,QAAA,EAAU;EACVC,WAAA,EAAa;EAAA;EAGbjC,IAAA,EAAM;EACNkC,MAAA,EAAQ;EACRC,QAAA,EAAU;AACZ;AAOA,SAASC,gBACP1C,KAAA,EACiC;EACjC,MAAM2C,OAAA,GAAe,CAAC;EAEtB,IAAIC,KAAA,GAAQ;EACZjE,QAAA,CAASqB,KAAA,EAAO,CAACf,KAAA,EAAOY,IAAA,KAAS;IAC/B,IAAI,CAACc,cAAA,CAAed,IAAI,GAAG;MACzB8C,OAAA,CAAQ9C,IAAI,IAAIZ,KAAA;MAChB2D,KAAA;IACF;EACF,CAAC;EAED,IAAIA,KAAA,EAAO;IACT,OAAOD,OAAA;EACT;AACF;AAMO,SAASE,QAA0B7C,KAAA,EAAsB;EAC9D,MAAM8C,GAAA,GAAKJ,eAAA,CAAgB1C,KAAK;EAChC,IAAI8C,GAAA,EAAI;IACN,MAAMC,GAAA,GAAW;MAAEjC,EAAA,EAAAgC;IAAG;IACtBnE,QAAA,CAASqB,KAAA,EAAO,CAACgD,GAAA,EAAKtD,GAAA,KAAQA,GAAA,IAAOoD,GAAA,KAAOC,GAAA,CAAIrD,GAAG,IAAIsD,GAAA,CAAI;IAC3D,OAAOD,GAAA;EACT;EACA,OAAAE,aAAA,KAAYjD,KAAA;AACd;AAGO,SAASkD,YAAejE,KAAA,EAA6B;EAC1DA,KAAA,GAAQL,aAAA,CAAcK,KAAK;EAC3B,OAAOR,EAAA,CAAG0E,GAAA,CAAIlE,KAAK,IACfA,KAAA,CAAMmE,GAAA,CAAIF,WAAW,IACrBrE,gBAAA,CAAiBI,KAAK,IACnBF,CAAA,CAAEsE,wBAAA,CAAyB;IAC1BC,KAAA,EAAO,CAAC,GAAG,CAAC;IACZC,MAAA,EAAQ,CAACtE,KAAA,EAAOA,KAAK;EACvB,CAAC,EAAE,CAAC,IACJA,KAAA;AACR;AAEO,SAASuE,SAASxD,KAAA,EAAe;EACtC,WAAWyD,CAAA,IAAKzD,KAAA,EAAO,OAAO;EAC9B,OAAO;AACT;AAEO,SAAS0D,UAAUZ,GAAA,EAAS;EACjC,OAAOrE,EAAA,CAAGe,GAAA,CAAIsD,GAAE,KAAMrE,EAAA,CAAG0E,GAAA,CAAIL,GAAE,KAAKrE,EAAA,CAAGqB,GAAA,CAAIgD,GAAA,CAAG,CAAC,CAAC;AAClD;AAGO,SAASa,WAAWC,IAAA,EAAkB7C,GAAA,EAAiB;EAAA,IAAA8C,SAAA;EAC5D,CAAAA,SAAA,GAAAD,IAAA,CAAK7C,GAAA,cAAA8C,SAAA,eAALA,SAAA,CAAUC,MAAA,CAAOF,IAAI;EACrB7C,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAK+C,MAAA,CAAOF,IAAI;AAClB;AAGO,SAASG,WAAWH,IAAA,EAAkB7C,GAAA,EAAiB;EAC5D,IAAIA,GAAA,IAAO6C,IAAA,CAAK7C,GAAA,KAAQA,GAAA,EAAK;IAAA,IAAAiD,UAAA;IAC3B,CAAAA,UAAA,GAAAJ,IAAA,CAAK7C,GAAA,cAAAiD,UAAA,eAALA,UAAA,CAAUF,MAAA,CAAOF,IAAI;IACrB7C,GAAA,CAAIkD,GAAA,CAAIL,IAAI;IACZA,IAAA,CAAK7C,GAAA,GAAMA,GAAA;EACb;AACF;;;AD/LO,SAASmD,SACdC,IAAA,EACAC,SAAA,EAEA;EAAA,IADAC,SAAA,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAY;EAEZX,yBAAA,CAA0B,MAAM;IAC9B,IAAI4F,SAAA,EAAW;MACb,IAAIE,SAAA,GAAY;MAChB/F,IAAA,CAAK4F,IAAA,EAAM,CAACpD,GAAA,EAAKwD,CAAA,KAAM;QACrB,MAAMC,WAAA,GAAczD,GAAA,CAAI0D,OAAA;QACxB,IAAID,WAAA,CAAYpF,MAAA,EAAQ;UACtB,IAAIkC,KAAA,GAAQ+C,SAAA,GAAYD,SAAA,CAAUG,CAAC;UAGnC,IAAIG,KAAA,CAAMpD,KAAK,GAAGA,KAAA,GAAQgD,SAAA,MACrBA,SAAA,GAAYhD,KAAA;UAEjB/C,IAAA,CAAKiG,WAAA,EAAaZ,IAAA,IAAQ;YACxBrF,IAAA,CAAKqF,IAAA,CAAKe,KAAA,EAAO3E,KAAA,IAAS;cAExB,MAAM4E,iBAAA,GAAoB5E,KAAA,CAAMsB,KAAA;cAChCtB,KAAA,CAAMsB,KAAA,GAAQ5B,GAAA,IAAO4B,KAAA,GAAQtC,QAAA,CAAS4F,iBAAA,IAAqB,GAAGlF,GAAG;YACnE,CAAC;UACH,CAAC;UAEDqB,GAAA,CAAI8D,KAAA,CAAM;QACZ;MACF,CAAC;IACH,OAAO;MACL,IAAIC,CAAA,GAAkBC,OAAA,CAAQC,OAAA,CAAQ;MACtCzG,IAAA,CAAK4F,IAAA,EAAMpD,GAAA,IAAO;QAChB,MAAMyD,WAAA,GAAczD,GAAA,CAAI0D,OAAA;QACxB,IAAID,WAAA,CAAYpF,MAAA,EAAQ;UAEtB,MAAM6F,MAAA,GAAST,WAAA,CAAYpB,GAAA,CAAIQ,IAAA,IAAQ;YACrC,MAAMsB,CAAA,GAAItB,IAAA,CAAKe,KAAA;YACff,IAAA,CAAKe,KAAA,GAAQ,EAAC;YACd,OAAOO,CAAA;UACT,CAAC;UAGDJ,CAAA,GAAIA,CAAA,CAAEK,IAAA,CAAK,MAAM;YACf5G,IAAA,CAAKiG,WAAA,EAAa,CAACZ,IAAA,EAAMW,CAAA,KACvBhG,IAAA,CAAK0G,MAAA,CAAOV,CAAC,KAAK,EAAC,EAAGa,OAAA,IAAUxB,IAAA,CAAKe,KAAA,CAAMU,IAAA,CAAKD,OAAM,CAAC,CACzD;YACA,OAAOL,OAAA,CAAQO,GAAA,CAAIvE,GAAA,CAAI8D,KAAA,CAAM,CAAC;UAChC,CAAC;QACH;MACF,CAAC;IACH;EACF,CAAC;AACH;;;AE7EA,SAASpG,EAAA,IAAA8G,GAAA,QAAU;;;ACDnB,SAASC,UAAA,IAAAC,WAAA,EAAYC,OAAA,EAASC,MAAA,QAAc;AAE5C,SACElH,EAAA,IAAAmH,GAAA,EACArH,IAAA,IAAAsH,KAAA,EACAC,OAAA,EACAC,OAAA,EACAC,cAAA,EACAxH,yBAAA,IAAAyH,0BAAA,QACK;;;ACTP,SACExH,EAAA,IAAAyH,GAAA,EACAC,GAAA,IAAAC,IAAA,EACA7H,IAAA,IAAA8H,KAAA,EACAC,OAAA,EACA5H,OAAA,IAAA6H,QAAA,EACA5H,QAAA,IAAA6H,SAAA,EACAC,SAAA,IAAAC,UAAA,EACAC,UAAA,EACA/H,aAAA,IAAAgI,cAAA,EACA/H,gBAAA,IAAAgI,iBAAA,EAEA/H,OAAA,IAAWgI,EAAA,EACXC,kBAAA,IAAAC,mBAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,iBAAA,QACK;AACP,SAEEC,aAAA,EACAC,cAAA,EACAC,UAAA,EACAC,WAAA,IAAAC,YAAA,EACAC,WAAA,EACAC,eAAA,QACK;;;AC3BP,SAASlJ,EAAA,IAAAmJ,GAAA,EAAIC,OAAA,QAAe;;;ACCrB,IAAMjH,MAAA,GAAS;EACpBX,OAAA,EAAS;IAAE6H,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACtCC,MAAA,EAAQ;IAAEF,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACrCE,MAAA,EAAQ;IAAEH,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACrCG,KAAA,EAAO;IAAEJ,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACpCI,IAAA,EAAM;IAAEL,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACnCK,QAAA,EAAU;IAAEN,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAI;AAC1C;;;ADJA,IAAMM,QAAA,GAAApF,aAAA,CAAAA,aAAA,KACDrC,MAAA,CAAQX,OAAA;EACXqI,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,MAAA,EAAQX,OAAA,CAAQY,MAAA;EAChBC,KAAA,EAAO;AAAA,EACT;AAEO,IAAMC,eAAA,GAAN,MAAsB;EA2I3BC,YAAA,EAAc;IAnFd;AAAA;AAAA;AAAA;AAAA;IAAA,KAAAC,QAAA,GAA8B;IAoF5BrI,MAAA,CAAOsI,MAAA,CAAO,MAAMT,QAAQ;EAC9B;AACF;AAQO,SAASU,YACdC,OAAA,EACAC,SAAA,EACAC,aAAA,EACA;EACA,IAAIA,aAAA,EAAe;IACjBA,aAAA,GAAAjG,aAAA,KAAqBiG,aAAA,CAAc;IACnCC,cAAA,CAAeD,aAAA,EAAeD,SAAS;IACvCA,SAAA,GAAAhG,aAAA,CAAAA,aAAA,KAAiBiG,aAAA,GAAkBD,SAAA,CAAU;EAC/C;EAEAE,cAAA,CAAeH,OAAA,EAAQC,SAAS;EAChCzI,MAAA,CAAOsI,MAAA,CAAOE,OAAA,EAAQC,SAAS;EAE/B,WAAWvJ,GAAA,IAAO2I,QAAA,EAAU;IAC1B,IAAIW,OAAA,CAAOtJ,GAAG,KAAK,MAAM;MACvBsJ,OAAA,CAAOtJ,GAAG,IAAI2I,QAAA,CAAS3I,GAAG;IAC5B;EACF;EAEA,IAAI;IAAE0J,SAAA;IAAWb;EAAQ,IAAIS,OAAA;EAC7B,MAAM;IAAEV;EAAK,IAAIU,OAAA;EACjB,IAAI,CAACpB,GAAA,CAAGlH,GAAA,CAAI0I,SAAS,GAAG;IACtB,IAAIA,SAAA,GAAY,MAAMA,SAAA,GAAY;IAClC,IAAIb,OAAA,GAAU,GAAGA,OAAA,GAAU;IAC3BS,OAAA,CAAOlB,OAAA,GAAUuB,IAAA,CAAKC,GAAA,CAAK,IAAID,IAAA,CAAKE,EAAA,GAAMH,SAAA,EAAW,CAAC,IAAId,IAAA;IAC1DU,OAAA,CAAOjB,QAAA,GAAY,IAAIsB,IAAA,CAAKE,EAAA,GAAKhB,OAAA,GAAUD,IAAA,GAAQc,SAAA;EACrD;EAEA,OAAOJ,OAAA;AACT;AAIA,SAASG,eACPH,OAAA,EACAhJ,KAAA,EACA;EACA,IAAI,CAAC4H,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAMwJ,KAAK,GAAG;IACxBR,OAAA,CAAOS,QAAA,GAAW;EACpB,OAAO;IACL,MAAMC,eAAA,GAAkB,CAAC9B,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAM8H,OAAO,KAAK,CAACF,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAM+H,QAAQ;IACxE,IACE2B,eAAA,IACA,CAAC9B,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAMoJ,SAAS,KACvB,CAACxB,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAMuI,OAAO,KACrB,CAACX,GAAA,CAAGlH,GAAA,CAAIV,KAAA,CAAMsI,IAAI,GAClB;MACAU,OAAA,CAAOS,QAAA,GAAW;MAClBT,OAAA,CAAOQ,KAAA,GAAQ;IACjB;IACA,IAAIE,eAAA,EAAiB;MACnBV,OAAA,CAAOI,SAAA,GAAY;IACrB;EACF;AACF;;;AEnNA,IAAMO,UAAA,GAA6B,EAAC;AAI7B,IAAMC,SAAA,GAAN,MAAyB;EAAzBhB,YAAA;IACL,KAAAiB,OAAA,GAAU;IACV,KAAAC,MAAA,GAAmCH,UAAA;IACnC,KAAAI,QAAA,GAAqC;IACrC,KAAAC,UAAA,GAAgCL,UAAA;IAIhC,KAAA/I,MAAA,GAAS,IAAI+H,eAAA,CAAgB;IAC7B,KAAAtH,SAAA,GAAY;EAAA;AACd;;;ACpBA,SAAkB5C,EAAA,IAAAwL,GAAA,EAAI9D,GAAA,EAAKrH,OAAA,IAAWoL,EAAA,QAAS;AAiCxC,SAASC,cACd3H,MAAA,EAAA4H,IAAA,EAEgB;EAAA,IADhB;IAAE1K,GAAA;IAAKM,KAAA;IAAOqK,YAAA;IAAcC,KAAA;IAAOC;EAAQ,IAAAH,IAAA;EAE3C,OAAO,IAAIrF,OAAA,CAAQ,CAACC,OAAA,EAASwF,MAAA,KAAW;IAAA,IAAAC,aAAA;IACtC,IAAInJ,KAAA;IACJ,IAAIoJ,OAAA;IAEJ,IAAIvJ,MAAA,GAAS1B,SAAA,EAAAgL,aAAA,GAAUzK,KAAA,CAAMmB,MAAA,cAAAsJ,aAAA,cAAAA,aAAA,GAAUJ,YAAA,aAAAA,YAAA,uBAAAA,YAAA,CAAclJ,MAAA,EAAQzB,GAAG;IAChE,IAAIyB,MAAA,EAAQ;MACVK,OAAA,CAAQ;IACV,OAAO;MAEL,IAAI,CAACyI,GAAA,CAAGvJ,GAAA,CAAIV,KAAA,CAAMkB,KAAK,GAAG;QACxBoJ,KAAA,CAAMK,MAAA,GAASlL,SAAA,CAAUO,KAAA,CAAMkB,KAAA,EAAOxB,GAAG;MAC3C;MAGA,IAAIwB,KAAA,GAAQmJ,YAAA,aAAAA,YAAA,uBAAAA,YAAA,CAAcnJ,KAAA;MAC1B,IAAIA,KAAA,KAAU,MAAM;QAClBA,KAAA,GAAQoJ,KAAA,CAAMK,MAAA,IAAUlL,SAAA,CAAUyB,KAAA,EAAOxB,GAAG;MAC9C;MAEA4B,KAAA,GAAQtC,QAAA,CAASgB,KAAA,CAAMsB,KAAA,IAAS,GAAG5B,GAAG;MACtC,IAAIwB,KAAA,EAAO;QACToJ,KAAA,CAAMM,WAAA,CAAY3G,GAAA,CAAItC,QAAQ;QAC9B4I,OAAA,CAAQrJ,KAAA,CAAM;MAChB,OAAO;QACLqJ,OAAA,CAAQM,MAAA,CAAO;QACflJ,QAAA,CAAS;MACX;IACF;IAEA,SAASD,QAAA,EAAU;MACjB4I,KAAA,CAAMM,WAAA,CAAY3G,GAAA,CAAItC,QAAQ;MAC9B2I,KAAA,CAAMQ,QAAA,CAAShH,MAAA,CAAO4G,OAAO;MAC7BA,OAAA,CAAQvJ,MAAA,CAAO;MAEfG,KAAA,GAAQoJ,OAAA,CAAQK,IAAA,GAAO5E,GAAA,CAAI6E,GAAA,CAAI;IACjC;IAEA,SAASrJ,SAAA,EAAW;MAClB,IAAIL,KAAA,GAAQ,KAAK,CAAC4I,EAAA,CAAEe,aAAA,EAAe;QACjCX,KAAA,CAAMY,OAAA,GAAU;QAChBR,OAAA,GAAUvE,GAAA,CAAIgF,UAAA,CAAW3J,OAAA,EAASF,KAAK;QACvCgJ,KAAA,CAAMc,UAAA,CAAWnH,GAAA,CAAIvC,OAAO;QAC5B4I,KAAA,CAAMQ,QAAA,CAAS7G,GAAA,CAAIyG,OAAO;MAC5B,OAAO;QACLlJ,OAAA,CAAQ;MACV;IACF;IAEA,SAASA,QAAA,EAAU;MACjB,IAAI8I,KAAA,CAAMY,OAAA,EAAS;QACjBZ,KAAA,CAAMY,OAAA,GAAU;MAClB;MAEAZ,KAAA,CAAMc,UAAA,CAAWtH,MAAA,CAAOpC,OAAO;MAC/B4I,KAAA,CAAMQ,QAAA,CAAShH,MAAA,CAAO4G,OAAO;MAG7B,IAAIlI,MAAA,KAAW8H,KAAA,CAAMe,QAAA,IAAY,IAAI;QACnClK,MAAA,GAAS;MACX;MAEA,IAAI;QACFoJ,OAAA,CAAQ1F,KAAA,CAAA5B,aAAA,CAAAA,aAAA,KAAWjD,KAAA;UAAOwC,MAAA;UAAQrB;QAAA,IAAU6D,OAAO;MACrD,SAASsG,GAAA,EAAP;QACAd,MAAA,CAAOc,GAAG;MACZ;IACF;EACF,CAAC;AACH;;;ACzGA,SACE7M,EAAA,IAAA8M,GAAA,EACApF,GAAA,IAAAqF,IAAA,EACAC,KAAA,EACA9M,QAAA,IAAA+M,SAAA,EAEA5M,OAAA,IAAW6M,EAAA,QACN;;;ACHA,IAAMC,iBAAA,GAAoBA,CAC/BC,MAAA,EACAC,OAAA,KAEAA,OAAA,CAAQ1M,MAAA,IAAU,IACd0M,OAAA,CAAQ,CAAC,IACTA,OAAA,CAAQC,IAAA,CAAKC,MAAA,IAAUA,MAAA,CAAOC,SAAS,IACrCC,kBAAA,CAAmBL,MAAA,CAAOM,GAAA,CAAI,CAAC,IAC/BL,OAAA,CAAQM,KAAA,CAAMJ,MAAA,IAAUA,MAAA,CAAOK,IAAI,IACjCC,aAAA,CAAcT,MAAA,CAAOM,GAAA,CAAI,CAAC,IAC1BI,iBAAA,CACEV,MAAA,CAAOM,GAAA,CAAI,GACXL,OAAA,CAAQM,KAAA,CAAMJ,MAAA,IAAUA,MAAA,CAAOQ,QAAQ,CACzC;AAGH,IAAMF,aAAA,GAAiBrN,KAAA,KAAgB;EAC5CA,KAAA;EACAoN,IAAA,EAAM;EACNG,QAAA,EAAU;EACVP,SAAA,EAAW;AACb;AAEO,IAAMM,iBAAA,GAAoB,SAAAA,CAC/BtN,KAAA,EACAuN,QAAA;EAAA,IACAP,SAAA,GAAA9M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAY;EAAA,OACR;IACJF,KAAA;IACAuN,QAAA;IACAP;EACF;AAAA;AAEO,IAAMC,kBAAA,GAAsBjN,KAAA,KAAgB;EACjDA,KAAA;EACAgN,SAAA,EAAW;EACXO,QAAA,EAAU;AACZ;;;ADKO,SAASC,SACd3J,GAAA,EACA9C,KAAA,EACAsK,KAAA,EACAuB,MAAA,EACgB;EAChB,MAAM;IAAErJ,MAAA;IAAQC,QAAA;IAAUb;EAAO,IAAI5B,KAAA;EACrC,MAAM;IAAE0M,OAAA,EAASC,MAAA;IAAQC,OAAA,EAASC;EAAY,IAAIvC,KAAA;EAElD,IAAI,CAAC7H,QAAA,IAAYK,GAAA,KAAO6J,MAAA,IAAU,CAAC3M,KAAA,CAAMiB,KAAA,EAAO;IAC9C,OAAO4L,WAAA;EACT;EAEA,OAAQvC,KAAA,CAAMsC,OAAA,IAAW,YAAY;IACnCtC,KAAA,CAAMwC,OAAA,GAAUtK,MAAA;IAChB8H,KAAA,CAAMoC,OAAA,GAAU5J,GAAA;IAGhB,MAAMuH,YAAA,GAAelK,eAAA,CAA+BH,KAAA,EAAO,CAACf,KAAA,EAAOS,GAAA;IAAA;IAEjEA,GAAA,KAAQ,WAAW,SAAYT,KACjC;IAEA,IAAI8N,WAAA;IACJ,IAAIC,IAAA;IAGJ,MAAMC,WAAA,GAAc,IAAIlI,OAAA,CACtB,CAACC,OAAA,EAASwF,MAAA,MAAauC,WAAA,GAAc/H,OAAA,EAAWgI,IAAA,GAAOxC,MAAA,CACzD;IAEA,MAAM0C,WAAA,GAAeC,UAAA,IAA2B;MAC9C,MAAMC,UAAA;MAAA;MAEH5K,MAAA,KAAW8H,KAAA,CAAMe,QAAA,IAAY,MAAMa,kBAAA,CAAmBL,MAAM;MAAA;MAE5DrJ,MAAA,KAAW8H,KAAA,CAAMwC,OAAA,IAAWP,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;MAE9D,IAAIuB,UAAA,EAAY;QACdD,UAAA,CAAWnB,MAAA,GAASoB,UAAA;QAIpBJ,IAAA,CAAKG,UAAU;QACf,MAAMA,UAAA;MACR;IACF;IAEA,MAAME,OAAA,GAAeA,CAACC,IAAA,EAAWC,IAAA,KAAe;MAG9C,MAAMJ,UAAA,GAAa,IAAIK,UAAA,CAAW;MAClC,MAAMC,mBAAA,GAAsB,IAAIC,mBAAA,CAAoB;MAEpD,QAAQ,YAAY;QAClB,IAAI/B,EAAA,CAAEV,aAAA,EAAe;UAMnB0C,SAAA,CAAUrD,KAAK;UAGfmD,mBAAA,CAAoBzB,MAAA,GAASO,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;UAC5DmB,IAAA,CAAKS,mBAAmB;UACxB,MAAMA,mBAAA;QACR;QAEAP,WAAA,CAAYC,UAAU;QAEtB,MAAMS,MAAA,GAAarC,GAAA,CAAGzL,GAAA,CAAIwN,IAAI,IAAArK,aAAA,KAASqK,IAAA,IAAArK,aAAA,CAAAA,aAAA,KAAcsK,IAAA;UAAMzM,EAAA,EAAIwM;QAAA,EAAK;QACpEM,MAAA,CAAMnL,QAAA,GAAWD,MAAA;QAEjBkJ,SAAA,CAASrB,YAAA,EAAc,CAACpL,KAAA,EAAOS,GAAA,KAAQ;UACrC,IAAI6L,GAAA,CAAG7K,GAAA,CAAIkN,MAAA,CAAMlO,GAAG,CAAC,GAAG;YACtBkO,MAAA,CAAMlO,GAAG,IAAIT,KAAA;UACf;QACF,CAAC;QAED,MAAM4O,OAAA,GAAS,MAAMhC,MAAA,CAAOhH,KAAA,CAAM+I,MAAK;QACvCV,WAAA,CAAYC,UAAU;QAEtB,IAAI7C,KAAA,CAAMK,MAAA,EAAQ;UAChB,MAAM,IAAI5F,OAAA,CAAc8F,MAAA,IAAU;YAChCP,KAAA,CAAMM,WAAA,CAAY3G,GAAA,CAAI4G,MAAM;UAC9B,CAAC;QACH;QAEA,OAAOgD,OAAA;MACT,GAAG;IACL;IAEA,IAAI7B,MAAA;IAEJ,IAAIL,EAAA,CAAEV,aAAA,EAAe;MAKnB0C,SAAA,CAAUrD,KAAK;MACf,OAAOiC,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;IACxC;IAEA,IAAI;MACF,IAAIiC,SAAA;MAGJ,IAAIvC,GAAA,CAAGpI,GAAA,CAAIL,GAAE,GAAG;QACdgL,SAAA,IAAa,MAAOnJ,KAAA,IAAiB;UACnC,WAAWiJ,MAAA,IAASjJ,KAAA,EAAO;YACzB,MAAM0I,OAAA,CAAQO,MAAK;UACrB;QACF,GAAG9K,GAAE;MACP,OAGK;QACHgL,SAAA,GAAY/I,OAAA,CAAQC,OAAA,CAAQlC,GAAA,CAAGuK,OAAA,EAASxB,MAAA,CAAOkC,IAAA,CAAKC,IAAA,CAAKnC,MAAM,CAAC,CAAC;MACnE;MAEA,MAAM9G,OAAA,CAAQO,GAAA,CAAI,CAACwI,SAAA,CAAU3I,IAAA,CAAK4H,WAAW,GAAGE,WAAW,CAAC;MAC5DjB,MAAA,GAASO,iBAAA,CAAkBV,MAAA,CAAOM,GAAA,CAAI,GAAG,MAAM,KAAK;IAGtD,SAASb,GAAA,EAAP;MACA,IAAIA,GAAA,YAAekC,UAAA,EAAY;QAC7BxB,MAAA,GAASV,GAAA,CAAIU,MAAA;MACf,WAAWV,GAAA,YAAeoC,mBAAA,EAAqB;QAC7C1B,MAAA,GAASV,GAAA,CAAIU,MAAA;MACf,OAAO;QACL,MAAMV,GAAA;MACR;IAGF,UAAE;MACA,IAAI9I,MAAA,IAAU8H,KAAA,CAAMwC,OAAA,EAAS;QAC3BxC,KAAA,CAAMwC,OAAA,GAAUrK,QAAA;QAChB6H,KAAA,CAAMoC,OAAA,GAAUjK,QAAA,GAAWkK,MAAA,GAAS;QACpCrC,KAAA,CAAMsC,OAAA,GAAUnK,QAAA,GAAWoK,WAAA,GAAc;MAC3C;IACF;IAEA,IAAItB,GAAA,CAAG/L,GAAA,CAAIoC,MAAM,GAAG;MAClB4J,IAAA,CAAIyC,cAAA,CAAe,MAAM;QACvBrM,MAAA,CAAOoK,MAAA,EAAQH,MAAA,EAAQA,MAAA,CAAOqC,IAAI;MACpC,CAAC;IACH;IAEA,OAAOlC,MAAA;EACT,GAAG;AACL;AAGO,SAAS2B,UAAUrD,KAAA,EAAsBe,QAAA,EAA2B;EACzEI,KAAA,CAAMnB,KAAA,CAAMQ,QAAA,EAAUqD,CAAA,IAAKA,CAAA,CAAEhN,MAAA,CAAO,CAAC;EACrCmJ,KAAA,CAAMc,UAAA,CAAWgD,KAAA,CAAM;EACvB9D,KAAA,CAAMM,WAAA,CAAYwD,KAAA,CAAM;EACxB9D,KAAA,CAAMwC,OAAA,GAAUxC,KAAA,CAAMoC,OAAA,GAAUpC,KAAA,CAAMsC,OAAA,GAAU;EAChD,IAAIvB,QAAA,EAAUf,KAAA,CAAMe,QAAA,GAAWA,QAAA;AACjC;AAGO,IAAMmC,UAAA,GAAN,cAAyBa,KAAA,CAAM;EAEpCzF,YAAA,EAAc;IACZ,MACE,yIAEF;EACF;AACF;AAEO,IAAM8E,mBAAA,GAAN,cAAkCW,KAAA,CAAM;EAG7CzF,YAAA,EAAc;IACZ,MAAM,qBAAqB;EAC7B;AACF;;;AEjOA,SACE0F,oBAAA,EACA7H,SAAA,EACA8H,UAAA,IAAAC,WAAA,EACA1P,OAAA,IAAW2P,EAAA,EACX1H,kBAAA,QACK;AAEP,SAASS,WAAA,QAAmB;AAIrB,IAAMkH,YAAA,GAAgBzP,KAAA,IAC3BA,KAAA,YAAiB0P,UAAA;AAEnB,IAAIC,MAAA,GAAS;AAON,IAAeD,UAAA,GAAf,cAA2CH,WAAA,CAGhD;EAHK5F,YAAA;IAAA,SAAAzJ,SAAA;IAIL,KAAS0P,EAAA,GAAKD,MAAA;IAKd,KAAUE,SAAA,GAAY;EAAA;EAEtB,IAAIC,SAAA,EAAW;IACb,OAAO,KAAKD,SAAA;EACd;EACA,IAAIC,SAASA,QAAA,EAAkB;IAC7B,IAAI,KAAKD,SAAA,IAAaC,QAAA,EAAU;MAC9B,KAAKD,SAAA,GAAYC,QAAA;MACjB,KAAKC,iBAAA,CAAkBD,QAAQ;IACjC;EACF;EAAA;EAGA5C,IAAA,EAAS;IACP,MAAM8C,IAAA,GAAOzH,WAAA,CAAY,IAAI;IAC7B,OAAOyH,IAAA,IAAQA,IAAA,CAAKC,QAAA,CAAS;EAC/B;EAAA;EAGApO,GAAA,EAA2C;IAAA,SAAAqO,KAAA,GAAAhQ,SAAA,CAAAC,MAAA,EAAhCC,IAAA,OAAAC,KAAA,CAAA6P,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAA/P,IAAA,CAAA+P,KAAA,IAAAjQ,SAAA,CAAAiQ,KAAA;IAAA;IACT,OAAOX,EAAA,CAAE3N,EAAA,CAAG,MAAMzB,IAAI;EACxB;EAAA;EAGAgQ,YAAA,EAAoD;IAClDf,oBAAA,CAAqB;IAAA,SAAAgB,KAAA,GAAAnQ,SAAA,CAAAC,MAAA,EADHC,IAAA,OAAAC,KAAA,CAAAgQ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAAlQ,IAAA,CAAAkQ,KAAA,IAAApQ,SAAA,CAAAoQ,KAAA;IAAA;IAElB,OAAOd,EAAA,CAAE3N,EAAA,CAAG,MAAMzB,IAAI;EACxB;EAEAmQ,OAAA,EAAS;IACP,OAAO,KAAKrD,GAAA,CAAI;EAClB;EAEUsD,cAAc7M,KAAA,EAAe;IACrC,IAAIA,KAAA,IAAS,GAAG,KAAK8M,OAAA,CAAQ;EAC/B;EAEUC,gBAAgB/M,KAAA,EAAe;IACvC,IAAIA,KAAA,IAAS,GAAG,KAAKgN,OAAA,CAAQ;EAC/B;EAAA;EASUF,QAAA,EAAU,CAAC;EAAA;EAGXE,QAAA,EAAU,CAAC;EAAA;EAGXC,UAAU5Q,KAAA,EAAwB;IAAA,IAAd6Q,IAAA,GAAA3Q,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAO;IACnC4H,kBAAA,CAAmB,MAAM;MACvBgJ,IAAA,EAAM;MACNC,MAAA,EAAQ;MACR/Q,KAAA;MACA6Q;IACF,CAAC;EACH;EAAA;EAGUd,kBAAkBD,QAAA,EAAkB;IAC5C,IAAI,CAAC,KAAKe,IAAA,EAAM;MACdrJ,SAAA,CAAUzE,IAAA,CAAK,IAAI;IACrB;IACA+E,kBAAA,CAAmB,MAAM;MACvBgJ,IAAA,EAAM;MACNC,MAAA,EAAQ;MACRjB;IACF,CAAC;EACH;AACF;;;ACxGA,IAAMkB,EAAA,GAAKC,MAAA,CAAOC,GAAA,CAAI,aAAa;AAEnC,IAAMC,YAAA,GAAe;AACrB,IAAMC,YAAA,GAAe;AACrB,IAAMC,SAAA,GAAY;AAGX,IAAMC,WAAA,GAAe1E,MAAA,KAAiBA,MAAA,CAAOoE,EAAE,IAAIG,YAAA,IAAgB;AAGnE,IAAMI,WAAA,GAAe3E,MAAA,KAAiBA,MAAA,CAAOoE,EAAE,IAAII,YAAA,IAAgB;AAGnE,IAAMI,QAAA,GAAY5E,MAAA,KAAiBA,MAAA,CAAOoE,EAAE,IAAIK,SAAA,IAAa;AAG7D,IAAMI,YAAA,GAAeA,CAAC7E,MAAA,EAAa8E,MAAA,KACxCA,MAAA,GACK9E,MAAA,CAAOoE,EAAE,KAAKI,YAAA,GAAeD,YAAA,GAC7BvE,MAAA,CAAOoE,EAAE,KAAK,CAACI,YAAA;AAEf,IAAMO,YAAA,GAAeA,CAAC/E,MAAA,EAAalB,MAAA,KACxCA,MAAA,GAAUkB,MAAA,CAAOoE,EAAE,KAAKK,SAAA,GAAczE,MAAA,CAAOoE,EAAE,KAAK,CAACK,SAAA;;;ARqDhD,IAAMO,WAAA,GAAN,cAAmClC,UAAA,CAAc;EAmCtD/F,YAAY0E,IAAA,EAAYC,IAAA,EAAY;IAClC,MAAM;IA/BR;IAAA,KAAAuD,SAAA,GAAY,IAAIlH,SAAA,CAAa;IAM7B;IAAA,KAAAS,YAAA,GAAsC,CAAC;IAGvC;IAAA,KAAU0G,MAAA,GAAwC;MAChDpG,MAAA,EAAQ;MACRO,OAAA,EAAS;MACTE,UAAA,EAAY,mBAAI4F,GAAA,CAAI;MACpBpG,WAAA,EAAa,mBAAIoG,GAAA,CAAI;MACrBlG,QAAA,EAAU,mBAAIkG,GAAA,CAAI;IACpB;IAGA;IAAA,KAAUC,aAAA,GAAgB,mBAAID,GAAA,CAA6B;IAG3D;IAAA,KAAUE,WAAA,GAAc;IAGxB;IAAA,KAAUC,SAAA,GAAY;IAEtB,KAAUC,iBAAA,GAAoB;IAM5B,IAAI,CAAClL,GAAA,CAAGxF,GAAA,CAAI4M,IAAI,KAAK,CAACpH,GAAA,CAAGxF,GAAA,CAAI6M,IAAI,GAAG;MAClC,MAAMvN,KAAA,GAAQkG,GAAA,CAAGpG,GAAA,CAAIwN,IAAI,IAAArK,aAAA,KAASqK,IAAA,IAAArK,aAAA,CAAAA,aAAA,KAAcsK,IAAA;QAAM1M,IAAA,EAAMyM;MAAA,EAAK;MACjE,IAAIpH,GAAA,CAAGxF,GAAA,CAAIV,KAAA,CAAMC,OAAO,GAAG;QACzBD,KAAA,CAAMC,OAAA,GAAU;MAClB;MACA,KAAK4E,KAAA,CAAM7E,KAAK;IAClB;EACF;EAAA;EAGA,IAAI8P,KAAA,EAAO;IACT,OAAO,EAAEU,WAAA,CAAY,IAAI,KAAK,KAAKO,MAAA,CAAOrE,OAAA,KAAY+D,QAAA,CAAS,IAAI;EACrE;EAEA,IAAIY,KAAA,EAAO;IACT,OAAOzK,cAAA,CAAc,KAAKkK,SAAA,CAAUhQ,EAAE;EACxC;EAEA,IAAI+H,SAAA,EAA4B;IAC9B,MAAMoG,IAAA,GAAOxH,YAAA,CAAY,IAAI;IAC7B,OACEwH,IAAA,YAAgB5H,aAAA,GACZ4H,IAAA,CAAKqC,YAAA,IAAgB,IACrBrC,IAAA,CAAK1H,UAAA,CAAW,EAAEnE,GAAA,CAAImO,KAAA,IAAQA,KAAA,CAAKD,YAAA,IAAgB,CAAC;EAE5D;EAAA;AAAA;AAAA;EAKA,IAAIf,YAAA,EAAc;IAChB,OAAOA,WAAA,CAAY,IAAI;EACzB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,YAAA,EAAc;IAChB,OAAOA,WAAA,CAAY,IAAI;EACzB;EAAA;AAAA;AAAA;EAKA,IAAIC,SAAA,EAAW;IACb,OAAOA,QAAA,CAAS,IAAI;EACtB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIe,UAAA,EAAY;IACd,OAAO,KAAKT,MAAA,CAAO7F,OAAA;EACrB;EAAA;EAGAuG,QAAQC,EAAA,EAAY;IAClB,IAAI5B,IAAA,GAAO;IACX,IAAIjG,OAAA,GAAU;IAEd,MAAM8H,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAI;MAAE/G;IAAS,IAAI4H,IAAA;IACnB,MAAM;MAAE/Q,MAAA,EAAAoI;IAAO,IAAI2I,IAAA;IAEnB,MAAMC,OAAA,GAAUrK,UAAA,CAAWoK,IAAA,CAAK7Q,EAAE;IAClC,IAAI,CAAC8Q,OAAA,IAAW3K,aAAA,CAAc0K,IAAA,CAAK7Q,EAAE,GAAG;MACtCiJ,QAAA,GAAWxD,QAAA,CAAQK,cAAA,CAAc+K,IAAA,CAAK7Q,EAAE,CAAC;IAC3C;IAEA6Q,IAAA,CAAK7H,MAAA,CAAO+H,OAAA,CAAQ,CAACN,KAAA,EAAMhN,CAAA,KAAM;MAC/B,IAAIgN,KAAA,CAAKO,IAAA,EAAM;MAEf,MAAMhP,GAAA;MAAA;MAEJyO,KAAA,CAAK3I,WAAA,IAAetB,cAAA,GAChB,IACAsK,OAAA,GACEA,OAAA,CAAQrN,CAAC,EAAEwN,YAAA,GACXhI,QAAA,CAAUxF,CAAC;MAEnB,IAAIiI,QAAA,GAAWmF,IAAA,CAAKtQ,SAAA;MACpB,IAAI2Q,QAAA,GAAWlP,GAAA;MAEf,IAAI,CAAC0J,QAAA,EAAU;QACbwF,QAAA,GAAWT,KAAA,CAAKQ,YAAA;QAGhB,IAAI/I,OAAA,CAAOlB,OAAA,IAAW,GAAG;UACvByJ,KAAA,CAAKO,IAAA,GAAO;UACZ;QACF;QAEA,IAAIG,OAAA,GAAWV,KAAA,CAAKW,WAAA,IAAeR,EAAA;QACnC,MAAM7Q,IAAA,GAAO8Q,IAAA,CAAK3H,UAAA,CAAWzF,CAAC;QAE9B,MAAM4N,EAAA,GACJZ,KAAA,CAAKY,EAAA,IAAM,OACPZ,KAAA,CAAKY,EAAA,GACJZ,KAAA,CAAKY,EAAA,GAAKjM,GAAA,CAAG/C,GAAA,CAAI6F,OAAA,CAAOH,QAAQ,IAC7BG,OAAA,CAAOH,QAAA,CAAStE,CAAC,IACjByE,OAAA,CAAOH,QAAA;QAEjB,IAAIA,QAAA;QAOJ,MAAMuJ,SAAA,GACJpJ,OAAA,CAAOoJ,SAAA,KACNvR,IAAA,IAAQiC,GAAA,GAAK,OAAQuG,IAAA,CAAKgJ,GAAA,CAAI,GAAGhJ,IAAA,CAAKiJ,GAAA,CAAIxP,GAAA,GAAKjC,IAAI,IAAI,IAAK;QAG/D,IAAI,CAACqF,GAAA,CAAGxF,GAAA,CAAIsI,OAAA,CAAOS,QAAQ,GAAG;UAC5B,IAAI3E,CAAA,GAAI;UACR,IAAIkE,OAAA,CAAOS,QAAA,GAAW,GAAG;YAOvB,IAAI,KAAK2H,iBAAA,KAAsBpI,OAAA,CAAOS,QAAA,EAAU;cAE9C,KAAK2H,iBAAA,GAAoBpI,OAAA,CAAOS,QAAA;cAGhC,IAAI8H,KAAA,CAAKgB,gBAAA,GAAmB,GAAG;gBAE7BhB,KAAA,CAAKW,WAAA,GAAclJ,OAAA,CAAOS,QAAA,GAAW8H,KAAA,CAAKgB,gBAAA;gBAE1CN,OAAA,GAAUV,KAAA,CAAKW,WAAA,IAAeR,EAAA;cAChC;YACF;YAGA5M,CAAA,IAAKkE,OAAA,CAAOwJ,QAAA,IAAY,KAAKP,OAAA,GAAU,KAAKb,iBAAA;YAE5CtM,CAAA,GAAIA,CAAA,GAAI,IAAI,IAAIA,CAAA,GAAI,IAAI,IAAIA,CAAA;YAE5ByM,KAAA,CAAKgB,gBAAA,GAAmBzN,CAAA;UAC1B;UAEAkN,QAAA,GAAWnR,IAAA,GAAOmI,OAAA,CAAOR,MAAA,CAAO1D,CAAC,KAAKhC,GAAA,GAAKjC,IAAA;UAC3CgI,QAAA,IAAYmJ,QAAA,GAAWT,KAAA,CAAKQ,YAAA,IAAgBL,EAAA;UAE5ClF,QAAA,GAAW1H,CAAA,IAAK;QAClB,WAGSkE,OAAA,CAAOQ,KAAA,EAAO;UACrB,MAAMA,KAAA,GAAQR,OAAA,CAAOQ,KAAA,KAAU,OAAO,QAAQR,OAAA,CAAOQ,KAAA;UACrD,MAAMiJ,CAAA,GAAIpJ,IAAA,CAAKqJ,GAAA,CAAI,EAAE,IAAIlJ,KAAA,IAASyI,OAAO;UAEzCD,QAAA,GAAWnR,IAAA,GAAQsR,EAAA,IAAM,IAAI3I,KAAA,KAAW,IAAIiJ,CAAA;UAC5CjG,QAAA,GAAWnD,IAAA,CAAKiJ,GAAA,CAAIf,KAAA,CAAKQ,YAAA,GAAeC,QAAQ,KAAKI,SAAA;UAGrDvJ,QAAA,GAAWsJ,EAAA,GAAKM,CAAA;QAClB,OAGK;UACH5J,QAAA,GAAW0I,KAAA,CAAKD,YAAA,IAAgB,OAAOa,EAAA,GAAKZ,KAAA,CAAKD,YAAA;UAGjD,MAAMqB,YAAA,GAAe3J,OAAA,CAAO2J,YAAA,IAAgBP,SAAA,GAAY;UAGxD,MAAMQ,YAAA,GAAe5J,OAAA,CAAON,KAAA,GAAQ,IAAIM,OAAA,CAAO6J,MAAA;UAC/C,MAAMC,SAAA,GAAY,CAAC5M,GAAA,CAAGxF,GAAA,CAAIkS,YAAY;UAGtC,MAAMG,SAAA,GAAYlS,IAAA,IAAQiC,GAAA,GAAKyO,KAAA,CAAKY,EAAA,GAAK,IAAItR,IAAA,GAAOiC,GAAA;UAGpD,IAAIkQ,QAAA;UAGJ,IAAIC,UAAA,GAAa;UAEjB,MAAMC,IAAA,GAAO;UACb,MAAMC,QAAA,GAAW9J,IAAA,CAAK+J,IAAA,CAAK1B,EAAA,GAAKwB,IAAI;UACpC,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIF,QAAA,EAAU,EAAEE,CAAA,EAAG;YACjCL,QAAA,GAAW3J,IAAA,CAAKiJ,GAAA,CAAIzJ,QAAQ,IAAI8J,YAAA;YAEhC,IAAI,CAACK,QAAA,EAAU;cACbxG,QAAA,GAAWnD,IAAA,CAAKiJ,GAAA,CAAIxP,GAAA,GAAKkP,QAAQ,KAAKI,SAAA;cACtC,IAAI5F,QAAA,EAAU;gBACZ;cACF;YACF;YAEA,IAAIsG,SAAA,EAAW;cACbG,UAAA,GAAajB,QAAA,IAAYlP,GAAA,IAAMkP,QAAA,GAAWlP,GAAA,IAAMiQ,SAAA;cAGhD,IAAIE,UAAA,EAAY;gBACdpK,QAAA,GAAW,CAACA,QAAA,GAAW+J,YAAA;gBACvBZ,QAAA,GAAWlP,GAAA;cACb;YACF;YAEA,MAAMwQ,WAAA,GAAc,CAACtK,OAAA,CAAOlB,OAAA,GAAU,QAAYkK,QAAA,GAAWlP,GAAA;YAC7D,MAAMyQ,YAAA,GAAe,CAACvK,OAAA,CAAOjB,QAAA,GAAW,OAAQc,QAAA;YAChD,MAAM2K,YAAA,IAAgBF,WAAA,GAAcC,YAAA,IAAgBvK,OAAA,CAAOV,IAAA;YAE3DO,QAAA,GAAWA,QAAA,GAAW2K,YAAA,GAAeN,IAAA;YACrClB,QAAA,GAAWA,QAAA,GAAWnJ,QAAA,GAAWqK,IAAA;UACnC;QACF;QAEA3B,KAAA,CAAKD,YAAA,GAAezI,QAAA;QAEpB,IAAI4K,MAAA,CAAO/O,KAAA,CAAMsN,QAAQ,GAAG;UAC1B0B,OAAA,CAAQC,IAAA,6BAAiC,IAAI;UAC7CnH,QAAA,GAAW;QACb;MACF;MAGA,IAAIoF,OAAA,IAAW,CAACA,OAAA,CAAQrN,CAAC,EAAEuN,IAAA,EAAM;QAC/BtF,QAAA,GAAW;MACb;MAEA,IAAIA,QAAA,EAAU;QACZ+E,KAAA,CAAKO,IAAA,GAAO;MACd,OAAO;QACLhC,IAAA,GAAO;MACT;MAEA,IAAIyB,KAAA,CAAKqC,QAAA,CAAS5B,QAAA,EAAUhJ,OAAA,CAAO6K,KAAK,GAAG;QACzChK,OAAA,GAAU;MACZ;IACF,CAAC;IAED,MAAMoF,IAAA,GAAOxH,YAAA,CAAY,IAAI;IAK7B,MAAMqM,OAAA,GAAU7E,IAAA,CAAKC,QAAA,CAAS;IAC9B,IAAIY,IAAA,EAAM;MAER,MAAMiE,QAAA,GAAWnN,cAAA,CAAc+K,IAAA,CAAK7Q,EAAE;MAKtC,KAAKgT,OAAA,KAAYC,QAAA,IAAYlK,OAAA,KAAY,CAACb,OAAA,CAAOQ,KAAA,EAAO;QAEtDyF,IAAA,CAAK2E,QAAA,CAASG,QAAQ;QACtB,KAAKlE,SAAA,CAAUkE,QAAQ;MACzB,WAAWlK,OAAA,IAAWb,OAAA,CAAOQ,KAAA,EAAO;QAKlC,KAAKqG,SAAA,CAAUiE,OAAO;MACxB;MAEA,KAAKE,KAAA,CAAM;IACb,WAAWnK,OAAA,EAAS;MAKlB,KAAKgG,SAAA,CAAUiE,OAAO;IACxB;EACF;EAAA;EAGAG,IAAIhV,KAAA,EAA0B;IAC5BmH,IAAA,CAAI6H,cAAA,CAAe,MAAM;MACvB,KAAK+F,KAAA,CAAM;MAIX,KAAKE,MAAA,CAAOjV,KAAK;MACjB,KAAKkV,IAAA,CAAKlV,KAAK;IACjB,CAAC;IACD,OAAO;EACT;EAAA;AAAA;AAAA;AAAA;EAMAiC,MAAA,EAAQ;IACN,KAAKkT,OAAA,CAAQ;MAAElT,KAAA,EAAO;IAAK,CAAC;EAC9B;EAAA;EAGA2J,OAAA,EAAS;IACP,KAAKuJ,OAAA,CAAQ;MAAElT,KAAA,EAAO;IAAM,CAAC;EAC/B;EAAA;EAGAmT,OAAA,EAAS;IACP,IAAI7D,WAAA,CAAY,IAAI,GAAG;MACrB,MAAM;QAAE1P,EAAA,EAAAgC,GAAA;QAAIlC,MAAA,EAAAoI;MAAO,IAAI,KAAK8H,SAAA;MAC5B1K,IAAA,CAAI6H,cAAA,CAAe,MAAM;QAEvB,KAAKqG,QAAA,CAAS;QAId,IAAI,CAACtL,OAAA,CAAOQ,KAAA,EAAO;UACjB,KAAK2K,IAAA,CAAKrR,GAAA,EAAI,KAAK;QACrB;QAEA,KAAKkR,KAAA,CAAM;MACb,CAAC;IACH;IACA,OAAO;EACT;EAAA;EAGA5R,OAAOpC,KAAA,EAAwB;IAC7B,MAAM2E,KAAA,GAAQ,KAAKA,KAAA,KAAU,KAAKA,KAAA,GAAQ,EAAC;IAC3CA,KAAA,CAAMU,IAAA,CAAKrF,KAAK;IAChB,OAAO;EACT;EAeA6E,MAAM/B,GAAA,EAAUyK,IAAA,EAAY;IAC1B,IAAI5I,KAAA;IACJ,IAAI,CAACuB,GAAA,CAAGxF,GAAA,CAAIoC,GAAE,GAAG;MACf6B,KAAA,GAAQ,CAACuB,GAAA,CAAGpG,GAAA,CAAIgD,GAAE,IAAIA,GAAA,GAAAG,aAAA,CAAAA,aAAA,KAAUsK,IAAA;QAAMzM,EAAA,EAAAgC;MAAA,EAAI;IAC5C,OAAO;MACL6B,KAAA,GAAQ,KAAKA,KAAA,IAAS,EAAC;MACvB,KAAKA,KAAA,GAAQ,EAAC;IAChB;IAEA,OAAOI,OAAA,CAAQO,GAAA,CACbX,KAAA,CAAMvB,GAAA,CAAIpD,KAAA,IAAS;MACjB,MAAMuU,EAAA,GAAK,KAAKH,OAAA,CAAQpU,KAAK;MAC7B,OAAOuU,EAAA;IACT,CAAC,CACH,EAAEpP,IAAA,CAAK2G,OAAA,IAAWF,iBAAA,CAAkB,MAAME,OAAO,CAAC;EACpD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAiC,KAAK5M,MAAA,EAAkB;IACrB,MAAM;MAAEL,EAAA,EAAAgC;IAAG,IAAI,KAAKgO,SAAA;IAGpB,KAAKoD,MAAA,CAAO,KAAK/H,GAAA,CAAI,CAAC;IAEtBwB,SAAA,CAAU,KAAKoD,MAAA,EAAQ5P,MAAA,IAAU,KAAK+P,WAAW;IACjD9K,IAAA,CAAI6H,cAAA,CAAe,MAAM,KAAK+F,KAAA,CAAMlR,GAAA,EAAI3B,MAAM,CAAC;IAE/C,OAAO;EACT;EAAA;EAGAF,MAAA,EAAQ;IACN,KAAKmT,OAAA,CAAQ;MAAEnT,KAAA,EAAO;IAAK,CAAC;EAC9B;EAAA;EAGAuT,cAAcC,KAAA,EAAyB;IACrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,KAAK2E,MAAA,CAAO;IACd,WAAWD,KAAA,CAAM1E,IAAA,IAAQ,YAAY;MACnC,KAAKhB,QAAA,GAAW0F,KAAA,CAAM1F,QAAA,GAAW;IACnC;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQU4F,aAAa3U,KAAA,EAKpB;IACD,MAAMN,GAAA,GAAM,KAAKA,GAAA,IAAO;IAExB,IAAI;MAAEoB,EAAA,EAAAgC,GAAA;MAAIjC;IAAK,IAAIb,KAAA;IAEnB8C,GAAA,GAAKoD,GAAA,CAAGpG,GAAA,CAAIgD,GAAE,IAAIA,GAAA,CAAGpD,GAAG,IAAIoD,GAAA;IAC5B,IAAIA,GAAA,IAAM,QAAQY,SAAA,CAAUZ,GAAE,GAAG;MAC/BA,GAAA,GAAK;IACP;IAEAjC,IAAA,GAAOqF,GAAA,CAAGpG,GAAA,CAAIe,IAAI,IAAIA,IAAA,CAAKnB,GAAG,IAAImB,IAAA;IAClC,IAAIA,IAAA,IAAQ,MAAM;MAChBA,IAAA,GAAO;IACT;IAGA,MAAMyC,KAAA,GAAQ;MAAExC,EAAA,EAAAgC,GAAA;MAAIjC;IAAK;IAIzB,IAAI,CAAC0P,WAAA,CAAY,IAAI,GAAG;MACtB,IAAIvQ,KAAA,CAAMoB,OAAA,EAAS,CAAC0B,GAAA,EAAIjC,IAAI,IAAI,CAACA,IAAA,EAAMiC,GAAE;MAEzCjC,IAAA,GAAO+F,cAAA,CAAc/F,IAAI;MACzB,IAAI,CAACqF,GAAA,CAAGxF,GAAA,CAAIG,IAAI,GAAG;QACjB,KAAKsT,IAAA,CAAKtT,IAAI;MAChB,WAES,CAAC4G,YAAA,CAAY,IAAI,GAAG;QAC3B,KAAK0M,IAAA,CAAKrR,GAAE;MACd;IACF;IAEA,OAAOQ,KAAA;EACT;EAAA;EAGU8Q,QAAAQ,KAAA,EAERC,MAAA,EAC6B;IAAA,IAFxB7U,KAAA,GAAAQ,MAAA,CAAAsI,MAAA,MAAAgM,yBAAA,CAAAF,KAAA,GAAAA,KAAA;IAGL,MAAM;MAAElV,GAAA;MAAK2K;IAAa,IAAI;IAG9B,IAAIrK,KAAA,CAAMC,OAAA,EACRO,MAAA,CAAOsI,MAAA,CACLuB,YAAA,EACAlK,eAAA,CAAgBH,KAAA,EAAO,CAACf,KAAA,EAAOY,IAAA,KAC7B,MAAMkV,IAAA,CAAKlV,IAAI,IAAID,WAAA,CAAYX,KAAA,EAAOS,GAAG,IAAIT,KAC/C,CACF;IAEF+V,aAAA,CAAc,MAAMhV,KAAA,EAAO,SAAS;IACpCiV,SAAA,CAAU,MAAM,WAAWjV,KAAA,EAAO,IAAI;IAGtC,MAAMsD,KAAA,GAAQ,KAAKqR,YAAA,CAAa3U,KAAK;IAErC,IAAIQ,MAAA,CAAO0U,QAAA,CAAS,IAAI,GAAG;MACzB,MAAM7G,KAAA,CACJ,4IAEF;IACF;IAEA,MAAM/D,KAAA,GAAQ,KAAKyG,MAAA;IAEnB,OAAO5G,aAAA,CAAc,EAAE,KAAK+G,WAAA,EAAa;MACvCxR,GAAA;MACAM,KAAA;MACAqK,YAAA;MACAC,KAAA;MACAC,OAAA,EAAS;QACPrJ,KAAA,EAAOA,CAAA,KAAM;UACX,IAAI,CAACuP,QAAA,CAAS,IAAI,GAAG;YACnBG,YAAA,CAAa,MAAM,IAAI;YACvBjK,UAAA,CAAW2D,KAAA,CAAMc,UAAU;YAC3B6J,SAAA,CACE,MACA,WACA1I,iBAAA,CAAkB,MAAM4I,aAAA,CAAc,MAAM,KAAKrE,SAAA,CAAUhQ,EAAE,CAAC,GAC9D,IACF;UACF;QACF;QACA+J,MAAA,EAAQA,CAAA,KAAM;UACZ,IAAI4F,QAAA,CAAS,IAAI,GAAG;YAClBG,YAAA,CAAa,MAAM,KAAK;YACxB,IAAIJ,WAAA,CAAY,IAAI,GAAG;cACrB,KAAK4E,OAAA,CAAQ;YACf;YACAzO,UAAA,CAAW2D,KAAA,CAAMM,WAAW;YAC5BqK,SAAA,CACE,MACA,YACA1I,iBAAA,CAAkB,MAAM4I,aAAA,CAAc,MAAM,KAAKrE,SAAA,CAAUhQ,EAAE,CAAC,GAC9D,IACF;UACF;QACF;QACA+D,KAAA,EAAO,KAAKwQ,MAAA,CAAOrH,IAAA,CAAK,MAAM1K,KAAK;MACrC;IACF,CAAC,EAAE6B,IAAA,CAAK6G,MAAA,IAAU;MAChB,IAAIhM,KAAA,CAAMgB,IAAA,IAAQgL,MAAA,CAAOQ,QAAA,IAAY,EAAEqI,MAAA,IAAU7I,MAAA,CAAOK,IAAA,GAAO;QAC7D,MAAMiJ,SAAA,GAAYC,gBAAA,CAAiBvV,KAAK;QACxC,IAAIsV,SAAA,EAAW;UACb,OAAO,KAAKlB,OAAA,CAAQkB,SAAA,EAAW,IAAI;QACrC;MACF;MACA,OAAOtJ,MAAA;IACT,CAAC;EACH;EAAA;EAGUqJ,OACR/R,KAAA,EACAtD,KAAA,EACAgF,OAAA,EACM;IAGN,IAAIhF,KAAA,CAAMmB,MAAA,EAAQ;MAChB,KAAK4M,IAAA,CAAK,IAAI;MACd,OAAO/I,OAAA,CAAQkH,kBAAA,CAAmB,IAAI,CAAC;IACzC;IAGA,MAAMsJ,SAAA,GAAY,CAACtP,GAAA,CAAGxF,GAAA,CAAI4C,KAAA,CAAMxC,EAAE;IAGlC,MAAM2U,WAAA,GAAc,CAACvP,GAAA,CAAGxF,GAAA,CAAI4C,KAAA,CAAMzC,IAAI;IAItC,IAAI2U,SAAA,IAAaC,WAAA,EAAa;MAC5B,IAAIzV,KAAA,CAAMwC,MAAA,GAAS,KAAK2O,SAAA,EAAW;QACjC,KAAKA,SAAA,GAAYnR,KAAA,CAAMwC,MAAA;MACzB,OAAO;QACL,OAAOwC,OAAA,CAAQkH,kBAAA,CAAmB,IAAI,CAAC;MACzC;IACF;IAEA,MAAM;MAAExM,GAAA;MAAK2K,YAAA;MAAcyG,SAAA,EAAWa;IAAK,IAAI;IAC/C,MAAM;MAAE7Q,EAAA,EAAI6L,MAAA;MAAQ9L,IAAA,EAAM6U;IAAS,IAAI/D,IAAA;IACvC,IAAI;MAAE7Q,EAAA,EAAAgC,GAAA,GAAK6J,MAAA;MAAQ9L,IAAA,GAAO6U;IAAS,IAAIpS,KAAA;IAIvC,IAAImS,WAAA,IAAe,CAACD,SAAA,KAAc,CAACxV,KAAA,CAAMC,OAAA,IAAWiG,GAAA,CAAGxF,GAAA,CAAIoC,GAAE,IAAI;MAC/DA,GAAA,GAAKjC,IAAA;IACP;IAGA,IAAIb,KAAA,CAAMoB,OAAA,EAAS,CAAC0B,GAAA,EAAIjC,IAAI,IAAI,CAACA,IAAA,EAAMiC,GAAE;IAGzC,MAAM6S,cAAA,GAAiB,CAACrP,OAAA,CAAQzF,IAAA,EAAM6U,QAAQ;IAE9C,IAAIC,cAAA,EAAgB;MAClBhE,IAAA,CAAK9Q,IAAA,GAAOA,IAAA;IACd;IAGAA,IAAA,GAAO+F,cAAA,CAAc/F,IAAI;IAGzB,MAAM+U,YAAA,GAAe,CAACtP,OAAA,CAAQxD,GAAA,EAAI6J,MAAM;IAExC,IAAIiJ,YAAA,EAAc;MAChB,KAAK1B,MAAA,CAAOpR,GAAE;IAChB;IAGA,MAAM+S,UAAA,GAAanS,SAAA,CAAU1D,KAAA,CAAMc,EAAE;IAErC,MAAM;MAAEF,MAAA,EAAAoI;IAAO,IAAI2I,IAAA;IACnB,MAAM;MAAEnI,KAAA;MAAOX;IAAS,IAAIG,OAAA;IAG5B,IAAIwM,SAAA,IAAaC,WAAA,EAAa;MAC5BzM,OAAA,CAAOH,QAAA,GAAW;IACpB;IAIA,IAAI7I,KAAA,CAAMY,MAAA,IAAU,CAACiV,UAAA,EAAY;MAC/B9M,WAAA,CACEC,OAAA,EACAhK,QAAA,CAASgB,KAAA,CAAMY,MAAA,EAAQlB,GAAI;MAAA;MAE3BM,KAAA,CAAMY,MAAA,KAAWyJ,YAAA,CAAazJ,MAAA,GAC1B5B,QAAA,CAASqL,YAAA,CAAazJ,MAAA,EAAQlB,GAAI,IAClC,MACN;IACF;IAIA,IAAIuP,IAAA,GAAOxH,YAAA,CAAY,IAAI;IAC3B,IAAI,CAACwH,IAAA,IAAQ/I,GAAA,CAAGxF,GAAA,CAAIoC,GAAE,GAAG;MACvB,OAAOkC,OAAA,CAAQuH,iBAAA,CAAkB,MAAM,IAAI,CAAC;IAC9C;IAGA,MAAMtL,KAAA;IAAA;IAAA;IAAA;IAIJiF,GAAA,CAAGxF,GAAA,CAAIV,KAAA,CAAMiB,KAAK,IACdwU,WAAA,IAAe,CAACzV,KAAA,CAAMC,OAAA,GACtB,CAACiG,GAAA,CAAGxF,GAAA,CAAIG,IAAI,KAAKpB,SAAA,CAAUO,KAAA,CAAMiB,KAAA,EAAOvB,GAAG;IAGjD,MAAMT,KAAA,GAAQgC,KAAA,GAASJ,IAAA,GAAa,KAAKsL,GAAA,CAAI;IAG7C,MAAMkF,IAAA,GAAOnO,WAAA,CAAiBJ,GAAE;IAGhC,MAAMgT,YAAA,GAAe5P,GAAA,CAAG6P,GAAA,CAAI1E,IAAI,KAAKnL,GAAA,CAAG/C,GAAA,CAAIkO,IAAI,KAAKxK,iBAAA,CAAiBwK,IAAI;IAG1E,MAAMhQ,SAAA,GACJ,CAACwU,UAAA,KACA,CAACC,YAAA,IACArW,SAAA,CAAU4K,YAAA,CAAahJ,SAAA,IAAarB,KAAA,CAAMqB,SAAA,EAAW3B,GAAG;IAE5D,IAAIkW,YAAA,EAAc;MAChB,MAAMI,QAAA,GAAWrO,eAAA,CAAgB7E,GAAE;MACnC,IAAIkT,QAAA,KAAa/G,IAAA,CAAKrG,WAAA,EAAa;QACjC,IAAIvH,SAAA,EAAW;UACb4N,IAAA,GAAO,KAAKkF,IAAA,CAAK9C,IAAI;QACvB,OACE,MAAMhD,KAAA,2BAAA4H,MAAA,CACsBhH,IAAA,CAAKrG,WAAA,CAAYsN,IAAA,WAAAD,MAAA,CAAYD,QAAA,CAASE,IAAA,kCAClE;MACJ;IACF;IAGA,MAAMC,QAAA,GAAWlH,IAAA,CAAKrG,WAAA;IAKtB,IAAIwN,OAAA,GAAUnP,aAAA,CAAcnE,GAAE;IAC9B,IAAI0J,QAAA,GAAW;IAEf,IAAI,CAAC4J,OAAA,EAAS;MAEZ,MAAMC,eAAA,GAAkBpV,KAAA,IAAU,CAACsP,WAAA,CAAY,IAAI,KAAKoF,cAAA;MAIxD,IAAIC,YAAA,IAAgBS,eAAA,EAAiB;QACnC7J,QAAA,GAAWlG,OAAA,CAAQpD,WAAA,CAAYjE,KAAK,GAAGoS,IAAI;QAC3C+E,OAAA,GAAU,CAAC5J,QAAA;MACb;MAGA,IACG,CAAClG,OAAA,CAAQqL,IAAA,CAAKtQ,SAAA,EAAWA,SAAS,KAAK,CAACA,SAAA,IACzC,CAACiF,OAAA,CAAQ0C,OAAA,CAAOQ,KAAA,EAAOA,KAAK,KAC5B,CAAClD,OAAA,CAAQ0C,OAAA,CAAOH,QAAA,EAAUA,QAAQ,GAClC;QACAuN,OAAA,GAAU;MACZ;IACF;IAGA,IAAI5J,QAAA,IAAYgE,WAAA,CAAY,IAAI,GAAG;MAGjC,IAAImB,IAAA,CAAK9H,OAAA,IAAW,CAAC5I,KAAA,EAAO;QAC1BmV,OAAA,GAAU;MACZ,WAES,CAACA,OAAA,EAAS;QACjB,KAAKpC,KAAA,CAAMrH,MAAM;MACnB;IACF;IAEA,IAAI,CAACkJ,UAAA,EAAY;MAGf,IAAIO,OAAA,IAAWnP,aAAA,CAAc0F,MAAM,GAAG;QACpCgF,IAAA,CAAK7H,MAAA,GAASmF,IAAA,CAAK1H,UAAA,CAAW;QAC9BoK,IAAA,CAAK5H,QAAA,GAAW9C,aAAA,CAAcnE,GAAE,IAC5B,OACAqT,QAAA,IAAY7O,cAAA,GACV,CAAC,CAAC,IACFf,QAAA,CAAQ8K,IAAI;MACpB;MAEA,IAAIM,IAAA,CAAKtQ,SAAA,IAAaA,SAAA,EAAW;QAC/BsQ,IAAA,CAAKtQ,SAAA,GAAYA,SAAA;QAGjB,IAAI,CAACA,SAAA,IAAa,CAACJ,KAAA,EAAO;UACxB,KAAKkT,IAAA,CAAKxH,MAAM;QAClB;MACF;MAEA,IAAIyJ,OAAA,EAAS;QACX,MAAM;UAAExU;QAAO,IAAI+P,IAAA;QAGnBtL,KAAA,CAAKiQ,aAAA,EAAevG,IAAA,IAAQiF,aAAA,CAAc,MAAMhV,KAAA,EAAO+P,IAAI,CAAC;QAE5D,MAAM/D,MAAA,GAASO,iBAAA,CAAkB,MAAM4I,aAAA,CAAc,MAAMxI,MAAM,CAAC;QAClEhG,UAAA,CAAW,KAAKsK,aAAA,EAAejF,MAAM;QACrC,KAAKiF,aAAA,CAAchN,GAAA,CAAIe,OAAO;QAE9B,IAAI2M,IAAA,CAAK9H,OAAA,EACPzD,IAAA,CAAI6H,cAAA,CAAe,MAAM;UAEvB0D,IAAA,CAAK9H,OAAA,GAAU,CAAC5I,KAAA;UAGhBW,MAAA,aAAAA,MAAA,eAAAA,MAAA,CAASoK,MAAA,EAAQ,IAAI;UAIrB,IAAI/K,KAAA,EAAO;YACTjC,QAAA,CAASqL,YAAA,CAAazI,MAAA,EAAQoK,MAAM;UACtC,OAIK;YAAA,IAAAuK,aAAA;YACH,CAAAA,aAAA,GAAA5E,IAAA,CAAKnQ,OAAA,cAAA+U,aAAA,eAALA,aAAA,CAAAC,IAAA,CAAA7E,IAAA,EAAe3F,MAAA,EAAQ,IAAI;UAC7B;QACF,CAAC;MACL;IACF;IAEA,IAAI/K,KAAA,EAAO;MACT,KAAKkT,IAAA,CAAKlV,KAAK;IACjB;IAEA,IAAI4W,UAAA,EAAY;MACd7Q,OAAA,CAAQyH,QAAA,CAASzM,KAAA,CAAMc,EAAA,EAAId,KAAA,EAAO,KAAK+Q,MAAA,EAAQ,IAAI,CAAC;IACtD,WAGSqF,OAAA,EAAS;MAChB,KAAK1B,MAAA,CAAO;IACd,WAISlE,WAAA,CAAY,IAAI,KAAK,CAACoF,YAAA,EAAc;MAC3C,KAAK3E,aAAA,CAAchN,GAAA,CAAIe,OAAO;IAChC,OAGK;MACHA,OAAA,CAAQsH,aAAA,CAAcrN,KAAK,CAAC;IAC9B;EACF;EAAA;EAGUiV,OAAOjV,KAAA,EAA0B;IACzC,MAAM0S,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAI7R,KAAA,KAAU0S,IAAA,CAAK7Q,EAAA,EAAI;MACrB,IAAIsG,iBAAA,CAAkB,IAAI,GAAG;QAC3B,KAAKwI,OAAA,CAAQ;MACf;MACA+B,IAAA,CAAK7Q,EAAA,GAAK7B,KAAA;MACV,IAAImI,iBAAA,CAAkB,IAAI,GAAG;QAC3B,KAAKsI,OAAA,CAAQ;MACf;IACF;EACF;EAEUA,QAAA,EAAU;IAClB,IAAIX,QAAA,GAAW;IAEf,MAAM;MAAEjO,EAAA,EAAAgC;IAAG,IAAI,KAAKgO,SAAA;IACpB,IAAI7J,aAAA,CAAcnE,GAAE,GAAG;MACrBoE,gBAAA,CAAiBpE,GAAA,EAAI,IAAI;MACzB,IAAI4L,YAAA,CAAa5L,GAAE,GAAG;QACpBiM,QAAA,GAAWjM,GAAA,CAAGiM,QAAA,GAAW;MAC3B;IACF;IAEA,KAAKA,QAAA,GAAWA,QAAA;EAClB;EAEUa,QAAA,EAAU;IAClB,MAAM;MAAE9O,EAAA,EAAAgC;IAAG,IAAI,KAAKgO,SAAA;IACpB,IAAI7J,aAAA,CAAcnE,GAAE,GAAG;MACrBqE,mBAAA,CAAoBrE,GAAA,EAAI,IAAI;IAC9B;EACF;EAAA;AAAA;AAAA;AAAA;EAMUqR,KAAKsC,GAAA,EAA2D;IAAA,IAAnC3G,IAAA,GAAA3Q,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAO;IAC5C,MAAMF,KAAA,GAAQ2H,cAAA,CAAc6P,GAAG;IAC/B,IAAI,CAACvQ,GAAA,CAAGxF,GAAA,CAAIzB,KAAK,GAAG;MAClB,MAAMyX,OAAA,GAAUjP,YAAA,CAAY,IAAI;MAChC,IAAI,CAACiP,OAAA,IAAW,CAACpQ,OAAA,CAAQrH,KAAA,EAAOyX,OAAA,CAAQxH,QAAA,CAAS,CAAC,GAAG;QAEnD,MAAM8G,QAAA,GAAWrO,eAAA,CAAgB1I,KAAK;QACtC,IAAI,CAACyX,OAAA,IAAWA,OAAA,CAAQ9N,WAAA,IAAeoN,QAAA,EAAU;UAC/CtO,WAAA,CAAY,MAAMsO,QAAA,CAASW,MAAA,CAAO1X,KAAK,CAAC;QAC1C,OAAO;UACLyX,OAAA,CAAQ9C,QAAA,CAAS3U,KAAK;QACxB;QAEA,IAAIyX,OAAA,EAAS;UACXtQ,IAAA,CAAI6H,cAAA,CAAe,MAAM;YACvB,KAAK4B,SAAA,CAAU5Q,KAAA,EAAO6Q,IAAI;UAC5B,CAAC;QACH;MACF;IACF;IACA,OAAOrI,YAAA,CAAY,IAAI;EACzB;EAEU6M,SAAA,EAAW;IACnB,MAAM3C,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAI,CAACa,IAAA,CAAK9H,OAAA,EAAS;MACjB8H,IAAA,CAAK9H,OAAA,GAAU;MACfoL,SAAA,CACE,MACA,WACA1I,iBAAA,CAAkB,MAAM4I,aAAA,CAAc,MAAMxD,IAAA,CAAK7Q,EAAE,CAAC,GACpD,IACF;IACF;EACF;EAEU+O,UAAU5Q,KAAA,EAAU6Q,IAAA,EAAgB;IAC5C,IAAI,CAACA,IAAA,EAAM;MACT,KAAKwE,QAAA,CAAS;MACdtV,QAAA,CAAS,KAAK8R,SAAA,CAAUrP,QAAA,EAAUxC,KAAA,EAAO,IAAI;IAC/C;IACAD,QAAA,CAAS,KAAKqL,YAAA,CAAa5I,QAAA,EAAUxC,KAAA,EAAO,IAAI;IAChD,MAAM4Q,SAAA,CAAU5Q,KAAA,EAAO6Q,IAAI;EAC7B;EAAA;EAAA;EAAA;EAKU4E,OAAA,EAAS;IACjB,MAAM/C,IAAA,GAAO,KAAKb,SAAA;IAGlBrJ,YAAA,CAAY,IAAI,EAAGxG,KAAA,CAAM2F,cAAA,CAAc+K,IAAA,CAAK7Q,EAAE,CAAC;IAG/C,IAAI,CAAC6Q,IAAA,CAAKtQ,SAAA,EAAW;MACnBsQ,IAAA,CAAK3H,UAAA,GAAa2H,IAAA,CAAK7H,MAAA,CAAO1G,GAAA,CAAI6L,IAAA,IAAQA,IAAA,CAAK8C,YAAY;IAC7D;IAEA,IAAI,CAACvB,WAAA,CAAY,IAAI,GAAG;MACtBE,YAAA,CAAa,MAAM,IAAI;MACvB,IAAI,CAACD,QAAA,CAAS,IAAI,GAAG;QACnB,KAAK2E,OAAA,CAAQ;MACf;IACF;EACF;EAEUA,QAAA,EAAU;IAElB,IAAItO,EAAA,CAAEmE,aAAA,EAAe;MACnB,KAAKoJ,MAAA,CAAO;IACd,OAAO;MACL3N,UAAA,CAAU7B,KAAA,CAAM,IAAI;IACtB;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUmP,MAAM3C,IAAA,EAAYlQ,MAAA,EAAkB;IAC5C,IAAIqP,WAAA,CAAY,IAAI,GAAG;MACrBE,YAAA,CAAa,MAAM,KAAK;MAExB,MAAMiB,IAAA,GAAO,KAAKb,SAAA;MAClBzK,KAAA,CAAKsL,IAAA,CAAK7H,MAAA,EAAQmF,IAAA,IAAQ;QACxBA,IAAA,CAAK6C,IAAA,GAAO;MACd,CAAC;MAKD,IAAIH,IAAA,CAAK5H,QAAA,EAAU;QACjB4H,IAAA,CAAKlQ,QAAA,GAAWkQ,IAAA,CAAKjQ,OAAA,GAAUiQ,IAAA,CAAKhQ,QAAA,GAAW;MACjD;MAEAqF,mBAAA,CAAmB,MAAM;QACvB+I,IAAA,EAAM;QACNC,MAAA,EAAQ;MACV,CAAC;MAED,MAAMhE,MAAA,GAAS7K,MAAA,GACX+K,kBAAA,CAAmB,KAAKC,GAAA,CAAI,CAAC,IAC7BI,iBAAA,CAAkB,KAAKJ,GAAA,CAAI,GAAGgJ,aAAA,CAAc,MAAM9D,IAAA,aAAAA,IAAA,cAAAA,IAAA,GAAQM,IAAA,CAAK7Q,EAAE,CAAC;MAEtE6F,UAAA,CAAW,KAAKsK,aAAA,EAAejF,MAAM;MACrC,IAAI2F,IAAA,CAAK9H,OAAA,EAAS;QAChB8H,IAAA,CAAK9H,OAAA,GAAU;QACfoL,SAAA,CAAU,MAAM,UAAUjJ,MAAA,EAAQ,IAAI;MACxC;IACF;EACF;AACF;AAGA,SAASmJ,cAAiBtJ,MAAA,EAAwB/I,GAAA,EAAuB;EACvE,MAAMuO,IAAA,GAAOnO,WAAA,CAAYJ,GAAE;EAC3B,MAAM7D,KAAA,GAAQiE,WAAA,CAAY2I,MAAA,CAAOM,GAAA,CAAI,CAAC;EACtC,OAAO7F,OAAA,CAAQrH,KAAA,EAAOoS,IAAI;AAC5B;AAEO,SAASkE,iBACdvV,KAAA,EAGe;EAAA,IAFfgB,IAAA,GAAA7B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAOa,KAAA,CAAMgB,IAAA;EAAA,IACb8B,GAAA,GAAA3D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MAAKa,KAAA,CAAMc,EAAA;EAEX,MAAM8V,OAAA,GAAU5X,QAAA,CAASgC,IAAI;EAC7B,IAAI4V,OAAA,EAAS;IACX,MAAMC,SAAA,GAAYD,OAAA,KAAY,QAAQ/T,OAAA,CAAQ+T,OAAO;IACrD,MAAMxV,OAAA,IAAWyV,SAAA,IAAa7W,KAAA,EAAOoB,OAAA;IACrC,MAAMH,KAAA,GAAQ,CAAC4V,SAAA,IAAaA,SAAA,CAAU5V,KAAA;IACtC,OAAO6V,YAAA,CAAA7T,aAAA,CAAAA,aAAA,KACFjD,KAAA;MACHgB,IAAA;MAAA;MAGAf,OAAA,EAAS;MAAA;MAGTiB,KAAA,EAAO;MAAA;MAAA;MAAA;MAKPJ,EAAA,EAAI,CAACM,OAAA,IAAWsC,SAAA,CAAUZ,GAAE,IAAIA,GAAA,GAAK;MAAA;MAGrCjC,IAAA,EAAMI,KAAA,GAAQjB,KAAA,CAAMa,IAAA,GAAO;MAC3BI;IAAA,GAIG4V,SAAA,CACJ;EACH;AACF;AASO,SAASC,aAAa9W,KAAA,EAAY;EACvC,MAAM;IAAEc,EAAA,EAAAgC,GAAA;IAAIjC;EAAK,IAAKb,KAAA,GAAQ6C,OAAA,CAAQ7C,KAAK;EAG3C,MAAMM,IAAA,GAAO,mBAAI0Q,GAAA,CAAY;EAE7B,IAAI9K,GAAA,CAAGpG,GAAA,CAAIgD,GAAE,GAAGiU,WAAA,CAAYjU,GAAA,EAAIxC,IAAI;EACpC,IAAI4F,GAAA,CAAGpG,GAAA,CAAIe,IAAI,GAAGkW,WAAA,CAAYlW,IAAA,EAAMP,IAAI;EAGxCN,KAAA,CAAMM,IAAA,GAAOA,IAAA,CAAK0W,IAAA,GAAO1X,KAAA,CAAMuB,IAAA,CAAKP,IAAI,IAAI;EAE5C,OAAON,KAAA;AACT;AAKO,SAASiX,cAAcjX,KAAA,EAAY;EACxC,MAAMoF,OAAA,GAAS0R,YAAA,CAAa9W,KAAK;EACjC,IAAIkG,GAAA,CAAGxF,GAAA,CAAI0E,OAAA,CAAOnF,OAAO,GAAG;IAC1BmF,OAAA,CAAOnF,OAAA,GAAUE,eAAA,CAAgBiF,OAAM;EACzC;EACA,OAAOA,OAAA;AACT;AAGA,SAAS2R,YAAYjN,MAAA,EAAgBxJ,IAAA,EAAmB;EACtDkG,SAAA,CAASsD,MAAA,EAAQ,CAAC7K,KAAA,EAAOS,GAAA,KAAQT,KAAA,IAAS,QAAQqB,IAAA,CAAK2D,GAAA,CAAIvE,GAAU,CAAC;AACxE;AAGA,IAAM4W,aAAA,GAAgB,CACpB,WACA,UACA,YACA,WACA,WACF;AAEA,SAAStB,cACPnJ,MAAA,EACA7L,KAAA,EACA+P,IAAA,EACA;EACAlE,MAAA,CAAOiF,SAAA,CAAUf,IAAI,IACnB/P,KAAA,CAAM+P,IAAI,MAAMhQ,cAAA,CAAeC,KAAA,EAAO+P,IAAI,IACtCnQ,WAAA,CAAiBI,KAAA,CAAM+P,IAAI,GAAGlE,MAAA,CAAOnM,GAAG,IACxC;AACR;AAOA,SAASuV,UACPpJ,MAAA,EACAkE,IAAA,EAEA;EAAA,IAAAmH,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,oBAAA;EAAA,SAAAC,KAAA,GAAAnY,SAAA,CAAAC,MAAA,EADGC,IAAA,OAAAC,KAAA,CAAAgY,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAAlY,IAAA,CAAAkY,KAAA,QAAApY,SAAA,CAAAoY,KAAA;EAAA;EAEH,CAAAL,qBAAA,IAAAC,iBAAA,GAAAtL,MAAA,CAAOiF,SAAA,EAAUf,IAAI,eAAAmH,qBAAA,eAArBA,qBAAA,CAAAV,IAAA,CAAAW,iBAAA,EAAyB,GAAI9X,IAAmB;EAChD,CAAA+X,qBAAA,IAAAC,oBAAA,GAAAxL,MAAA,CAAOxB,YAAA,EAAa0F,IAAI,eAAAqH,qBAAA,eAAxBA,qBAAA,CAAAZ,IAAA,CAAAa,oBAAA,EAA4B,GAAIhY,IAAmB;AACrD;;;ASnnCA,SACEZ,EAAA,IAAA+Y,GAAA,EACArR,GAAA,IAAAsR,IAAA,EACAlZ,IAAA,IAAAmZ,KAAA,EACArL,IAAA,EACAZ,KAAA,IAAAkM,MAAA,EACAjZ,OAAA,IAAAkZ,QAAA,EACAjZ,QAAA,IAAAkZ,SAAA,EACAlR,UAAA,IAAAmR,WAAA,EACA5Q,gBAAA,IAAA6Q,iBAAA,QAEK;AAuBP,IAAMC,cAAA,GAAiB,CAAC,WAAW,YAAY,QAAQ;AAEvD,IAAIC,OAAA,GAAS;AAWN,IAAMC,UAAA,GAAN,MAAgD;EA2DrDtP,YACE5I,KAAA,EACAmY,MAAA,EACA;IA7DF,KAAStJ,EAAA,GAAKoJ,OAAA;IAGd;IAAA,KAAAG,OAAA,GAA+B,CAAC;IAGhC;IAAA,KAAAzT,KAAA,GAAgC,EAAC;IAejC;IAAA,KAAU0T,YAAA,GAAe;IAGzB;IAAA,KAAUC,OAAA,GAAU,mBAAItH,GAAA,CAAgB;IAGxC;IAAA,KAAUuH,QAAA,GAAW,mBAAIvH,GAAA,CAAgB;IAGzC;IAAA,KAAUwH,QAAA,GAAW;IAKrB;IAAA,KAAUzH,MAAA,GAA8B;MACtCpG,MAAA,EAAQ;MACRS,UAAA,EAAY,mBAAI4F,GAAA,CAAI;MACpBpG,WAAA,EAAa,mBAAIoG,GAAA,CAAI;MACrBlG,QAAA,EAAU,mBAAIkG,GAAA,CAAI;IACpB;IAGA;IAAA,KAAUyH,OAAA,GAAU;MAClBjX,OAAA,EAAS,mBAAIkX,GAAA,CAGX;MACFjX,QAAA,EAAU,mBAAIiX,GAAA,CAGZ;MACF9W,MAAA,EAAQ,mBAAI8W,GAAA,CAGV;IACJ;IAME,KAAKC,QAAA,GAAW,KAAKA,QAAA,CAAS3K,IAAA,CAAK,IAAI;IACvC,IAAImK,MAAA,EAAO;MACT,KAAKS,MAAA,GAAST,MAAA;IAChB;IACA,IAAInY,KAAA,EAAO;MACT,KAAK6E,KAAA,CAAA5B,aAAA;QAAQhD,OAAA,EAAS;MAAA,GAASD,KAAA,CAAO;IACxC;EACF;EAAA;AAAA;AAAA;AAAA;EAMA,IAAI8P,KAAA,EAAO;IACT,OACE,CAAC,KAAKiB,MAAA,CAAOrE,OAAA,IACblM,MAAA,CAAOsJ,MAAA,CAAO,KAAKsO,OAA8B,EAAEhM,KAAA,CAAMyM,MAAA,IAAU;MACjE,OAAOA,MAAA,CAAO/I,IAAA,IAAQ,CAAC+I,MAAA,CAAOrH,SAAA,IAAa,CAACqH,MAAA,CAAOpI,QAAA;IACrD,CAAC;EAEL;EAEA,IAAIvC,KAAA,EAAO;IACT,OAAO,KAAK4K,KAAA;EACd;EAEA,IAAI5K,KAAKA,IAAA,EAAM;IACb,KAAK4K,KAAA,GAAQ5K,IAAA;EACf;EAAA;EAGA/B,IAAA,EAA4B;IAC1B,MAAMrC,MAAA,GAAc,CAAC;IACrB,KAAKvL,IAAA,CAAK,CAACsa,MAAA,EAAQnZ,GAAA,KAASoK,MAAA,CAAOpK,GAAG,IAAImZ,MAAA,CAAO1M,GAAA,CAAI,CAAE;IACvD,OAAOrC,MAAA;EACT;EAAA;EAGAmK,IAAInK,MAAA,EAAwB;IAC1B,WAAWpK,GAAA,IAAOoK,MAAA,EAAQ;MACxB,MAAM7K,KAAA,GAAQ6K,MAAA,CAAOpK,GAAG;MACxB,IAAI,CAAC8X,GAAA,CAAG9W,GAAA,CAAIzB,KAAK,GAAG;QAClB,KAAKmZ,OAAA,CAAQ1Y,GAAG,EAAEuU,GAAA,CAAIhV,KAAK;MAC7B;IACF;EACF;EAAA;EAGAmD,OAAOpC,KAAA,EAAwC;IAC7C,IAAIA,KAAA,EAAO;MACT,KAAK2E,KAAA,CAAMU,IAAA,CAAKyR,YAAA,CAAa9W,KAAK,CAAC;IACrC;IACA,OAAO;EACT;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA6E,MAAM7E,KAAA,EAAsE;IAC1E,IAAI;MAAE2E;IAAM,IAAI;IAChB,IAAI3E,KAAA,EAAO;MACT2E,KAAA,GAAQiT,QAAA,CAAa5X,KAAK,EAAEoD,GAAA,CAAI0T,YAAY;IAC9C,OAAO;MACL,KAAKnS,KAAA,GAAQ,EAAC;IAChB;IAEA,IAAI,KAAKiU,MAAA,EAAQ;MACf,OAAO,KAAKA,MAAA,CAAO,MAAMjU,KAAK;IAChC;IAEAoU,WAAA,CAAY,MAAMpU,KAAK;IACvB,OAAOqU,gBAAA,CAAiB,MAAMrU,KAAK;EACrC;EAAA;EAeAoJ,KAAK0I,GAAA,EAAmCnW,IAAA,EAA0B;IAChE,IAAImW,GAAA,KAAQ,CAAC,CAACA,GAAA,EAAK;MACjBnW,IAAA,GAAOmW,GAAA;IACT;IACA,IAAInW,IAAA,EAAM;MACR,MAAM8X,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQtX,IAAI,GAAeZ,GAAA,IAAO0Y,OAAA,CAAQ1Y,GAAG,EAAEqO,IAAA,CAAK,CAAC,CAAC0I,GAAG,CAAC;IACjE,OAAO;MACL9I,SAAA,CAAU,KAAKoD,MAAA,EAAQ,KAAKsH,YAAY;MACxC,KAAK9Z,IAAA,CAAKsa,MAAA,IAAUA,MAAA,CAAO9K,IAAA,CAAK,CAAC,CAAC0I,GAAG,CAAC;IACxC;IACA,OAAO;EACT;EAAA;EAGAvV,MAAMZ,IAAA,EAA0B;IAC9B,IAAIkX,GAAA,CAAG9W,GAAA,CAAIJ,IAAI,GAAG;MAChB,KAAKuE,KAAA,CAAM;QAAE3D,KAAA,EAAO;MAAK,CAAC;IAC5B,OAAO;MACL,MAAMkX,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQtX,IAAI,GAAeZ,GAAA,IAAO0Y,OAAA,CAAQ1Y,GAAG,EAAEwB,KAAA,CAAM,CAAC;IAC7D;IACA,OAAO;EACT;EAAA;EAGA2J,OAAOvK,IAAA,EAA0B;IAC/B,IAAIkX,GAAA,CAAG9W,GAAA,CAAIJ,IAAI,GAAG;MAChB,KAAKuE,KAAA,CAAM;QAAE3D,KAAA,EAAO;MAAM,CAAC;IAC7B,OAAO;MACL,MAAMkX,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQtX,IAAI,GAAeZ,GAAA,IAAO0Y,OAAA,CAAQ1Y,GAAG,EAAEmL,MAAA,CAAO,CAAC;IAC9D;IACA,OAAO;EACT;EAAA;EAGAtM,KAAK0a,QAAA,EAAsD;IACzDpB,SAAA,CAAS,KAAKO,OAAA,EAASa,QAAe;EACxC;EAAA;EAGUN,SAAA,EAAW;IACnB,MAAM;MAAEnX,OAAA;MAASC,QAAA;MAAUG;IAAO,IAAI,KAAK6W,OAAA;IAE3C,MAAM9H,MAAA,GAAS,KAAK2H,OAAA,CAAQtB,IAAA,GAAO;IACnC,MAAMnN,OAAA,GAAU,KAAK0O,QAAA,CAASvB,IAAA,GAAO;IAErC,IAAKrG,MAAA,IAAU,CAAC,KAAK6H,QAAA,IAAc3O,OAAA,IAAW,CAAC,KAAK2O,QAAA,EAAW;MAC7D,KAAKA,QAAA,GAAW;MAChBb,MAAA,CAAMnW,OAAA,EAAS0X,KAAA,IAAuB;QAAA,IAAtB,CAACC,QAAA,EAASnN,MAAM,IAAAkN,KAAA;QAC9BlN,MAAA,CAAO/M,KAAA,GAAQ,KAAKkN,GAAA,CAAI;QACxBgN,QAAA,CAAQnN,MAAA,EAAQ,MAAM,KAAK8M,KAAK;MAClC,CAAC;IACH;IAEA,MAAMhJ,IAAA,GAAO,CAACa,MAAA,IAAU,KAAK6H,QAAA;IAC7B,MAAM1O,MAAA,GAASD,OAAA,IAAYiG,IAAA,IAAQlO,MAAA,CAAOoV,IAAA,GAAQ,KAAK7K,GAAA,CAAI,IAAI;IAE/D,IAAItC,OAAA,IAAWpI,QAAA,CAASuV,IAAA,EAAM;MAC5BW,MAAA,CAAMlW,QAAA,EAAU2X,KAAA,IAAwB;QAAA,IAAvB,CAACC,SAAA,EAAUrN,MAAM,IAAAoN,KAAA;QAChCpN,MAAA,CAAO/M,KAAA,GAAQ6K,MAAA;QACfuP,SAAA,CAASrN,MAAA,EAAQ,MAAM,KAAK8M,KAAK;MACnC,CAAC;IACH;IAGA,IAAIhJ,IAAA,EAAM;MACR,KAAK0I,QAAA,GAAW;MAChBb,MAAA,CAAM/V,MAAA,EAAQ0X,KAAA,IAAsB;QAAA,IAArB,CAACC,OAAA,EAAQvN,MAAM,IAAAsN,KAAA;QAC5BtN,MAAA,CAAO/M,KAAA,GAAQ6K,MAAA;QACfyP,OAAA,CAAOvN,MAAA,EAAQ,MAAM,KAAK8M,KAAK;MACjC,CAAC;IACH;EACF;EAAA;EAGAtE,cAAcC,KAAA,EAAyB;IACrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,KAAKwI,QAAA,CAAStU,GAAA,CAAIwQ,KAAA,CAAMzE,MAAM;MAC9B,IAAI,CAACyE,KAAA,CAAM3E,IAAA,EAAM;QACf,KAAKwI,OAAA,CAAQrU,GAAA,CAAIwQ,KAAA,CAAMzE,MAAM;MAC/B;IACF,WAAWyE,KAAA,CAAM1E,IAAA,IAAQ,QAAQ;MAC/B,KAAKuI,OAAA,CAAQxU,MAAA,CAAO2Q,KAAA,CAAMzE,MAAM;IAClC,OAEK;IACLyH,IAAA,CAAI+B,OAAA,CAAQ,KAAKb,QAAQ;EAC3B;AACF;AAKO,SAASK,iBACdpV,IAAA,EACAe,KAAA,EACA;EACA,OAAOI,OAAA,CAAQO,GAAA,CAAIX,KAAA,CAAMvB,GAAA,CAAIpD,KAAA,IAASyZ,WAAA,CAAY7V,IAAA,EAAM5D,KAAK,CAAC,CAAC,EAAEmF,IAAA,CAC/D2G,OAAA,IAAWF,iBAAA,CAAkBhI,IAAA,EAAMkI,OAAO,CAC5C;AACF;AAWA,eAAsB2N,YACpB7V,IAAA,EACA5D,KAAA,EACA6U,MAAA,EACa;EACb,MAAM;IAAEvU,IAAA;IAAMQ,EAAA,EAAAgC,GAAA;IAAIjC,IAAA;IAAMG,IAAA;IAAMY,MAAA;IAAQC;EAAU,IAAI7B,KAAA;EACpD,MAAMS,SAAA,GAAW+W,GAAA,CAAG1X,GAAA,CAAIE,KAAA,CAAMC,OAAO,KAAKD,KAAA,CAAMC,OAAA;EAIhD,IAAIe,IAAA,EAAM;IACRhB,KAAA,CAAMgB,IAAA,GAAO;EACf;EAGA,IAAI8B,GAAA,KAAO,OAAO9C,KAAA,CAAMc,EAAA,GAAK;EAC7B,IAAID,IAAA,KAAS,OAAOb,KAAA,CAAMa,IAAA,GAAO;EAEjC,MAAM6L,OAAA,GAAU8K,GAAA,CAAGrU,GAAA,CAAIL,GAAE,KAAK0U,GAAA,CAAGhY,GAAA,CAAIsD,GAAE,IAAIA,GAAA,GAAK;EAChD,IAAI4J,OAAA,EAAS;IACX1M,KAAA,CAAMc,EAAA,GAAK;IACXd,KAAA,CAAM4B,MAAA,GAAS;IACf,IAAInB,SAAA,EAAU;MACZA,SAAA,CAASmB,MAAA,GAAS;IACpB;EACF,OAIK;IACH8V,KAAA,CAAKM,cAAA,EAAgBtY,GAAA,IAAO;MAC1B,MAAMga,OAAA,GAAe1Z,KAAA,CAAMN,GAAG;MAC9B,IAAI8X,GAAA,CAAGhY,GAAA,CAAIka,OAAO,GAAG;QACnB,MAAM/U,KAAA,GAAQf,IAAA,CAAK,SAAS,EAAElE,GAAG;QACjCM,KAAA,CAAMN,GAAG,IAAKia,KAAA,IAA8C;UAAA,IAA7C;YAAEnN,QAAA;YAAUP;UAAU,IAAA0N,KAAA;UACnC,MAAM9L,OAAA,GAASlJ,KAAA,CAAMwH,GAAA,CAAIuN,OAAO;UAChC,IAAI7L,OAAA,EAAQ;YACV,IAAI,CAACrB,QAAA,EAAUqB,OAAA,CAAOrB,QAAA,GAAW;YACjC,IAAIP,SAAA,EAAW4B,OAAA,CAAO5B,SAAA,GAAY;UACpC,OAAO;YAELtH,KAAA,CAAMsP,GAAA,CAAIyF,OAAA,EAAS;cACjBza,KAAA,EAAO;cACPuN,QAAA,EAAUA,QAAA,IAAY;cACtBP,SAAA,EAAWA,SAAA,IAAa;YAC1B,CAAC;UACH;QACF;QAGA,IAAIxL,SAAA,EAAU;UACZA,SAAA,CAASf,GAAG,IAAIM,KAAA,CAAMN,GAAG;QAC3B;MACF;IACF,CAAC;EACH;EAEA,MAAM4K,KAAA,GAAQ1G,IAAA,CAAK,QAAQ;EAG3B,IAAI5D,KAAA,CAAMkB,KAAA,KAAU,CAACoJ,KAAA,CAAMK,MAAA,EAAQ;IACjCL,KAAA,CAAMK,MAAA,GAAS3K,KAAA,CAAMkB,KAAA;IACrB4W,WAAA,CAAW9X,KAAA,CAAMkB,KAAA,GAAQoJ,KAAA,CAAMc,UAAA,GAAad,KAAA,CAAMM,WAAW;EAC/D,WAESN,KAAA,CAAMK,MAAA,EAAQ;IACrB3K,KAAA,CAAMkB,KAAA,GAAQ;EAChB;EAEA,MAAM0Y,QAAA,IAA2BtZ,IAAA,IAAQE,MAAA,CAAOF,IAAA,CAAKsD,IAAA,CAAKwU,OAAO,GAAGhV,GAAA,CAAI1D,GAAA,IACtEkE,IAAA,CAAKwU,OAAA,CAAQ1Y,GAAG,EAAGmF,KAAA,CAAM7E,KAAY,CACvC;EAEA,MAAMmB,MAAA,GACJnB,KAAA,CAAMmB,MAAA,KAAW,QAAQpB,cAAA,CAAeC,KAAA,EAAO,QAAQ,MAAM;EAE/D,IAAI0M,OAAA,IAAYvL,MAAA,IAAUmJ,KAAA,CAAMwC,OAAA,EAAU;IACxC8M,QAAA,CAASvU,IAAA,CACP8E,aAAA,CAAc,EAAEvG,IAAA,CAAK,cAAc,GAAG;MACpC5D,KAAA;MACAsK,KAAA;MACAC,OAAA,EAAS;QACPrJ,KAAA,EAAOmL,IAAA;QACPxB,MAAA,EAAQwB,IAAA;QACRxH,MAAM+I,MAAA,EAAO5I,OAAA,EAAS;UACpB,IAAI7D,MAAA,EAAQ;YACVwM,SAAA,CAAUrD,KAAA,EAAO1G,IAAA,CAAK,cAAc,CAAC;YACrCoB,OAAA,CAAQkH,kBAAA,CAAmBtI,IAAI,CAAC;UAClC,OAAO;YACLgK,MAAA,CAAMhM,MAAA,GAASA,MAAA;YACfoD,OAAA,CACEyH,QAAA,CACEC,OAAA,EACAkB,MAAA,EACAtD,KAAA,EACA1G,IACF,CACF;UACF;QACF;MACF;IACF,CAAC,CACH;EACF;EAIA,IAAI0G,KAAA,CAAMK,MAAA,EAAQ;IAGhB,MAAM,IAAI5F,OAAA,CAAc8F,MAAA,IAAU;MAChCP,KAAA,CAAMM,WAAA,CAAY3G,GAAA,CAAI4G,MAAM;IAC9B,CAAC;EACH;EAEA,MAAMmB,MAAA,GAASJ,iBAAA,CAAuBhI,IAAA,EAAM,MAAMmB,OAAA,CAAQO,GAAA,CAAIsU,QAAQ,CAAC;EACvE,IAAI5Y,IAAA,IAAQgL,MAAA,CAAOQ,QAAA,IAAY,EAAEqI,MAAA,IAAU7I,MAAA,CAAOK,IAAA,GAAO;IACvD,MAAMiJ,SAAA,GAAYC,gBAAA,CAAiBvV,KAAA,EAAOgB,IAAA,EAAM8B,GAAE;IAClD,IAAIwS,SAAA,EAAW;MACbyD,WAAA,CAAYnV,IAAA,EAAM,CAAC0R,SAAS,CAAC;MAC7B,OAAOmE,WAAA,CAAY7V,IAAA,EAAM0R,SAAA,EAAW,IAAI;IAC1C;EACF;EACA,IAAIzT,SAAA,EAAW;IACb4V,IAAA,CAAIxJ,cAAA,CAAe,MAAMpM,SAAA,CAAUmK,MAAA,EAAQpI,IAAA,EAAMA,IAAA,CAAKsK,IAAI,CAAC;EAC7D;EACA,OAAOlC,MAAA;AACT;AAUO,SAAS6N,WACdjW,IAAA,EACA5D,KAAA,EACA;EACA,MAAMoY,OAAA,GAAAnV,aAAA,KAAeW,IAAA,CAAKwU,OAAA,CAAQ;EAClC,IAAIpY,KAAA,EAAO;IACT0X,KAAA,CAAKE,QAAA,CAAQ5X,KAAK,GAAI4N,MAAA,IAAe;MACnC,IAAI4J,GAAA,CAAG9W,GAAA,CAAIkN,MAAA,CAAMtN,IAAI,GAAG;QACtBsN,MAAA,GAAQkJ,YAAA,CAAalJ,MAAK;MAC5B;MACA,IAAI,CAAC4J,GAAA,CAAG1X,GAAA,CAAI8N,MAAA,CAAM9M,EAAE,GAAG;QAErB8M,MAAA,GAAA3K,aAAA,CAAAA,aAAA,KAAa2K,MAAA;UAAO9M,EAAA,EAAI;QAAA,EAAU;MACpC;MACAgZ,cAAA,CAAe1B,OAAA,EAAgBxK,MAAA,EAAOlO,GAAA,IAAO;QAC3C,OAAOqa,YAAA,CAAara,GAAG;MACzB,CAAC;IACH,CAAC;EACH;EACAsa,UAAA,CAAWpW,IAAA,EAAMwU,OAAO;EACxB,OAAOA,OAAA;AACT;AAMO,SAAS4B,WACdpW,IAAA,EACAwU,OAAA,EACA;EACAP,SAAA,CAASO,OAAA,EAAS,CAACS,MAAA,EAAQnZ,GAAA,KAAQ;IACjC,IAAI,CAACkE,IAAA,CAAKwU,OAAA,CAAQ1Y,GAAG,GAAG;MACtBkE,IAAA,CAAKwU,OAAA,CAAQ1Y,GAAG,IAAImZ,MAAA;MACpBd,iBAAA,CAAiBc,MAAA,EAAQjV,IAAI;IAC/B;EACF,CAAC;AACH;AAEA,SAASmW,aAAara,GAAA,EAAaua,QAAA,EAA4C;EAC7E,MAAMpB,MAAA,GAAS,IAAIhI,WAAA,CAAY;EAC/BgI,MAAA,CAAOnZ,GAAA,GAAMA,GAAA;EACb,IAAIua,QAAA,EAAU;IACZlC,iBAAA,CAAiBc,MAAA,EAAQoB,QAAQ;EACnC;EACA,OAAOpB,MAAA;AACT;AAQA,SAASiB,eACP1B,OAAA,EACApY,KAAA,EACA2W,MAAA,EACA;EACA,IAAI3W,KAAA,CAAMM,IAAA,EAAM;IACdoX,KAAA,CAAK1X,KAAA,CAAMM,IAAA,EAAMZ,GAAA,IAAO;MACtB,MAAMmZ,MAAA,GAAST,OAAA,CAAQ1Y,GAAG,MAAM0Y,OAAA,CAAQ1Y,GAAG,IAAIiX,MAAA,CAAOjX,GAAG;MACzDmZ,MAAA,CAAO,cAAc,EAAE7Y,KAAK;IAC9B,CAAC;EACH;AACF;AAQA,SAAS+Y,YAAYnV,IAAA,EAAuBe,KAAA,EAAkC;EAC5E+S,KAAA,CAAK/S,KAAA,EAAO3E,KAAA,IAAS;IACnB8Z,cAAA,CAAelW,IAAA,CAAKwU,OAAA,EAASpY,KAAA,EAAON,GAAA,IAAO;MACzC,OAAOqa,YAAA,CAAara,GAAA,EAAKkE,IAAI;IAC/B,CAAC;EACH,CAAC;AACH;;;ACnhBA,YAAYsW,KAAA,MAAW;AACvB,SAAS1U,UAAA,QAAqC;AAC9C,SAAS2U,UAAA,QAAkB;AAapB,IAAMC,aAAA,GAAgBC,KAAA,IAGW;EAAA,IAHV;MAC5B/X;IAEF,IAAA+X,KAAA;IADKra,KAAA,GAAAsa,wBAAA,CAAAD,KAAA,EAAAE,SAAA;EAEH,MAAMC,SAAA,GAAYhV,UAAA,CAAWiV,GAAG;EAGhC,MAAMvZ,KAAA,GAAQlB,KAAA,CAAMkB,KAAA,IAAS,CAAC,CAACsZ,SAAA,CAAUtZ,KAAA;IACvCG,SAAA,GAAYrB,KAAA,CAAMqB,SAAA,IAAa,CAAC,CAACmZ,SAAA,CAAUnZ,SAAA;EAG7CrB,KAAA,GAAQma,UAAA,CAAW,OAAO;IAAEjZ,KAAA;IAAOG;EAAU,IAAI,CAACH,KAAA,EAAOG,SAAS,CAAC;EAEnE,MAAM;IAAEqZ;EAAS,IAAID,GAAA;EACrB,OAAO,eAAAP,KAAA,CAAAS,aAAA,CAACD,QAAA;IAASzb,KAAA,EAAOe;EAAA,GAAQsC,QAAS;AAC3C;AAEA,IAAMmY,GAAA,GAAMG,WAAA,CAAYR,aAAA,EAAe,CAAC,CAAkB;AAG1DA,aAAA,CAAcM,QAAA,GAAWD,GAAA,CAAIC,QAAA;AAC7BN,aAAA,CAAcS,QAAA,GAAWJ,GAAA,CAAII,QAAA;AAG7B,SAASD,YAAe/O,MAAA,EAAaiP,IAAA,EAA2B;EAC9Dta,MAAA,CAAOsI,MAAA,CAAO+C,MAAA,EAAcqO,KAAA,CAAAa,aAAA,CAAcD,IAAI,CAAC;EAC/CjP,MAAA,CAAO6O,QAAA,CAASM,QAAA,GAAWnP,MAAA;EAC3BA,MAAA,CAAOgP,QAAA,CAASG,QAAA,GAAWnP,MAAA;EAC3B,OAAOA,MAAA;AACT;;;AC5CA,SAAStN,IAAA,IAAA0c,KAAA,EAAMxc,EAAA,IAAAyc,GAAA,EAAIC,mBAAA,QAA2B;AA8EvC,IAAMC,SAAA,GAAYA,CAAA,KAEA;EACvB,MAAM3W,OAAA,GAA+B,EAAC;EAEtC,MAAM4W,UAAA,GAA8B,SAAAD,CAAUpb,KAAA,EAAO;IACnDmb,mBAAA,CAAoB;IAEpB,MAAMrP,OAAA,GAAyB,EAAC;IAEhCmP,KAAA,CAAKxW,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAM;MACzB,IAAI2W,GAAA,CAAGxa,GAAA,CAAIV,KAAK,GAAG;QACjB8L,OAAA,CAAQzG,IAAA,CAAKzB,IAAA,CAAKiB,KAAA,CAAM,CAAC;MAC3B,OAAO;QACL,MAAMO,OAAA,GAASkW,SAAA,CAAUtb,KAAA,EAAO4D,IAAA,EAAMW,CAAC;QACvC,IAAIa,OAAA,EAAQ;UACV0G,OAAA,CAAQzG,IAAA,CAAKzB,IAAA,CAAKiB,KAAA,CAAMO,OAAM,CAAC;QACjC;MACF;IACF,CAAC;IAED,OAAO0G,OAAA;EACT;EAEAuP,UAAA,CAAU5W,OAAA,GAAUA,OAAA;EAGpB4W,UAAA,CAAUpX,GAAA,GAAM,UAAUL,IAAA,EAAyB;IACjD,IAAI,CAACa,OAAA,CAAQ9E,QAAA,CAASiE,IAAI,GAAG;MAC3Ba,OAAA,CAAQY,IAAA,CAAKzB,IAAI;IACnB;EACF;EAGAyX,UAAA,CAAUvX,MAAA,GAAS,UAAUF,IAAA,EAAyB;IACpD,MAAMW,CAAA,GAAIE,OAAA,CAAQ8W,OAAA,CAAQ3X,IAAI;IAC9B,IAAI,CAACW,CAAA,EAAGE,OAAA,CAAQ+W,MAAA,CAAOjX,CAAA,EAAG,CAAC;EAC7B;EAGA8W,UAAA,CAAUna,KAAA,GAAQ,YAAY;IAC5B+Z,KAAA,CAAKxW,OAAA,EAASb,IAAA,IAAQA,IAAA,CAAK1C,KAAA,CAAM,GAAG/B,SAAS,CAAC;IAC9C,OAAO;EACT;EAGAkc,UAAA,CAAUxQ,MAAA,GAAS,YAAY;IAC7BoQ,KAAA,CAAKxW,OAAA,EAASb,IAAA,IAAQA,IAAA,CAAKiH,MAAA,CAAO,GAAG1L,SAAS,CAAC;IAC/C,OAAO;EACT;EAGAkc,UAAA,CAAUpH,GAAA,GAAM,UACdnK,MAAA,EAGA;IACAmR,KAAA,CAAKxW,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAM;MACzB,MAAMa,OAAA,GAAS8V,GAAA,CAAG1b,GAAA,CAAIsK,MAAM,IAAIA,MAAA,CAAOvF,CAAA,EAAGX,IAAI,IAAIkG,MAAA;MAClD,IAAI1E,OAAA,EAAQ;QACVxB,IAAA,CAAKqQ,GAAA,CAAI7O,OAAM;MACjB;IACF,CAAC;EACH;EAEAiW,UAAA,CAAUxW,KAAA,GAAQ,UAAU7E,KAAA,EAA4C;IACtE,MAAM8L,OAAA,GAAyB,EAAC;IAEhCmP,KAAA,CAAKxW,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAM;MACzB,IAAI2W,GAAA,CAAGxa,GAAA,CAAIV,KAAK,GAAG;QACjB8L,OAAA,CAAQzG,IAAA,CAAKzB,IAAA,CAAKiB,KAAA,CAAM,CAAC;MAC3B,OAAO;QACL,MAAMO,OAAA,GAAS,KAAKkW,SAAA,CAAUtb,KAAA,EAAO4D,IAAA,EAAMW,CAAC;QAC5C,IAAIa,OAAA,EAAQ;UACV0G,OAAA,CAAQzG,IAAA,CAAKzB,IAAA,CAAKiB,KAAA,CAAMO,OAAM,CAAC;QACjC;MACF;IACF,CAAC;IAED,OAAO0G,OAAA;EACT;EAGAuP,UAAA,CAAUtN,IAAA,GAAO,YAAY;IAC3BkN,KAAA,CAAKxW,OAAA,EAASb,IAAA,IAAQA,IAAA,CAAKmK,IAAA,CAAK,GAAG5O,SAAS,CAAC;IAC7C,OAAO;EACT;EAEAkc,UAAA,CAAUjZ,MAAA,GAAS,UAAUpC,KAAA,EAA2C;IACtEib,KAAA,CAAKxW,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAMX,IAAA,CAAKxB,MAAA,CAAO,KAAKkZ,SAAA,CAAUtb,KAAA,EAAO4D,IAAA,EAAMW,CAAC,CAAC,CAAC;IACtE,OAAO;EACT;EAGA,MAAM+W,SAAA,GAAY,SAAAA,CAChB7E,GAAA,EACA7S,IAAA,EACA6X,KAAA,EACA;IACA,OAAOP,GAAA,CAAG1b,GAAA,CAAIiX,GAAG,IAAIA,GAAA,CAAIgF,KAAA,EAAO7X,IAAI,IAAI6S,GAAA;EAC1C;EAEA4E,UAAA,CAAUC,SAAA,GAAYA,SAAA;EAEtB,OAAOD,UAAA;AACT;;;AZ9GO,SAASK,WACdtc,MAAA,EACAY,KAAA,EACA2b,IAAA,EACK;EACL,MAAMC,OAAA,GAAUhW,GAAA,CAAGpG,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EACjC,IAAI4b,OAAA,IAAW,CAACD,IAAA,EAAMA,IAAA,GAAO,EAAC;EAG9B,MAAM5a,GAAA,GAAM2E,OAAA,CACV,MAAOkW,OAAA,IAAWzc,SAAA,CAAUC,MAAA,IAAU,IAAIgc,SAAA,CAAU,IAAI,QACxD,EACF;EAYA,MAAMS,QAAA,GAAWlW,MAAA,CAAO,CAAC;EACzB,MAAMmW,WAAA,GAAc9V,cAAA,CAAe;EAGnC,MAAMsE,KAAA,GAAQ5E,OAAA,CACZ,OAAc;IACZqW,KAAA,EAAO,EAAC;IACRpX,KAAA,EAAO,EAAC;IACR8G,MAAM7H,IAAA,EAAMoY,QAAA,EAAS;MACnB,MAAMC,QAAA,GAAUpC,UAAA,CAAWjW,IAAA,EAAMoY,QAAO;MAIxC,MAAME,YAAA,GACJL,QAAA,CAASpX,OAAA,GAAU,KACnB,CAAC6F,KAAA,CAAM3F,KAAA,CAAMvF,MAAA,IACb,CAACoB,MAAA,CAAOF,IAAA,CAAK2b,QAAO,EAAElQ,IAAA,CAAKrM,GAAA,IAAO,CAACkE,IAAA,CAAKwU,OAAA,CAAQ1Y,GAAG,CAAC;MAEtD,OAAOwc,YAAA,GACHlD,gBAAA,CAAiBpV,IAAA,EAAMoY,QAAO,IAC9B,IAAIjX,OAAA,CAAaC,OAAA,IAAW;QAC1BgV,UAAA,CAAWpW,IAAA,EAAMqY,QAAO;QACxB3R,KAAA,CAAM3F,KAAA,CAAMU,IAAA,CAAK,MAAM;UACrBL,OAAA,CAAQgU,gBAAA,CAAiBpV,IAAA,EAAMoY,QAAO,CAAC;QACzC,CAAC;QACDF,WAAA,CAAY;MACd,CAAC;IACP;EACF,IACA,EACF;EAEA,MAAMC,KAAA,GAAQpW,MAAA,CAAO,CAAC,GAAG2E,KAAA,CAAMyR,KAAK,CAAC;EACrC,MAAMI,OAAA,GAAiB,EAAC;EAGxB,MAAMC,UAAA,GAAatW,OAAA,CAAQ1G,MAAM,KAAK;EAItCsG,OAAA,CAAQ,MAAM;IAEZG,KAAA,CAAKkW,KAAA,CAAMtX,OAAA,CAAQ4X,KAAA,CAAMjd,MAAA,EAAQgd,UAAU,GAAGxY,IAAA,IAAQ;MACpDD,UAAA,CAAWC,IAAA,EAAM7C,GAAG;MACpB6C,IAAA,CAAKmK,IAAA,CAAK,IAAI;IAChB,CAAC;IACDgO,KAAA,CAAMtX,OAAA,CAAQrF,MAAA,GAASA,MAAA;IAEvBkd,cAAA,CAAeF,UAAA,EAAYhd,MAAM;EACnC,GAAG,CAACA,MAAM,CAAC;EAGXsG,OAAA,CAAQ,MAAM;IACZ4W,cAAA,CAAe,GAAGjT,IAAA,CAAKgJ,GAAA,CAAI+J,UAAA,EAAYhd,MAAM,CAAC;EAChD,GAAGuc,IAAI;EAGP,SAASW,eAAeC,UAAA,EAAoBC,QAAA,EAAkB;IAC5D,SAASjY,CAAA,GAAIgY,UAAA,EAAYhY,CAAA,GAAIiY,QAAA,EAAUjY,CAAA,IAAK;MAC1C,MAAMX,IAAA,GACJmY,KAAA,CAAMtX,OAAA,CAAQF,CAAC,MACdwX,KAAA,CAAMtX,OAAA,CAAQF,CAAC,IAAI,IAAI2T,UAAA,CAAW,MAAM5N,KAAA,CAAMmB,KAAK;MAEtD,MAAMrG,OAAA,GAA8BwW,OAAA,GAChCA,OAAA,CAAQrX,CAAA,EAAGX,IAAI,IACd5D,KAAA,CAAcuE,CAAC;MAEpB,IAAIa,OAAA,EAAQ;QACV+W,OAAA,CAAQ5X,CAAC,IAAI0S,aAAA,CAAc7R,OAAM;MACnC;IACF;EACF;EAKA,MAAMgT,OAAA,GAAU2D,KAAA,CAAMtX,OAAA,CAAQrB,GAAA,CAAI,CAACQ,IAAA,EAAMW,CAAA,KAAMsV,UAAA,CAAWjW,IAAA,EAAMuY,OAAA,CAAQ5X,CAAC,CAAC,CAAC;EAE3E,MAAMkY,OAAA,GAAUhX,WAAA,CAAW2U,aAAa;EACxC,MAAMsC,WAAA,GAAc5W,OAAA,CAAQ2W,OAAO;EACnC,MAAME,UAAA,GAAaF,OAAA,KAAYC,WAAA,IAAelZ,QAAA,CAASiZ,OAAO;EAE9DxW,0BAAA,CAA0B,MAAM;IAC9B4V,QAAA,CAASpX,OAAA;IAGT6F,KAAA,CAAMyR,KAAA,GAAQA,KAAA,CAAMtX,OAAA;IAGpB,MAAM;MAAEE;IAAM,IAAI2F,KAAA;IAClB,IAAI3F,KAAA,CAAMvF,MAAA,EAAQ;MAChBkL,KAAA,CAAM3F,KAAA,GAAQ,EAAC;MACfkB,KAAA,CAAKlB,KAAA,EAAOiY,EAAA,IAAMA,EAAA,CAAG,CAAC;IACxB;IAGA/W,KAAA,CAAKkW,KAAA,CAAMtX,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAM;MAE/BxD,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAKkD,GAAA,CAAIL,IAAI;MAGb,IAAI+Y,UAAA,EAAY;QACd/Y,IAAA,CAAKiB,KAAA,CAAM;UAAE5E,OAAA,EAASwc;QAAQ,CAAC;MACjC;MAGA,MAAMrX,OAAA,GAAS+W,OAAA,CAAQ5X,CAAC;MACxB,IAAIa,OAAA,EAAQ;QAEVrB,UAAA,CAAWH,IAAA,EAAMwB,OAAA,CAAOrE,GAAG;QAI3B,IAAI6C,IAAA,CAAK7C,GAAA,EAAK;UACZ6C,IAAA,CAAKe,KAAA,CAAMU,IAAA,CAAKD,OAAM;QACxB,OAAO;UACLxB,IAAA,CAAKiB,KAAA,CAAMO,OAAM;QACnB;MACF;IACF,CAAC;EACH,CAAC;EAGDW,OAAA,CAAQ,MAAM,MAAM;IAClBF,KAAA,CAAKyE,KAAA,CAAMyR,KAAA,EAAOnY,IAAA,IAAQA,IAAA,CAAKmK,IAAA,CAAK,IAAI,CAAC;EAC3C,CAAC;EAID,MAAMjE,MAAA,GAASsO,OAAA,CAAQhV,GAAA,CAAIyZ,CAAA,IAAA5Z,aAAA,KAAW4Z,CAAA,CAAI;EAE1C,OAAO9b,GAAA,GAAM,CAAC+I,MAAA,EAAQ/I,GAAG,IAAI+I,MAAA;AAC/B;;;ADvKO,SAASgT,UAAU9c,KAAA,EAAY2b,IAAA,EAAuB;EAC3D,MAAMoB,IAAA,GAAOxX,GAAA,CAAG/F,GAAA,CAAIQ,KAAK;EACzB,MAAM,CAAC,CAAC8J,MAAM,GAAG/I,GAAG,IAAI2a,UAAA,CACtB,GACAqB,IAAA,GAAO/c,KAAA,GAAQ,CAACA,KAAK,GACrB+c,IAAA,GAAOpB,IAAA,IAAQ,EAAC,GAAIA,IACtB;EACA,OAAOoB,IAAA,IAAQ5d,SAAA,CAAUC,MAAA,IAAU,IAAI,CAAC0K,MAAA,EAAQ/I,GAAG,IAAI+I,MAAA;AACzD;;;ActEA,SAASkT,QAAA,QAAgB;AAKzB,IAAMC,aAAA,GAAgBA,CAAA,KAAM7B,SAAA,CAAe;AAEpC,IAAM8B,YAAA,GAAeA,CAAA,KAC1BF,QAAA,CAASC,aAAa,EAAE,CAAC;;;ACR3B,SAASE,WAAA,EAAapX,OAAA,IAAAqX,QAAA,QAAe;AAwB9B,IAAMC,cAAA,GAAiBA,CAC5Bnb,OAAA,EACAlC,KAAA,KACG;EACH,MAAMsd,WAAA,GAAcH,WAAA,CAAY,MAAM,IAAItM,WAAA,CAAY3O,OAAA,EAASlC,KAAK,CAAC;EAErEod,QAAA,CAAQ,MAAM,MAAM;IAClBE,WAAA,CAAYvP,IAAA,CAAK;EACnB,CAAC;EAED,OAAOuP,WAAA;AACT;;;ACnCA,SAAS/e,IAAA,IAAAgf,KAAA,EAAM9e,EAAA,IAAA+e,IAAA,EAAIhf,yBAAA,IAAAif,0BAAA,QAAiC;AAiF7C,SAASC,SACdte,MAAA,EACAue,QAAA,EACAhC,IAAA,EACA;EACA,MAAMC,OAAA,GAAU4B,IAAA,CAAGhe,GAAA,CAAIme,QAAQ,KAAKA,QAAA;EACpC,IAAI/B,OAAA,IAAW,CAACD,IAAA,EAAMA,IAAA,GAAO,EAAC;EAG9B,IAAIva,OAAA,GAAU;EACd,IAAIwc,SAAA,GAAmC;EAEvC,MAAM5R,MAAA,GAAS0P,UAAA,CACbtc,MAAA,EACA,CAACmF,CAAA,EAAGX,IAAA,KAAS;IACX,MAAM5D,KAAA,GAAQ4b,OAAA,GAAUA,OAAA,CAAQrX,CAAA,EAAGX,IAAI,IAAI+Z,QAAA;IAC3CC,SAAA,GAAY5d,KAAA,CAAMe,GAAA;IAClBK,OAAA,GAAUA,OAAA,IAAWpB,KAAA,CAAMoB,OAAA;IAE3B,OAAOpB,KAAA;EACT;EAAA;EAAA;EAGA2b,IAAA,IAAQ,CAAC,CAAC,CAAC,CACb;EAEA8B,0BAAA,CAA0B,MAAM;IAI9BF,KAAA,CAAKvR,MAAA,CAAO,CAAC,EAAEvH,OAAA,EAAS,CAACb,IAAA,EAAMW,CAAA,KAAM;MACnC,MAAMyL,MAAA,GAAShE,MAAA,CAAO,CAAC,EAAEvH,OAAA,CAAQF,CAAA,IAAKnD,OAAA,GAAU,IAAI,GAAG;MAKvD2C,UAAA,CAAWH,IAAA,EAAMga,SAAS;MAO1B,IAAIha,IAAA,CAAK7C,GAAA,EAAK;QACZ,IAAIiP,MAAA,EAAQ;UACVpM,IAAA,CAAKxB,MAAA,CAAO;YAAEtB,EAAA,EAAIkP,MAAA,CAAOoI;UAAQ,CAAC;QACpC;QAEA;MACF;MAEA,IAAIpI,MAAA,EAAQ;QACVpM,IAAA,CAAKiB,KAAA,CAAM;UAAE/D,EAAA,EAAIkP,MAAA,CAAOoI;QAAQ,CAAC;MACnC,OAAO;QACLxU,IAAA,CAAKiB,KAAA,CAAM;MACb;IACF,CAAC;EACH,GAAG8W,IAAI;EAEP,IAAIC,OAAA,IAAWzc,SAAA,CAAUC,MAAA,IAAU,GAAG;IACpC,MAAM2B,GAAA,GAAM6c,SAAA,aAAAA,SAAA,cAAAA,SAAA,GAAa5R,MAAA,CAAO,CAAC;IAEjCjL,GAAA,CAAI,WAAW,IAAI,CAAC8c,SAAA,EAAUja,IAAA,EAAMW,CAAA,KAAM;MACxC,MAAMvE,KAAA,GAAQwd,IAAA,CAAGhe,GAAA,CAAIqe,SAAQ,IAAIA,SAAA,CAAStZ,CAAA,EAAGX,IAAI,IAAIia,SAAA;MACrD,IAAI7d,KAAA,EAAO;QACT,MAAMgQ,MAAA,GAASjP,GAAA,CAAI0D,OAAA,CAAQF,CAAA,IAAKvE,KAAA,CAAMoB,OAAA,GAAU,IAAI,GAAG;QACvD,IAAI4O,MAAA,EAAQhQ,KAAA,CAAMc,EAAA,GAAKkP,MAAA,CAAOoI,OAAA;QAC9B,OAAOpY,KAAA;MACT;IACF;IACA,OAAOgM,MAAA;EACT;EAEA,OAAOA,MAAA,CAAO,CAAC;AACjB;;;AC3JA,YAAY8R,MAAA,MAAW;AACvB,SAAStY,UAAA,IAAAuY,WAAA,EAAYpY,MAAA,IAAAqY,OAAA,EAAQtY,OAAA,IAAAuY,QAAA,QAAe;AAE5C,SACExf,EAAA,IAAAyf,IAAA,EACAxf,OAAA,IAAAyf,QAAA,EACAnY,cAAA,IAAAoY,eAAA,EACArY,OAAA,IAAAsY,QAAA,EACAvY,OAAA,IAAAwY,QAAA,EACA/f,IAAA,IAAAggB,KAAA,EACA/f,yBAAA,IAAAggB,0BAAA,QACK;AA6DA,SAASC,cACdC,IAAA,EACA1e,KAAA,EACA2b,IAAA,EACK;EACL,MAAMC,OAAA,GAAUsC,IAAA,CAAG1e,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EAEjC,MAAM;IACJiB,KAAA;IACAe,IAAA;IACAD,KAAA,GAAQ;IACRE,OAAA,GAAU;IACV0c,eAAA,GAAkB;IAClBpc,WAAA;IACAxB,GAAA,EAAK6d,QAAA;IACLhe,MAAA,EAAQie;EACV,IAA6BjD,OAAA,GAAUA,OAAA,CAAQ,IAAI5b,KAAA;EAGnD,MAAMe,GAAA,GAAMkd,QAAA,CACV,MAAOrC,OAAA,IAAWzc,SAAA,CAAUC,MAAA,IAAU,IAAIgc,SAAA,CAAU,IAAI,QACxD,EACF;EAGA,MAAMtZ,KAAA,GAAQqc,QAAA,CAAQO,IAAI;EAC1B,MAAMI,WAAA,GAAiC,EAAC;EAGxC,MAAMC,eAAA,GAAkBf,OAAA,CAAiC,IAAI;EAC7D,MAAMgB,eAAA,GAAkB/d,KAAA,GAAQ,OAAO8d,eAAA,CAAgBta,OAAA;EAEvD+Z,0BAAA,CAA0B,MAAM;IAC9BO,eAAA,CAAgBta,OAAA,GAAUqa,WAAA;EAC5B,CAAC;EAEDT,QAAA,CAAQ,MAAM;IASZE,KAAA,CAAKO,WAAA,EAAa3Q,CAAA,IAAK;MACrBpN,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAKkD,GAAA,CAAIkK,CAAA,CAAEvK,IAAI;MACfuK,CAAA,CAAEvK,IAAA,CAAK7C,GAAA,GAAMA,GAAA;IACf,CAAC;IAGD,OAAO,MAAM;MACXwd,KAAA,CAAKQ,eAAA,CAAgBta,OAAA,EAAU0J,CAAA,IAAK;QAClC,IAAIA,CAAA,CAAE8Q,OAAA,EAAS;UACbC,YAAA,CAAa/Q,CAAA,CAAEgR,YAAa;QAC9B;QACAxb,UAAA,CAAWwK,CAAA,CAAEvK,IAAA,EAAM7C,GAAG;QACtBoN,CAAA,CAAEvK,IAAA,CAAKmK,IAAA,CAAK,IAAI;MAClB,CAAC;IACH;EACF,CAAC;EAMD,MAAMzN,IAAA,GAAO8e,OAAA,CAAQtd,KAAA,EAAO8Z,OAAA,GAAUA,OAAA,CAAQ,IAAI5b,KAAA,EAAOgf,eAAe;EAGxE,MAAMC,OAAA,GAAWhe,KAAA,IAAS8d,eAAA,CAAgBta,OAAA,IAAY,EAAC;EACvD+Z,0BAAA,CAA0B,MACxBD,KAAA,CAAKU,OAAA,EAASI,KAAA,IAAyB;IAAA,IAAxB;MAAEzb,IAAA;MAAMsK,IAAA;MAAMxO;IAAI,IAAA2f,KAAA;IAC/B1b,UAAA,CAAWC,IAAA,EAAM7C,GAAG;IACpB/B,QAAA,CAASuD,WAAA,EAAa2L,IAAA,EAAMxO,GAAG;EACjC,CAAC,CACH;EAGA,MAAM4f,MAAA,GAAmB,EAAC;EAC1B,IAAIN,eAAA,EACFT,KAAA,CAAKS,eAAA,EAAiB,CAAC7Q,CAAA,EAAG5J,CAAA,KAAM;IAE9B,IAAI4J,CAAA,CAAE8Q,OAAA,EAAS;MACbC,YAAA,CAAa/Q,CAAA,CAAEgR,YAAa;MAC5BF,OAAA,CAAQ5Z,IAAA,CAAK8I,CAAC;IAChB,OAAO;MACL5J,CAAA,GAAI+a,MAAA,CAAO/a,CAAC,IAAIjE,IAAA,CAAKib,OAAA,CAAQpN,CAAA,CAAEzO,GAAG;MAClC,IAAI,CAAC6E,CAAA,EAAGua,WAAA,CAAYva,CAAC,IAAI4J,CAAA;IAC3B;EACF,CAAC;EAGHoQ,KAAA,CAAKzc,KAAA,EAAO,CAACoM,IAAA,EAAM3J,CAAA,KAAM;IACvB,IAAI,CAACua,WAAA,CAAYva,CAAC,GAAG;MACnBua,WAAA,CAAYva,CAAC,IAAI;QACf7E,GAAA,EAAKY,IAAA,CAAKiE,CAAC;QACX2J,IAAA;QACAqR,KAAA;QACA3b,IAAA,EAAM,IAAIsU,UAAA,CAAW;MACvB;MAEA4G,WAAA,CAAYva,CAAC,EAAEX,IAAA,CAAKsK,IAAA,GAAOA,IAAA;IAC7B;EACF,CAAC;EAID,IAAIoR,MAAA,CAAOlgB,MAAA,EAAQ;IACjB,IAAImF,CAAA,GAAI;IACR,MAAM;MAAElC;IAAM,IAA6BuZ,OAAA,GAAUA,OAAA,CAAQ,IAAI5b,KAAA;IACjEue,KAAA,CAAKe,MAAA,EAAQ,CAACE,QAAA,EAAUC,SAAA,KAAc;MACpC,MAAMtR,CAAA,GAAI6Q,eAAA,CAAiBS,SAAS;MACpC,IAAI,CAACD,QAAA,EAAU;QACbjb,CAAA,GAAIua,WAAA,CAAYvD,OAAA,CAAQpN,CAAC;QACzB2Q,WAAA,CAAYva,CAAC,IAAAtB,aAAA,CAAAA,aAAA,KAASkL,CAAA;UAAGD,IAAA,EAAMpM,KAAA,CAAM0d,QAAQ;QAAA,EAAE;MACjD,WAAWnd,KAAA,EAAO;QAChByc,WAAA,CAAYtD,MAAA,CAAO,EAAEjX,CAAA,EAAG,GAAG4J,CAAC;MAC9B;IACF,CAAC;EACH;EAEA,IAAI+P,IAAA,CAAG1e,GAAA,CAAIwC,IAAI,GAAG;IAChB8c,WAAA,CAAY9c,IAAA,CAAK,CAAC0d,CAAA,EAAGC,CAAA,KAAM3d,IAAA,CAAK0d,CAAA,CAAExR,IAAA,EAAMyR,CAAA,CAAEzR,IAAI,CAAC;EACjD;EAGA,IAAI5M,KAAA,GAAQ,CAACS,KAAA;EAGb,MAAM+Z,WAAA,GAAcsC,eAAA,CAAe;EAGnC,MAAM/T,YAAA,GAAelK,eAAA,CAAoCH,KAAK;EAE9D,MAAM4f,OAAA,GAAU,mBAAIlH,GAAA,CAA6B;EACjD,MAAMmH,kBAAA,GAAqB7B,OAAA,CAAO,mBAAItF,GAAA,CAA6B,CAAC;EAEpE,MAAMoH,WAAA,GAAc9B,OAAA,CAAO,KAAK;EAChCO,KAAA,CAAKO,WAAA,EAAa,CAAC3Q,CAAA,EAAG5J,CAAA,KAAM;IAC1B,MAAM7E,GAAA,GAAMyO,CAAA,CAAEzO,GAAA;IACd,MAAMqgB,SAAA,GAAY5R,CAAA,CAAEoR,KAAA;IAEpB,MAAMza,CAAA,GAA6B8W,OAAA,GAAUA,OAAA,CAAQ,IAAI5b,KAAA;IAEzD,IAAI8C,GAAA;IACJ,IAAIyc,KAAA;IAEJ,MAAMS,UAAA,GAAahhB,QAAA,CAAS8F,CAAA,CAAExD,KAAA,IAAS,GAAG5B,GAAG;IAE7C,IAAIqgB,SAAA,yBAAoC;MACtCjd,GAAA,GAAKgC,CAAA,CAAE3C,KAAA;MACPod,KAAA;IACF,OAAO;MACL,MAAMU,OAAA,GAAU3f,IAAA,CAAKib,OAAA,CAAQ7b,GAAG,IAAI;MACpC,IAAIqgB,SAAA,yBAAoC;QACtC,IAAIE,OAAA,EAAS;UACXnd,GAAA,GAAKgC,CAAA,CAAEzC,KAAA;UACPkd,KAAA;QACF,WAAYzc,GAAA,GAAKgC,CAAA,CAAE1C,MAAA,EAAS;UAC1Bmd,KAAA;QACF,OAAO;MACT,WAAW,CAACU,OAAA,EAAS;QACnBnd,GAAA,GAAKgC,CAAA,CAAE3C,KAAA;QACPod,KAAA;MACF,OAAO;IACT;IAIAzc,GAAA,GAAK9D,QAAA,CAAS8D,GAAA,EAAIqL,CAAA,CAAED,IAAA,EAAM3J,CAAC;IAC3BzB,GAAA,GAAKob,IAAA,CAAGpe,GAAA,CAAIgD,GAAE,IAAID,OAAA,CAAQC,GAAE,IAAI;MAAEhC,EAAA,EAAAgC;IAAG;IAarC,IAAI,CAACA,GAAA,CAAGlC,MAAA,EAAQ;MACd,MAAMoI,OAAA,GAAS6V,WAAA,IAAexU,YAAA,CAAazJ,MAAA;MAC3CkC,GAAA,CAAGlC,MAAA,GAAS5B,QAAA,CAASgK,OAAA,EAAQmF,CAAA,CAAED,IAAA,EAAM3J,CAAA,EAAGgb,KAAK;IAC/C;IAEAje,KAAA,IAASS,KAAA;IAGT,MAAM6P,OAAA,GAAA3O,aAAA,CAAAA,aAAA,KACDoH,YAAA;MAAA;MAEH/I,KAAA,EAAO0e,UAAA,GAAa1e,KAAA;MACpBP,GAAA,EAAK6d,QAAA;MACLvd,SAAA,EAAWyD,CAAA,CAAEzD,SAAA;MAAA;MAEbJ,KAAA,EAAO;IAAA,GAEH6B,GAAA,CACN;IAEA,IAAIyc,KAAA,2BAAkCrB,IAAA,CAAGxd,GAAA,CAAIkR,OAAA,CAAQ/Q,IAAI,GAAG;MAC1D,MAAMqf,EAAA,GAAItE,OAAA,GAAUA,OAAA,CAAQ,IAAI5b,KAAA;MAIhC,MAAMa,IAAA,GAAOqd,IAAA,CAAGxd,GAAA,CAAIwf,EAAA,CAAEhe,OAAO,KAAK8c,eAAA,GAAkBkB,EAAA,CAAErf,IAAA,GAAOqf,EAAA,CAAEhe,OAAA;MAE/D0P,OAAA,CAAQ/Q,IAAA,GAAO7B,QAAA,CAAS6B,IAAA,EAAMsN,CAAA,CAAED,IAAA,EAAM3J,CAAC;IACzC;IAEA,MAAM;MAAE1C;IAAU,IAAI+P,OAAA;IACtBA,OAAA,CAAQ/P,SAAA,GAAYmK,MAAA,IAAU;MAC5BhN,QAAA,CAAS6C,SAAA,EAAWmK,MAAM;MAE1B,MAAMmU,YAAA,GAAcpB,eAAA,CAAgBta,OAAA;MACpC,MAAM2b,EAAA,GAAID,YAAA,CAAYE,IAAA,CAAKC,EAAA,IAAKA,EAAA,CAAE5gB,GAAA,KAAQA,GAAG;MAC7C,IAAI,CAAC0gB,EAAA,EAAG;MAIR,IAAIpU,MAAA,CAAOC,SAAA,IAAamU,EAAA,CAAEb,KAAA,2BAAiC;QAQzD;MACF;MAEA,IAAIa,EAAA,CAAExc,IAAA,CAAKkM,IAAA,EAAM;QACf,MAAMA,IAAA,GAAOqQ,YAAA,CAAY/T,KAAA,CAAMkU,EAAA,IAAKA,EAAA,CAAE1c,IAAA,CAAKkM,IAAI;QAC/C,IAAIsQ,EAAA,CAAEb,KAAA,yBAAgC;UACpC,MAAMgB,MAAA,GAASvhB,QAAA,CAASiD,OAAA,EAASme,EAAA,CAAElS,IAAI;UACvC,IAAIqS,MAAA,KAAW,OAAO;YACpB,MAAMC,QAAA,GAAWD,MAAA,KAAW,OAAO,IAAIA,MAAA;YACvCH,EAAA,CAAEnB,OAAA,GAAU;YAGZ,IAAI,CAACnP,IAAA,IAAQ0Q,QAAA,GAAW,GAAG;cAEzB,IAAIA,QAAA,IAAY,YACdJ,EAAA,CAAEjB,YAAA,GAAehU,UAAA,CAAW2Q,WAAA,EAAa0E,QAAQ;cACnD;YACF;UACF;QACF;QAEA,IAAI1Q,IAAA,IAAQqQ,YAAA,CAAYpU,IAAA,CAAKuU,EAAA,IAAKA,EAAA,CAAErB,OAAO,GAAG;UAK5CY,kBAAA,CAAmBpb,OAAA,CAAQX,MAAA,CAAOsc,EAAC;UAEnC,IAAIzB,eAAA,EAAiB;YAKnBmB,WAAA,CAAYrb,OAAA,GAAU;UACxB;UAEAqX,WAAA,CAAY;QACd;MACF;IACF;IAEA,MAAM1D,OAAA,GAAUyB,UAAA,CAAW1L,CAAA,CAAEvK,IAAA,EAAMgO,OAAO;IAK1C,IAAI2N,KAAA,4BAAmCZ,eAAA,EAAiB;MACtDkB,kBAAA,CAAmBpb,OAAA,CAAQwP,GAAA,CAAI9F,CAAA,EAAG;QAAEoR,KAAA;QAAOnH,OAAA;QAASxG;MAAQ,CAAC;IAC/D,OAAO;MACLgO,OAAA,CAAQ3L,GAAA,CAAI9F,CAAA,EAAG;QAAEoR,KAAA;QAAOnH,OAAA;QAASxG;MAAQ,CAAC;IAC5C;EACF,CAAC;EAGD,MAAM6K,OAAA,GAAUsB,WAAA,CAAW3D,aAAa;EACxC,MAAMsC,WAAA,GAAc4B,QAAA,CAAQ7B,OAAO;EACnC,MAAME,UAAA,GAAaF,OAAA,KAAYC,WAAA,IAAelZ,QAAA,CAASiZ,OAAO;EAG9D+B,0BAAA,CAA0B,MAAM;IAC9B,IAAI7B,UAAA,EAAY;MACd4B,KAAA,CAAKO,WAAA,EAAa3Q,CAAA,IAAK;QACrBA,CAAA,CAAEvK,IAAA,CAAKiB,KAAA,CAAM;UAAE5E,OAAA,EAASwc;QAAQ,CAAC;MACnC,CAAC;IACH;EACF,GAAG,CAACA,OAAO,CAAC;EAEZ8B,KAAA,CAAKqB,OAAA,EAAS,CAACnc,CAAA,EAAG0K,CAAA,KAAM;IAMtB,IAAI0R,kBAAA,CAAmBpb,OAAA,CAAQuS,IAAA,EAAM;MACnC,MAAMyJ,GAAA,GAAM3B,WAAA,CAAY4B,SAAA,CAAUpW,KAAA,IAASA,KAAA,CAAM5K,GAAA,KAAQyO,CAAA,CAAEzO,GAAG;MAC9Dof,WAAA,CAAYtD,MAAA,CAAOiF,GAAA,EAAK,CAAC;IAC3B;EACF,CAAC;EAEDjC,0BAAA,CACE,MAAM;IAKJD,KAAA,CACEsB,kBAAA,CAAmBpb,OAAA,CAAQuS,IAAA,GAAO6I,kBAAA,CAAmBpb,OAAA,GAAUmb,OAAA,EAC/D,CAAAe,KAAA,EAAqBxS,CAAA,KAAM;MAAA,IAA1B;QAAEoR,KAAA;QAAO3N;MAAQ,IAAA+O,KAAA;MAChB,MAAM;QAAE/c;MAAK,IAAIuK,CAAA;MAEjBA,CAAA,CAAEoR,KAAA,GAAQA,KAAA;MAGVxe,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAKkD,GAAA,CAAIL,IAAI;MAGb,IAAI+Y,UAAA,IAAc4C,KAAA,yBAAgC;QAChD3b,IAAA,CAAKiB,KAAA,CAAM;UAAE5E,OAAA,EAASwc;QAAQ,CAAC;MACjC;MAEA,IAAI7K,OAAA,EAAS;QAEX7N,UAAA,CAAWH,IAAA,EAAMgO,OAAA,CAAQ7Q,GAAG;QAQ5B,KAAK6C,IAAA,CAAK7C,GAAA,IAAOA,GAAA,KAAQ,CAAC+e,WAAA,CAAYrb,OAAA,EAAS;UAC7Cb,IAAA,CAAKxB,MAAA,CAAOwP,OAAO;QACrB,OAAO;UACLhO,IAAA,CAAKiB,KAAA,CAAM+M,OAAO;UAElB,IAAIkO,WAAA,CAAYrb,OAAA,EAAS;YACvBqb,WAAA,CAAYrb,OAAA,GAAU;UACxB;QACF;MACF;IACF,CACF;EACF,GACAxD,KAAA,GAAQ,SAAS0a,IACnB;EAEA,MAAMiF,iBAAA,GAAkCC,MAAA,IACtC,eAAA/C,MAAA,CAAAnD,aAAA,CAAAmD,MAAA,CAAAgD,QAAA,QACGhC,WAAA,CAAY1b,GAAA,CAAI,CAAC+K,CAAA,EAAG5J,CAAA,KAAM;IACzB,MAAM;MAAE6T;IAAQ,IAAIwH,OAAA,CAAQzT,GAAA,CAAIgC,CAAC,KAAKA,CAAA,CAAEvK,IAAA;IACxC,MAAMmd,IAAA,GAAYF,MAAA,CAAA5d,aAAA,KAAYmV,OAAA,GAAWjK,CAAA,CAAED,IAAA,EAAMC,CAAA,EAAG5J,CAAC;IACrD,OAAOwc,IAAA,IAAQA,IAAA,CAAKhR,IAAA,GAClB,eAAA+N,MAAA,CAAAnD,aAAA,CAACoG,IAAA,CAAKhR,IAAA,EAAA9M,aAAA,CAAAA,aAAA,KACA8d,IAAA,CAAK/gB,KAAA;MACTN,GAAA,EAAKwe,IAAA,CAAG8C,GAAA,CAAI7S,CAAA,CAAEzO,GAAG,KAAKwe,IAAA,CAAGnI,GAAA,CAAI5H,CAAA,CAAEzO,GAAG,IAAIyO,CAAA,CAAEzO,GAAA,GAAMyO,CAAA,CAAEvK,IAAA,CAAKiL,EAAA;MACrD9N,GAAA,EAAKggB,IAAA,CAAKhgB;IAAA,EACZ,IAEAggB,IAAA;EAEJ,CAAC,CACH;EAGF,OAAOhgB,GAAA,GAAM,CAAC6f,iBAAA,EAAmB7f,GAAG,IAAI6f,iBAAA;AAC1C;AAGA,IAAIK,OAAA,GAAU;AAEd,SAAS7B,QACPtd,KAAA,EAAAof,KAAA,EAEAlC,eAAA,EACgB;EAAA,IAFhB;IAAEtf,GAAA;IAAKY,IAAA,GAAOZ;EAAI,IAAAwhB,KAAA;EAGlB,IAAI5gB,IAAA,KAAS,MAAM;IACjB,MAAMgf,MAAA,GAAS,mBAAItO,GAAA,CAAI;IACvB,OAAOlP,KAAA,CAAMsB,GAAA,CAAI8K,IAAA,IAAQ;MACvB,MAAMC,CAAA,GACJ6Q,eAAA,IACAA,eAAA,CAAgBqB,IAAA,CACdD,EAAA,IACEA,EAAA,CAAElS,IAAA,KAASA,IAAA,IACXkS,EAAA,CAAEb,KAAA,4BACF,CAACD,MAAA,CAAO6B,GAAA,CAAIf,EAAC,CACjB;MACF,IAAIjS,CAAA,EAAG;QACLmR,MAAA,CAAOrb,GAAA,CAAIkK,CAAC;QACZ,OAAOA,CAAA,CAAEzO,GAAA;MACX;MACA,OAAOuhB,OAAA;IACT,CAAC;EACH;EACA,OAAO/C,IAAA,CAAGxd,GAAA,CAAIJ,IAAI,IAAIwB,KAAA,GAAQoc,IAAA,CAAG1e,GAAA,CAAIc,IAAI,IAAIwB,KAAA,CAAMsB,GAAA,CAAI9C,IAAI,IAAI6d,QAAA,CAAQ7d,IAAI;AAC7E;;;AC5dA,SAAS/B,IAAA,IAAA6iB,KAAA,EAAMC,QAAA,EAAU7iB,yBAAA,IAAA8iB,0BAAA,QAAiC;AAmCnD,IAAMC,SAAA,GAAY,SAAAA,CAAA,EAQnB;EAAA,IAAAC,KAAA,GAAAriB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkB,SAAA,GAAAlB,SAAA,MALgB,CAAC;IAHG;MACxBsiB;IAEF,IAAAD,KAAA;IADKE,aAAA,GAAApH,wBAAA,CAAAkH,KAAA,EAAAG,UAAA;EAOH,MAAM,CAACC,YAAA,EAAcC,GAAG,IAAI/E,SAAA,CAC1B,MAAA7Z,aAAA;IACE6e,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,eAAA,EAAiB;IACjBC,eAAA,EAAiB;EAAA,GACdP,aAAA,CACL,EACA,EACF;EAEAJ,0BAAA,CAA0B,MAAM;IAC9B,MAAMY,aAAA,GAAgBb,QAAA,CACpBc,MAAA,IAAc;MAAA,IAAb;QAAEtF,CAAA;QAAGuF;MAAE,IAAAD,MAAA;MACNN,GAAA,CAAIhd,KAAA,CAAM;QACRid,OAAA,EAASjF,CAAA,CAAEpY,OAAA;QACXud,eAAA,EAAiBnF,CAAA,CAAErK,QAAA;QACnBuP,OAAA,EAASK,CAAA,CAAE3d,OAAA;QACXwd,eAAA,EAAiBG,CAAA,CAAE5P;MACrB,CAAC;IACH,GACA;MAAEiP,SAAA,EAAW,CAAAA,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAWhd,OAAA,KAAW;IAAU,CAC/C;IAEA,OAAO,MAAM;MAIX2c,KAAA,CAAK5gB,MAAA,CAAOsJ,MAAA,CAAO8X,YAAY,GAAG3iB,KAAA,IAASA,KAAA,CAAM8O,IAAA,CAAK,CAAC;MAEvDmU,aAAA,CAAc;IAChB;EACF,GAAG,EAAE;EAEL,OAAON,YAAA;AACT;;;AC/EA,SAASS,QAAA,EAAU9jB,IAAA,IAAA+jB,KAAA,EAAM9jB,yBAAA,IAAA+jB,0BAAA,QAAiC;AAmCnD,IAAMC,SAAA,GAAYC,MAAA,IAMnB;EAAA,IANoB;MACxBhB;IAEF,IAAAgB,MAAA;IADKf,aAAA,GAAApH,wBAAA,CAAAmI,MAAA,EAAAC,UAAA;EAKH,MAAM,CAACC,UAAA,EAAYd,GAAG,IAAI/E,SAAA,CACxB,MAAA7Z,aAAA;IACE2f,KAAA,EAAO;IACPC,MAAA,EAAQ;EAAA,GACLnB,aAAA,CACL,EACA,EACF;EAEAa,0BAAA,CAA0B,MAAM;IAC9B,MAAML,aAAA,GAAgBG,QAAA,CACpBS,MAAA,IAAuB;MAAA,IAAtB;QAAEF,KAAA;QAAOC;MAAO,IAAAC,MAAA;MACfjB,GAAA,CAAIhd,KAAA,CAAM;QACR+d,KAAA;QACAC,MAAA;QACAxhB,SAAA,EACEshB,UAAA,CAAWC,KAAA,CAAMzW,GAAA,CAAI,MAAM,KAAKwW,UAAA,CAAWE,MAAA,CAAO1W,GAAA,CAAI,MAAM;MAChE,CAAC;IACH,GACA;MAAEsV,SAAA,EAAW,CAAAA,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAWhd,OAAA,KAAW;IAAU,CAC/C;IAEA,OAAO,MAAM;MAIX6d,KAAA,CAAK9hB,MAAA,CAAOsJ,MAAA,CAAO6Y,UAAU,GAAG1jB,KAAA,IAASA,KAAA,CAAM8O,IAAA,CAAK,CAAC;MAErDmU,aAAA,CAAc;IAChB;EACF,GAAG,EAAE;EAEL,OAAOS,UAAA;AACT;;;AC5EA,SAAoBhd,MAAA,IAAAod,OAAA,EAAQ/F,QAAA,IAAAgG,SAAA,QAAgB;AAC5C,SAASvkB,EAAA,IAAAwkB,IAAA,EAAIzkB,yBAAA,IAAA0kB,0BAAA,QAAiC;AAc9C,IAAMC,uBAAA,GAA0B;EAC9BC,GAAA,EAAK;EACL9d,GAAA,EAAK;AACP;AAcO,SAAS+d,UACdrjB,KAAA,EACAX,IAAA,EACA;EACA,MAAM,CAACikB,QAAA,EAAUC,WAAW,IAAIP,SAAA,CAAS,KAAK;EAC9C,MAAMjiB,GAAA,GAAMgiB,OAAA,CAAiB;EAE7B,MAAMnH,OAAA,GAAUqH,IAAA,CAAGzjB,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EAEjC,MAAMwjB,YAAA,GAAe5H,OAAA,GAAUA,OAAA,CAAQ,IAAI,CAAC;EAC5C,MAAM;MAAE9a,EAAA,EAAAgC,GAAA,GAAK,CAAC;MAAGjC,IAAA,GAAO,CAAC;IAAsB,IAAI2iB,YAAA;IAApBC,eAAA,GAAAnJ,wBAAA,CAAoBkJ,YAAA,EAAAE,UAAA;EAEnD,MAAMC,qBAAA,GAAwB/H,OAAA,GAAUvc,IAAA,GAAOW,KAAA;EAE/C,MAAM,CAACoY,OAAA,EAASyJ,GAAG,IAAI/E,SAAA,CAAU,MAAA7Z,aAAA;IAASpC;EAAA,GAAS4iB,eAAA,CAAgB,EAAI,EAAE;EAEzEP,0BAAA,CAA0B,MAAM;IAC9B,MAAMU,OAAA,GAAU7iB,GAAA,CAAI0D,OAAA;IACpB,MAAAof,MAAA,GAKIF,qBAAA,aAAAA,qBAAA,cAAAA,qBAAA,GAAyB,CAAC;MALxB;QACJG,IAAA;QACAC,IAAA;QACAC,MAAA,GAAS;MAEX,IAAAH,MAAA;MADKI,QAAA,GAAA3J,wBAAA,CAAAuJ,MAAA,EAAAK,UAAA;IAGL,IACE,CAACN,OAAA,IACAG,IAAA,IAAQT,QAAA,IACT,OAAOa,oBAAA,KAAyB,aAEhC;IAEF,MAAMC,mBAAA,GAAsB,mBAAIC,OAAA,CAA+B;IAE/D,MAAMC,OAAA,GAAUA,CAAA,KAAM;MACpB,IAAIxhB,GAAA,EAAI;QAEN+e,GAAA,CAAIhd,KAAA,CAAM/B,GAAE;MACd;MAEAygB,WAAA,CAAY,IAAI;MAEhB,MAAMgB,OAAA,GAAUA,CAAA,KAAM;QACpB,IAAI1jB,IAAA,EAAM;UACRghB,GAAA,CAAIhd,KAAA,CAAMhE,IAAI;QAChB;QACA0iB,WAAA,CAAY,KAAK;MACnB;MAEA,OAAOQ,IAAA,GAAO,SAAYQ,OAAA;IAC5B;IAEA,MAAMC,kBAAA,GAAmDC,OAAA,IAAW;MAClEA,OAAA,CAAQ5S,OAAA,CAAQ6S,KAAA,IAAS;QACvB,MAAMC,OAAA,GAAUP,mBAAA,CAAoBjY,GAAA,CAAIuY,KAAA,CAAM7Y,MAAM;QAEpD,IAAI6Y,KAAA,CAAME,cAAA,KAAmBC,OAAA,CAAQF,OAAO,GAAG;UAC7C;QACF;QAEA,IAAID,KAAA,CAAME,cAAA,EAAgB;UACxB,MAAME,UAAA,GAAaR,OAAA,CAAQ;UAC3B,IAAIrB,IAAA,CAAGzjB,GAAA,CAAIslB,UAAU,GAAG;YACtBV,mBAAA,CAAoBnQ,GAAA,CAAIyQ,KAAA,CAAM7Y,MAAA,EAAQiZ,UAAU;UAClD,OAAO;YACL7K,QAAA,CAAS8K,SAAA,CAAUL,KAAA,CAAM7Y,MAAM;UACjC;QACF,WAAW8Y,OAAA,EAAS;UAClBA,OAAA,CAAQ;UACRP,mBAAA,CAAoBtgB,MAAA,CAAO4gB,KAAA,CAAM7Y,MAAM;QACzC;MACF,CAAC;IACH;IAEA,MAAMoO,QAAA,GAAW,IAAIkK,oBAAA,CAAqBK,kBAAA,EAAAvhB,aAAA;MACxC6gB,IAAA,EAAOA,IAAA,IAAQA,IAAA,CAAKrf,OAAA,IAAY;MAChCugB,SAAA,EACE,OAAOhB,MAAA,KAAW,YAAY1kB,KAAA,CAAM2lB,OAAA,CAAQjB,MAAM,IAC9CA,MAAA,GACAb,uBAAA,CAAwBa,MAAM;IAAA,GACjCC,QAAA,CACJ;IAEDhK,QAAA,CAASiL,OAAA,CAAQtB,OAAO;IAExB,OAAO,MAAM3J,QAAA,CAAS8K,SAAA,CAAUnB,OAAO;EACzC,GAAG,CAACD,qBAAqB,CAAC;EAE1B,IAAI/H,OAAA,EAAS;IACX,OAAO,CAAC7a,GAAA,EAAKqX,OAAO;EACtB;EAEA,OAAO,CAACrX,GAAA,EAAKuiB,QAAQ;AACvB;;;ACtGO,SAAS6B,OAAAC,MAAA,EAAoC;EAAA,IAA7B;MAAE9iB;IAAmB,IAAA8iB,MAAA;IAANplB,KAAA,GAAAsa,wBAAA,CAAA8K,MAAA,EAAAC,UAAA;EACpC,OAAO/iB,QAAA,CAASwa,SAAA,CAAU9c,KAAK,CAAC;AAClC;;;ACvBA,SAASvB,EAAA,IAAA6mB,IAAA,QAAU;AAgBZ,SAASC,MAAAC,MAAA,EAI2C;EAAA,IAJU;MACnE1jB,KAAA;MACAQ;IAEF,IAAAkjB,MAAA;IADKxlB,KAAA,GAAAsa,wBAAA,CAAAkL,MAAA,EAAAC,UAAA;EAEH,MAAMC,MAAA,GAAgBhI,QAAA,CAAS5b,KAAA,CAAM1C,MAAA,EAAQY,KAAK;EAClD,OAAO8B,KAAA,CAAMsB,GAAA,CAAI,CAAC8K,IAAA,EAAMuN,KAAA,KAAU;IAChC,MAAMzP,MAAA,GAAS1J,QAAA,CAAS4L,IAAA,EAAMuN,KAAK;IACnC,OAAO6J,IAAA,CAAG9lB,GAAA,CAAIwM,MAAM,IAAIA,MAAA,CAAO0Z,MAAA,CAAOjK,KAAK,CAAC,IAAIzP,MAAA;EAClD,CAAC;AACH;;;AClBO,SAAS2Z,WAAAC,MAAA,EAIkB;EAAA,IAJP;MACzB9jB,KAAA;MACAQ;IAEF,IAAAsjB,MAAA;IADK5lB,KAAA,GAAAsa,wBAAA,CAAAsL,MAAA,EAAAC,UAAA;EAEH,OAAOpH,aAAA,CAAc3c,KAAA,EAAO9B,KAAK,EAAEsC,QAAQ;AAC7C;;;AChBA,SAAqBgM,oBAAA,IAAAwX,qBAAA,QAA4B;;;ACCjD,SACErnB,EAAA,IAAAsnB,IAAA,EACA5f,GAAA,IAAA6f,IAAA,EACAznB,IAAA,IAAA0nB,MAAA,EACA3f,OAAA,IAAA4f,QAAA,EACAxnB,OAAA,IAAAynB,QAAA,EACA1f,SAAA,IAAA2f,UAAA,EAEAxnB,aAAA,IAAAynB,cAAA,EACAC,kBAAA,EACAxnB,OAAA,IAAWynB,EAAA,EACXxf,kBAAA,IAAAyf,mBAAA,EACAtf,gBAAA,IAAAuf,iBAAA,EACAtf,mBAAA,IAAAuf,oBAAA,EACAzf,aAAA,IAAA0f,cAAA,QACK;AAGP,SACEnf,WAAA,IAAAof,YAAA,EACAlf,WAAA,IAAAmf,YAAA,EACAlf,eAAA,IAAAmf,gBAAA,EACAvf,UAAA,IAAAwf,WAAA,QACK;AAUA,IAAMC,aAAA,GAAN,cAGGrY,UAAA,CAAmB;EAa3B/F,YAEWqe,MAAA,EACT5nB,IAAA,EACA;IACA,MAAM;IAHG,KAAA4nB,MAAA,GAAAA,MAAA;IAVX;IAAA,KAAAnX,IAAA,GAAO;IAMP;IAAA,KAAUwI,OAAA,GAAU,mBAAItH,GAAA,CAAgB;IAQtC,KAAKkW,IAAA,GAAOZ,kBAAA,CAAmB,GAAGjnB,IAAI;IAEtC,MAAMJ,KAAA,GAAQ,KAAKkoB,IAAA,CAAK;IACxB,MAAMnR,QAAA,GAAW8Q,gBAAA,CAAgB7nB,KAAK;IAGtC4nB,YAAA,CAAY,MAAM7Q,QAAA,CAASW,MAAA,CAAO1X,KAAK,CAAC;EAC1C;EAEAwS,QAAQ2V,GAAA,EAAc;IACpB,MAAMnoB,KAAA,GAAQ,KAAKkoB,IAAA,CAAK;IACxB,MAAME,QAAA,GAAW,KAAKlb,GAAA,CAAI;IAC1B,IAAI,CAAC+Z,QAAA,CAAQjnB,KAAA,EAAOooB,QAAQ,GAAG;MAC7BT,YAAA,CAAY,IAAI,EAAGhT,QAAA,CAAS3U,KAAK;MACjC,KAAK4Q,SAAA,CAAU5Q,KAAA,EAAO,KAAK6Q,IAAI;IACjC;IAEA,IAAI,CAAC,KAAKA,IAAA,IAAQwX,SAAA,CAAU,KAAKhP,OAAO,GAAG;MACzCiP,UAAA,CAAW,IAAI;IACjB;EACF;EAEUJ,KAAA,EAAO;IACf,MAAMK,MAAA,GAAwBzB,IAAA,CAAG5iB,GAAA,CAAI,KAAK8jB,MAAM,IAC5C,KAAKA,MAAA,CAAO7jB,GAAA,CAAIijB,cAAa,IAC5BF,QAAA,CAAQE,cAAA,CAAc,KAAKY,MAAM,CAAC;IAEvC,OAAO,KAAKC,IAAA,CAAK,GAAGM,MAAM;EAC5B;EAEU9S,OAAA,EAAS;IACjB,IAAI,KAAK5E,IAAA,IAAQ,CAACwX,SAAA,CAAU,KAAKhP,OAAO,GAAG;MACzC,KAAKxI,IAAA,GAAO;MAEZmW,MAAA,CAAKc,WAAA,CAAW,IAAI,GAAI9X,IAAA,IAAQ;QAC9BA,IAAA,CAAK6C,IAAA,GAAO;MACd,CAAC;MAED,IAAIyU,EAAA,CAAEtb,aAAA,EAAe;QACnB+a,IAAA,CAAI/X,cAAA,CAAe,MAAM,KAAKwD,OAAA,CAAQ,CAAC;QACvC8V,UAAA,CAAW,IAAI;MACjB,OAAO;QACLnB,UAAA,CAAUvhB,KAAA,CAAM,IAAI;MACtB;IACF;EACF;EAAA;EAGU6K,QAAA,EAAU;IAClB,IAAIX,QAAA,GAAW;IACfkX,MAAA,CAAKE,QAAA,CAAQ,KAAKc,MAAM,GAAGA,MAAA,IAAU;MACnC,IAAIN,cAAA,CAAcM,MAAM,GAAG;QACzBR,iBAAA,CAAiBQ,MAAA,EAAQ,IAAI;MAC/B;MACA,IAAIvY,YAAA,CAAauY,MAAM,GAAG;QACxB,IAAI,CAACA,MAAA,CAAOnX,IAAA,EAAM;UAChB,KAAKwI,OAAA,CAAQrU,GAAA,CAAIgjB,MAAM;QACzB;QACAlY,QAAA,GAAW1F,IAAA,CAAKoe,GAAA,CAAI1Y,QAAA,EAAUkY,MAAA,CAAOlY,QAAA,GAAW,CAAC;MACnD;IACF,CAAC;IACD,KAAKA,QAAA,GAAWA,QAAA;IAChB,KAAK2F,MAAA,CAAO;EACd;EAAA;EAGU9E,QAAA,EAAU;IAClBqW,MAAA,CAAKE,QAAA,CAAQ,KAAKc,MAAM,GAAGA,MAAA,IAAU;MACnC,IAAIN,cAAA,CAAcM,MAAM,GAAG;QACzBP,oBAAA,CAAoBO,MAAA,EAAQ,IAAI;MAClC;IACF,CAAC;IACD,KAAK3O,OAAA,CAAQlK,KAAA,CAAM;IACnBmZ,UAAA,CAAW,IAAI;EACjB;EAAA;EAGA/S,cAAcC,KAAA,EAAyB;IAGrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,IAAI0E,KAAA,CAAM3E,IAAA,EAAM;QACd,KAAK2B,OAAA,CAAQ;MACf,OAAO;QACL,KAAK6G,OAAA,CAAQrU,GAAA,CAAIwQ,KAAA,CAAMzE,MAAM;QAC7B,KAAK0E,MAAA,CAAO;MACd;IACF,WAGSD,KAAA,CAAM1E,IAAA,IAAQ,QAAQ;MAC7B,KAAKuI,OAAA,CAAQxU,MAAA,CAAO2Q,KAAA,CAAMzE,MAAM;IAClC,WAGSyE,KAAA,CAAM1E,IAAA,IAAQ,YAAY;MACjC,KAAKhB,QAAA,GAAWoX,QAAA,CAAQ,KAAKc,MAAM,EAAES,MAAA,CACnC,CAACC,OAAA,EAAiB3X,MAAA,KAChB3G,IAAA,CAAKoe,GAAA,CAAIE,OAAA,GAAUjZ,YAAA,CAAasB,MAAM,IAAIA,MAAA,CAAOjB,QAAA,GAAW,KAAK,CAAC,GACpE,CACF;IACF;EACF;AACF;AAGA,SAAS6Y,OAAOX,MAAA,EAAa;EAC3B,OAAOA,MAAA,CAAOnX,IAAA,KAAS;AACzB;AAGA,SAASwX,UAAU3W,MAAA,EAAyB;EAG1C,OAAO,CAACA,MAAA,CAAOqG,IAAA,IAAQ1X,KAAA,CAAMuB,IAAA,CAAK8P,MAAM,EAAEvE,KAAA,CAAMwb,MAAM;AACxD;AAGA,SAASL,WAAWM,IAAA,EAAqB;EACvC,IAAI,CAACA,IAAA,CAAK/X,IAAA,EAAM;IACd+X,IAAA,CAAK/X,IAAA,GAAO;IAEZmW,MAAA,CAAKc,WAAA,CAAWc,IAAI,GAAI5Y,IAAA,IAAQ;MAC9BA,IAAA,CAAK6C,IAAA,GAAO;IACd,CAAC;IAED0U,mBAAA,CAAmBqB,IAAA,EAAM;MACvB9X,IAAA,EAAM;MACNC,MAAA,EAAQ6X;IACV,CAAC;EACH;AACF;;;AD/KO,IAAM/mB,EAAA,GAAmB,SAAAA,CAACmmB,MAAA;EAAA,SAAAa,KAAA,GAAA3oB,SAAA,CAAAC,MAAA,EAAgBC,IAAA,OAAAC,KAAA,CAAAwoB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAA1oB,IAAA,CAAA0oB,KAAA,QAAA5oB,SAAA,CAAA4oB,KAAA;EAAA;EAAA,OAC/C,IAAIf,aAAA,CAAcC,MAAA,EAAQ5nB,IAAI;AAAA;AAGzB,IAAMgQ,WAAA,GAA4B,SAAAA,CAAC4X,MAAA;EAAA,SAAAe,KAAA,GAAA7oB,SAAA,CAAAC,MAAA,EAAgBC,IAAA,OAAAC,KAAA,CAAA0oB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAA5oB,IAAA,CAAA4oB,KAAA,QAAA9oB,SAAA,CAAA8oB,KAAA;EAAA;EAAA,OACxDnC,qBAAA,CAAqB,GAAG,IAAIkB,aAAA,CAAcC,MAAA,EAAQ5nB,IAAI;AAAA;;;AEjBxD,SACEP,OAAA,EACA2H,SAAA,IAAAyhB,UAAA,EACA7kB,wBAAA,QACK;AAIPvE,OAAA,CAAQgK,MAAA,CAAO;EACbzF,wBAAA;EACAvC,EAAA,EAAIA,CAACmmB,MAAA,EAAQ5nB,IAAA,KAAS,IAAI2nB,aAAA,CAAcC,MAAA,EAAQ5nB,IAAI;AACtD,CAAC;AAKM,IAAM+C,MAAA,GAAS8lB,UAAA,CAAUzW,OAAA;;;ACFhC,SACE6U,kBAAA,IAAA6B,mBAAA,EACA3pB,yBAAA,IAAA4pB,0BAAA,EACAC,gBAAA,EACAxgB,OAAA,IAAAygB,QAAA,QACK;AAIP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}