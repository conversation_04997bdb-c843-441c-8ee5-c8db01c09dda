{"ast": null, "code": "/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nexport default isHostComponent;", "map": {"version": 3, "names": ["isHostComponent", "element"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/utils/esm/isHostComponent/isHostComponent.js"], "sourcesContent": ["/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nexport default isHostComponent;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,OAAO,EAAE;EAChC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AACA,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}