{"ast": null, "code": "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      var TempCtor = function () {};\n      TempCtor.prototype = superCtor.prototype;\n      ctor.prototype = new TempCtor();\n      ctor.prototype.constructor = ctor;\n    }\n  };\n}", "map": {"version": 3, "names": ["Object", "create", "module", "exports", "inherits", "ctor", "superCtor", "super_", "prototype", "constructor", "value", "enumerable", "writable", "configurable", "TempCtor"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/inherits/inherits_browser.js"], "sourcesContent": ["if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n"], "mappings": "AAAA,IAAI,OAAOA,MAAM,CAACC,MAAM,KAAK,UAAU,EAAE;EACvC;EACAC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAClD,IAAIA,SAAS,EAAE;MACbD,IAAI,CAACE,MAAM,GAAGD,SAAS;MACvBD,IAAI,CAACG,SAAS,GAAGR,MAAM,CAACC,MAAM,CAACK,SAAS,CAACE,SAAS,EAAE;QAClDC,WAAW,EAAE;UACXC,KAAK,EAAEL,IAAI;UACXM,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,IAAI;UACdC,YAAY,EAAE;QAChB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC,MAAM;EACL;EACAX,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAClD,IAAIA,SAAS,EAAE;MACbD,IAAI,CAACE,MAAM,GAAGD,SAAS;MACvB,IAAIQ,QAAQ,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;MAC7BA,QAAQ,CAACN,SAAS,GAAGF,SAAS,CAACE,SAAS;MACxCH,IAAI,CAACG,SAAS,GAAG,IAAIM,QAAQ,CAAC,CAAC;MAC/BT,IAAI,CAACG,SAAS,CAACC,WAAW,GAAGJ,IAAI;IACnC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}