{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nfunction initializeValue(key, defaultValue) {\n  if (typeof window === 'undefined') {\n    return undefined;\n  }\n  let value;\n  try {\n    value = localStorage.getItem(key) || undefined;\n    if (!value) {\n      // the first time that user enters the site.\n      localStorage.setItem(key, defaultValue);\n    }\n  } catch (e) {\n    // Unsupported\n  }\n  return value || defaultValue;\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const [state, setState] = React.useState(() => {\n    const initialMode = initializeValue(modeStorageKey, defaultMode);\n    const lightColorScheme = initializeValue(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n    const darkColorScheme = initializeValue(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode != null ? mode : defaultMode;\n      try {\n        localStorage.setItem(modeStorageKey, newMode);\n      } catch (e) {\n        // Unsupported\n      }\n      return _extends({}, currentState, {\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      });\n    });\n  }, [modeStorageKey, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        try {\n          localStorage.setItem(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n          localStorage.setItem(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n        } catch (e) {\n          // Unsupported\n        }\n        return _extends({}, currentState, {\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        });\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(`\\`${value}\\` does not exist in \\`theme.colorSchemes\\`.`);\n      } else {\n        setState(currentState => {\n          const newState = _extends({}, currentState);\n          processState(currentState, mode => {\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-${mode}`, value);\n            } catch (e) {\n              // Unsupported\n            }\n            if (mode === 'light') {\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = _extends({}, currentState);\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(`\\`${newLightColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-light`, newLightColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(`\\`${newDarkColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-dark`, newDarkColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, colorSchemeStorageKey, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event != null && event.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return _extends({}, currentState, {\n          systemMode\n        });\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    const handler = (...args) => mediaListener.current(...args);\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, []);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (storageWindow) {\n      const handleStorage = event => {\n        const value = event.newValue;\n        if (typeof event.key === 'string' && event.key.startsWith(colorSchemeStorageKey) && (!value || joinedColorSchemes.match(value))) {\n          // If the key is deleted, value will be null then reset color scheme to the default one.\n          if (event.key.endsWith('light')) {\n            setColorScheme({\n              light: value\n            });\n          }\n          if (event.key.endsWith('dark')) {\n            setColorScheme({\n              dark: value\n            });\n          }\n        }\n        if (event.key === modeStorageKey && (!value || ['light', 'dark', 'system'].includes(value))) {\n          setMode(value || defaultMode);\n        }\n      };\n      // For syncing color-scheme changes between iframes\n      storageWindow.addEventListener('storage', handleStorage);\n      return () => {\n        storageWindow.removeEventListener('storage', handleStorage);\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, modeStorageKey, colorSchemeStorageKey, joinedColorSchemes, defaultMode, storageWindow]);\n  return _extends({}, state, {\n    colorScheme,\n    setMode,\n    setColorScheme\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "DEFAULT_MODE_STORAGE_KEY", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "getSystemMode", "mode", "window", "mql", "matchMedia", "matches", "undefined", "processState", "state", "callback", "systemMode", "getColorScheme", "lightColorScheme", "darkColorScheme", "initializeValue", "key", "defaultValue", "value", "localStorage", "getItem", "setItem", "e", "useCurrentColorScheme", "options", "defaultMode", "defaultLightColorScheme", "defaultDarkColorScheme", "supportedColorSchemes", "modeStorageKey", "colorSchemeStorageKey", "storageWindow", "joinedColorSchemes", "join", "setState", "useState", "initialMode", "colorScheme", "setMode", "useCallback", "currentState", "newMode", "setColorScheme", "includes", "console", "error", "newState", "newLightColorScheme", "light", "newDarkColorScheme", "dark", "handleMediaQuery", "event", "mediaListener", "useRef", "current", "useEffect", "handler", "args", "media", "addListener", "removeListener", "handleStorage", "newValue", "startsWith", "match", "endsWith", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DEFAULT_MODE_STORAGE_KEY, DEFAULT_COLOR_SCHEME_STORAGE_KEY } from '../InitColorSchemeScript/InitColorSchemeScript';\nexport function getSystemMode(mode) {\n  if (typeof window !== 'undefined' && mode === 'system') {\n    const mql = window.matchMedia('(prefers-color-scheme: dark)');\n    if (mql.matches) {\n      return 'dark';\n    }\n    return 'light';\n  }\n  return undefined;\n}\nfunction processState(state, callback) {\n  if (state.mode === 'light' || state.mode === 'system' && state.systemMode === 'light') {\n    return callback('light');\n  }\n  if (state.mode === 'dark' || state.mode === 'system' && state.systemMode === 'dark') {\n    return callback('dark');\n  }\n  return undefined;\n}\nexport function getColorScheme(state) {\n  return processState(state, mode => {\n    if (mode === 'light') {\n      return state.lightColorScheme;\n    }\n    if (mode === 'dark') {\n      return state.darkColorScheme;\n    }\n    return undefined;\n  });\n}\nfunction initializeValue(key, defaultValue) {\n  if (typeof window === 'undefined') {\n    return undefined;\n  }\n  let value;\n  try {\n    value = localStorage.getItem(key) || undefined;\n    if (!value) {\n      // the first time that user enters the site.\n      localStorage.setItem(key, defaultValue);\n    }\n  } catch (e) {\n    // Unsupported\n  }\n  return value || defaultValue;\n}\nexport default function useCurrentColorScheme(options) {\n  const {\n    defaultMode = 'light',\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    supportedColorSchemes = [],\n    modeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    storageWindow = typeof window === 'undefined' ? undefined : window\n  } = options;\n  const joinedColorSchemes = supportedColorSchemes.join(',');\n  const [state, setState] = React.useState(() => {\n    const initialMode = initializeValue(modeStorageKey, defaultMode);\n    const lightColorScheme = initializeValue(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n    const darkColorScheme = initializeValue(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n    return {\n      mode: initialMode,\n      systemMode: getSystemMode(initialMode),\n      lightColorScheme,\n      darkColorScheme\n    };\n  });\n  const colorScheme = getColorScheme(state);\n  const setMode = React.useCallback(mode => {\n    setState(currentState => {\n      if (mode === currentState.mode) {\n        // do nothing if mode does not change\n        return currentState;\n      }\n      const newMode = mode != null ? mode : defaultMode;\n      try {\n        localStorage.setItem(modeStorageKey, newMode);\n      } catch (e) {\n        // Unsupported\n      }\n      return _extends({}, currentState, {\n        mode: newMode,\n        systemMode: getSystemMode(newMode)\n      });\n    });\n  }, [modeStorageKey, defaultMode]);\n  const setColorScheme = React.useCallback(value => {\n    if (!value) {\n      setState(currentState => {\n        try {\n          localStorage.setItem(`${colorSchemeStorageKey}-light`, defaultLightColorScheme);\n          localStorage.setItem(`${colorSchemeStorageKey}-dark`, defaultDarkColorScheme);\n        } catch (e) {\n          // Unsupported\n        }\n        return _extends({}, currentState, {\n          lightColorScheme: defaultLightColorScheme,\n          darkColorScheme: defaultDarkColorScheme\n        });\n      });\n    } else if (typeof value === 'string') {\n      if (value && !joinedColorSchemes.includes(value)) {\n        console.error(`\\`${value}\\` does not exist in \\`theme.colorSchemes\\`.`);\n      } else {\n        setState(currentState => {\n          const newState = _extends({}, currentState);\n          processState(currentState, mode => {\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-${mode}`, value);\n            } catch (e) {\n              // Unsupported\n            }\n            if (mode === 'light') {\n              newState.lightColorScheme = value;\n            }\n            if (mode === 'dark') {\n              newState.darkColorScheme = value;\n            }\n          });\n          return newState;\n        });\n      }\n    } else {\n      setState(currentState => {\n        const newState = _extends({}, currentState);\n        const newLightColorScheme = value.light === null ? defaultLightColorScheme : value.light;\n        const newDarkColorScheme = value.dark === null ? defaultDarkColorScheme : value.dark;\n        if (newLightColorScheme) {\n          if (!joinedColorSchemes.includes(newLightColorScheme)) {\n            console.error(`\\`${newLightColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.lightColorScheme = newLightColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-light`, newLightColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        if (newDarkColorScheme) {\n          if (!joinedColorSchemes.includes(newDarkColorScheme)) {\n            console.error(`\\`${newDarkColorScheme}\\` does not exist in \\`theme.colorSchemes\\`.`);\n          } else {\n            newState.darkColorScheme = newDarkColorScheme;\n            try {\n              localStorage.setItem(`${colorSchemeStorageKey}-dark`, newDarkColorScheme);\n            } catch (error) {\n              // Unsupported\n            }\n          }\n        }\n        return newState;\n      });\n    }\n  }, [joinedColorSchemes, colorSchemeStorageKey, defaultLightColorScheme, defaultDarkColorScheme]);\n  const handleMediaQuery = React.useCallback(event => {\n    if (state.mode === 'system') {\n      setState(currentState => {\n        const systemMode = event != null && event.matches ? 'dark' : 'light';\n\n        // Early exit, nothing changed.\n        if (currentState.systemMode === systemMode) {\n          return currentState;\n        }\n        return _extends({}, currentState, {\n          systemMode\n        });\n      });\n    }\n  }, [state.mode]);\n\n  // Ref hack to avoid adding handleMediaQuery as a dep\n  const mediaListener = React.useRef(handleMediaQuery);\n  mediaListener.current = handleMediaQuery;\n  React.useEffect(() => {\n    const handler = (...args) => mediaListener.current(...args);\n\n    // Always listen to System preference\n    const media = window.matchMedia('(prefers-color-scheme: dark)');\n\n    // Intentionally use deprecated listener methods to support iOS & old browsers\n    media.addListener(handler);\n    handler(media);\n    return () => {\n      media.removeListener(handler);\n    };\n  }, []);\n\n  // Handle when localStorage has changed\n  React.useEffect(() => {\n    if (storageWindow) {\n      const handleStorage = event => {\n        const value = event.newValue;\n        if (typeof event.key === 'string' && event.key.startsWith(colorSchemeStorageKey) && (!value || joinedColorSchemes.match(value))) {\n          // If the key is deleted, value will be null then reset color scheme to the default one.\n          if (event.key.endsWith('light')) {\n            setColorScheme({\n              light: value\n            });\n          }\n          if (event.key.endsWith('dark')) {\n            setColorScheme({\n              dark: value\n            });\n          }\n        }\n        if (event.key === modeStorageKey && (!value || ['light', 'dark', 'system'].includes(value))) {\n          setMode(value || defaultMode);\n        }\n      };\n      // For syncing color-scheme changes between iframes\n      storageWindow.addEventListener('storage', handleStorage);\n      return () => {\n        storageWindow.removeEventListener('storage', handleStorage);\n      };\n    }\n    return undefined;\n  }, [setColorScheme, setMode, modeStorageKey, colorSchemeStorageKey, joinedColorSchemes, defaultMode, storageWindow]);\n  return _extends({}, state, {\n    colorScheme,\n    setMode,\n    setColorScheme\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,EAAEC,gCAAgC,QAAQ,gDAAgD;AAC3H,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAID,IAAI,KAAK,QAAQ,EAAE;IACtD,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;IAC7D,IAAID,GAAG,CAACE,OAAO,EAAE;MACf,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB;EACA,OAAOC,SAAS;AAClB;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACrC,IAAID,KAAK,CAACP,IAAI,KAAK,OAAO,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,OAAO,EAAE;IACrF,OAAOD,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACA,IAAID,KAAK,CAACP,IAAI,KAAK,MAAM,IAAIO,KAAK,CAACP,IAAI,KAAK,QAAQ,IAAIO,KAAK,CAACE,UAAU,KAAK,MAAM,EAAE;IACnF,OAAOD,QAAQ,CAAC,MAAM,CAAC;EACzB;EACA,OAAOH,SAAS;AAClB;AACA,OAAO,SAASK,cAAcA,CAACH,KAAK,EAAE;EACpC,OAAOD,YAAY,CAACC,KAAK,EAAEP,IAAI,IAAI;IACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOO,KAAK,CAACI,gBAAgB;IAC/B;IACA,IAAIX,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOO,KAAK,CAACK,eAAe;IAC9B;IACA,OAAOP,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,SAASQ,eAAeA,CAACC,GAAG,EAAEC,YAAY,EAAE;EAC1C,IAAI,OAAOd,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOI,SAAS;EAClB;EACA,IAAIW,KAAK;EACT,IAAI;IACFA,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACJ,GAAG,CAAC,IAAIT,SAAS;IAC9C,IAAI,CAACW,KAAK,EAAE;MACV;MACAC,YAAY,CAACE,OAAO,CAACL,GAAG,EAAEC,YAAY,CAAC;IACzC;EACF,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV;EAAA;EAEF,OAAOJ,KAAK,IAAID,YAAY;AAC9B;AACA,eAAe,SAASM,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,OAAO;IACrBC,uBAAuB;IACvBC,sBAAsB;IACtBC,qBAAqB,GAAG,EAAE;IAC1BC,cAAc,GAAG9B,wBAAwB;IACzC+B,qBAAqB,GAAG9B,gCAAgC;IACxD+B,aAAa,GAAG,OAAO5B,MAAM,KAAK,WAAW,GAAGI,SAAS,GAAGJ;EAC9D,CAAC,GAAGqB,OAAO;EACX,MAAMQ,kBAAkB,GAAGJ,qBAAqB,CAACK,IAAI,CAAC,GAAG,CAAC;EAC1D,MAAM,CAACxB,KAAK,EAAEyB,QAAQ,CAAC,GAAGpC,KAAK,CAACqC,QAAQ,CAAC,MAAM;IAC7C,MAAMC,WAAW,GAAGrB,eAAe,CAACc,cAAc,EAAEJ,WAAW,CAAC;IAChE,MAAMZ,gBAAgB,GAAGE,eAAe,CAAC,GAAGe,qBAAqB,QAAQ,EAAEJ,uBAAuB,CAAC;IACnG,MAAMZ,eAAe,GAAGC,eAAe,CAAC,GAAGe,qBAAqB,OAAO,EAAEH,sBAAsB,CAAC;IAChG,OAAO;MACLzB,IAAI,EAAEkC,WAAW;MACjBzB,UAAU,EAAEV,aAAa,CAACmC,WAAW,CAAC;MACtCvB,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMuB,WAAW,GAAGzB,cAAc,CAACH,KAAK,CAAC;EACzC,MAAM6B,OAAO,GAAGxC,KAAK,CAACyC,WAAW,CAACrC,IAAI,IAAI;IACxCgC,QAAQ,CAACM,YAAY,IAAI;MACvB,IAAItC,IAAI,KAAKsC,YAAY,CAACtC,IAAI,EAAE;QAC9B;QACA,OAAOsC,YAAY;MACrB;MACA,MAAMC,OAAO,GAAGvC,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGuB,WAAW;MACjD,IAAI;QACFN,YAAY,CAACE,OAAO,CAACQ,cAAc,EAAEY,OAAO,CAAC;MAC/C,CAAC,CAAC,OAAOnB,CAAC,EAAE;QACV;MAAA;MAEF,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,EAAE;QAChCtC,IAAI,EAAEuC,OAAO;QACb9B,UAAU,EAAEV,aAAa,CAACwC,OAAO;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,cAAc,EAAEJ,WAAW,CAAC,CAAC;EACjC,MAAMiB,cAAc,GAAG5C,KAAK,CAACyC,WAAW,CAACrB,KAAK,IAAI;IAChD,IAAI,CAACA,KAAK,EAAE;MACVgB,QAAQ,CAACM,YAAY,IAAI;QACvB,IAAI;UACFrB,YAAY,CAACE,OAAO,CAAC,GAAGS,qBAAqB,QAAQ,EAAEJ,uBAAuB,CAAC;UAC/EP,YAAY,CAACE,OAAO,CAAC,GAAGS,qBAAqB,OAAO,EAAEH,sBAAsB,CAAC;QAC/E,CAAC,CAAC,OAAOL,CAAC,EAAE;UACV;QAAA;QAEF,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,EAAE;UAChC3B,gBAAgB,EAAEa,uBAAuB;UACzCZ,eAAe,EAAEa;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAIA,KAAK,IAAI,CAACc,kBAAkB,CAACW,QAAQ,CAACzB,KAAK,CAAC,EAAE;QAChD0B,OAAO,CAACC,KAAK,CAAC,KAAK3B,KAAK,8CAA8C,CAAC;MACzE,CAAC,MAAM;QACLgB,QAAQ,CAACM,YAAY,IAAI;UACvB,MAAMM,QAAQ,GAAGjD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,CAAC;UAC3ChC,YAAY,CAACgC,YAAY,EAAEtC,IAAI,IAAI;YACjC,IAAI;cACFiB,YAAY,CAACE,OAAO,CAAC,GAAGS,qBAAqB,IAAI5B,IAAI,EAAE,EAAEgB,KAAK,CAAC;YACjE,CAAC,CAAC,OAAOI,CAAC,EAAE;cACV;YAAA;YAEF,IAAIpB,IAAI,KAAK,OAAO,EAAE;cACpB4C,QAAQ,CAACjC,gBAAgB,GAAGK,KAAK;YACnC;YACA,IAAIhB,IAAI,KAAK,MAAM,EAAE;cACnB4C,QAAQ,CAAChC,eAAe,GAAGI,KAAK;YAClC;UACF,CAAC,CAAC;UACF,OAAO4B,QAAQ;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLZ,QAAQ,CAACM,YAAY,IAAI;QACvB,MAAMM,QAAQ,GAAGjD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,CAAC;QAC3C,MAAMO,mBAAmB,GAAG7B,KAAK,CAAC8B,KAAK,KAAK,IAAI,GAAGtB,uBAAuB,GAAGR,KAAK,CAAC8B,KAAK;QACxF,MAAMC,kBAAkB,GAAG/B,KAAK,CAACgC,IAAI,KAAK,IAAI,GAAGvB,sBAAsB,GAAGT,KAAK,CAACgC,IAAI;QACpF,IAAIH,mBAAmB,EAAE;UACvB,IAAI,CAACf,kBAAkB,CAACW,QAAQ,CAACI,mBAAmB,CAAC,EAAE;YACrDH,OAAO,CAACC,KAAK,CAAC,KAAKE,mBAAmB,8CAA8C,CAAC;UACvF,CAAC,MAAM;YACLD,QAAQ,CAACjC,gBAAgB,GAAGkC,mBAAmB;YAC/C,IAAI;cACF5B,YAAY,CAACE,OAAO,CAAC,GAAGS,qBAAqB,QAAQ,EAAEiB,mBAAmB,CAAC;YAC7E,CAAC,CAAC,OAAOF,KAAK,EAAE;cACd;YAAA;UAEJ;QACF;QACA,IAAII,kBAAkB,EAAE;UACtB,IAAI,CAACjB,kBAAkB,CAACW,QAAQ,CAACM,kBAAkB,CAAC,EAAE;YACpDL,OAAO,CAACC,KAAK,CAAC,KAAKI,kBAAkB,8CAA8C,CAAC;UACtF,CAAC,MAAM;YACLH,QAAQ,CAAChC,eAAe,GAAGmC,kBAAkB;YAC7C,IAAI;cACF9B,YAAY,CAACE,OAAO,CAAC,GAAGS,qBAAqB,OAAO,EAAEmB,kBAAkB,CAAC;YAC3E,CAAC,CAAC,OAAOJ,KAAK,EAAE;cACd;YAAA;UAEJ;QACF;QACA,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,kBAAkB,EAAEF,qBAAqB,EAAEJ,uBAAuB,EAAEC,sBAAsB,CAAC,CAAC;EAChG,MAAMwB,gBAAgB,GAAGrD,KAAK,CAACyC,WAAW,CAACa,KAAK,IAAI;IAClD,IAAI3C,KAAK,CAACP,IAAI,KAAK,QAAQ,EAAE;MAC3BgC,QAAQ,CAACM,YAAY,IAAI;QACvB,MAAM7B,UAAU,GAAGyC,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC9C,OAAO,GAAG,MAAM,GAAG,OAAO;;QAEpE;QACA,IAAIkC,YAAY,CAAC7B,UAAU,KAAKA,UAAU,EAAE;UAC1C,OAAO6B,YAAY;QACrB;QACA,OAAO3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,EAAE;UAChC7B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,KAAK,CAACP,IAAI,CAAC,CAAC;;EAEhB;EACA,MAAMmD,aAAa,GAAGvD,KAAK,CAACwD,MAAM,CAACH,gBAAgB,CAAC;EACpDE,aAAa,CAACE,OAAO,GAAGJ,gBAAgB;EACxCrD,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,MAAMC,OAAO,GAAGA,CAAC,GAAGC,IAAI,KAAKL,aAAa,CAACE,OAAO,CAAC,GAAGG,IAAI,CAAC;;IAE3D;IACA,MAAMC,KAAK,GAAGxD,MAAM,CAACE,UAAU,CAAC,8BAA8B,CAAC;;IAE/D;IACAsD,KAAK,CAACC,WAAW,CAACH,OAAO,CAAC;IAC1BA,OAAO,CAACE,KAAK,CAAC;IACd,OAAO,MAAM;MACXA,KAAK,CAACE,cAAc,CAACJ,OAAO,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3D,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAIzB,aAAa,EAAE;MACjB,MAAM+B,aAAa,GAAGV,KAAK,IAAI;QAC7B,MAAMlC,KAAK,GAAGkC,KAAK,CAACW,QAAQ;QAC5B,IAAI,OAAOX,KAAK,CAACpC,GAAG,KAAK,QAAQ,IAAIoC,KAAK,CAACpC,GAAG,CAACgD,UAAU,CAAClC,qBAAqB,CAAC,KAAK,CAACZ,KAAK,IAAIc,kBAAkB,CAACiC,KAAK,CAAC/C,KAAK,CAAC,CAAC,EAAE;UAC/H;UACA,IAAIkC,KAAK,CAACpC,GAAG,CAACkD,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC/BxB,cAAc,CAAC;cACbM,KAAK,EAAE9B;YACT,CAAC,CAAC;UACJ;UACA,IAAIkC,KAAK,CAACpC,GAAG,CAACkD,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC9BxB,cAAc,CAAC;cACbQ,IAAI,EAAEhC;YACR,CAAC,CAAC;UACJ;QACF;QACA,IAAIkC,KAAK,CAACpC,GAAG,KAAKa,cAAc,KAAK,CAACX,KAAK,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAACyB,QAAQ,CAACzB,KAAK,CAAC,CAAC,EAAE;UAC3FoB,OAAO,CAACpB,KAAK,IAAIO,WAAW,CAAC;QAC/B;MACF,CAAC;MACD;MACAM,aAAa,CAACoC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;MACxD,OAAO,MAAM;QACX/B,aAAa,CAACqC,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;MAC7D,CAAC;IACH;IACA,OAAOvD,SAAS;EAClB,CAAC,EAAE,CAACmC,cAAc,EAAEJ,OAAO,EAAET,cAAc,EAAEC,qBAAqB,EAAEE,kBAAkB,EAAEP,WAAW,EAAEM,aAAa,CAAC,CAAC;EACpH,OAAOlC,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAE;IACzB4B,WAAW;IACXC,OAAO;IACPI;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}