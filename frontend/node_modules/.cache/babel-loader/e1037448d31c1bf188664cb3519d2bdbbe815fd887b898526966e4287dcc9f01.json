{"ast": null, "code": "export { default } from './useForkRef';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/useForkRef/index.js"], "sourcesContent": ["export { default } from './useForkRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}