{"ast": null, "code": "'use strict';\n\nvar eventUtils = require('./event'),\n  browser = require('./browser');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\nmodule.exports = {\n  WPrefix: '_jp',\n  currentWindowId: null,\n  polluteGlobalNamespace: function () {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  },\n  postMessage: function (type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId,\n        type: type,\n        data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  },\n  createIframe: function (iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function () {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function () {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function () {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function (err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function (msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function () {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n    iframe.onload = function () {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function () {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n\n  /* eslint no-undef: \"off\", new-cap: \"off\" */,\n  createHtmlfile: function (iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function () {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function () {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function (r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function (msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function () {\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n    doc.open();\n    doc.write('<html><s' + 'cript>' + 'document.domain=\"' + global.document.domain + '\";' + '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n};\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' || typeof global.postMessage === 'object') && !browser.isKonqueror();\n}", "map": {"version": 3, "names": ["eventUtils", "require", "browser", "debug", "process", "env", "NODE_ENV", "module", "exports", "WPrefix", "currentWindowId", "polluteGlobalNamespace", "global", "postMessage", "type", "data", "parent", "JSON", "stringify", "windowId", "createIframe", "iframeUrl", "<PERSON><PERSON><PERSON><PERSON>", "iframe", "document", "createElement", "tref", "unloadRef", "unattach", "clearTimeout", "onload", "x", "onerror", "cleanup", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "unloadDel", "err", "post", "msg", "origin", "contentWindow", "src", "style", "display", "position", "body", "append<PERSON><PERSON><PERSON>", "unloadAdd", "loaded", "createHtmlfile", "axo", "concat", "join", "doc", "CollectGarbage", "r", "open", "write", "domain", "close", "parentWindow", "c", "iframeEnabled", "isKonqueror"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/utils/iframe.js"], "sourcesContent": ["'use strict';\n\nvar eventUtils = require('./event')\n  , browser = require('./browser')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\n\nmodule.exports = {\n  WPrefix: '_jp'\n, currentWindowId: null\n\n, polluteGlobalNamespace: function() {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  }\n\n, postMessage: function(type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId\n      , type: type\n      , data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  }\n\n, createIframe: function(iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function() {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function() {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function(err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function(msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function() {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    iframe.onload = function() {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function() {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n\n/* eslint no-undef: \"off\", new-cap: \"off\" */\n, createHtmlfile: function(iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function() {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function(r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function(msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function() {\n          if (iframe && iframe.contentWindow) {\n              iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n\n    doc.open();\n    doc.write('<html><s' + 'cript>' +\n              'document.domain=\"' + global.document.domain + '\";' +\n              '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n};\n\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' ||\n    typeof global.postMessage === 'object') && (!browser.isKonqueror());\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,SAAS,CAAC;EAC/BC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AAGlC,IAAIE,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC;AACxD;AAEAM,MAAM,CAACC,OAAO,GAAG;EACfC,OAAO,EAAE,KAAK;EACdC,eAAe,EAAE,IAAI;EAErBC,sBAAsB,EAAE,SAAAA,CAAA,EAAW;IACjC,IAAI,EAAEJ,MAAM,CAACC,OAAO,CAACC,OAAO,IAAIG,MAAM,CAAC,EAAE;MACvCA,MAAM,CAACL,MAAM,CAACC,OAAO,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC;EACF,CAAC;EAEDI,WAAW,EAAE,SAAAA,CAASC,IAAI,EAAEC,IAAI,EAAE;IAChC,IAAIH,MAAM,CAACI,MAAM,KAAKJ,MAAM,EAAE;MAC5BA,MAAM,CAACI,MAAM,CAACH,WAAW,CAACI,IAAI,CAACC,SAAS,CAAC;QACvCC,QAAQ,EAAEZ,MAAM,CAACC,OAAO,CAACE,eAAe;QACxCI,IAAI,EAAEA,IAAI;QACVC,IAAI,EAAEA,IAAI,IAAI;MAChB,CAAC,CAAC,EAAE,GAAG,CAAC;IACV,CAAC,MAAM;MACLZ,KAAK,CAAC,uCAAuC,EAAEW,IAAI,EAAEC,IAAI,CAAC;IAC5D;EACF,CAAC;EAEDK,YAAY,EAAE,SAAAA,CAASC,SAAS,EAAEC,aAAa,EAAE;IAC/C,IAAIC,MAAM,GAAGX,MAAM,CAACY,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpD,IAAIC,IAAI,EAAEC,SAAS;IACnB,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAW;MACxBzB,KAAK,CAAC,UAAU,CAAC;MACjB0B,YAAY,CAACH,IAAI,CAAC;MAClB;MACA,IAAI;QACFH,MAAM,CAACO,MAAM,GAAG,IAAI;MACtB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV;MAAA;MAEFR,MAAM,CAACS,OAAO,GAAG,IAAI;IACvB,CAAC;IACD,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAW;MACvB9B,KAAK,CAAC,SAAS,CAAC;MAChB,IAAIoB,MAAM,EAAE;QACVK,QAAQ,CAAC,CAAC;QACV;QACA;QACA;QACAM,UAAU,CAAC,YAAW;UACpB,IAAIX,MAAM,EAAE;YACVA,MAAM,CAACY,UAAU,CAACC,WAAW,CAACb,MAAM,CAAC;UACvC;UACAA,MAAM,GAAG,IAAI;QACf,CAAC,EAAE,CAAC,CAAC;QACLvB,UAAU,CAACqC,SAAS,CAACV,SAAS,CAAC;MACjC;IACF,CAAC;IACD,IAAIK,OAAO,GAAG,SAAAA,CAASM,GAAG,EAAE;MAC1BnC,KAAK,CAAC,SAAS,EAAEmC,GAAG,CAAC;MACrB,IAAIf,MAAM,EAAE;QACVU,OAAO,CAAC,CAAC;QACTX,aAAa,CAACgB,GAAG,CAAC;MACpB;IACF,CAAC;IACD,IAAIC,IAAI,GAAG,SAAAA,CAASC,GAAG,EAAEC,MAAM,EAAE;MAC/BtC,KAAK,CAAC,MAAM,EAAEqC,GAAG,EAAEC,MAAM,CAAC;MAC1BP,UAAU,CAAC,YAAW;QACpB,IAAI;UACF;UACA;UACA,IAAIX,MAAM,IAAIA,MAAM,CAACmB,aAAa,EAAE;YAClCnB,MAAM,CAACmB,aAAa,CAAC7B,WAAW,CAAC2B,GAAG,EAAEC,MAAM,CAAC;UAC/C;QACF,CAAC,CAAC,OAAOV,CAAC,EAAE;UACV;QAAA;MAEJ,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAEDR,MAAM,CAACoB,GAAG,GAAGtB,SAAS;IACtBE,MAAM,CAACqB,KAAK,CAACC,OAAO,GAAG,MAAM;IAC7BtB,MAAM,CAACqB,KAAK,CAACE,QAAQ,GAAG,UAAU;IAClCvB,MAAM,CAACS,OAAO,GAAG,YAAW;MAC1BA,OAAO,CAAC,SAAS,CAAC;IACpB,CAAC;IACDT,MAAM,CAACO,MAAM,GAAG,YAAW;MACzB3B,KAAK,CAAC,QAAQ,CAAC;MACf;MACA;MACA0B,YAAY,CAACH,IAAI,CAAC;MAClBA,IAAI,GAAGQ,UAAU,CAAC,YAAW;QAC3BF,OAAO,CAAC,gBAAgB,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACDpB,MAAM,CAACY,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAACzB,MAAM,CAAC;IACxCG,IAAI,GAAGQ,UAAU,CAAC,YAAW;MAC3BF,OAAO,CAAC,SAAS,CAAC;IACpB,CAAC,EAAE,KAAK,CAAC;IACTL,SAAS,GAAG3B,UAAU,CAACiD,SAAS,CAAChB,OAAO,CAAC;IACzC,OAAO;MACLM,IAAI,EAAEA,IAAI;MACVN,OAAO,EAAEA,OAAO;MAChBiB,MAAM,EAAEtB;IACV,CAAC;EACH;;EAEF;EACEuB,cAAc,EAAE,SAAAA,CAAS9B,SAAS,EAAEC,aAAa,EAAE;IACjD,IAAI8B,GAAG,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC/C,IAAIC,GAAG,GAAG,IAAI3C,MAAM,CAACwC,GAAG,CAAC,CAAC,UAAU,CAAC;IACrC,IAAI1B,IAAI,EAAEC,SAAS;IACnB,IAAIJ,MAAM;IACV,IAAIK,QAAQ,GAAG,SAAAA,CAAA,EAAW;MACxBC,YAAY,CAACH,IAAI,CAAC;MAClBH,MAAM,CAACS,OAAO,GAAG,IAAI;IACvB,CAAC;IACD,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAW;MACvB,IAAIsB,GAAG,EAAE;QACP3B,QAAQ,CAAC,CAAC;QACV5B,UAAU,CAACqC,SAAS,CAACV,SAAS,CAAC;QAC/BJ,MAAM,CAACY,UAAU,CAACC,WAAW,CAACb,MAAM,CAAC;QACrCA,MAAM,GAAGgC,GAAG,GAAG,IAAI;QACnBC,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAIxB,OAAO,GAAG,SAAAA,CAASyB,CAAC,EAAE;MACxBtD,KAAK,CAAC,SAAS,EAAEsD,CAAC,CAAC;MACnB,IAAIF,GAAG,EAAE;QACPtB,OAAO,CAAC,CAAC;QACTX,aAAa,CAACmC,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAIlB,IAAI,GAAG,SAAAA,CAASC,GAAG,EAAEC,MAAM,EAAE;MAC/B,IAAI;QACF;QACA;QACAP,UAAU,CAAC,YAAW;UACpB,IAAIX,MAAM,IAAIA,MAAM,CAACmB,aAAa,EAAE;YAChCnB,MAAM,CAACmB,aAAa,CAAC7B,WAAW,CAAC2B,GAAG,EAAEC,MAAM,CAAC;UACjD;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC,OAAOV,CAAC,EAAE;QACV;MAAA;IAEJ,CAAC;IAEDwB,GAAG,CAACG,IAAI,CAAC,CAAC;IACVH,GAAG,CAACI,KAAK,CAAC,UAAU,GAAG,QAAQ,GACrB,mBAAmB,GAAG/C,MAAM,CAACY,QAAQ,CAACoC,MAAM,GAAG,IAAI,GACnD,KAAK,GAAG,eAAe,CAAC;IAClCL,GAAG,CAACM,KAAK,CAAC,CAAC;IACXN,GAAG,CAACO,YAAY,CAACvD,MAAM,CAACC,OAAO,CAACC,OAAO,CAAC,GAAGG,MAAM,CAACL,MAAM,CAACC,OAAO,CAACC,OAAO,CAAC;IACzE,IAAIsD,CAAC,GAAGR,GAAG,CAAC9B,aAAa,CAAC,KAAK,CAAC;IAChC8B,GAAG,CAACR,IAAI,CAACC,WAAW,CAACe,CAAC,CAAC;IACvBxC,MAAM,GAAGgC,GAAG,CAAC9B,aAAa,CAAC,QAAQ,CAAC;IACpCsC,CAAC,CAACf,WAAW,CAACzB,MAAM,CAAC;IACrBA,MAAM,CAACoB,GAAG,GAAGtB,SAAS;IACtBE,MAAM,CAACS,OAAO,GAAG,YAAW;MAC1BA,OAAO,CAAC,SAAS,CAAC;IACpB,CAAC;IACDN,IAAI,GAAGQ,UAAU,CAAC,YAAW;MAC3BF,OAAO,CAAC,SAAS,CAAC;IACpB,CAAC,EAAE,KAAK,CAAC;IACTL,SAAS,GAAG3B,UAAU,CAACiD,SAAS,CAAChB,OAAO,CAAC;IACzC,OAAO;MACLM,IAAI,EAAEA,IAAI;MACVN,OAAO,EAAEA,OAAO;MAChBiB,MAAM,EAAEtB;IACV,CAAC;EACH;AACF,CAAC;AAEDrB,MAAM,CAACC,OAAO,CAACwD,aAAa,GAAG,KAAK;AACpC,IAAIpD,MAAM,CAACY,QAAQ,EAAE;EACnB;EACA;EACAjB,MAAM,CAACC,OAAO,CAACwD,aAAa,GAAG,CAAC,OAAOpD,MAAM,CAACC,WAAW,KAAK,UAAU,IACtE,OAAOD,MAAM,CAACC,WAAW,KAAK,QAAQ,KAAM,CAACX,OAAO,CAAC+D,WAAW,CAAC,CAAE;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}