{"ast": null, "code": "export { default } from './useOnMount';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/useOnMount/index.js"], "sourcesContent": ["export { default } from './useOnMount';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}