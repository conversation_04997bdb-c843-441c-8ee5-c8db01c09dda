{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  urlUtils = require('../../utils/url'),\n  BufferedSender = require('./buffered-sender'),\n  Polling = require('./polling');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function (msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function (code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\ninherits(SenderReceiver, BufferedSender);\nSenderReceiver.prototype.close = function () {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\nmodule.exports = SenderReceiver;", "map": {"version": 3, "names": ["inherits", "require", "urlUtils", "BufferedSender", "Polling", "debug", "process", "env", "NODE_ENV", "SenderReceiver", "transUrl", "urlSuffix", "senderFunc", "Receiver", "AjaxObject", "pollUrl", "addPath", "self", "call", "poll", "on", "msg", "emit", "once", "code", "reason", "close", "prototype", "removeAllListeners", "abort", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/lib/sender-receiver.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , BufferedSender = require('./buffered-sender')\n  , Polling = require('./polling')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\n\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function(msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function(code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\n\ninherits(SenderReceiver, BufferedSender);\n\nSenderReceiver.prototype.close = function() {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\n\nmodule.exports = SenderReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACrCE,cAAc,GAAGF,OAAO,CAAC,mBAAmB,CAAC;EAC7CG,OAAO,GAAGH,OAAO,CAAC,WAAW,CAAC;AAGlC,IAAII,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC;AAC3D;AAEA,SAASQ,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7E,IAAIC,OAAO,GAAGb,QAAQ,CAACc,OAAO,CAACN,QAAQ,EAAEC,SAAS,CAAC;EACnDN,KAAK,CAACU,OAAO,CAAC;EACd,IAAIE,IAAI,GAAG,IAAI;EACfd,cAAc,CAACe,IAAI,CAAC,IAAI,EAAER,QAAQ,EAAEE,UAAU,CAAC;EAE/C,IAAI,CAACO,IAAI,GAAG,IAAIf,OAAO,CAACS,QAAQ,EAAEE,OAAO,EAAED,UAAU,CAAC;EACtD,IAAI,CAACK,IAAI,CAACC,EAAE,CAAC,SAAS,EAAE,UAASC,GAAG,EAAE;IACpChB,KAAK,CAAC,cAAc,EAAEgB,GAAG,CAAC;IAC1BJ,IAAI,CAACK,IAAI,CAAC,SAAS,EAAED,GAAG,CAAC;EAC3B,CAAC,CAAC;EACF,IAAI,CAACF,IAAI,CAACI,IAAI,CAAC,OAAO,EAAE,UAASC,IAAI,EAAEC,MAAM,EAAE;IAC7CpB,KAAK,CAAC,YAAY,EAAEmB,IAAI,EAAEC,MAAM,CAAC;IACjCR,IAAI,CAACE,IAAI,GAAG,IAAI;IAChBF,IAAI,CAACK,IAAI,CAAC,OAAO,EAAEE,IAAI,EAAEC,MAAM,CAAC;IAChCR,IAAI,CAACS,KAAK,CAAC,CAAC;EACd,CAAC,CAAC;AACJ;AAEA1B,QAAQ,CAACS,cAAc,EAAEN,cAAc,CAAC;AAExCM,cAAc,CAACkB,SAAS,CAACD,KAAK,GAAG,YAAW;EAC1CvB,cAAc,CAACwB,SAAS,CAACD,KAAK,CAACR,IAAI,CAAC,IAAI,CAAC;EACzCb,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAACuB,kBAAkB,CAAC,CAAC;EACzB,IAAI,IAAI,CAACT,IAAI,EAAE;IACb,IAAI,CAACA,IAAI,CAACU,KAAK,CAAC,CAAC;IACjB,IAAI,CAACV,IAAI,GAAG,IAAI;EAClB;AACF,CAAC;AAEDW,MAAM,CAACC,OAAO,GAAGtB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}