{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  eventUtils = require('../../utils/event'),\n  browser = require('../../utils/browser'),\n  urlUtils = require('../../utils/url');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n}\n\n// References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self._start(method, url, payload);\n  }, 0);\n}\ninherits(XDRObject, EventEmitter);\nXDRObject.prototype._start = function (method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest();\n  // IE caches even POSTs\n  url = urlUtils.addQuery(url, 't=' + +new Date());\n  xdr.onerror = function () {\n    debug('onerror');\n    self._error();\n  };\n  xdr.ontimeout = function () {\n    debug('ontimeout');\n    self._error();\n  };\n  xdr.onprogress = function () {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n  xdr.onload = function () {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n    self._cleanup(false);\n  };\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function () {\n    self._cleanup(true);\n  });\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\nXDRObject.prototype._error = function () {\n  this.emit('finish', 0, '');\n  this._cleanup(false);\n};\nXDRObject.prototype._cleanup = function (abort) {\n  debug('cleanup', abort);\n  if (!this.xdr) {\n    return;\n  }\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xdr = null;\n};\nXDRObject.prototype.close = function () {\n  debug('close');\n  this._cleanup(true);\n};\n\n// IE 8/9 if the request target uses the same scheme - #79\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\nmodule.exports = XDRObject;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "eventUtils", "browser", "urlUtils", "debug", "process", "env", "NODE_ENV", "XDRObject", "method", "url", "payload", "self", "call", "setTimeout", "_start", "prototype", "xdr", "global", "XDomainRequest", "<PERSON><PERSON><PERSON><PERSON>", "Date", "onerror", "_error", "ontimeout", "onprogress", "responseText", "emit", "onload", "_cleanup", "unloadRef", "unloadAdd", "open", "timeout", "send", "x", "abort", "removeAllListeners", "unloadDel", "close", "enabled", "hasDomain", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/sender/xdr.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , eventUtils = require('../../utils/event')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n}\n\n// References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self._start(method, url, payload);\n  }, 0);\n}\n\ninherits(XDRObject, EventEmitter);\n\nXDRObject.prototype._start = function(method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest();\n  // IE caches even POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  xdr.onerror = function() {\n    debug('onerror');\n    self._error();\n  };\n  xdr.ontimeout = function() {\n    debug('ontimeout');\n    self._error();\n  };\n  xdr.onprogress = function() {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n  xdr.onload = function() {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n    self._cleanup(false);\n  };\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function() {\n    self._cleanup(true);\n  });\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\n\nXDRObject.prototype._error = function() {\n  this.emit('finish', 0, '');\n  this._cleanup(false);\n};\n\nXDRObject.prototype._cleanup = function(abort) {\n  debug('cleanup', abort);\n  if (!this.xdr) {\n    return;\n  }\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xdr = null;\n};\n\nXDRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\n// IE 8/9 if the request target uses the same scheme - #79\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\n\nmodule.exports = XDRObject;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC9BE,UAAU,GAAGF,OAAO,CAAC,mBAAmB,CAAC;EACzCG,OAAO,GAAGH,OAAO,CAAC,qBAAqB,CAAC;EACxCI,QAAQ,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAGzC,IAAIK,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC;AACtD;;AAEA;AACA;AACA;;AAEA,SAASS,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACvCP,KAAK,CAACK,MAAM,EAAEC,GAAG,CAAC;EAClB,IAAIE,IAAI,GAAG,IAAI;EACfd,YAAY,CAACe,IAAI,CAAC,IAAI,CAAC;EAEvBC,UAAU,CAAC,YAAW;IACpBF,IAAI,CAACG,MAAM,CAACN,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC;EACnC,CAAC,EAAE,CAAC,CAAC;AACP;AAEAX,QAAQ,CAACQ,SAAS,EAAEV,YAAY,CAAC;AAEjCU,SAAS,CAACQ,SAAS,CAACD,MAAM,GAAG,UAASN,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC1DP,KAAK,CAAC,QAAQ,CAAC;EACf,IAAIQ,IAAI,GAAG,IAAI;EACf,IAAIK,GAAG,GAAG,IAAIC,MAAM,CAACC,cAAc,CAAC,CAAC;EACrC;EACAT,GAAG,GAAGP,QAAQ,CAACiB,QAAQ,CAACV,GAAG,EAAE,IAAI,GAAI,CAAC,IAAIW,IAAI,CAAC,CAAE,CAAC;EAElDJ,GAAG,CAACK,OAAO,GAAG,YAAW;IACvBlB,KAAK,CAAC,SAAS,CAAC;IAChBQ,IAAI,CAACW,MAAM,CAAC,CAAC;EACf,CAAC;EACDN,GAAG,CAACO,SAAS,GAAG,YAAW;IACzBpB,KAAK,CAAC,WAAW,CAAC;IAClBQ,IAAI,CAACW,MAAM,CAAC,CAAC;EACf,CAAC;EACDN,GAAG,CAACQ,UAAU,GAAG,YAAW;IAC1BrB,KAAK,CAAC,UAAU,EAAEa,GAAG,CAACS,YAAY,CAAC;IACnCd,IAAI,CAACe,IAAI,CAAC,OAAO,EAAE,GAAG,EAAEV,GAAG,CAACS,YAAY,CAAC;EAC3C,CAAC;EACDT,GAAG,CAACW,MAAM,GAAG,YAAW;IACtBxB,KAAK,CAAC,MAAM,CAAC;IACbQ,IAAI,CAACe,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEV,GAAG,CAACS,YAAY,CAAC;IAC1Cd,IAAI,CAACiB,QAAQ,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,IAAI,CAACZ,GAAG,GAAGA,GAAG;EACd,IAAI,CAACa,SAAS,GAAG7B,UAAU,CAAC8B,SAAS,CAAC,YAAW;IAC/CnB,IAAI,CAACiB,QAAQ,CAAC,IAAI,CAAC;EACrB,CAAC,CAAC;EACF,IAAI;IACF;IACA,IAAI,CAACZ,GAAG,CAACe,IAAI,CAACvB,MAAM,EAAEC,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACuB,OAAO,EAAE;MAChB,IAAI,CAAChB,GAAG,CAACgB,OAAO,GAAG,IAAI,CAACA,OAAO;IACjC;IACA,IAAI,CAAChB,GAAG,CAACiB,IAAI,CAACvB,OAAO,CAAC;EACxB,CAAC,CAAC,OAAOwB,CAAC,EAAE;IACV,IAAI,CAACZ,MAAM,CAAC,CAAC;EACf;AACF,CAAC;AAEDf,SAAS,CAACQ,SAAS,CAACO,MAAM,GAAG,YAAW;EACtC,IAAI,CAACI,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;EAC1B,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC;AACtB,CAAC;AAEDrB,SAAS,CAACQ,SAAS,CAACa,QAAQ,GAAG,UAASO,KAAK,EAAE;EAC7ChC,KAAK,CAAC,SAAS,EAAEgC,KAAK,CAAC;EACvB,IAAI,CAAC,IAAI,CAACnB,GAAG,EAAE;IACb;EACF;EACA,IAAI,CAACoB,kBAAkB,CAAC,CAAC;EACzBpC,UAAU,CAACqC,SAAS,CAAC,IAAI,CAACR,SAAS,CAAC;EAEpC,IAAI,CAACb,GAAG,CAACO,SAAS,GAAG,IAAI,CAACP,GAAG,CAACK,OAAO,GAAG,IAAI,CAACL,GAAG,CAACQ,UAAU,GAAG,IAAI,CAACR,GAAG,CAACW,MAAM,GAAG,IAAI;EACpF,IAAIQ,KAAK,EAAE;IACT,IAAI;MACF,IAAI,CAACnB,GAAG,CAACmB,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOD,CAAC,EAAE;MACV;IAAA;EAEJ;EACA,IAAI,CAACL,SAAS,GAAG,IAAI,CAACb,GAAG,GAAG,IAAI;AAClC,CAAC;AAEDT,SAAS,CAACQ,SAAS,CAACuB,KAAK,GAAG,YAAW;EACrCnC,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAACyB,QAAQ,CAAC,IAAI,CAAC;AACrB,CAAC;;AAED;AACArB,SAAS,CAACgC,OAAO,GAAG,CAAC,EAAEtB,MAAM,CAACC,cAAc,IAAIjB,OAAO,CAACuC,SAAS,CAAC,CAAC,CAAC;AAEpEC,MAAM,CAACC,OAAO,GAAGnC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}