{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  XhrReceiver = require('./receiver/xhr'),\n  XDRObject = require('./sender/xdr');\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\ninherits(XdrStreamingTransport, AjaxBasedTransport);\nXdrStreamingTransport.enabled = function (info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XDRObject", "XdrStreamingTransport", "transUrl", "enabled", "Error", "call", "info", "cookie_needed", "<PERSON><PERSON><PERSON><PERSON>", "sameScheme", "transportName", "roundTrips", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/sockjs-client/lib/transport/xdr-streaming.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\n\ninherits(XdrStreamingTransport, AjaxBasedTransport);\n\nXdrStreamingTransport.enabled = function(info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\n\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,kBAAkB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAChDE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,SAAS,GAAGH,OAAO,CAAC,cAAc,CAAC;;AAGvC;AACA;AACA;;AAEA,SAASI,qBAAqBA,CAACC,QAAQ,EAAE;EACvC,IAAI,CAACF,SAAS,CAACG,OAAO,EAAE;IACtB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAN,kBAAkB,CAACO,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,gBAAgB,EAAEH,WAAW,EAAEC,SAAS,CAAC;AACnF;AAEAJ,QAAQ,CAACK,qBAAqB,EAAEH,kBAAkB,CAAC;AAEnDG,qBAAqB,CAACE,OAAO,GAAG,UAASG,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU,EAAE;IACzC,OAAO,KAAK;EACd;EACA,OAAOR,SAAS,CAACG,OAAO,IAAIG,IAAI,CAACG,UAAU;AAC7C,CAAC;AAEDR,qBAAqB,CAACS,aAAa,GAAG,eAAe;AACrDT,qBAAqB,CAACU,UAAU,GAAG,CAAC,CAAC,CAAC;;AAEtCC,MAAM,CAACC,OAAO,GAAGZ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}