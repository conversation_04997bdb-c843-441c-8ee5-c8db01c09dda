{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect}from'react';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';import{Container,Box,Paper,Typography,Grid,Alert,Chip}from'@mui/material';import Header from'./components/Header';import FloodMap from'./components/FloodMap';import PredictionForm from'./components/PredictionForm';import ResultDisplay from'./components/ResultDisplay';import RiskFactorsChart from'./components/RiskFactorsChart';import TimelineRiskPredictor from'./components/TimelineRiskPredictor';import VoiceEmergencyAssistant from'./components/VoiceEmergencyAssistant';import CommunityReports from'./components/CommunityReports';import{SlideUp,ScaleIn}from'./components/animations/AnimatedComponents';import LoadingAnimation from'./components/animations/LoadingAnimation';import axios from'axios';import{neuromorphicStyles,getNeuromorphicPalette,getNeuromorphicShadow,getPressedEffect}from'./theme/neuromorphicUtils';import'./components/ResponsiveLayout.css';// Create a theme with neuromorphic design\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:_objectSpread(_objectSpread({mode:'light'},getNeuromorphicPalette('#3A86FF')),{},{info:{main:'#4CC9F0',// Light blue\nlight:'#e6f7fc',dark:'#3AA1C0',contrastText:'#ffffff'},divider:'rgba(0, 0, 0, 0.08)'}),typography:{fontFamily:'\"Poppins\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',h1:{fontSize:'clamp(2.5rem, 5vw, 3.5rem)',fontWeight:800,letterSpacing:'-0.01em',lineHeight:1.2},h2:{fontSize:'clamp(2rem, 4vw, 2.75rem)',fontWeight:700,letterSpacing:'-0.01em',lineHeight:1.2},h3:{fontSize:'clamp(1.5rem, 3vw, 2rem)',fontWeight:600,letterSpacing:'-0.01em',lineHeight:1.3},h4:{fontSize:'clamp(1.25rem, 2.5vw, 1.75rem)',fontWeight:600,letterSpacing:'-0.01em',lineHeight:1.4},h5:{fontSize:'clamp(1.1rem, 2vw, 1.5rem)',fontWeight:500,letterSpacing:'-0.01em',lineHeight:1.4},h6:{fontSize:'clamp(1rem, 1.5vw, 1.25rem)',fontWeight:500,letterSpacing:'-0.01em',lineHeight:1.5},body1:{fontSize:'clamp(0.875rem, 1.5vw, 1rem)',lineHeight:1.6},body2:{fontSize:'clamp(0.8125rem, 1.25vw, 0.875rem)',lineHeight:1.6},button:{fontWeight:600,textTransform:'none',letterSpacing:'0.02em',fontSize:'clamp(0.875rem, 1.5vw, 1rem)'},subtitle1:{fontSize:'clamp(0.9375rem, 1.75vw, 1.125rem)',fontWeight:500,lineHeight:1.5},subtitle2:{fontSize:'clamp(0.8125rem, 1.5vw, 0.9375rem)',fontWeight:500,lineHeight:1.5}},shape:{borderRadius:12},shadows:['none','0px 2px 4px rgba(0, 0, 0, 0.03), 0px 1px 2px rgba(0, 0, 0, 0.06)','0px 4px 6px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06)','0px 6px 8px rgba(0, 0, 0, 0.04), 0px 3px 6px rgba(0, 0, 0, 0.06)','0px 8px 12px rgba(0, 0, 0, 0.05), 0px 4px 8px rgba(0, 0, 0, 0.06)','0px 10px 15px rgba(0, 0, 0, 0.05), 0px 6px 10px rgba(0, 0, 0, 0.06)','0px 12px 18px rgba(0, 0, 0, 0.05), 0px 7px 12px rgba(0, 0, 0, 0.06)','0px 14px 21px rgba(0, 0, 0, 0.05), 0px 8px 14px rgba(0, 0, 0, 0.06)','0px 16px 24px rgba(0, 0, 0, 0.05), 0px 9px 16px rgba(0, 0, 0, 0.06)','0px 18px 27px rgba(0, 0, 0, 0.05), 0px 10px 18px rgba(0, 0, 0, 0.06)','0px 20px 30px rgba(0, 0, 0, 0.05), 0px 11px 20px rgba(0, 0, 0, 0.06)','0px 22px 33px rgba(0, 0, 0, 0.05), 0px 12px 22px rgba(0, 0, 0, 0.06)','0px 24px 36px rgba(0, 0, 0, 0.05), 0px 13px 24px rgba(0, 0, 0, 0.06)','0px 26px 39px rgba(0, 0, 0, 0.05), 0px 14px 26px rgba(0, 0, 0, 0.06)','0px 28px 42px rgba(0, 0, 0, 0.05), 0px 15px 28px rgba(0, 0, 0, 0.06)','0px 30px 45px rgba(0, 0, 0, 0.05), 0px 16px 30px rgba(0, 0, 0, 0.06)','0px 32px 48px rgba(0, 0, 0, 0.05), 0px 17px 32px rgba(0, 0, 0, 0.06)','0px 34px 51px rgba(0, 0, 0, 0.05), 0px 18px 34px rgba(0, 0, 0, 0.06)','0px 36px 54px rgba(0, 0, 0, 0.05), 0px 19px 36px rgba(0, 0, 0, 0.06)','0px 38px 57px rgba(0, 0, 0, 0.05), 0px 20px 38px rgba(0, 0, 0, 0.06)','0px 40px 60px rgba(0, 0, 0, 0.05), 0px 21px 40px rgba(0, 0, 0, 0.06)','0px 42px 63px rgba(0, 0, 0, 0.05), 0px 22px 42px rgba(0, 0, 0, 0.06)','0px 44px 66px rgba(0, 0, 0, 0.05), 0px 23px 44px rgba(0, 0, 0, 0.06)','0px 46px 69px rgba(0, 0, 0, 0.05), 0px 24px 46px rgba(0, 0, 0, 0.06)'],breakpoints:{values:{xs:0,sm:600,md:960,lg:1280,xl:1920}},components:{MuiContainer:{styleOverrides:{root:{paddingLeft:16,paddingRight:16,'@media (min-width:600px)':{paddingLeft:24,paddingRight:24},'@media (min-width:960px)':{paddingLeft:32,paddingRight:32},'@media (min-width:1200px)':{paddingLeft:48,paddingRight:48},maxWidth:'100%','@media (min-width:1280px)':{maxWidth:'1280px'},'@media (min-width:1920px)':{maxWidth:'1920px'}}}},MuiGrid:{styleOverrides:{container:{marginTop:0,marginLeft:0,width:'100%'},item:{paddingTop:12,paddingLeft:12,'@media (min-width:600px)':{paddingTop:16,paddingLeft:16},'@media (min-width:960px)':{paddingTop:20,paddingLeft:20},'@media (min-width:1200px)':{paddingTop:24,paddingLeft:24}}}},MuiPaper:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.3s ease-in-out',padding:{xs:2,sm:3,md:4},border:'none','&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \"}}}},MuiButton:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:12,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.2s ease',border:'none',padding:{xs:'10px 20px',sm:'12px 24px',md:'14px 28px'},fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},'&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \",backgroundColor:'#f0f4f8'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(174, 174, 192, 0.3),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.5)\\n            \",backgroundColor:'#f0f4f8'}},containedPrimary:{color:'#ffffff',backgroundColor:'#3A86FF',boxShadow:\"\\n            6px 6px 12px rgba(58, 134, 255, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",'&:hover':{backgroundColor:'#2a6bc9'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\\n            \"}},containedSecondary:{color:'#ffffff',backgroundColor:'#FF595E',boxShadow:\"\\n            6px 6px 12px rgba(255, 89, 94, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",'&:hover':{backgroundColor:'#d04649'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\\n            \"}},outlined:{backgroundColor:'transparent',boxShadow:'none',border:'2px solid #f0f4f8','&:hover':{boxShadow:\"\\n              4px 4px 8px rgba(174, 174, 192, 0.2),\\n              -4px -4px 8px rgba(255, 255, 255, 0.3)\\n            \"},'&:active':{boxShadow:\"\\n              inset 2px 2px 4px rgba(174, 174, 192, 0.2),\\n              inset -2px -2px 4px rgba(255, 255, 255, 0.3)\\n            \"}}}},MuiTextField:{styleOverrides:{root:{transition:'all 0.3s ease',marginBottom:{xs:2,sm:2.5,md:3},'& .MuiOutlinedInput-root':{backgroundColor:'#f0f4f8',borderRadius:12,boxShadow:\"\\n              inset 2px 2px 5px rgba(174, 174, 192, 0.2),\\n              inset -2px -2px 5px rgba(255, 255, 255, 0.7)\\n            \",border:'none','& fieldset':{border:'none'},'&:hover fieldset':{border:'none'},'&.Mui-focused':{boxShadow:\"\\n                inset 4px 4px 8px rgba(174, 174, 192, 0.3),\\n                inset -4px -4px 8px rgba(255, 255, 255, 0.5)\\n              \",'& fieldset':{border:'none'}}},'& .MuiInputLabel-root':{fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},color:'#1a202c',fontWeight:500,'&.Mui-focused':{color:'#3A86FF',fontWeight:600}},'& .MuiInputBase-input':{padding:'16px 14px',color:'#1a202c',fontWeight:500}}}},MuiSlider:{styleOverrides:{root:{height:10,'& .MuiSlider-track':{border:'none',boxShadow:'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',backgroundColor:'#3A86FF'},'& .MuiSlider-rail':{boxShadow:\"\\n              inset 2px 2px 4px rgba(174, 174, 192, 0.3),\\n              inset -2px -2px 4px rgba(255, 255, 255, 0.5)\\n            \",backgroundColor:'#f0f4f8',opacity:1},'& .MuiSlider-thumb':{height:24,width:24,backgroundColor:'#f0f4f8',boxShadow:\"\\n              6px 6px 12px rgba(174, 174, 192, 0.3),\\n              -6px -6px 12px rgba(255, 255, 255, 0.5)\\n            \",'&:focus, &:hover, &.Mui-active, &.Mui-focusVisible':{boxShadow:\"\\n                8px 8px 16px rgba(174, 174, 192, 0.35),\\n                -8px -8px 16px rgba(255, 255, 255, 0.6)\\n              \"}}}}},MuiCard:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.3s ease-in-out',border:'none',overflow:'hidden','&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \"}}}},MuiCardContent:{styleOverrides:{root:{padding:{xs:'20px',sm:'24px',md:'28px'},'&:last-child':{paddingBottom:{xs:'20px',sm:'24px',md:'28px'}}}}},MuiAlert:{styleOverrides:{root:{borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},fontWeight:500},standardSuccess:{backgroundColor:'rgba(6, 214, 160, 0.1)',color:'#05ab80','& .MuiAlert-icon':{color:'#06d6a0'}},standardError:{backgroundColor:'rgba(255, 89, 94, 0.1)',color:'#d04649','& .MuiAlert-icon':{color:'#ff595e'}},standardWarning:{backgroundColor:'rgba(255, 159, 28, 0.1)',color:'#d18016','& .MuiAlert-icon':{color:'#ff9f1c'}},standardInfo:{backgroundColor:'rgba(76, 201, 240, 0.1)',color:'#3aa8cc','& .MuiAlert-icon':{color:'#4cc9f0'}}}},MuiChip:{styleOverrides:{root:{borderRadius:12,backgroundColor:'#f0f4f8',boxShadow:\"\\n            2px 2px 4px rgba(174, 174, 192, 0.3),\\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \",fontSize:{xs:'0.75rem',sm:'0.8125rem',md:'0.875rem'},color:'#1a202c',fontWeight:500,'&:hover':{boxShadow:\"\\n              3px 3px 6px rgba(174, 174, 192, 0.35),\\n              -3px -3px 6px rgba(255, 255, 255, 0.6)\\n            \"}},colorPrimary:{backgroundColor:'#3A86FF',color:'#ffffff'},colorSecondary:{backgroundColor:'#FF595E',color:'#ffffff'},colorSuccess:{backgroundColor:'#06d6a0',color:'#ffffff'},colorError:{backgroundColor:'#ff595e',color:'#ffffff'},colorWarning:{backgroundColor:'#ff9f1c',color:'#ffffff'},colorInfo:{backgroundColor:'#4cc9f0',color:'#ffffff'}}},MuiTypography:{styleOverrides:{root:{marginBottom:{xs:1,sm:1.5,md:2},color:'#1a202c'},h1:{color:'#1a202c',fontWeight:800},h2:{color:'#1a202c',fontWeight:700},h3:{color:'#1a202c',fontWeight:700},h4:{color:'#1a202c',fontWeight:600},h5:{color:'#1a202c',fontWeight:600},h6:{color:'#1a202c',fontWeight:600},subtitle1:{color:'#2d3748',fontWeight:500},subtitle2:{color:'#2d3748',fontWeight:500},body1:{color:'#2d3748'},body2:{color:'#4a5568'}}},MuiSwitch:{styleOverrides:{root:{width:56,height:32,padding:0},switchBase:{padding:4,'&.Mui-checked':{transform:'translateX(24px)','& + .MuiSwitch-track':{opacity:1,backgroundColor:'#e6eef8'}}},thumb:{width:24,height:24,backgroundColor:'#f0f4f8',boxShadow:\"\\n            2px 2px 4px rgba(174, 174, 192, 0.3),\\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \"},track:{opacity:1,borderRadius:16,backgroundColor:'#e6eef8',boxShadow:\"\\n            inset 2px 2px 4px rgba(174, 174, 192, 0.3),\\n            inset -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \"}}}}});function App(){const[mapData,setMapData]=useState([]);const[options,setOptions]=useState({land_cover:[],soil_type:[]});const[prediction,setPrediction]=useState(null);const[loading,setLoading]=useState(false);const[initialLoading,setInitialLoading]=useState(true);const[showPredictionResult,setShowPredictionResult]=useState(false);const[forecastSummary,setForecastSummary]=useState(null);const[showForecastAlert,setShowForecastAlert]=useState(false);useEffect(()=>{// Fetch map data and options when component mounts\nconst fetchData=async()=>{setInitialLoading(true);let loadingTimer;try{const[mapResponse,optionsResponse]=await Promise.all([axios.get('/api/map-data'),axios.get('/api/options')]);setMapData(mapResponse.data);setOptions(optionsResponse.data);}catch(error){console.error('Error fetching data:',error);}finally{// Add a slight delay to make the loading animation visible\n// but ensure it gets cleared if component unmounts\nloadingTimer=setTimeout(()=>{setInitialLoading(false);},1500);}// Cleanup function to ensure loading state is reset if component unmounts\nreturn()=>{if(loadingTimer)clearTimeout(loadingTimer);setInitialLoading(false);};};fetchData();},[]);const handleSubmit=async formData=>{setLoading(true);setShowPredictionResult(false);setShowForecastAlert(false);try{// Add a slight delay to make the loading animation visible\nawait new Promise(resolve=>setTimeout(resolve,1200));const response=await axios.post('/api/predict',formData);setPrediction(response.data);// Add a slight delay before showing the result for better animation\nsetTimeout(()=>{setShowPredictionResult(true);},300);}catch(error){console.error('Error making prediction:',error);}finally{setLoading(false);}};const handleForecastGenerated=summary=>{setForecastSummary(summary);setShowForecastAlert(true);// Hide the alert after 10 seconds\nsetTimeout(()=>{setShowForecastAlert(false);},10000);};return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(VoiceEmergencyAssistant,{}),initialLoading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',minHeight:'80vh',background:'#e6eef8'},children:[/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',width:200,height:200},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:'100%',height:'100%',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.primary.main,animation:'spin 1.5s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:15,left:15,width:'calc(100% - 30px)',height:'calc(100% - 30px)',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.secondary.main,animation:'spin 2s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:30,left:30,width:'calc(100% - 60px)',height:'calc(100% - 60px)',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.info.main,animation:'spin 2.5s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{mt:4,fontWeight:600,background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',backgroundClip:'text',textFillColor:'transparent',animation:'pulse 2s infinite','@keyframes pulse':{'0%':{opacity:0.6},'50%':{opacity:1},'100%':{opacity:0.6}}},children:\"Loading Flood Prediction System...\"})]}):/*#__PURE__*/_jsx(Container,{maxWidth:\"xl\",sx:{mt:{xs:3,sm:4,md:5},mb:{xs:5,sm:7,md:10},px:{xs:2,sm:3,md:4},py:{xs:4,sm:5,md:6},overflow:'hidden',backgroundColor:'#e6eef8',borderRadius:'24px',boxShadow:\"\\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\\n            \",position:'relative',zIndex:1},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:3,sm:4,md:5},alignItems:\"stretch\",sx:{'& .MuiGrid-item':{display:'flex',flexDirection:'column'}},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:4,sm:5,md:6},mb:{xs:3,sm:4,md:5},backgroundColor:'#f0f4f8',position:'relative',overflow:'hidden',borderRadius:3,transition:'all 0.3s ease-in-out',boxShadow:\"\\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\\n                  \",'&:hover':{boxShadow:\"\\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\\n                    \"},minHeight:{xs:'auto',sm:'200px'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'180px',height:'180px',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,left:0,width:'120px',height:'120px',background:'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 100% 0 0',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:{xs:'flex-start',sm:'center'},flexDirection:{xs:'column',sm:'row'},mb:{xs:3,sm:4},gap:{xs:2,sm:0}},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:{xs:0,sm:2},p:{xs:1.5,sm:2},borderRadius:'50%',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 12px rgba(76, 201, 240, 0.15)',alignSelf:{xs:'center',sm:'flex-start'}},children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"span\",children:\"\\uD83C\\uDF0A\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h2\",component:\"h1\",sx:{background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',backgroundClip:'text',textFillColor:'transparent',fontWeight:800,letterSpacing:'-0.5px',fontSize:{xs:'2rem',sm:'2.5rem',md:'3rem'},textAlign:{xs:'center',sm:'left'},lineHeight:1.2},children:\"Flood Risk Prediction System\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{fontSize:{xs:'1rem',sm:'1.1rem'},maxWidth:{xs:'100%',sm:'90%'},color:'text.secondary',lineHeight:1.6,mb:{xs:3,sm:4},textAlign:{xs:'center',sm:'left'}},children:\"This interactive tool helps predict flood risk based on various environmental and geographical factors. For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:{xs:1.5,sm:2},flexWrap:'wrap',mt:{xs:3,sm:4},justifyContent:{xs:'center',sm:'flex-start'}},children:[/*#__PURE__*/_jsx(Chip,{label:\"Real-time Weather Data\",color:\"primary\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\u26C8\\uFE0F\"}),sx:{fontWeight:500,px:1}}),/*#__PURE__*/_jsx(Chip,{label:\"Historical Flood Analysis\",color:\"info\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCA\"}),sx:{fontWeight:500,px:1}}),/*#__PURE__*/_jsx(Chip,{label:\"Indian Cities Database\",color:\"success\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFD9\\uFE0F\"}),sx:{fontWeight:500,px:1}})]})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,sm:4,md:5},height:'100%',minHeight:{xs:'auto',lg:'600px'},position:'relative',overflow:'hidden',borderRadius:3,transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'},display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:'100px',height:'100px',background:'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 100% 0',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,right:0,width:'80px',height:'80px',background:'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'100% 0 0 0',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:{xs:'flex-start',sm:'center'},flexDirection:{xs:'column',sm:'row'},mb:{xs:3,sm:4},gap:{xs:2,sm:0}},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:{xs:0,sm:2},p:{xs:1,sm:1.5},borderRadius:'12px',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center',alignSelf:{xs:'center',sm:'flex-start'}},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDCDD\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.main,letterSpacing:'-0.5px',fontSize:{xs:'1.5rem',sm:'2rem',md:'2.125rem'},textAlign:{xs:'center',sm:'left'}},children:\"Predict Flood Risk\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:{xs:3,sm:4},color:'text.secondary',maxWidth:'100%',textAlign:{xs:'center',sm:'left'}},children:\"Enter location and environmental factors to get a precise flood risk assessment. For Indian cities, we provide enhanced accuracy using historical data.\"}),/*#__PURE__*/_jsx(PredictionForm,{options:options,onSubmit:handleSubmit,loading:loading})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,sm:4,md:5},height:'100%',minHeight:{xs:'auto',lg:'600px'},position:'relative',overflow:'hidden',borderRadius:3,transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'},display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'100px',height:'100px',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,left:0,width:'80px',height:'80px',background:'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 100% 0 0',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'relative',zIndex:1},children:loading?/*#__PURE__*/_jsx(LoadingAnimation,{theme:theme}):prediction&&showPredictionResult?/*#__PURE__*/_jsxs(Box,{sx:{opacity:showPredictionResult?1:0,transform:showPredictionResult?'translateY(0)':'translateY(20px)',transition:'all 0.5s ease-in-out'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDCCA\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.main,letterSpacing:'-0.5px'},children:\"Risk Assessment\"})]}),/*#__PURE__*/_jsx(ResultDisplay,{prediction:prediction})]}):/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',py:8,opacity:loading?0:1,transition:'opacity 0.3s ease-in-out'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:100,height:100,borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',boxShadow:'0 8px 32px rgba(76, 201, 240, 0.12)',mb:3,animation:'float 3s ease-in-out infinite','@keyframes float':{'0%':{transform:'translateY(0px)'},'50%':{transform:'translateY(-10px)'},'100%':{transform:'translateY(0px)'}}},children:/*#__PURE__*/_jsx(Typography,{variant:\"h2\",component:\"span\",children:\"\\uD83D\\uDCCA\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{fontWeight:600,color:theme.palette.primary.main,textAlign:'center',mb:1},children:\"Results will appear here\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",sx:{textAlign:'center',maxWidth:'80%',mx:'auto'},children:\"Fill out the form on the left to generate a detailed flood risk assessment\"})]})})]})}),prediction&&prediction.risk_assessment&&showPredictionResult&&/*#__PURE__*/_jsx(SlideUp,{delay:200,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:2,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,left:0,width:'120px',height:'120px',background:'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 100% 0 0',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDCC8\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.dark,letterSpacing:'-0.5px'},children:\"Risk Factor Analysis\"})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{mb:3,color:'text.secondary',maxWidth:'800px'},children:[\"This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.\",prediction.accurate_data&&\" For Indian cities, this includes historical flood data and real-time weather conditions.\"]}),/*#__PURE__*/_jsx(RiskFactorsChart,{riskAssessment:prediction.risk_assessment})]})]})})}),showForecastAlert&&forecastSummary&&/*#__PURE__*/_jsx(ScaleIn,{delay:100,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Alert,{severity:forecastSummary.maxRiskScore>70?\"error\":forecastSummary.maxRiskScore>40?\"warning\":\"info\",variant:\"filled\",sx:{mb:3,borderRadius:2,boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)','& .MuiAlert-icon':{fontSize:'1.8rem'},p:2,animation:'pulse 2s infinite','@keyframes pulse':{'0%':{boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)'},'50%':{boxShadow:'0 8px 36px rgba(0, 0, 0, 0.25)'},'100%':{boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)'}}},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'flex-start'},children:/*#__PURE__*/_jsxs(Box,{sx:{flexGrow:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold',mb:0.5},children:forecastSummary.maxRiskScore>70?\"⚠️ High flood risk detected in the forecast!\":forecastSummary.maxRiskScore>40?\"⚠️ Medium flood risk detected in the forecast\":\"ℹ️ Flood risk forecast generated\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{fontWeight:500},children:[\"Location: \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.location||'Selected area'})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"Peak risk score of \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.maxRiskScore.toFixed(1)}),\" expected around \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.maxRiskTime}),\". Risk trend is \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.riskTrend}),\".\"]}),forecastSummary.maxRiskScore>60&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:1,fontStyle:'italic'},children:\"Please monitor local weather updates and follow emergency guidelines.\"})]})})})})}),prediction&&showPredictionResult&&/*#__PURE__*/_jsx(SlideUp,{delay:300,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{borderRadius:2,overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:/*#__PURE__*/_jsx(TimelineRiskPredictor,{formData:prediction.input_data,onForecastGenerated:handleForecastGenerated})})})}),/*#__PURE__*/_jsx(SlideUp,{delay:200,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,sm:4,md:5},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:3,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDDFA\\uFE0F\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.dark,letterSpacing:'-0.5px'},children:\"Flood Risk Map\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{mb:3,color:'text.secondary',maxWidth:'800px'},children:\"This interactive map shows areas with predicted flood risk based on our analysis. Green markers indicate low risk areas, while red markers indicate high risk zones. Click on markers to see detailed information.\"}),/*#__PURE__*/_jsx(Box,{sx:{borderRadius:3,overflow:'hidden',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',height:{xs:'400px',sm:'500px',md:'600px'},mt:{xs:2,sm:3}},children:/*#__PURE__*/_jsx(FloodMap,{mapData:mapData})})]})]})})}),/*#__PURE__*/_jsx(SlideUp,{delay:300,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,sm:4,md:5},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:3,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDC65\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.dark,letterSpacing:'-0.5px'},children:\"Community Reports\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{mb:3,color:'text.secondary',maxWidth:'800px'},children:\"View and submit real-time flood reports from community members. These reports help validate our predictions and provide valuable on-the-ground information during flood events.\"}),/*#__PURE__*/_jsx(CommunityReports,{})]})]})})})]})})]});}export default App;", "map": {"version": 3, "names": ["useState", "useEffect", "ThemeProvider", "createTheme", "CssBaseline", "Container", "Box", "Paper", "Typography", "Grid", "<PERSON><PERSON>", "Chip", "Header", "FloodMap", "PredictionForm", "ResultDisplay", "RiskFactorsChart", "TimelineRiskPredictor", "VoiceEmergencyAssistant", "CommunityReports", "SlideUp", "ScaleIn", "LoadingAnimation", "axios", "neuromorphicStyles", "getNeuromorphicPalette", "getNeuromorphicShadow", "getPressedEffect", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "_objectSpread", "mode", "info", "main", "light", "dark", "contrastText", "divider", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "letterSpacing", "lineHeight", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "button", "textTransform", "subtitle1", "subtitle2", "shape", "borderRadius", "shadows", "breakpoints", "values", "xs", "sm", "md", "lg", "xl", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleOverrides", "root", "paddingLeft", "paddingRight", "max<PERSON><PERSON><PERSON>", "MuiGrid", "container", "marginTop", "marginLeft", "width", "item", "paddingTop", "MuiPaper", "backgroundColor", "boxShadow", "transition", "padding", "border", "MuiB<PERSON>on", "containedPrimary", "color", "containedSecondary", "outlined", "MuiTextField", "marginBottom", "<PERSON>i<PERSON><PERSON><PERSON>", "height", "opacity", "MuiCard", "overflow", "MuiCardContent", "paddingBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standardSuccess", "standardError", "standardWarning", "standardInfo", "MuiChip", "colorPrimary", "colorSecondary", "colorSuccess", "colorError", "colorWarning", "colorInfo", "MuiTypography", "MuiSwitch", "switchBase", "transform", "thumb", "track", "App", "mapData", "setMapData", "options", "setOptions", "land_cover", "soil_type", "prediction", "setPrediction", "loading", "setLoading", "initialLoading", "setInitialLoading", "showPredictionResult", "setShowPredictionResult", "forecastSummary", "setForecastSummary", "showForecastAlert", "setShowForecastAlert", "fetchData", "loadingTimer", "mapResponse", "optionsResponse", "Promise", "all", "get", "data", "error", "console", "setTimeout", "clearTimeout", "handleSubmit", "formData", "resolve", "response", "post", "handleForecastGenerated", "summary", "children", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "background", "position", "top", "left", "borderTopColor", "primary", "animation", "secondary", "variant", "mt", "backgroundClip", "textFillColor", "mb", "px", "py", "zIndex", "spacing", "elevation", "p", "right", "bottom", "gap", "mr", "alignSelf", "component", "textAlign", "paragraph", "flexWrap", "label", "size", "icon", "onSubmit", "mx", "risk_assessment", "delay", "accurate_data", "riskAssessment", "severity", "maxRiskScore", "flexGrow", "location", "toFixed", "maxRiskTime", "riskTrend", "fontStyle", "input_data", "onForecastGenerated"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/src/App.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';\nimport Header from './components/Header';\nimport FloodMap from './components/FloodMap';\nimport PredictionForm from './components/PredictionForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport RiskFactorsChart from './components/RiskFactorsChart';\nimport TimelineRiskPredictor from './components/TimelineRiskPredictor';\nimport VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';\nimport CommunityReports from './components/CommunityReports';\nimport { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';\nimport LoadingAnimation from './components/animations/LoadingAnimation';\nimport axios from 'axios';\nimport { neuromorphicStyles, getNeuromorphicPalette, getNeuromorphicShadow, getPressedEffect } from './theme/neuromorphicUtils';\nimport './components/ResponsiveLayout.css';\n\n// Create a theme with neuromorphic design\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    ...getNeuromorphicPalette('#3A86FF'),\n    info: {\n      main: '#4CC9F0', // Light blue\n      light: '#e6f7fc',\n      dark: '#3AA1C0',\n      contrastText: '#ffffff',\n    },\n    divider: 'rgba(0, 0, 0, 0.08)',\n  },\n  typography: {\n    fontFamily: '\"Poppins\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',\n      fontWeight: 800,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: 'clamp(2rem, 4vw, 2.75rem)',\n      fontWeight: 700,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.2,\n    },\n    h3: {\n      fontSize: 'clamp(1.5rem, 3vw, 2rem)',\n      fontWeight: 600,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.3,\n    },\n    h4: {\n      fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',\n      fontWeight: 600,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.4,\n    },\n    h5: {\n      fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',\n      fontWeight: 500,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.4,\n    },\n    h6: {\n      fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',\n      fontWeight: 500,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.5,\n    },\n    body1: {\n      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n      lineHeight: 1.6,\n    },\n    body2: {\n      fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',\n      lineHeight: 1.6,\n    },\n    button: {\n      fontWeight: 600,\n      textTransform: 'none',\n      letterSpacing: '0.02em',\n      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n    },\n    subtitle1: {\n      fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    subtitle2: {\n      fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  shadows: [\n    'none',\n    '0px 2px 4px rgba(0, 0, 0, 0.03), 0px 1px 2px rgba(0, 0, 0, 0.06)',\n    '0px 4px 6px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06)',\n    '0px 6px 8px rgba(0, 0, 0, 0.04), 0px 3px 6px rgba(0, 0, 0, 0.06)',\n    '0px 8px 12px rgba(0, 0, 0, 0.05), 0px 4px 8px rgba(0, 0, 0, 0.06)',\n    '0px 10px 15px rgba(0, 0, 0, 0.05), 0px 6px 10px rgba(0, 0, 0, 0.06)',\n    '0px 12px 18px rgba(0, 0, 0, 0.05), 0px 7px 12px rgba(0, 0, 0, 0.06)',\n    '0px 14px 21px rgba(0, 0, 0, 0.05), 0px 8px 14px rgba(0, 0, 0, 0.06)',\n    '0px 16px 24px rgba(0, 0, 0, 0.05), 0px 9px 16px rgba(0, 0, 0, 0.06)',\n    '0px 18px 27px rgba(0, 0, 0, 0.05), 0px 10px 18px rgba(0, 0, 0, 0.06)',\n    '0px 20px 30px rgba(0, 0, 0, 0.05), 0px 11px 20px rgba(0, 0, 0, 0.06)',\n    '0px 22px 33px rgba(0, 0, 0, 0.05), 0px 12px 22px rgba(0, 0, 0, 0.06)',\n    '0px 24px 36px rgba(0, 0, 0, 0.05), 0px 13px 24px rgba(0, 0, 0, 0.06)',\n    '0px 26px 39px rgba(0, 0, 0, 0.05), 0px 14px 26px rgba(0, 0, 0, 0.06)',\n    '0px 28px 42px rgba(0, 0, 0, 0.05), 0px 15px 28px rgba(0, 0, 0, 0.06)',\n    '0px 30px 45px rgba(0, 0, 0, 0.05), 0px 16px 30px rgba(0, 0, 0, 0.06)',\n    '0px 32px 48px rgba(0, 0, 0, 0.05), 0px 17px 32px rgba(0, 0, 0, 0.06)',\n    '0px 34px 51px rgba(0, 0, 0, 0.05), 0px 18px 34px rgba(0, 0, 0, 0.06)',\n    '0px 36px 54px rgba(0, 0, 0, 0.05), 0px 19px 36px rgba(0, 0, 0, 0.06)',\n    '0px 38px 57px rgba(0, 0, 0, 0.05), 0px 20px 38px rgba(0, 0, 0, 0.06)',\n    '0px 40px 60px rgba(0, 0, 0, 0.05), 0px 21px 40px rgba(0, 0, 0, 0.06)',\n    '0px 42px 63px rgba(0, 0, 0, 0.05), 0px 22px 42px rgba(0, 0, 0, 0.06)',\n    '0px 44px 66px rgba(0, 0, 0, 0.05), 0px 23px 44px rgba(0, 0, 0, 0.06)',\n    '0px 46px 69px rgba(0, 0, 0, 0.05), 0px 24px 46px rgba(0, 0, 0, 0.06)',\n  ],\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 960,\n      lg: 1280,\n      xl: 1920,\n    },\n  },\n  components: {\n    MuiContainer: {\n      styleOverrides: {\n        root: {\n          paddingLeft: 16,\n          paddingRight: 16,\n          '@media (min-width:600px)': {\n            paddingLeft: 24,\n            paddingRight: 24,\n          },\n          '@media (min-width:960px)': {\n            paddingLeft: 32,\n            paddingRight: 32,\n          },\n          '@media (min-width:1200px)': {\n            paddingLeft: 48,\n            paddingRight: 48,\n          },\n          maxWidth: '100%',\n          '@media (min-width:1280px)': {\n            maxWidth: '1280px',\n          },\n          '@media (min-width:1920px)': {\n            maxWidth: '1920px',\n          },\n        },\n      },\n    },\n    MuiGrid: {\n      styleOverrides: {\n        container: {\n          marginTop: 0,\n          marginLeft: 0,\n          width: '100%',\n        },\n        item: {\n          paddingTop: 12,\n          paddingLeft: 12,\n          '@media (min-width:600px)': {\n            paddingTop: 16,\n            paddingLeft: 16,\n          },\n          '@media (min-width:960px)': {\n            paddingTop: 20,\n            paddingLeft: 20,\n          },\n          '@media (min-width:1200px)': {\n            paddingTop: 24,\n            paddingLeft: 24,\n          },\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.3s ease-in-out',\n          padding: {\n            xs: 2,\n            sm: 3,\n            md: 4,\n          },\n          border: 'none',\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 12,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.2s ease',\n          border: 'none',\n          padding: {\n            xs: '10px 20px',\n            sm: '12px 24px',\n            md: '14px 28px',\n          },\n          fontSize: {\n            xs: '0.875rem',\n            sm: '0.9375rem',\n            md: '1rem',\n          },\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n            backgroundColor: '#f0f4f8',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n            `,\n            backgroundColor: '#f0f4f8',\n          },\n        },\n        containedPrimary: {\n          color: '#ffffff',\n          backgroundColor: '#3A86FF',\n          boxShadow: `\n            6px 6px 12px rgba(58, 134, 255, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          '&:hover': {\n            backgroundColor: '#2a6bc9',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\n            `,\n          }\n        },\n        containedSecondary: {\n          color: '#ffffff',\n          backgroundColor: '#FF595E',\n          boxShadow: `\n            6px 6px 12px rgba(255, 89, 94, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          '&:hover': {\n            backgroundColor: '#d04649',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\n            `,\n          }\n        },\n        outlined: {\n          backgroundColor: 'transparent',\n          boxShadow: 'none',\n          border: '2px solid #f0f4f8',\n          '&:hover': {\n            boxShadow: `\n              4px 4px 8px rgba(174, 174, 192, 0.2),\n              -4px -4px 8px rgba(255, 255, 255, 0.3)\n            `,\n          },\n          '&:active': {\n            boxShadow: `\n              inset 2px 2px 4px rgba(174, 174, 192, 0.2),\n              inset -2px -2px 4px rgba(255, 255, 255, 0.3)\n            `,\n          },\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          transition: 'all 0.3s ease',\n          marginBottom: {\n            xs: 2,\n            sm: 2.5,\n            md: 3,\n          },\n          '& .MuiOutlinedInput-root': {\n            backgroundColor: '#f0f4f8',\n            borderRadius: 12,\n            boxShadow: `\n              inset 2px 2px 5px rgba(174, 174, 192, 0.2),\n              inset -2px -2px 5px rgba(255, 255, 255, 0.7)\n            `,\n            border: 'none',\n            '& fieldset': {\n              border: 'none',\n            },\n            '&:hover fieldset': {\n              border: 'none',\n            },\n            '&.Mui-focused': {\n              boxShadow: `\n                inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n                inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n              `,\n              '& fieldset': {\n                border: 'none',\n              },\n            },\n          },\n          '& .MuiInputLabel-root': {\n            fontSize: {\n              xs: '0.875rem',\n              sm: '0.9375rem',\n              md: '1rem',\n            },\n            color: '#1a202c',\n            fontWeight: 500,\n            '&.Mui-focused': {\n              color: '#3A86FF',\n              fontWeight: 600,\n            }\n          },\n          '& .MuiInputBase-input': {\n            padding: '16px 14px',\n            color: '#1a202c',\n            fontWeight: 500,\n          },\n        },\n      },\n    },\n    MuiSlider: {\n      styleOverrides: {\n        root: {\n          height: 10,\n          '& .MuiSlider-track': {\n            border: 'none',\n            boxShadow: 'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',\n            backgroundColor: '#3A86FF',\n          },\n          '& .MuiSlider-rail': {\n            boxShadow: `\n              inset 2px 2px 4px rgba(174, 174, 192, 0.3),\n              inset -2px -2px 4px rgba(255, 255, 255, 0.5)\n            `,\n            backgroundColor: '#f0f4f8',\n            opacity: 1,\n          },\n          '& .MuiSlider-thumb': {\n            height: 24,\n            width: 24,\n            backgroundColor: '#f0f4f8',\n            boxShadow: `\n              6px 6px 12px rgba(174, 174, 192, 0.3),\n              -6px -6px 12px rgba(255, 255, 255, 0.5)\n            `,\n            '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {\n              boxShadow: `\n                8px 8px 16px rgba(174, 174, 192, 0.35),\n                -8px -8px 16px rgba(255, 255, 255, 0.6)\n              `,\n            },\n          },\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.3s ease-in-out',\n          border: 'none',\n          overflow: 'hidden',\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n      },\n    },\n    MuiCardContent: {\n      styleOverrides: {\n        root: {\n          padding: {\n            xs: '20px',\n            sm: '24px',\n            md: '28px',\n          },\n          '&:last-child': {\n            paddingBottom: {\n              xs: '20px',\n              sm: '24px',\n              md: '28px',\n            },\n          },\n        },\n      },\n    },\n    MuiAlert: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          fontSize: {\n            xs: '0.875rem',\n            sm: '0.9375rem',\n            md: '1rem',\n          },\n          fontWeight: 500,\n        },\n        standardSuccess: {\n          backgroundColor: 'rgba(6, 214, 160, 0.1)',\n          color: '#05ab80',\n          '& .MuiAlert-icon': {\n            color: '#06d6a0',\n          },\n        },\n        standardError: {\n          backgroundColor: 'rgba(255, 89, 94, 0.1)',\n          color: '#d04649',\n          '& .MuiAlert-icon': {\n            color: '#ff595e',\n          },\n        },\n        standardWarning: {\n          backgroundColor: 'rgba(255, 159, 28, 0.1)',\n          color: '#d18016',\n          '& .MuiAlert-icon': {\n            color: '#ff9f1c',\n          },\n        },\n        standardInfo: {\n          backgroundColor: 'rgba(76, 201, 240, 0.1)',\n          color: '#3aa8cc',\n          '& .MuiAlert-icon': {\n            color: '#4cc9f0',\n          },\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          backgroundColor: '#f0f4f8',\n          boxShadow: `\n            2px 2px 4px rgba(174, 174, 192, 0.3),\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n          fontSize: {\n            xs: '0.75rem',\n            sm: '0.8125rem',\n            md: '0.875rem',\n          },\n          color: '#1a202c',\n          fontWeight: 500,\n          '&:hover': {\n            boxShadow: `\n              3px 3px 6px rgba(174, 174, 192, 0.35),\n              -3px -3px 6px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n        colorPrimary: {\n          backgroundColor: '#3A86FF',\n          color: '#ffffff',\n        },\n        colorSecondary: {\n          backgroundColor: '#FF595E',\n          color: '#ffffff',\n        },\n        colorSuccess: {\n          backgroundColor: '#06d6a0',\n          color: '#ffffff',\n        },\n        colorError: {\n          backgroundColor: '#ff595e',\n          color: '#ffffff',\n        },\n        colorWarning: {\n          backgroundColor: '#ff9f1c',\n          color: '#ffffff',\n        },\n        colorInfo: {\n          backgroundColor: '#4cc9f0',\n          color: '#ffffff',\n        },\n      },\n    },\n    MuiTypography: {\n      styleOverrides: {\n        root: {\n          marginBottom: {\n            xs: 1,\n            sm: 1.5,\n            md: 2,\n          },\n          color: '#1a202c',\n        },\n        h1: {\n          color: '#1a202c',\n          fontWeight: 800,\n        },\n        h2: {\n          color: '#1a202c',\n          fontWeight: 700,\n        },\n        h3: {\n          color: '#1a202c',\n          fontWeight: 700,\n        },\n        h4: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        h5: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        h6: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        subtitle1: {\n          color: '#2d3748',\n          fontWeight: 500,\n        },\n        subtitle2: {\n          color: '#2d3748',\n          fontWeight: 500,\n        },\n        body1: {\n          color: '#2d3748',\n        },\n        body2: {\n          color: '#4a5568',\n        },\n      },\n    },\n    MuiSwitch: {\n      styleOverrides: {\n        root: {\n          width: 56,\n          height: 32,\n          padding: 0,\n        },\n        switchBase: {\n          padding: 4,\n          '&.Mui-checked': {\n            transform: 'translateX(24px)',\n            '& + .MuiSwitch-track': {\n              opacity: 1,\n              backgroundColor: '#e6eef8',\n            },\n          },\n        },\n        thumb: {\n          width: 24,\n          height: 24,\n          backgroundColor: '#f0f4f8',\n          boxShadow: `\n            2px 2px 4px rgba(174, 174, 192, 0.3),\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n        },\n        track: {\n          opacity: 1,\n          borderRadius: 16,\n          backgroundColor: '#e6eef8',\n          boxShadow: `\n            inset 2px 2px 4px rgba(174, 174, 192, 0.3),\n            inset -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  const [mapData, setMapData] = useState([]);\n  const [options, setOptions] = useState({ land_cover: [], soil_type: [] });\n  const [prediction, setPrediction] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [initialLoading, setInitialLoading] = useState(true);\n  const [showPredictionResult, setShowPredictionResult] = useState(false);\n  const [forecastSummary, setForecastSummary] = useState(null);\n  const [showForecastAlert, setShowForecastAlert] = useState(false);\n\n  useEffect(() => {\n    // Fetch map data and options when component mounts\n    const fetchData = async () => {\n      setInitialLoading(true);\n      let loadingTimer;\n\n      try {\n        const [mapResponse, optionsResponse] = await Promise.all([\n          axios.get('/api/map-data'),\n          axios.get('/api/options')\n        ]);\n        setMapData(mapResponse.data);\n        setOptions(optionsResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        // Add a slight delay to make the loading animation visible\n        // but ensure it gets cleared if component unmounts\n        loadingTimer = setTimeout(() => {\n          setInitialLoading(false);\n        }, 1500);\n      }\n\n      // Cleanup function to ensure loading state is reset if component unmounts\n      return () => {\n        if (loadingTimer) clearTimeout(loadingTimer);\n        setInitialLoading(false);\n      };\n    };\n\n    fetchData();\n  }, []);\n\n  const handleSubmit = async (formData) => {\n    setLoading(true);\n    setShowPredictionResult(false);\n    setShowForecastAlert(false);\n\n    try {\n      // Add a slight delay to make the loading animation visible\n      await new Promise(resolve => setTimeout(resolve, 1200));\n\n      const response = await axios.post('/api/predict', formData);\n      setPrediction(response.data);\n\n      // Add a slight delay before showing the result for better animation\n      setTimeout(() => {\n        setShowPredictionResult(true);\n      }, 300);\n    } catch (error) {\n      console.error('Error making prediction:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleForecastGenerated = (summary) => {\n    setForecastSummary(summary);\n    setShowForecastAlert(true);\n\n    // Hide the alert after 10 seconds\n    setTimeout(() => {\n      setShowForecastAlert(false);\n    }, 10000);\n  };\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Header />\n      <VoiceEmergencyAssistant />\n\n      {initialLoading ? (\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            minHeight: '80vh',\n            background: '#e6eef8'\n          }}\n        >\n          <Box sx={{ position: 'relative', width: 200, height: 200 }}>\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.primary.main,\n                animation: 'spin 1.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 15,\n                left: 15,\n                width: 'calc(100% - 30px)',\n                height: 'calc(100% - 30px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.secondary.main,\n                animation: 'spin 2s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 30,\n                left: 30,\n                width: 'calc(100% - 60px)',\n                height: 'calc(100% - 60px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.info.main,\n                animation: 'spin 2.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n          </Box>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              mt: 4,\n              fontWeight: 600,\n              background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n              backgroundClip: 'text',\n              textFillColor: 'transparent',\n              animation: 'pulse 2s infinite',\n              '@keyframes pulse': {\n                '0%': { opacity: 0.6 },\n                '50%': { opacity: 1 },\n                '100%': { opacity: 0.6 }\n              }\n            }}\n          >\n            Loading Flood Prediction System...\n          </Typography>\n        </Box>\n      ) : (\n        <Container\n          maxWidth=\"xl\"\n          sx={{\n            mt: { xs: 3, sm: 4, md: 5 },\n            mb: { xs: 5, sm: 7, md: 10 },\n            px: { xs: 2, sm: 3, md: 4 },\n            py: { xs: 4, sm: 5, md: 6 },\n            overflow: 'hidden',\n            backgroundColor: '#e6eef8',\n            borderRadius: '24px',\n            boxShadow: `\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n            `,\n            position: 'relative',\n            zIndex: 1\n          }}\n        >\n          <Grid\n            container\n            spacing={{ xs: 3, sm: 4, md: 5 }}\n            alignItems=\"stretch\"\n            sx={{\n              '& .MuiGrid-item': {\n                display: 'flex',\n                flexDirection: 'column'\n              }\n            }}\n          >\n            <Grid item xs={12}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 4, sm: 5, md: 6 },\n                  mb: { xs: 3, sm: 4, md: 5 },\n                  backgroundColor: '#f0f4f8',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  boxShadow: `\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\n                  `,\n                  '&:hover': {\n                    boxShadow: `\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\n                    `\n                  },\n                  minHeight: { xs: 'auto', sm: '200px' }\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '180px',\n                    height: '180px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '120px',\n                    height: '120px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: { xs: 'flex-start', sm: 'center' },\n                    flexDirection: { xs: 'column', sm: 'row' },\n                    mb: { xs: 3, sm: 4 },\n                    gap: { xs: 2, sm: 0 }\n                  }}>\n                    <Box\n                      sx={{\n                        mr: { xs: 0, sm: 2 },\n                        p: { xs: 1.5, sm: 2 },\n                        borderRadius: '50%',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)',\n                        alignSelf: { xs: 'center', sm: 'flex-start' }\n                      }}\n                    >\n                      <Typography variant=\"h4\" component=\"span\">🌊</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h2\"\n                      component=\"h1\"\n                      sx={{\n                        background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n                        backgroundClip: 'text',\n                        textFillColor: 'transparent',\n                        fontWeight: 800,\n                        letterSpacing: '-0.5px',\n                        fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },\n                        textAlign: { xs: 'center', sm: 'left' },\n                        lineHeight: 1.2\n                      }}\n                    >\n                      Flood Risk Prediction System\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    paragraph\n                    sx={{\n                      fontSize: { xs: '1rem', sm: '1.1rem' },\n                      maxWidth: { xs: '100%', sm: '90%' },\n                      color: 'text.secondary',\n                      lineHeight: 1.6,\n                      mb: { xs: 3, sm: 4 },\n                      textAlign: { xs: 'center', sm: 'left' }\n                    }}\n                  >\n                    This interactive tool helps predict flood risk based on various environmental and geographical factors.\n                    For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\n                  </Typography>\n                  <Box\n                    sx={{\n                      display: 'flex',\n                      gap: { xs: 1.5, sm: 2 },\n                      flexWrap: 'wrap',\n                      mt: { xs: 3, sm: 4 },\n                      justifyContent: { xs: 'center', sm: 'flex-start' }\n                    }}\n                  >\n                    <Chip\n                      label=\"Real-time Weather Data\"\n                      color=\"primary\"\n                      size=\"medium\"\n                      icon={<span>⛈️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Historical Flood Analysis\"\n                      color=\"info\"\n                      size=\"medium\"\n                      icon={<span>📊</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Indian Cities Database\"\n                      color=\"success\"\n                      size=\"medium\"\n                      icon={<span>🏙️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                  </Box>\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, sm: 4, md: 5 },\n                  height: '100%',\n                  minHeight: { xs: 'auto', lg: '600px' },\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  },\n                  display: 'flex',\n                  flexDirection: 'column'\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 100% 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    right: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '100% 0 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: { xs: 'flex-start', sm: 'center' },\n                    flexDirection: { xs: 'column', sm: 'row' },\n                    mb: { xs: 3, sm: 4 },\n                    gap: { xs: 2, sm: 0 }\n                  }}>\n                    <Box\n                      sx={{\n                        mr: { xs: 0, sm: 2 },\n                        p: { xs: 1, sm: 1.5 },\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        alignSelf: { xs: 'center', sm: 'flex-start' }\n                      }}\n                    >\n                      <Typography variant=\"h5\" component=\"span\">📝</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h4\"\n                      sx={{\n                        fontWeight: 700,\n                        color: theme.palette.primary.main,\n                        letterSpacing: '-0.5px',\n                        fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },\n                        textAlign: { xs: 'center', sm: 'left' }\n                      }}\n                    >\n                      Predict Flood Risk\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{\n                      mb: { xs: 3, sm: 4 },\n                      color: 'text.secondary',\n                      maxWidth: '100%',\n                      textAlign: { xs: 'center', sm: 'left' }\n                    }}\n                  >\n                    Enter location and environmental factors to get a precise flood risk assessment.\n                    For Indian cities, we provide enhanced accuracy using historical data.\n                  </Typography>\n                  <PredictionForm\n                    options={options}\n                    onSubmit={handleSubmit}\n                    loading={loading}\n                  />\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, sm: 4, md: 5 },\n                  height: '100%',\n                  minHeight: { xs: 'auto', lg: '600px' },\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  },\n                  display: 'flex',\n                  flexDirection: 'column'\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  {loading ? (\n                    <LoadingAnimation theme={theme} />\n                  ) : prediction && showPredictionResult ? (\n                    <Box sx={{\n                      opacity: showPredictionResult ? 1 : 0,\n                      transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',\n                      transition: 'all 0.5s ease-in-out'\n                    }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                        <Box\n                          sx={{\n                            mr: 2,\n                            p: 1,\n                            borderRadius: '12px',\n                            background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}\n                        >\n                          <Typography variant=\"h5\" component=\"span\">📊</Typography>\n                        </Box>\n                        <Typography\n                          variant=\"h4\"\n                          sx={{\n                            fontWeight: 700,\n                            color: theme.palette.primary.main,\n                            letterSpacing: '-0.5px'\n                          }}\n                        >\n                          Risk Assessment\n                        </Typography>\n                      </Box>\n                      <ResultDisplay prediction={prediction} />\n                    </Box>\n                  ) : (\n                    <Box sx={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      py: 8,\n                      opacity: loading ? 0 : 1,\n                      transition: 'opacity 0.3s ease-in-out'\n                    }}>\n                      <Box\n                        sx={{\n                          width: 100,\n                          height: 100,\n                          borderRadius: '50%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',\n                          mb: 3,\n                          animation: 'float 3s ease-in-out infinite',\n                          '@keyframes float': {\n                            '0%': { transform: 'translateY(0px)' },\n                            '50%': { transform: 'translateY(-10px)' },\n                            '100%': { transform: 'translateY(0px)' }\n                          }\n                        }}\n                      >\n                        <Typography variant=\"h2\" component=\"span\">📊</Typography>\n                      </Box>\n                      <Typography variant=\"h5\" sx={{ fontWeight: 600, color: theme.palette.primary.main, textAlign: 'center', mb: 1 }}>\n                        Results will appear here\n                      </Typography>\n                      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ textAlign: 'center', maxWidth: '80%', mx: 'auto' }}>\n                        Fill out the form on the left to generate a detailed flood risk assessment\n                      </Typography>\n                    </Box>\n                  )}\n                </Box>\n              </Paper>\n            </Grid>\n\n            {prediction && prediction.risk_assessment && showPredictionResult && (\n              <SlideUp delay={200}>\n                <Grid item xs={12}>\n                  <Paper\n                    elevation={3}\n                    sx={{\n                      p: { xs: 3, md: 4 },\n                      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                      borderRadius: 2,\n                      position: 'relative',\n                      overflow: 'hidden',\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                      }\n                    }}\n                  >\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: 0,\n                        right: 0,\n                        width: '150px',\n                        height: '150px',\n                        background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '0 0 0 100%',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        bottom: 0,\n                        left: 0,\n                        width: '120px',\n                        height: '120px',\n                        background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '0 100% 0 0',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box sx={{ position: 'relative', zIndex: 1 }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                        <Box\n                          sx={{\n                            mr: 2,\n                            p: 1,\n                            borderRadius: '12px',\n                            background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}\n                        >\n                          <Typography variant=\"h5\" component=\"span\">📈</Typography>\n                        </Box>\n                        <Typography\n                          variant=\"h4\"\n                          sx={{\n                            fontWeight: 700,\n                            color: theme.palette.primary.dark,\n                            letterSpacing: '-0.5px'\n                          }}\n                        >\n                          Risk Factor Analysis\n                        </Typography>\n                      </Box>\n                      <Typography\n                        variant=\"body1\"\n                        sx={{\n                          mb: 3,\n                          color: 'text.secondary',\n                          maxWidth: '800px'\n                        }}\n                      >\n                        This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.\n                        {prediction.accurate_data && \" For Indian cities, this includes historical flood data and real-time weather conditions.\"}\n                      </Typography>\n                      <RiskFactorsChart riskAssessment={prediction.risk_assessment} />\n                    </Box>\n                  </Paper>\n                </Grid>\n              </SlideUp>\n            )}\n\n            {/* Forecast Alert */}\n            {showForecastAlert && forecastSummary && (\n              <ScaleIn delay={100}>\n                <Grid item xs={12}>\n                  <Alert\n                    severity={forecastSummary.maxRiskScore > 70 ? \"error\" : forecastSummary.maxRiskScore > 40 ? \"warning\" : \"info\"}\n                    variant=\"filled\"\n                    sx={{\n                      mb: 3,\n                      borderRadius: 2,\n                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n                      '& .MuiAlert-icon': { fontSize: '1.8rem' },\n                      p: 2,\n                      animation: 'pulse 2s infinite',\n                      '@keyframes pulse': {\n                        '0%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' },\n                        '50%': { boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)' },\n                        '100%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' }\n                      }\n                    }}\n                  >\n                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>\n                      <Box sx={{ flexGrow: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n                          {forecastSummary.maxRiskScore > 70\n                            ? \"⚠️ High flood risk detected in the forecast!\"\n                            : forecastSummary.maxRiskScore > 40\n                              ? \"⚠️ Medium flood risk detected in the forecast\"\n                              : \"ℹ️ Flood risk forecast generated\"}\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          Location: <strong>{forecastSummary.location || 'Selected area'}</strong>\n                        </Typography>\n                        <Typography variant=\"body1\">\n                          Peak risk score of <strong>{forecastSummary.maxRiskScore.toFixed(1)}</strong> expected around <strong>{forecastSummary.maxRiskTime}</strong>.\n                          Risk trend is <strong>{forecastSummary.riskTrend}</strong>.\n                        </Typography>\n                        {forecastSummary.maxRiskScore > 60 && (\n                          <Typography variant=\"body2\" sx={{ mt: 1, fontStyle: 'italic' }}>\n                            Please monitor local weather updates and follow emergency guidelines.\n                          </Typography>\n                        )}\n                      </Box>\n                    </Box>\n                  </Alert>\n                </Grid>\n              </ScaleIn>\n            )}\n\n            {/* Temporal Prediction Component */}\n            {prediction && showPredictionResult && (\n              <SlideUp delay={300}>\n                <Grid item xs={12}>\n                  <Paper\n                    elevation={3}\n                    sx={{\n                      borderRadius: 2,\n                      overflow: 'hidden',\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                      }\n                    }}\n                  >\n                    <TimelineRiskPredictor\n                      formData={prediction.input_data}\n                      onForecastGenerated={handleForecastGenerated}\n                    />\n                  </Paper>\n                </Grid>\n              </SlideUp>\n            )}\n\n            <SlideUp delay={200}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, sm: 4, md: 5 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 3,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">🗺️</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Flood Risk Map\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      This interactive map shows areas with predicted flood risk based on our analysis.\n                      Green markers indicate low risk areas, while red markers indicate high risk zones.\n                      Click on markers to see detailed information.\n                    </Typography>\n                    <Box sx={{\n                      borderRadius: 3,\n                      overflow: 'hidden',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      height: { xs: '400px', sm: '500px', md: '600px' },\n                      mt: { xs: 2, sm: 3 }\n                    }}>\n                      <FloodMap mapData={mapData} />\n                    </Box>\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n\n            {/* Community Reports Section */}\n            <SlideUp delay={300}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, sm: 4, md: 5 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 3,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">👥</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Community Reports\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      View and submit real-time flood reports from community members. These reports help validate our predictions\n                      and provide valuable on-the-ground information during flood events.\n                    </Typography>\n                    <CommunityReports />\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n          </Grid>\n        </Container>\n      )}\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "uIAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,OAASC,SAAS,CAAEC,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CACpF,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,qBAAqB,KAAM,oCAAoC,CACtE,MAAO,CAAAC,uBAAuB,KAAM,sCAAsC,CAC1E,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,OAASC,OAAO,CAAEC,OAAO,KAAQ,4CAA4C,CAC7E,MAAO,CAAAC,gBAAgB,KAAM,0CAA0C,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,kBAAkB,CAAEC,sBAAsB,CAAEC,qBAAqB,CAAEC,gBAAgB,KAAQ,2BAA2B,CAC/H,MAAO,mCAAmC,CAE1C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAG7B,WAAW,CAAC,CACxB8B,OAAO,CAAAC,aAAA,CAAAA,aAAA,EACLC,IAAI,CAAE,OAAO,EACVV,sBAAsB,CAAC,SAAS,CAAC,MACpCW,IAAI,CAAE,CACJC,IAAI,CAAE,SAAS,CAAE;AACjBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDC,OAAO,CAAE,qBAAqB,EAC/B,CACDC,UAAU,CAAE,CACVC,UAAU,CAAE,uDAAuD,CACnEC,EAAE,CAAE,CACFC,QAAQ,CAAE,4BAA4B,CACtCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDC,EAAE,CAAE,CACFJ,QAAQ,CAAE,2BAA2B,CACrCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDE,EAAE,CAAE,CACFL,QAAQ,CAAE,0BAA0B,CACpCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDG,EAAE,CAAE,CACFN,QAAQ,CAAE,gCAAgC,CAC1CC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDI,EAAE,CAAE,CACFP,QAAQ,CAAE,4BAA4B,CACtCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDK,EAAE,CAAE,CACFR,QAAQ,CAAE,6BAA6B,CACvCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDM,KAAK,CAAE,CACLT,QAAQ,CAAE,8BAA8B,CACxCG,UAAU,CAAE,GACd,CAAC,CACDO,KAAK,CAAE,CACLV,QAAQ,CAAE,oCAAoC,CAC9CG,UAAU,CAAE,GACd,CAAC,CACDQ,MAAM,CAAE,CACNV,UAAU,CAAE,GAAG,CACfW,aAAa,CAAE,MAAM,CACrBV,aAAa,CAAE,QAAQ,CACvBF,QAAQ,CAAE,8BACZ,CAAC,CACDa,SAAS,CAAE,CACTb,QAAQ,CAAE,oCAAoC,CAC9CC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,GACd,CAAC,CACDW,SAAS,CAAE,CACTd,QAAQ,CAAE,oCAAoC,CAC9CC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,GACd,CACF,CAAC,CACDY,KAAK,CAAE,CACLC,YAAY,CAAE,EAChB,CAAC,CACDC,OAAO,CAAE,CACP,MAAM,CACN,kEAAkE,CAClE,kEAAkE,CAClE,kEAAkE,CAClE,mEAAmE,CACnE,qEAAqE,CACrE,qEAAqE,CACrE,qEAAqE,CACrE,qEAAqE,CACrE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACvE,CACDC,WAAW,CAAE,CACXC,MAAM,CAAE,CACNC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,IAAI,CACRC,EAAE,CAAE,IACN,CACF,CAAC,CACDC,UAAU,CAAE,CACVC,YAAY,CAAE,CACZC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,WAAW,CAAE,EAAE,CACfC,YAAY,CAAE,EAAE,CAChB,0BAA0B,CAAE,CAC1BD,WAAW,CAAE,EAAE,CACfC,YAAY,CAAE,EAChB,CAAC,CACD,0BAA0B,CAAE,CAC1BD,WAAW,CAAE,EAAE,CACfC,YAAY,CAAE,EAChB,CAAC,CACD,2BAA2B,CAAE,CAC3BD,WAAW,CAAE,EAAE,CACfC,YAAY,CAAE,EAChB,CAAC,CACDC,QAAQ,CAAE,MAAM,CAChB,2BAA2B,CAAE,CAC3BA,QAAQ,CAAE,QACZ,CAAC,CACD,2BAA2B,CAAE,CAC3BA,QAAQ,CAAE,QACZ,CACF,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPL,cAAc,CAAE,CACdM,SAAS,CAAE,CACTC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CAAC,CACbC,KAAK,CAAE,MACT,CAAC,CACDC,IAAI,CAAE,CACJC,UAAU,CAAE,EAAE,CACdT,WAAW,CAAE,EAAE,CACf,0BAA0B,CAAE,CAC1BS,UAAU,CAAE,EAAE,CACdT,WAAW,CAAE,EACf,CAAC,CACD,0BAA0B,CAAE,CAC1BS,UAAU,CAAE,EAAE,CACdT,WAAW,CAAE,EACf,CAAC,CACD,2BAA2B,CAAE,CAC3BS,UAAU,CAAE,EAAE,CACdT,WAAW,CAAE,EACf,CACF,CACF,CACF,CAAC,CACDU,QAAQ,CAAE,CACRZ,cAAc,CAAE,CACdC,IAAI,CAAE,CACJY,eAAe,CAAE,SAAS,CAC1BxB,YAAY,CAAE,EAAE,CAChByB,SAAS,wHAGR,CACDC,UAAU,CAAE,sBAAsB,CAClCC,OAAO,CAAE,CACPvB,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAC,CACDsB,MAAM,CAAE,MAAM,CACd,SAAS,CAAE,CACTH,SAAS,+HAIX,CACF,CACF,CACF,CAAC,CACDI,SAAS,CAAE,CACTlB,cAAc,CAAE,CACdC,IAAI,CAAE,CACJY,eAAe,CAAE,SAAS,CAC1BxB,YAAY,CAAE,EAAE,CAChByB,SAAS,wHAGR,CACDC,UAAU,CAAE,eAAe,CAC3BE,MAAM,CAAE,MAAM,CACdD,OAAO,CAAE,CACPvB,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WACN,CAAC,CACDtB,QAAQ,CAAE,CACRoB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACD,SAAS,CAAE,CACTmB,SAAS,+HAGR,CACDD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,wIAGR,CACDD,eAAe,CAAE,SACnB,CACF,CAAC,CACDM,gBAAgB,CAAE,CAChBC,KAAK,CAAE,SAAS,CAChBP,eAAe,CAAE,SAAS,CAC1BC,SAAS,uHAGR,CACD,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,kIAIX,CACF,CAAC,CACDO,kBAAkB,CAAE,CAClBD,KAAK,CAAE,SAAS,CAChBP,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAGR,CACD,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,kIAIX,CACF,CAAC,CACDQ,QAAQ,CAAE,CACRT,eAAe,CAAE,aAAa,CAC9BC,SAAS,CAAE,MAAM,CACjBG,MAAM,CAAE,mBAAmB,CAC3B,SAAS,CAAE,CACTH,SAAS,4HAIX,CAAC,CACD,UAAU,CAAE,CACVA,SAAS,wIAIX,CACF,CACF,CACF,CAAC,CACDS,YAAY,CAAE,CACZvB,cAAc,CAAE,CACdC,IAAI,CAAE,CACJc,UAAU,CAAE,eAAe,CAC3BS,YAAY,CAAE,CACZ/B,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CAAC,CACD,0BAA0B,CAAE,CAC1BkB,eAAe,CAAE,SAAS,CAC1BxB,YAAY,CAAE,EAAE,CAChByB,SAAS,wIAGR,CACDG,MAAM,CAAE,MAAM,CACd,YAAY,CAAE,CACZA,MAAM,CAAE,MACV,CAAC,CACD,kBAAkB,CAAE,CAClBA,MAAM,CAAE,MACV,CAAC,CACD,eAAe,CAAE,CACfH,SAAS,8IAGR,CACD,YAAY,CAAE,CACZG,MAAM,CAAE,MACV,CACF,CACF,CAAC,CACD,uBAAuB,CAAE,CACvB5C,QAAQ,CAAE,CACRoB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACDyB,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GAAG,CACf,eAAe,CAAE,CACf8C,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CACF,CAAC,CACD,uBAAuB,CAAE,CACvB0C,OAAO,CAAE,WAAW,CACpBI,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CACF,CACF,CACF,CAAC,CACDmD,SAAS,CAAE,CACTzB,cAAc,CAAE,CACdC,IAAI,CAAE,CACJyB,MAAM,CAAE,EAAE,CACV,oBAAoB,CAAE,CACpBT,MAAM,CAAE,MAAM,CACdH,SAAS,CAAE,4CAA4C,CACvDD,eAAe,CAAE,SACnB,CAAC,CACD,mBAAmB,CAAE,CACnBC,SAAS,wIAGR,CACDD,eAAe,CAAE,SAAS,CAC1Bc,OAAO,CAAE,CACX,CAAC,CACD,oBAAoB,CAAE,CACpBD,MAAM,CAAE,EAAE,CACVjB,KAAK,CAAE,EAAE,CACTI,eAAe,CAAE,SAAS,CAC1BC,SAAS,8HAGR,CACD,oDAAoD,CAAE,CACpDA,SAAS,qIAIX,CACF,CACF,CACF,CACF,CAAC,CACDc,OAAO,CAAE,CACP5B,cAAc,CAAE,CACdC,IAAI,CAAE,CACJY,eAAe,CAAE,SAAS,CAC1BxB,YAAY,CAAE,EAAE,CAChByB,SAAS,wHAGR,CACDC,UAAU,CAAE,sBAAsB,CAClCE,MAAM,CAAE,MAAM,CACdY,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,CACTf,SAAS,+HAIX,CACF,CACF,CACF,CAAC,CACDgB,cAAc,CAAE,CACd9B,cAAc,CAAE,CACdC,IAAI,CAAE,CACJe,OAAO,CAAE,CACPvB,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MACN,CAAC,CACD,cAAc,CAAE,CACdoC,aAAa,CAAE,CACbtC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MACN,CACF,CACF,CACF,CACF,CAAC,CACDqC,QAAQ,CAAE,CACRhC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJZ,YAAY,CAAE,EAAE,CAChByB,SAAS,wHAGR,CACDzC,QAAQ,CAAE,CACRoB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACDrB,UAAU,CAAE,GACd,CAAC,CACD2D,eAAe,CAAE,CACfpB,eAAe,CAAE,wBAAwB,CACzCO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDc,aAAa,CAAE,CACbrB,eAAe,CAAE,wBAAwB,CACzCO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDe,eAAe,CAAE,CACftB,eAAe,CAAE,yBAAyB,CAC1CO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDgB,YAAY,CAAE,CACZvB,eAAe,CAAE,yBAAyB,CAC1CO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CACF,CACF,CAAC,CACDiB,OAAO,CAAE,CACPrC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJZ,YAAY,CAAE,EAAE,CAChBwB,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAGR,CACDzC,QAAQ,CAAE,CACRoB,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,UACN,CAAC,CACDyB,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GAAG,CACf,SAAS,CAAE,CACTwC,SAAS,6HAIX,CACF,CAAC,CACDwB,YAAY,CAAE,CACZzB,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDmB,cAAc,CAAE,CACd1B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDoB,YAAY,CAAE,CACZ3B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDqB,UAAU,CAAE,CACV5B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDsB,YAAY,CAAE,CACZ7B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDuB,SAAS,CAAE,CACT9B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CACF,CACF,CAAC,CACDwB,aAAa,CAAE,CACb5C,cAAc,CAAE,CACdC,IAAI,CAAE,CACJuB,YAAY,CAAE,CACZ/B,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CAAC,CACDyB,KAAK,CAAE,SACT,CAAC,CACDhD,EAAE,CAAE,CACFgD,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDG,EAAE,CAAE,CACF2C,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDI,EAAE,CAAE,CACF0C,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDK,EAAE,CAAE,CACFyC,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDM,EAAE,CAAE,CACFwC,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDO,EAAE,CAAE,CACFuC,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDY,SAAS,CAAE,CACTkC,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDa,SAAS,CAAE,CACTiC,KAAK,CAAE,SAAS,CAChB9C,UAAU,CAAE,GACd,CAAC,CACDQ,KAAK,CAAE,CACLsC,KAAK,CAAE,SACT,CAAC,CACDrC,KAAK,CAAE,CACLqC,KAAK,CAAE,SACT,CACF,CACF,CAAC,CACDyB,SAAS,CAAE,CACT7C,cAAc,CAAE,CACdC,IAAI,CAAE,CACJQ,KAAK,CAAE,EAAE,CACTiB,MAAM,CAAE,EAAE,CACVV,OAAO,CAAE,CACX,CAAC,CACD8B,UAAU,CAAE,CACV9B,OAAO,CAAE,CAAC,CACV,eAAe,CAAE,CACf+B,SAAS,CAAE,kBAAkB,CAC7B,sBAAsB,CAAE,CACtBpB,OAAO,CAAE,CAAC,CACVd,eAAe,CAAE,SACnB,CACF,CACF,CAAC,CACDmC,KAAK,CAAE,CACLvC,KAAK,CAAE,EAAE,CACTiB,MAAM,CAAE,EAAE,CACVb,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAIX,CAAC,CACDmC,KAAK,CAAE,CACLtB,OAAO,CAAE,CAAC,CACVtC,YAAY,CAAE,EAAE,CAChBwB,eAAe,CAAE,SAAS,CAC1BC,SAAS,kIAIX,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,QAAS,CAAAoC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG5H,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6H,OAAO,CAAEC,UAAU,CAAC,CAAG9H,QAAQ,CAAC,CAAE+H,UAAU,CAAE,EAAE,CAAEC,SAAS,CAAE,EAAG,CAAC,CAAC,CACzE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlI,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACmI,OAAO,CAAEC,UAAU,CAAC,CAAGpI,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqI,cAAc,CAAEC,iBAAiB,CAAC,CAAGtI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACuI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxI,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACyI,eAAe,CAAEC,kBAAkB,CAAC,CAAG1I,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAAC2I,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5I,QAAQ,CAAC,KAAK,CAAC,CAEjEC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA4I,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5BP,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAI,CAAAQ,YAAY,CAEhB,GAAI,CACF,KAAM,CAACC,WAAW,CAAEC,eAAe,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACvD3H,KAAK,CAAC4H,GAAG,CAAC,eAAe,CAAC,CAC1B5H,KAAK,CAAC4H,GAAG,CAAC,cAAc,CAAC,CAC1B,CAAC,CACFvB,UAAU,CAACmB,WAAW,CAACK,IAAI,CAAC,CAC5BtB,UAAU,CAACkB,eAAe,CAACI,IAAI,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAAC,OAAS,CACR;AACA;AACAP,YAAY,CAAGS,UAAU,CAAC,IAAM,CAC9BjB,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CAEA;AACA,MAAO,IAAM,CACX,GAAIQ,YAAY,CAAEU,YAAY,CAACV,YAAY,CAAC,CAC5CR,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CACH,CAAC,CAEDO,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,YAAY,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACvCtB,UAAU,CAAC,IAAI,CAAC,CAChBI,uBAAuB,CAAC,KAAK,CAAC,CAC9BI,oBAAoB,CAAC,KAAK,CAAC,CAE3B,GAAI,CACF;AACA,KAAM,IAAI,CAAAK,OAAO,CAACU,OAAO,EAAIJ,UAAU,CAACI,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAArI,KAAK,CAACsI,IAAI,CAAC,cAAc,CAAEH,QAAQ,CAAC,CAC3DxB,aAAa,CAAC0B,QAAQ,CAACR,IAAI,CAAC,CAE5B;AACAG,UAAU,CAAC,IAAM,CACff,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,uBAAuB,CAAIC,OAAO,EAAK,CAC3CrB,kBAAkB,CAACqB,OAAO,CAAC,CAC3BnB,oBAAoB,CAAC,IAAI,CAAC,CAE1B;AACAW,UAAU,CAAC,IAAM,CACfX,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAAE,KAAK,CAAC,CACX,CAAC,CAED,mBACE7G,KAAA,CAAC7B,aAAa,EAAC8B,KAAK,CAAEA,KAAM,CAAAgI,QAAA,eAC1BnI,IAAA,CAACzB,WAAW,GAAE,CAAC,cACfyB,IAAA,CAACjB,MAAM,GAAE,CAAC,cACViB,IAAA,CAACX,uBAAuB,GAAE,CAAC,CAE1BmH,cAAc,cACbtG,KAAA,CAACzB,GAAG,EACF2J,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,SAAS,CAAE,MAAM,CACjBC,UAAU,CAAE,SACd,CAAE,CAAAP,QAAA,eAEFjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEvF,KAAK,CAAE,GAAG,CAAEiB,MAAM,CAAE,GAAI,CAAE,CAAA8D,QAAA,eACzDnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPzF,KAAK,CAAE,MAAM,CACbiB,MAAM,CAAE,MAAM,CACdrC,YAAY,CAAE,KAAK,CACnB4B,MAAM,CAAE,uBAAuB,CAC/BkF,cAAc,CAAE3I,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACvI,IAAI,CAC1CwI,SAAS,CAAE,2BAA2B,CACtC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,cACF1F,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRzF,KAAK,CAAE,mBAAmB,CAC1BiB,MAAM,CAAE,mBAAmB,CAC3BrC,YAAY,CAAE,KAAK,CACnB4B,MAAM,CAAE,uBAAuB,CAC/BkF,cAAc,CAAE3I,KAAK,CAACC,OAAO,CAAC6I,SAAS,CAACzI,IAAI,CAC5CwI,SAAS,CAAE,yBAAyB,CACpC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,cACF1F,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRzF,KAAK,CAAE,mBAAmB,CAC1BiB,MAAM,CAAE,mBAAmB,CAC3BrC,YAAY,CAAE,KAAK,CACnB4B,MAAM,CAAE,uBAAuB,CAC/BkF,cAAc,CAAE3I,KAAK,CAACC,OAAO,CAACG,IAAI,CAACC,IAAI,CACvCwI,SAAS,CAAE,2BAA2B,CACtC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,EACC,CAAC,cACN1F,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFe,EAAE,CAAE,CAAC,CACLlI,UAAU,CAAE,GAAG,CACfyH,UAAU,CAAE,kDAAkD,CAC9DU,cAAc,CAAE,MAAM,CACtBC,aAAa,CAAE,aAAa,CAC5BL,SAAS,CAAE,mBAAmB,CAC9B,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAE1E,OAAO,CAAE,GAAI,CAAC,CACtB,KAAK,CAAE,CAAEA,OAAO,CAAE,CAAE,CAAC,CACrB,MAAM,CAAE,CAAEA,OAAO,CAAE,GAAI,CACzB,CACF,CAAE,CAAA6D,QAAA,CACH,oCAED,CAAY,CAAC,EACV,CAAC,cAENnI,IAAA,CAACxB,SAAS,EACRuE,QAAQ,CAAC,IAAI,CACbqF,EAAE,CAAE,CACFe,EAAE,CAAE,CAAE/G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BgH,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,EAAG,CAAC,CAC5BiH,EAAE,CAAE,CAAEnH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BkH,EAAE,CAAE,CAAEpH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BkC,QAAQ,CAAE,QAAQ,CAClBhB,eAAe,CAAE,SAAS,CAC1BxB,YAAY,CAAE,MAAM,CACpByB,SAAS,wIAGR,CACDkF,QAAQ,CAAE,UAAU,CACpBc,MAAM,CAAE,CACV,CAAE,CAAAtB,QAAA,cAEFjI,KAAA,CAACtB,IAAI,EACHqE,SAAS,MACTyG,OAAO,CAAE,CAAEtH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CACjCiG,UAAU,CAAC,SAAS,CACpBH,EAAE,CAAE,CACF,iBAAiB,CAAE,CACjBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CACF,CAAE,CAAAH,QAAA,eAEFnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1BgH,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BkB,eAAe,CAAE,SAAS,CAC1BmF,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBxC,YAAY,CAAE,CAAC,CACf0B,UAAU,CAAE,sBAAsB,CAClCD,SAAS,gJAGR,CACD,SAAS,CAAE,CACTA,SAAS,uJAIX,CAAC,CACDgF,SAAS,CAAE,CAAErG,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CACvC,CAAE,CAAA8F,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFzJ,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTjB,IAAI,CAAE,CAAC,CACPzF,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFvJ,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC3CjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,CAAEnG,EAAE,CAAE,YAAY,CAAEC,EAAE,CAAE,QAAS,CAAC,CAC9CiG,aAAa,CAAE,CAAElG,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,KAAM,CAAC,CAC1CiH,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpB0H,GAAG,CAAE,CAAE3H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACtB,CAAE,CAAA8F,QAAA,eACAnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAE5H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBuH,CAAC,CAAE,CAAExH,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBL,YAAY,CAAE,KAAK,CACnB0G,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB/E,SAAS,CAAE,qCAAqC,CAChDwG,SAAS,CAAE,CAAE7H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,YAAa,CAC9C,CAAE,CAAA8F,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZgB,SAAS,CAAC,IAAI,CACd9B,EAAE,CAAE,CACFM,UAAU,CAAE,kDAAkD,CAC9DU,cAAc,CAAE,MAAM,CACtBC,aAAa,CAAE,aAAa,CAC5BpI,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,QAAQ,CACvBF,QAAQ,CAAE,CAAEoB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CAAC,CAClD6H,SAAS,CAAE,CAAE/H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CAAC,CACvClB,UAAU,CAAE,GACd,CAAE,CAAAgH,QAAA,CACH,8BAED,CAAY,CAAC,EACV,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,OAAO,CACfkB,SAAS,MACThC,EAAE,CAAE,CACFpH,QAAQ,CAAE,CAAEoB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtCU,QAAQ,CAAE,CAAEX,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAC,CACnC0B,KAAK,CAAE,gBAAgB,CACvB5C,UAAU,CAAE,GAAG,CACfmI,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpB8H,SAAS,CAAE,CAAE/H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CACxC,CAAE,CAAA8F,QAAA,CACH,0NAGD,CAAY,CAAC,cACbjI,KAAA,CAACzB,GAAG,EACF2J,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACf0B,GAAG,CAAE,CAAE3H,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACvBgI,QAAQ,CAAE,MAAM,CAChBlB,EAAE,CAAE,CAAE/G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBmG,cAAc,CAAE,CAAEpG,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,YAAa,CACnD,CAAE,CAAA8F,QAAA,eAEFnI,IAAA,CAAClB,IAAI,EACHwL,KAAK,CAAC,wBAAwB,CAC9BvG,KAAK,CAAC,SAAS,CACfwG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAExK,IAAA,SAAAmI,QAAA,CAAM,cAAE,CAAM,CAAE,CACtBC,EAAE,CAAE,CAAEnH,UAAU,CAAE,GAAG,CAAEsI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,cACFvJ,IAAA,CAAClB,IAAI,EACHwL,KAAK,CAAC,2BAA2B,CACjCvG,KAAK,CAAC,MAAM,CACZwG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAExK,IAAA,SAAAmI,QAAA,CAAM,cAAE,CAAM,CAAE,CACtBC,EAAE,CAAE,CAAEnH,UAAU,CAAE,GAAG,CAAEsI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,cACFvJ,IAAA,CAAClB,IAAI,EACHwL,KAAK,CAAC,wBAAwB,CAC9BvG,KAAK,CAAC,SAAS,CACfwG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAExK,IAAA,SAAAmI,QAAA,CAAM,oBAAG,CAAM,CAAE,CACvBC,EAAE,CAAE,CAAEnH,UAAU,CAAE,GAAG,CAAEsI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,EACC,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,cAEPvJ,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAACG,EAAE,CAAE,CAAE,CAAA4F,QAAA,cACvBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1B+B,MAAM,CAAE,MAAM,CACdoE,SAAS,CAAE,CAAErG,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,OAAQ,CAAC,CACtCoG,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBxC,YAAY,CAAE,CAAC,CACf0B,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CAAC,CACD4E,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAH,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPzF,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFzJ,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTD,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,MAAM,CACbiB,MAAM,CAAE,MAAM,CACdqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFvJ,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC3CjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,CAAEnG,EAAE,CAAE,YAAY,CAAEC,EAAE,CAAE,QAAS,CAAC,CAC9CiG,aAAa,CAAE,CAAElG,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,KAAM,CAAC,CAC1CiH,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpB0H,GAAG,CAAE,CAAE3H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACtB,CAAE,CAAA8F,QAAA,eACAnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAE5H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBuH,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CACrBL,YAAY,CAAE,MAAM,CACpB0G,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxByB,SAAS,CAAE,CAAE7H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,YAAa,CAC9C,CAAE,CAAA8F,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFnH,UAAU,CAAE,GAAG,CACf8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACvI,IAAI,CACjCU,aAAa,CAAE,QAAQ,CACvBF,QAAQ,CAAE,CAAEoB,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,UAAW,CAAC,CACtD6H,SAAS,CAAE,CAAE/H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CACxC,CAAE,CAAA8F,QAAA,CACH,oBAED,CAAY,CAAC,EACV,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,OAAO,CACfd,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAElH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpB0B,KAAK,CAAE,gBAAgB,CACvBhB,QAAQ,CAAE,MAAM,CAChBoH,SAAS,CAAE,CAAE/H,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CACxC,CAAE,CAAA8F,QAAA,CACH,yJAGD,CAAY,CAAC,cACbnI,IAAA,CAACf,cAAc,EACb+G,OAAO,CAAEA,OAAQ,CACjByE,QAAQ,CAAE7C,YAAa,CACvBtB,OAAO,CAAEA,OAAQ,CAClB,CAAC,EACC,CAAC,EACD,CAAC,CACJ,CAAC,cAEPtG,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAACG,EAAE,CAAE,CAAE,CAAA4F,QAAA,cACvBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1B+B,MAAM,CAAE,MAAM,CACdoE,SAAS,CAAE,CAAErG,EAAE,CAAE,MAAM,CAAEG,EAAE,CAAE,OAAQ,CAAC,CACtCoG,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBxC,YAAY,CAAE,CAAC,CACf0B,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CAAC,CACD4E,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAH,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFzJ,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTjB,IAAI,CAAE,CAAC,CACPzF,KAAK,CAAE,MAAM,CACbiB,MAAM,CAAE,MAAM,CACdqE,UAAU,CAAE,qEAAqE,CACjF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFzJ,IAAA,CAACvB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,CAC1C7B,OAAO,cACNtG,IAAA,CAACP,gBAAgB,EAACU,KAAK,CAAEA,KAAM,CAAE,CAAC,CAChCiG,UAAU,EAAIM,oBAAoB,cACpCxG,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CACP9D,OAAO,CAAEoC,oBAAoB,CAAG,CAAC,CAAG,CAAC,CACrChB,SAAS,CAAEgB,oBAAoB,CAAG,eAAe,CAAG,kBAAkB,CACtEhD,UAAU,CAAE,sBACd,CAAE,CAAAyE,QAAA,eACAjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJ5H,YAAY,CAAE,MAAM,CACpB0G,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFnH,UAAU,CAAE,GAAG,CACf8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACvI,IAAI,CACjCU,aAAa,CAAE,QACjB,CAAE,CAAAiH,QAAA,CACH,iBAED,CAAY,CAAC,EACV,CAAC,cACNnI,IAAA,CAACd,aAAa,EAACkH,UAAU,CAAEA,UAAW,CAAE,CAAC,EACtC,CAAC,cAENlG,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBgB,EAAE,CAAE,CAAC,CACLlF,OAAO,CAAEgC,OAAO,CAAG,CAAC,CAAG,CAAC,CACxB5C,UAAU,CAAE,0BACd,CAAE,CAAAyE,QAAA,eACAnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFhF,KAAK,CAAE,GAAG,CACViB,MAAM,CAAE,GAAG,CACXrC,YAAY,CAAE,KAAK,CACnBqG,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBE,UAAU,CAAE,mFAAmF,CAC/FjF,SAAS,CAAE,qCAAqC,CAChD6F,EAAE,CAAE,CAAC,CACLN,SAAS,CAAE,+BAA+B,CAC1C,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAEtD,SAAS,CAAE,iBAAkB,CAAC,CACtC,KAAK,CAAE,CAAEA,SAAS,CAAE,mBAAoB,CAAC,CACzC,MAAM,CAAE,CAAEA,SAAS,CAAE,iBAAkB,CACzC,CACF,CAAE,CAAAyC,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACd,EAAE,CAAE,CAAEnH,UAAU,CAAE,GAAG,CAAE8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACvI,IAAI,CAAE2J,SAAS,CAAE,QAAQ,CAAEb,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,CAAC,0BAEjH,CAAY,CAAC,cACbnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,OAAO,CAACnF,KAAK,CAAC,gBAAgB,CAACqE,EAAE,CAAE,CAAE+B,SAAS,CAAE,QAAQ,CAAEpH,QAAQ,CAAE,KAAK,CAAE2H,EAAE,CAAE,MAAO,CAAE,CAAAvC,QAAA,CAAC,4EAE7G,CAAY,CAAC,EACV,CACN,CACE,CAAC,EACD,CAAC,CACJ,CAAC,CAEN/B,UAAU,EAAIA,UAAU,CAACuE,eAAe,EAAIjE,oBAAoB,eAC/D1G,IAAA,CAACT,OAAO,EAACqL,KAAK,CAAE,GAAI,CAAAzC,QAAA,cAClBnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnBoG,UAAU,CAAE,mDAAmD,CAC/D1G,YAAY,CAAE,CAAC,CACf2G,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAA0E,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFzJ,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTjB,IAAI,CAAE,CAAC,CACPzF,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFvJ,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC3CjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJ5H,YAAY,CAAE,MAAM,CACpB0G,UAAU,CAAE,kFAAkF,CAC9FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFnH,UAAU,CAAE,GAAG,CACf8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACrI,IAAI,CACjCQ,aAAa,CAAE,QACjB,CAAE,CAAAiH,QAAA,CACH,sBAED,CAAY,CAAC,EACV,CAAC,cACNjI,KAAA,CAACvB,UAAU,EACTuK,OAAO,CAAC,OAAO,CACfd,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLvF,KAAK,CAAE,gBAAgB,CACvBhB,QAAQ,CAAE,OACZ,CAAE,CAAAoF,QAAA,EACH,6HAEC,CAAC/B,UAAU,CAACyE,aAAa,EAAI,2FAA2F,EAC9G,CAAC,cACb7K,IAAA,CAACb,gBAAgB,EAAC2L,cAAc,CAAE1E,UAAU,CAACuE,eAAgB,CAAE,CAAC,EAC7D,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CACV,CAGA7D,iBAAiB,EAAIF,eAAe,eACnC5G,IAAA,CAACR,OAAO,EAACoL,KAAK,CAAE,GAAI,CAAAzC,QAAA,cAClBnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBnI,IAAA,CAACnB,KAAK,EACJkM,QAAQ,CAAEnE,eAAe,CAACoE,YAAY,CAAG,EAAE,CAAG,OAAO,CAAGpE,eAAe,CAACoE,YAAY,CAAG,EAAE,CAAG,SAAS,CAAG,MAAO,CAC/G9B,OAAO,CAAC,QAAQ,CAChBd,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLtH,YAAY,CAAE,CAAC,CACfyB,SAAS,CAAE,gCAAgC,CAC3C,kBAAkB,CAAE,CAAEzC,QAAQ,CAAE,QAAS,CAAC,CAC1C4I,CAAC,CAAE,CAAC,CACJZ,SAAS,CAAE,mBAAmB,CAC9B,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAEvF,SAAS,CAAE,gCAAiC,CAAC,CACrD,KAAK,CAAE,CAAEA,SAAS,CAAE,gCAAiC,CAAC,CACtD,MAAM,CAAE,CAAEA,SAAS,CAAE,gCAAiC,CACxD,CACF,CAAE,CAAA0E,QAAA,cAEFnI,IAAA,CAACvB,GAAG,EAAC2J,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,YAAa,CAAE,CAAAJ,QAAA,cACrDjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAE6C,QAAQ,CAAE,CAAE,CAAE,CAAA9C,QAAA,eACvBnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACd,EAAE,CAAE,CAAEnH,UAAU,CAAE,MAAM,CAAEqI,EAAE,CAAE,GAAI,CAAE,CAAAnB,QAAA,CAC1DvB,eAAe,CAACoE,YAAY,CAAG,EAAE,CAC9B,8CAA8C,CAC9CpE,eAAe,CAACoE,YAAY,CAAG,EAAE,CAC/B,+CAA+C,CAC/C,kCAAkC,CAC9B,CAAC,cACb9K,KAAA,CAACvB,UAAU,EAACuK,OAAO,CAAC,OAAO,CAACd,EAAE,CAAE,CAAEnH,UAAU,CAAE,GAAI,CAAE,CAAAkH,QAAA,EAAC,YACzC,cAAAnI,IAAA,WAAAmI,QAAA,CAASvB,eAAe,CAACsE,QAAQ,EAAI,eAAe,CAAS,CAAC,EAC9D,CAAC,cACbhL,KAAA,CAACvB,UAAU,EAACuK,OAAO,CAAC,OAAO,CAAAf,QAAA,EAAC,qBACP,cAAAnI,IAAA,WAAAmI,QAAA,CAASvB,eAAe,CAACoE,YAAY,CAACG,OAAO,CAAC,CAAC,CAAC,CAAS,CAAC,oBAAiB,cAAAnL,IAAA,WAAAmI,QAAA,CAASvB,eAAe,CAACwE,WAAW,CAAS,CAAC,mBAC9H,cAAApL,IAAA,WAAAmI,QAAA,CAASvB,eAAe,CAACyE,SAAS,CAAS,CAAC,IAC5D,EAAY,CAAC,CACZzE,eAAe,CAACoE,YAAY,CAAG,EAAE,eAChChL,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,OAAO,CAACd,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEmC,SAAS,CAAE,QAAS,CAAE,CAAAnD,QAAA,CAAC,uEAEhE,CAAY,CACb,EACE,CAAC,CACH,CAAC,CACD,CAAC,CACJ,CAAC,CACA,CACV,CAGA/B,UAAU,EAAIM,oBAAoB,eACjC1G,IAAA,CAACT,OAAO,EAACqL,KAAK,CAAE,GAAI,CAAAzC,QAAA,cAClBnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBnI,IAAA,CAACtB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFpG,YAAY,CAAE,CAAC,CACfwC,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAA0E,QAAA,cAEFnI,IAAA,CAACZ,qBAAqB,EACpByI,QAAQ,CAAEzB,UAAU,CAACmF,UAAW,CAChCC,mBAAmB,CAAEvD,uBAAwB,CAC9C,CAAC,CACG,CAAC,CACJ,CAAC,CACA,CACV,cAEDjI,IAAA,CAACT,OAAO,EAACqL,KAAK,CAAE,GAAI,CAAAzC,QAAA,cAClBnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1BoG,UAAU,CAAE,mDAAmD,CAC/D1G,YAAY,CAAE,CAAC,CACf2G,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAA0E,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFvJ,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC3CjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJ5H,YAAY,CAAE,MAAM,CACpB0G,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,oBAAG,CAAY,CAAC,CACvD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFnH,UAAU,CAAE,GAAG,CACf8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACrI,IAAI,CACjCQ,aAAa,CAAE,QACjB,CAAE,CAAAiH,QAAA,CACH,gBAED,CAAY,CAAC,EACV,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,OAAO,CACfkB,SAAS,MACThC,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLvF,KAAK,CAAE,gBAAgB,CACvBhB,QAAQ,CAAE,OACZ,CAAE,CAAAoF,QAAA,CACH,oNAID,CAAY,CAAC,cACbnI,IAAA,CAACvB,GAAG,EAAC2J,EAAE,CAAE,CACPpG,YAAY,CAAE,CAAC,CACfwC,QAAQ,CAAE,QAAQ,CAClBf,SAAS,CAAE,gCAAgC,CAC3CY,MAAM,CAAE,CAAEjC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,OAAQ,CAAC,CACjD6G,EAAE,CAAE,CAAE/G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACrB,CAAE,CAAA8F,QAAA,cACAnI,IAAA,CAAChB,QAAQ,EAAC8G,OAAO,CAAEA,OAAQ,CAAE,CAAC,CAC3B,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CAAC,cAGV9F,IAAA,CAACT,OAAO,EAACqL,KAAK,CAAE,GAAI,CAAAzC,QAAA,cAClBnI,IAAA,CAACpB,IAAI,EAACyE,IAAI,MAACjB,EAAE,CAAE,EAAG,CAAA+F,QAAA,cAChBjI,KAAA,CAACxB,KAAK,EACJiL,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,CAAC,CAAE,CAAExH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1BoG,UAAU,CAAE,mDAAmD,CAC/D1G,YAAY,CAAE,CAAC,CACf2G,QAAQ,CAAE,UAAU,CACpBnE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAA0E,QAAA,eAEFnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNiB,KAAK,CAAE,CAAC,CACRzG,KAAK,CAAE,OAAO,CACdiB,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjF1G,YAAY,CAAE,YAAY,CAC1ByH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFvJ,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEc,MAAM,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC3CjI,KAAA,CAACzB,GAAG,EAAC2J,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDnI,IAAA,CAACvB,GAAG,EACF2J,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJ5H,YAAY,CAAE,MAAM,CACpB0G,UAAU,CAAE,kFAAkF,CAC9FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFnI,IAAA,CAACrB,UAAU,EAACuK,OAAO,CAAC,IAAI,CAACgB,SAAS,CAAC,MAAM,CAAA/B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFnH,UAAU,CAAE,GAAG,CACf8C,KAAK,CAAE5D,KAAK,CAACC,OAAO,CAAC2I,OAAO,CAACrI,IAAI,CACjCQ,aAAa,CAAE,QACjB,CAAE,CAAAiH,QAAA,CACH,mBAED,CAAY,CAAC,EACV,CAAC,cACNnI,IAAA,CAACrB,UAAU,EACTuK,OAAO,CAAC,OAAO,CACfkB,SAAS,MACThC,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLvF,KAAK,CAAE,gBAAgB,CACvBhB,QAAQ,CAAE,OACZ,CAAE,CAAAoF,QAAA,CACH,iLAGD,CAAY,CAAC,cACbnI,IAAA,CAACV,gBAAgB,GAAE,CAAC,EACjB,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CAAC,EACN,CAAC,CACE,CACZ,EACY,CAAC,CAEpB,CAEA,cAAe,CAAAuG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}