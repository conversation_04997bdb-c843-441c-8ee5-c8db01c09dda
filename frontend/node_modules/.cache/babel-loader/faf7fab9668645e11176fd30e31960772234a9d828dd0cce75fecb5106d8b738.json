{"ast": null, "code": "export { default } from './getDisplayName';\nexport * from './getDisplayName';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/utils/esm/getDisplayName/index.js"], "sourcesContent": ["export { default } from './getDisplayName';\nexport * from './getDisplayName';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}