{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(\"mui-\".concat(globalId));\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// downstream bundlers may remove unnecessary concatenation, but won't remove toString call -- Workaround for https://github.com/webpack/webpack/issues/14814\nconst maybeReactUseId = React['useId'.toString()];\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride != null ? idOverride : reactId;\n  }\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}", "map": {"version": 3, "names": ["React", "globalId", "useGlobalId", "idOverride", "defaultId", "setDefaultId", "useState", "id", "useEffect", "concat", "maybeReactUseId", "toString", "useId", "undefined", "reactId"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/useId/useId.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// downstream bundlers may remove unnecessary concatenation, but won't remove toString call -- Workaround for https://github.com/webpack/webpack/issues/14814\nconst maybeReactUseId = React['useId'.toString()];\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride != null ? idOverride : reactId;\n  }\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,QAAQ,GAAG,CAAC;AAChB,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGL,KAAK,CAACM,QAAQ,CAACH,UAAU,CAAC;EAC5D,MAAMI,EAAE,GAAGJ,UAAU,IAAIC,SAAS;EAClCJ,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIJ,SAAS,IAAI,IAAI,EAAE;MACrB;MACA;MACA;MACA;MACAH,QAAQ,IAAI,CAAC;MACbI,YAAY,QAAAI,MAAA,CAAQR,QAAQ,CAAE,CAAC;IACjC;EACF,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EACf,OAAOG,EAAE;AACX;;AAEA;AACA,MAAMG,eAAe,GAAGV,KAAK,CAAC,OAAO,CAACW,QAAQ,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,KAAKA,CAACT,UAAU,EAAE;EACxC,IAAIO,eAAe,KAAKG,SAAS,EAAE;IACjC,MAAMC,OAAO,GAAGJ,eAAe,CAAC,CAAC;IACjC,OAAOP,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGW,OAAO;EAClD;EACA;EACA,OAAOZ,WAAW,CAACC,UAAU,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}