{"ast": null, "code": "import { DomUtil } from 'leaflet';\nfunction splitClassName(className) {\n  return className.split(' ').filter(Boolean);\n}\nexport function addClassName(element, className) {\n  splitClassName(className).forEach(cls => {\n    DomUtil.addClass(element, cls);\n  });\n}\nexport function removeClassName(element, className) {\n  splitClassName(className).forEach(cls => {\n    DomUtil.removeClass(element, cls);\n  });\n}\nexport function updateClassName(element, prevClassName, nextClassName) {\n  if (element != null && nextClassName !== prevClassName) {\n    if (prevClassName != null && prevClassName.length > 0) {\n      removeClassName(element, prevClassName);\n    }\n    if (nextClassName != null && nextClassName.length > 0) {\n      addClassName(element, nextClassName);\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "splitClassName", "className", "split", "filter", "Boolean", "addClassName", "element", "for<PERSON>ach", "cls", "addClass", "removeClassName", "removeClass", "updateClassName", "prevClassName", "nextClassName", "length"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/dom.js"], "sourcesContent": ["import { DomUtil } from 'leaflet';\nfunction splitClassName(className) {\n    return className.split(' ').filter(Boolean);\n}\nexport function addClassName(element, className) {\n    splitClassName(className).forEach((cls)=>{\n        DomUtil.addClass(element, cls);\n    });\n}\nexport function removeClassName(element, className) {\n    splitClassName(className).forEach((cls)=>{\n        DomUtil.removeClass(element, cls);\n    });\n}\nexport function updateClassName(element, prevClassName, nextClassName) {\n    if (element != null && nextClassName !== prevClassName) {\n        if (prevClassName != null && prevClassName.length > 0) {\n            removeClassName(element, prevClassName);\n        }\n        if (nextClassName != null && nextClassName.length > 0) {\n            addClassName(element, nextClassName);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASC,cAAcA,CAACC,SAAS,EAAE;EAC/B,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAC/C;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEL,SAAS,EAAE;EAC7CD,cAAc,CAACC,SAAS,CAAC,CAACM,OAAO,CAAEC,GAAG,IAAG;IACrCT,OAAO,CAACU,QAAQ,CAACH,OAAO,EAAEE,GAAG,CAAC;EAClC,CAAC,CAAC;AACN;AACA,OAAO,SAASE,eAAeA,CAACJ,OAAO,EAAEL,SAAS,EAAE;EAChDD,cAAc,CAACC,SAAS,CAAC,CAACM,OAAO,CAAEC,GAAG,IAAG;IACrCT,OAAO,CAACY,WAAW,CAACL,OAAO,EAAEE,GAAG,CAAC;EACrC,CAAC,CAAC;AACN;AACA,OAAO,SAASI,eAAeA,CAACN,OAAO,EAAEO,aAAa,EAAEC,aAAa,EAAE;EACnE,IAAIR,OAAO,IAAI,IAAI,IAAIQ,aAAa,KAAKD,aAAa,EAAE;IACpD,IAAIA,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;MACnDL,eAAe,CAACJ,OAAO,EAAEO,aAAa,CAAC;IAC3C;IACA,IAAIC,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACnDV,YAAY,CAACC,OAAO,EAAEQ,aAAa,CAAC;IACxC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}