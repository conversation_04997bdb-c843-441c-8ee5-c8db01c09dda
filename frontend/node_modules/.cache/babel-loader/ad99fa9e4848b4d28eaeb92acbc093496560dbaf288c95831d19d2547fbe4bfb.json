{"ast": null, "code": "'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function (length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function (length) {\n    var bytes = new Array(length);\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n    return bytes;\n  };\n}", "map": {"version": 3, "names": ["global", "crypto", "getRandomValues", "module", "exports", "randomBytes", "length", "bytes", "Uint8Array", "Array", "i", "Math", "floor", "random"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/utils/browser-crypto.js"], "sourcesContent": ["'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Array(length);\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n    return bytes;\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,eAAe,EAAE;EAClDC,MAAM,CAACC,OAAO,CAACC,WAAW,GAAG,UAASC,MAAM,EAAE;IAC5C,IAAIC,KAAK,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;IAClCN,MAAM,CAACC,MAAM,CAACC,eAAe,CAACK,KAAK,CAAC;IACpC,OAAOA,KAAK;EACd,CAAC;AACH,CAAC,MAAM;EACLJ,MAAM,CAACC,OAAO,CAACC,WAAW,GAAG,UAASC,MAAM,EAAE;IAC5C,IAAIC,KAAK,GAAG,IAAIE,KAAK,CAACH,MAAM,CAAC;IAC7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC/BH,KAAK,CAACG,CAAC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;IAC5C;IACA,OAAON,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}