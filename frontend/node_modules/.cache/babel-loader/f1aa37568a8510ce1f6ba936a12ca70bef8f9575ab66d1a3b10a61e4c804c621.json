{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventTarget = require('./eventtarget');\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\ninherits(EventEmitter, EventTarget);\nEventEmitter.prototype.removeAllListeners = function (type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\nEventEmitter.prototype.once = function (type, listener) {\n  var self = this,\n    fired = false;\n  function g() {\n    self.removeListener(type, g);\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n  this.on(type, g);\n};\nEventEmitter.prototype.emit = function () {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\nmodule.exports.EventEmitter = EventEmitter;", "map": {"version": 3, "names": ["inherits", "require", "EventTarget", "EventEmitter", "call", "prototype", "removeAllListeners", "type", "_listeners", "once", "listener", "self", "fired", "g", "removeListener", "apply", "arguments", "on", "emit", "listeners", "l", "length", "args", "Array", "ai", "i", "addListener", "addEventListener", "removeEventListener", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/sockjs-client/lib/event/emitter.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventTarget = require('./eventtarget')\n  ;\n\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\n\ninherits(EventEmitter, EventTarget);\n\nEventEmitter.prototype.removeAllListeners = function(type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\n\nEventEmitter.prototype.once = function(type, listener) {\n  var self = this\n    , fired = false;\n\n  function g() {\n    self.removeListener(type, g);\n\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n\n  this.on(type, g);\n};\n\nEventEmitter.prototype.emit = function() {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\n\nmodule.exports.EventEmitter = EventEmitter;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;AAG1C,SAASE,YAAYA,CAAA,EAAG;EACtBD,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC;AACxB;AAEAJ,QAAQ,CAACG,YAAY,EAAED,WAAW,CAAC;AAEnCC,YAAY,CAACE,SAAS,CAACC,kBAAkB,GAAG,UAASC,IAAI,EAAE;EACzD,IAAIA,IAAI,EAAE;IACR,OAAO,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC;EAC9B,CAAC,MAAM;IACL,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;EACtB;AACF,CAAC;AAEDL,YAAY,CAACE,SAAS,CAACI,IAAI,GAAG,UAASF,IAAI,EAAEG,QAAQ,EAAE;EACrD,IAAIC,IAAI,GAAG,IAAI;IACXC,KAAK,GAAG,KAAK;EAEjB,SAASC,CAACA,CAAA,EAAG;IACXF,IAAI,CAACG,cAAc,CAACP,IAAI,EAAEM,CAAC,CAAC;IAE5B,IAAI,CAACD,KAAK,EAAE;MACVA,KAAK,GAAG,IAAI;MACZF,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACjC;EACF;EAEA,IAAI,CAACC,EAAE,CAACV,IAAI,EAAEM,CAAC,CAAC;AAClB,CAAC;AAEDV,YAAY,CAACE,SAAS,CAACa,IAAI,GAAG,YAAW;EACvC,IAAIX,IAAI,GAAGS,SAAS,CAAC,CAAC,CAAC;EACvB,IAAIG,SAAS,GAAG,IAAI,CAACX,UAAU,CAACD,IAAI,CAAC;EACrC,IAAI,CAACY,SAAS,EAAE;IACd;EACF;EACA;EACA,IAAIC,CAAC,GAAGJ,SAAS,CAACK,MAAM;EACxB,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAACH,CAAC,GAAG,CAAC,CAAC;EAC3B,KAAK,IAAII,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGJ,CAAC,EAAEI,EAAE,EAAE,EAAE;IAC7BF,IAAI,CAACE,EAAE,GAAG,CAAC,CAAC,GAAGR,SAAS,CAACQ,EAAE,CAAC;EAC9B;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,SAAS,CAACE,MAAM,EAAEI,CAAC,EAAE,EAAE;IACzCN,SAAS,CAACM,CAAC,CAAC,CAACV,KAAK,CAAC,IAAI,EAAEO,IAAI,CAAC;EAChC;AACF,CAAC;AAEDnB,YAAY,CAACE,SAAS,CAACY,EAAE,GAAGd,YAAY,CAACE,SAAS,CAACqB,WAAW,GAAGxB,WAAW,CAACG,SAAS,CAACsB,gBAAgB;AACvGxB,YAAY,CAACE,SAAS,CAACS,cAAc,GAAGZ,WAAW,CAACG,SAAS,CAACuB,mBAAmB;AAEjFC,MAAM,CAACC,OAAO,CAAC3B,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}