{"ast": null, "code": "import React from'react';import{AppBar,Toolbar,Typo<PERSON>,Box,Button}from'@mui/material';import WaterDropIcon from'@mui/icons-material/WaterDrop';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=()=>{return/*#__PURE__*/_jsx(AppBar,{position:\"static\",elevation:0,sx:{mb:4},children:/*#__PURE__*/_jsxs(Toolbar,{children:[/*#__PURE__*/_jsx(WaterDropIcon,{sx:{mr:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"div\",sx:{flexGrow:1},children:\"Flood Risk Prediction\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(<PERSON><PERSON>,{color:\"inherit\",children:\"About\"}),/*#__PURE__*/_jsx(<PERSON><PERSON>,{color:\"inherit\",children:\"Documentation\"})]})]})});};export default Header;", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "WaterDropIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "position", "elevation", "sx", "mb", "children", "mr", "variant", "component", "flexGrow", "color"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { AppBar, Toolbar, Typography, Box, Button } from '@mui/material';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\n\nconst Header = () => {\n  return (\n    <AppBar position=\"static\" elevation={0} sx={{ mb: 4 }}>\n      <Toolbar>\n        <WaterDropIcon sx={{ mr: 2 }} />\n        <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n          Flood Risk Prediction\n        </Typography>\n        <Box>\n          <Button color=\"inherit\">About</Button>\n          <Button color=\"inherit\">Documentation</Button>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,OAAO,CAAEC,UAAU,CAAEC,GAAG,CAAEC,MAAM,KAAQ,eAAe,CACxE,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1D,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACEH,IAAA,CAACP,MAAM,EAACW,QAAQ,CAAC,QAAQ,CAACC,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACpDN,KAAA,CAACR,OAAO,EAAAc,QAAA,eACNR,IAAA,CAACF,aAAa,EAACQ,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAChCT,IAAA,CAACL,UAAU,EAACe,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAACL,EAAE,CAAE,CAAEM,QAAQ,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,uBAE9D,CAAY,CAAC,cACbN,KAAA,CAACN,GAAG,EAAAY,QAAA,eACFR,IAAA,CAACH,MAAM,EAACgB,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,OAAK,CAAQ,CAAC,cACtCR,IAAA,CAACH,MAAM,EAACgB,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,eAAa,CAAQ,CAAC,EAC3C,CAAC,EACC,CAAC,CACJ,CAAC,CAEb,CAAC,CAED,cAAe,CAAAL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}