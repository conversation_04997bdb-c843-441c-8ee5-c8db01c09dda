{"ast": null, "code": "'use strict';\n\nvar required = require('requires-port'),\n  qs = require('querystringify'),\n  controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/,\n  CRHTLF = /[\\n\\r\\t]/g,\n  slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//,\n  port = /:\\d+$/,\n  protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i,\n  windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [['#', 'hash'],\n// Extract from the back.\n['?', 'query'],\n// Extract from the back.\nfunction sanitize(address, url) {\n  // Sanitize what is left of the address\n  return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n}, ['/', 'pathname'],\n// Extract from the back.\n['@', 'auth', 1],\n// Extract from the front.\n[NaN, 'host', undefined, 1, 1],\n// Set left over value.\n[/:(\\d*)$/, 'port', undefined, 1],\n// RegExp the back.\n[NaN, 'hostname', undefined, 1, 1] // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = {\n  hash: 1,\n  query: 1\n};\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n  if (typeof window !== 'undefined') globalVar = window;else if (typeof global !== 'undefined') globalVar = global;else if (typeof self !== 'undefined') globalVar = self;else globalVar = {};\n  var location = globalVar.location || {};\n  loc = loc || location;\n  var finaldestination = {},\n    type = typeof loc,\n    key;\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return scheme === 'file:' || scheme === 'ftp:' || scheme === 'http:' || scheme === 'https:' || scheme === 'ws:' || scheme === 'wss:';\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4];\n    }\n  }\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/')),\n    i = path.length,\n    last = path[i - 1],\n    unshift = false,\n    up = 0;\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n  var relative,\n    extracted,\n    parse,\n    instruction,\n    index,\n    key,\n    instructions = rules.slice(),\n    type = typeof location,\n    url = this,\n    i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (extracted.protocol === 'file:' && (extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) || !extracted.slashes && (extracted.protocol || extracted.slashesCount < 2 || !isSpecial(url.protocol))) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n    parse = instruction[0];\n    key = instruction[1];\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@' ? address.lastIndexOf(parse) : address.indexOf(parse);\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if (index = parse.exec(address)) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n    url[key] = url[key] || (relative && instruction[3] ? location[key] || '' : '');\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (relative && location.slashes && url.pathname.charAt(0) !== '/' && (url.pathname !== '' || location.pathname !== '')) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password));\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n    url.auth = url.password ? url.username + ':' + url.password : url.username;\n  }\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n      url[part] = value;\n      break;\n    case 'port':\n      url[part] = value;\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname + ':' + value;\n      }\n      break;\n    case 'hostname':\n      url[part] = value;\n      if (url.port) value += ':' + url.port;\n      url.host = value;\n      break;\n    case 'host':\n      url[part] = value;\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n      break;\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n    case 'auth':\n      var index = value.indexOf(':');\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n  url.auth = url.password ? url.username + ':' + url.password : url.username;\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null';\n  url.href = url.toString();\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n  var query,\n    url = this,\n    host = url.host,\n    protocol = url.protocol;\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n  var result = protocol + (url.protocol && url.slashes || isSpecial(url.protocol) ? '//' : '');\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':' + url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':' + url.password;\n    result += '@';\n  } else if (url.protocol !== 'file:' && isSpecial(url.protocol) && !host && url.pathname !== '/') {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || port.test(url.hostname) && !url.port) {\n    host += ':';\n  }\n  result += host + url.pathname;\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?' + query : query;\n  if (url.hash) result += url.hash;\n  return result;\n}\nUrl.prototype = {\n  set: set,\n  toString: toString\n};\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\nmodule.exports = Url;", "map": {"version": 3, "names": ["required", "require", "qs", "controlOrWhitespace", "CRHTLF", "slashes", "port", "protocolre", "windowsDriveLetter", "trimLeft", "str", "toString", "replace", "rules", "sanitize", "address", "url", "isSpecial", "protocol", "NaN", "undefined", "ignore", "hash", "query", "lolcation", "loc", "globalVar", "window", "global", "self", "location", "finaldestination", "type", "key", "Url", "unescape", "pathname", "test", "href", "scheme", "extractProtocol", "match", "exec", "toLowerCase", "forwardSlashes", "otherSlashes", "slashesCount", "rest", "length", "slice", "resolve", "relative", "base", "path", "split", "concat", "i", "last", "unshift", "up", "splice", "push", "join", "parser", "extracted", "parse", "instruction", "index", "instructions", "lastIndexOf", "indexOf", "char<PERSON>t", "host", "hostname", "username", "password", "auth", "encodeURIComponent", "decodeURIComponent", "origin", "set", "part", "value", "fn", "pop", "char", "ins", "stringify", "result", "prototype", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/url-parse/index.js"], "sourcesContent": ["'use strict';\n\nvar required = require('requires-port')\n  , qs = require('querystringify')\n  , controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/\n  , CRHTLF = /[\\n\\r\\t]/g\n  , slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//\n  , port = /:\\d+$/\n  , protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i\n  , windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [\n  ['#', 'hash'],                        // Extract from the back.\n  ['?', 'query'],                       // Extract from the back.\n  function sanitize(address, url) {     // Sanitize what is left of the address\n    return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n  },\n  ['/', 'pathname'],                    // Extract from the back.\n  ['@', 'auth', 1],                     // Extract from the front.\n  [NaN, 'host', undefined, 1, 1],       // Set left over value.\n  [/:(\\d*)$/, 'port', undefined, 1],    // RegExp the back.\n  [NaN, 'hostname', undefined, 1, 1]    // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = { hash: 1, query: 1 };\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n\n  if (typeof window !== 'undefined') globalVar = window;\n  else if (typeof global !== 'undefined') globalVar = global;\n  else if (typeof self !== 'undefined') globalVar = self;\n  else globalVar = {};\n\n  var location = globalVar.location || {};\n  loc = loc || location;\n\n  var finaldestination = {}\n    , type = typeof loc\n    , key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return (\n    scheme === 'file:' ||\n    scheme === 'ftp:' ||\n    scheme === 'http:' ||\n    scheme === 'https:' ||\n    scheme === 'ws:' ||\n    scheme === 'wss:'\n  );\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4]\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/'))\n    , i = path.length\n    , last = path[i - 1]\n    , unshift = false\n    , up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative, extracted, parse, instruction, index, key\n    , instructions = rules.slice()\n    , type = typeof location\n    , url = this\n    , i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (\n    extracted.protocol === 'file:' && (\n      extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) ||\n    (!extracted.slashes &&\n      (extracted.protocol ||\n        extracted.slashesCount < 2 ||\n        !isSpecial(url.protocol)))\n  ) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@'\n        ? address.lastIndexOf(parse)\n        : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if ((index = parse.exec(address))) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (\n      relative && instruction[3] ? location[key] || '' : ''\n    );\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (\n      relative\n    && location.slashes\n    && url.pathname.charAt(0) !== '/'\n    && (url.pathname !== '' || location.pathname !== '')\n  ) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password))\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username +':'+ url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname +':'+ value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n\n      if (url.port) value += ':'+ url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username +':'+ url.password : url.username;\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  url.href = url.toString();\n\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n\n  var query\n    , url = this\n    , host = url.host\n    , protocol = url.protocol;\n\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n\n  var result =\n    protocol +\n    ((url.protocol && url.slashes) || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':'+ url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':'+ url.password;\n    result += '@';\n  } else if (\n    url.protocol !== 'file:' &&\n    isSpecial(url.protocol) &&\n    !host &&\n    url.pathname !== '/'\n  ) {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || (port.test(url.hostname) && !url.port)) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?'+ query : query;\n\n  if (url.hash) result += url.hash;\n\n  return result;\n}\n\nUrl.prototype = { set: set, toString: toString };\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\n\nmodule.exports = Url;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;EACnCC,EAAE,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EAC9BE,mBAAmB,GAAG,4EAA4E;EAClGC,MAAM,GAAG,WAAW;EACpBC,OAAO,GAAG,+BAA+B;EACzCC,IAAI,GAAG,OAAO;EACdC,UAAU,GAAG,kDAAkD;EAC/DC,kBAAkB,GAAG,YAAY;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAO,CAACA,GAAG,GAAGA,GAAG,GAAG,EAAE,EAAEC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACT,mBAAmB,EAAE,EAAE,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIU,KAAK,GAAG,CACV,CAAC,GAAG,EAAE,MAAM,CAAC;AAAyB;AACtC,CAAC,GAAG,EAAE,OAAO,CAAC;AAAwB;AACtC,SAASC,QAAQA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAAM;EACpC,OAAOC,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,GAAGH,OAAO,CAACH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAGG,OAAO;AACxE,CAAC,EACD,CAAC,GAAG,EAAE,UAAU,CAAC;AAAqB;AACtC,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;AAAsB;AACtC,CAACI,GAAG,EAAE,MAAM,EAAEC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAAQ;AACtC,CAAC,SAAS,EAAE,MAAM,EAAEA,SAAS,EAAE,CAAC,CAAC;AAAK;AACtC,CAACD,GAAG,EAAE,UAAU,EAAEC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAI;AAAA,CACvC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EAAEC,IAAI,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAE,CAAC;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,SAAS;EAEb,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAED,SAAS,GAAGC,MAAM,CAAC,KACjD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAEF,SAAS,GAAGE,MAAM,CAAC,KACtD,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAEH,SAAS,GAAGG,IAAI,CAAC,KAClDH,SAAS,GAAG,CAAC,CAAC;EAEnB,IAAII,QAAQ,GAAGJ,SAAS,CAACI,QAAQ,IAAI,CAAC,CAAC;EACvCL,GAAG,GAAGA,GAAG,IAAIK,QAAQ;EAErB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,IAAI,GAAG,OAAOP,GAAG;IACjBQ,GAAG;EAEP,IAAI,OAAO,KAAKR,GAAG,CAACP,QAAQ,EAAE;IAC5Ba,gBAAgB,GAAG,IAAIG,GAAG,CAACC,QAAQ,CAACV,GAAG,CAACW,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,CAAC,MAAM,IAAI,QAAQ,KAAKJ,IAAI,EAAE;IAC5BD,gBAAgB,GAAG,IAAIG,GAAG,CAACT,GAAG,EAAE,CAAC,CAAC,CAAC;IACnC,KAAKQ,GAAG,IAAIZ,MAAM,EAAE,OAAOU,gBAAgB,CAACE,GAAG,CAAC;EAClD,CAAC,MAAM,IAAI,QAAQ,KAAKD,IAAI,EAAE;IAC5B,KAAKC,GAAG,IAAIR,GAAG,EAAE;MACf,IAAIQ,GAAG,IAAIZ,MAAM,EAAE;MACnBU,gBAAgB,CAACE,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;IAClC;IAEA,IAAIF,gBAAgB,CAAC1B,OAAO,KAAKe,SAAS,EAAE;MAC1CW,gBAAgB,CAAC1B,OAAO,GAAGA,OAAO,CAACgC,IAAI,CAACZ,GAAG,CAACa,IAAI,CAAC;IACnD;EACF;EAEA,OAAOP,gBAAgB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASd,SAASA,CAACsB,MAAM,EAAE;EACzB,OACEA,MAAM,KAAK,OAAO,IAClBA,MAAM,KAAK,MAAM,IACjBA,MAAM,KAAK,OAAO,IAClBA,MAAM,KAAK,QAAQ,IACnBA,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,MAAM;AAErB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACzB,OAAO,EAAEe,QAAQ,EAAE;EAC1Cf,OAAO,GAAGN,QAAQ,CAACM,OAAO,CAAC;EAC3BA,OAAO,GAAGA,OAAO,CAACH,OAAO,CAACR,MAAM,EAAE,EAAE,CAAC;EACrC0B,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;EAEzB,IAAIW,KAAK,GAAGlC,UAAU,CAACmC,IAAI,CAAC3B,OAAO,CAAC;EACpC,IAAIG,QAAQ,GAAGuB,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,GAAG,EAAE;EACrD,IAAIC,cAAc,GAAG,CAAC,CAACH,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAII,YAAY,GAAG,CAAC,CAACJ,KAAK,CAAC,CAAC,CAAC;EAC7B,IAAIK,YAAY,GAAG,CAAC;EACpB,IAAIC,IAAI;EAER,IAAIH,cAAc,EAAE;IAClB,IAAIC,YAAY,EAAE;MAChBE,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrCK,YAAY,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,GAAGP,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM;IAClD,CAAC,MAAM;MACLD,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;MAC1BK,YAAY,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM;IAChC;EACF,CAAC,MAAM;IACL,IAAIH,YAAY,EAAE;MAChBE,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;MAC1BK,YAAY,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM;IAChC,CAAC,MAAM;MACLD,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC;IACjB;EACF;EAEA,IAAIvB,QAAQ,KAAK,OAAO,EAAE;IACxB,IAAI4B,YAAY,IAAI,CAAC,EAAE;MACrBC,IAAI,GAAGA,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,MAAM,IAAIhC,SAAS,CAACC,QAAQ,CAAC,EAAE;IAC9B6B,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM,IAAIvB,QAAQ,EAAE;IACnB,IAAI0B,cAAc,EAAE;MAClBG,IAAI,GAAGA,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,MAAM,IAAIH,YAAY,IAAI,CAAC,IAAI7B,SAAS,CAACa,QAAQ,CAACZ,QAAQ,CAAC,EAAE;IAC5D6B,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC;EACjB;EAEA,OAAO;IACLvB,QAAQ,EAAEA,QAAQ;IAClBb,OAAO,EAAEuC,cAAc,IAAI3B,SAAS,CAACC,QAAQ,CAAC;IAC9C4B,YAAY,EAAEA,YAAY;IAC1BC,IAAI,EAAEA;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC/B,IAAID,QAAQ,KAAK,EAAE,EAAE,OAAOC,IAAI;EAEhC,IAAIC,IAAI,GAAG,CAACD,IAAI,IAAI,GAAG,EAAEE,KAAK,CAAC,GAAG,CAAC,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACM,MAAM,CAACJ,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;IACxEE,CAAC,GAAGH,IAAI,CAACL,MAAM;IACfS,IAAI,GAAGJ,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC;IAClBE,OAAO,GAAG,KAAK;IACfC,EAAE,GAAG,CAAC;EAEV,OAAOH,CAAC,EAAE,EAAE;IACV,IAAIH,IAAI,CAACG,CAAC,CAAC,KAAK,GAAG,EAAE;MACnBH,IAAI,CAACO,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIH,IAAI,CAACG,CAAC,CAAC,KAAK,IAAI,EAAE;MAC3BH,IAAI,CAACO,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;MACjBG,EAAE,EAAE;IACN,CAAC,MAAM,IAAIA,EAAE,EAAE;MACb,IAAIH,CAAC,KAAK,CAAC,EAAEE,OAAO,GAAG,IAAI;MAC3BL,IAAI,CAACO,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;MACjBG,EAAE,EAAE;IACN;EACF;EAEA,IAAID,OAAO,EAAEL,IAAI,CAACK,OAAO,CAAC,EAAE,CAAC;EAC7B,IAAID,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,EAAEJ,IAAI,CAACQ,IAAI,CAAC,EAAE,CAAC;EAEhD,OAAOR,IAAI,CAACS,IAAI,CAAC,GAAG,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5B,GAAGA,CAACnB,OAAO,EAAEe,QAAQ,EAAEiC,MAAM,EAAE;EACtChD,OAAO,GAAGN,QAAQ,CAACM,OAAO,CAAC;EAC3BA,OAAO,GAAGA,OAAO,CAACH,OAAO,CAACR,MAAM,EAAE,EAAE,CAAC;EAErC,IAAI,EAAE,IAAI,YAAY8B,GAAG,CAAC,EAAE;IAC1B,OAAO,IAAIA,GAAG,CAACnB,OAAO,EAAEe,QAAQ,EAAEiC,MAAM,CAAC;EAC3C;EAEA,IAAIZ,QAAQ;IAAEa,SAAS;IAAEC,KAAK;IAAEC,WAAW;IAAEC,KAAK;IAAElC,GAAG;IACnDmC,YAAY,GAAGvD,KAAK,CAACoC,KAAK,CAAC,CAAC;IAC5BjB,IAAI,GAAG,OAAOF,QAAQ;IACtBd,GAAG,GAAG,IAAI;IACVwC,CAAC,GAAG,CAAC;;EAET;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,QAAQ,KAAKxB,IAAI,IAAI,QAAQ,KAAKA,IAAI,EAAE;IAC1C+B,MAAM,GAAGjC,QAAQ;IACjBA,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIiC,MAAM,IAAI,UAAU,KAAK,OAAOA,MAAM,EAAEA,MAAM,GAAG7D,EAAE,CAAC+D,KAAK;EAE7DnC,QAAQ,GAAGN,SAAS,CAACM,QAAQ,CAAC;;EAE9B;EACA;EACA;EACAkC,SAAS,GAAGxB,eAAe,CAACzB,OAAO,IAAI,EAAE,EAAEe,QAAQ,CAAC;EACpDqB,QAAQ,GAAG,CAACa,SAAS,CAAC9C,QAAQ,IAAI,CAAC8C,SAAS,CAAC3D,OAAO;EACpDW,GAAG,CAACX,OAAO,GAAG2D,SAAS,CAAC3D,OAAO,IAAI8C,QAAQ,IAAIrB,QAAQ,CAACzB,OAAO;EAC/DW,GAAG,CAACE,QAAQ,GAAG8C,SAAS,CAAC9C,QAAQ,IAAIY,QAAQ,CAACZ,QAAQ,IAAI,EAAE;EAC5DH,OAAO,GAAGiD,SAAS,CAACjB,IAAI;;EAExB;EACA;EACA;EACA;EACA,IACEiB,SAAS,CAAC9C,QAAQ,KAAK,OAAO,KAC5B8C,SAAS,CAAClB,YAAY,KAAK,CAAC,IAAItC,kBAAkB,CAAC6B,IAAI,CAACtB,OAAO,CAAC,CAAC,IAClE,CAACiD,SAAS,CAAC3D,OAAO,KAChB2D,SAAS,CAAC9C,QAAQ,IACjB8C,SAAS,CAAClB,YAAY,GAAG,CAAC,IAC1B,CAAC7B,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAE,EAC9B;IACAkD,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;EACxC;EAEA,OAAOZ,CAAC,GAAGY,YAAY,CAACpB,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACnCU,WAAW,GAAGE,YAAY,CAACZ,CAAC,CAAC;IAE7B,IAAI,OAAOU,WAAW,KAAK,UAAU,EAAE;MACrCnD,OAAO,GAAGmD,WAAW,CAACnD,OAAO,EAAEC,GAAG,CAAC;MACnC;IACF;IAEAiD,KAAK,GAAGC,WAAW,CAAC,CAAC,CAAC;IACtBjC,GAAG,GAAGiC,WAAW,CAAC,CAAC,CAAC;IAEpB,IAAID,KAAK,KAAKA,KAAK,EAAE;MACnBjD,GAAG,CAACiB,GAAG,CAAC,GAAGlB,OAAO;IACpB,CAAC,MAAM,IAAI,QAAQ,KAAK,OAAOkD,KAAK,EAAE;MACpCE,KAAK,GAAGF,KAAK,KAAK,GAAG,GACjBlD,OAAO,CAACsD,WAAW,CAACJ,KAAK,CAAC,GAC1BlD,OAAO,CAACuD,OAAO,CAACL,KAAK,CAAC;MAE1B,IAAI,CAACE,KAAK,EAAE;QACV,IAAI,QAAQ,KAAK,OAAOD,WAAW,CAAC,CAAC,CAAC,EAAE;UACtClD,GAAG,CAACiB,GAAG,CAAC,GAAGlB,OAAO,CAACkC,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAAC;UAClCpD,OAAO,GAAGA,OAAO,CAACkC,KAAK,CAACkB,KAAK,GAAGD,WAAW,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,MAAM;UACLlD,GAAG,CAACiB,GAAG,CAAC,GAAGlB,OAAO,CAACkC,KAAK,CAACkB,KAAK,CAAC;UAC/BpD,OAAO,GAAGA,OAAO,CAACkC,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAAC;QACnC;MACF;IACF,CAAC,MAAM,IAAKA,KAAK,GAAGF,KAAK,CAACvB,IAAI,CAAC3B,OAAO,CAAC,EAAG;MACxCC,GAAG,CAACiB,GAAG,CAAC,GAAGkC,KAAK,CAAC,CAAC,CAAC;MACnBpD,OAAO,GAAGA,OAAO,CAACkC,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAACA,KAAK,CAAC;IACzC;IAEAnD,GAAG,CAACiB,GAAG,CAAC,GAAGjB,GAAG,CAACiB,GAAG,CAAC,KACjBkB,QAAQ,IAAIe,WAAW,CAAC,CAAC,CAAC,GAAGpC,QAAQ,CAACG,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CACtD;;IAED;IACA;IACA;IACA;IACA,IAAIiC,WAAW,CAAC,CAAC,CAAC,EAAElD,GAAG,CAACiB,GAAG,CAAC,GAAGjB,GAAG,CAACiB,GAAG,CAAC,CAACU,WAAW,CAAC,CAAC;EACvD;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAIoB,MAAM,EAAE/C,GAAG,CAACO,KAAK,GAAGwC,MAAM,CAAC/C,GAAG,CAACO,KAAK,CAAC;;EAEzC;EACA;EACA;EACA,IACI4B,QAAQ,IACPrB,QAAQ,CAACzB,OAAO,IAChBW,GAAG,CAACoB,QAAQ,CAACmC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAC7BvD,GAAG,CAACoB,QAAQ,KAAK,EAAE,IAAIN,QAAQ,CAACM,QAAQ,KAAK,EAAE,CAAC,EACpD;IACApB,GAAG,CAACoB,QAAQ,GAAGc,OAAO,CAAClC,GAAG,CAACoB,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,CAAC;EACzD;;EAEA;EACA;EACA;EACA;EACA,IAAIpB,GAAG,CAACoB,QAAQ,CAACmC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAItD,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,EAAE;IAC7DF,GAAG,CAACoB,QAAQ,GAAG,GAAG,GAAGpB,GAAG,CAACoB,QAAQ;EACnC;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACpC,QAAQ,CAACgB,GAAG,CAACV,IAAI,EAAEU,GAAG,CAACE,QAAQ,CAAC,EAAE;IACrCF,GAAG,CAACwD,IAAI,GAAGxD,GAAG,CAACyD,QAAQ;IACvBzD,GAAG,CAACV,IAAI,GAAG,EAAE;EACf;;EAEA;EACA;EACA;EACAU,GAAG,CAAC0D,QAAQ,GAAG1D,GAAG,CAAC2D,QAAQ,GAAG,EAAE;EAEhC,IAAI3D,GAAG,CAAC4D,IAAI,EAAE;IACZT,KAAK,GAAGnD,GAAG,CAAC4D,IAAI,CAACN,OAAO,CAAC,GAAG,CAAC;IAE7B,IAAI,CAACH,KAAK,EAAE;MACVnD,GAAG,CAAC0D,QAAQ,GAAG1D,GAAG,CAAC4D,IAAI,CAAC3B,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAAC;MACvCnD,GAAG,CAAC0D,QAAQ,GAAGG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC0D,QAAQ,CAAC,CAAC;MAEnE1D,GAAG,CAAC2D,QAAQ,GAAG3D,GAAG,CAAC4D,IAAI,CAAC3B,KAAK,CAACkB,KAAK,GAAG,CAAC,CAAC;MACxCnD,GAAG,CAAC2D,QAAQ,GAAGE,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC2D,QAAQ,CAAC,CAAC;IACrE,CAAC,MAAM;MACL3D,GAAG,CAAC0D,QAAQ,GAAGG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC4D,IAAI,CAAC,CAAC;IACjE;IAEA5D,GAAG,CAAC4D,IAAI,GAAG5D,GAAG,CAAC2D,QAAQ,GAAG3D,GAAG,CAAC0D,QAAQ,GAAE,GAAG,GAAE1D,GAAG,CAAC2D,QAAQ,GAAG3D,GAAG,CAAC0D,QAAQ;EAC1E;EAEA1D,GAAG,CAAC+D,MAAM,GAAG/D,GAAG,CAACE,QAAQ,KAAK,OAAO,IAAID,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAIF,GAAG,CAACwD,IAAI,GACxExD,GAAG,CAACE,QAAQ,GAAE,IAAI,GAAEF,GAAG,CAACwD,IAAI,GAC5B,MAAM;;EAEV;EACA;EACA;EACAxD,GAAG,CAACsB,IAAI,GAAGtB,GAAG,CAACL,QAAQ,CAAC,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqE,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,EAAE,EAAE;EAC5B,IAAInE,GAAG,GAAG,IAAI;EAEd,QAAQiE,IAAI;IACV,KAAK,OAAO;MACV,IAAI,QAAQ,KAAK,OAAOC,KAAK,IAAIA,KAAK,CAAClC,MAAM,EAAE;QAC7CkC,KAAK,GAAG,CAACC,EAAE,IAAIjF,EAAE,CAAC+D,KAAK,EAAEiB,KAAK,CAAC;MACjC;MAEAlE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK;MACjB;IAEF,KAAK,MAAM;MACTlE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK;MAEjB,IAAI,CAAClF,QAAQ,CAACkF,KAAK,EAAElE,GAAG,CAACE,QAAQ,CAAC,EAAE;QAClCF,GAAG,CAACwD,IAAI,GAAGxD,GAAG,CAACyD,QAAQ;QACvBzD,GAAG,CAACiE,IAAI,CAAC,GAAG,EAAE;MAChB,CAAC,MAAM,IAAIC,KAAK,EAAE;QAChBlE,GAAG,CAACwD,IAAI,GAAGxD,GAAG,CAACyD,QAAQ,GAAE,GAAG,GAAES,KAAK;MACrC;MAEA;IAEF,KAAK,UAAU;MACblE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK;MAEjB,IAAIlE,GAAG,CAACV,IAAI,EAAE4E,KAAK,IAAI,GAAG,GAAElE,GAAG,CAACV,IAAI;MACpCU,GAAG,CAACwD,IAAI,GAAGU,KAAK;MAChB;IAEF,KAAK,MAAM;MACTlE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK;MAEjB,IAAI5E,IAAI,CAAC+B,IAAI,CAAC6C,KAAK,CAAC,EAAE;QACpBA,KAAK,GAAGA,KAAK,CAAC5B,KAAK,CAAC,GAAG,CAAC;QACxBtC,GAAG,CAACV,IAAI,GAAG4E,KAAK,CAACE,GAAG,CAAC,CAAC;QACtBpE,GAAG,CAACyD,QAAQ,GAAGS,KAAK,CAACpB,IAAI,CAAC,GAAG,CAAC;MAChC,CAAC,MAAM;QACL9C,GAAG,CAACyD,QAAQ,GAAGS,KAAK;QACpBlE,GAAG,CAACV,IAAI,GAAG,EAAE;MACf;MAEA;IAEF,KAAK,UAAU;MACbU,GAAG,CAACE,QAAQ,GAAGgE,KAAK,CAACvC,WAAW,CAAC,CAAC;MAClC3B,GAAG,CAACX,OAAO,GAAG,CAAC8E,EAAE;MACjB;IAEF,KAAK,UAAU;IACf,KAAK,MAAM;MACT,IAAID,KAAK,EAAE;QACT,IAAIG,IAAI,GAAGJ,IAAI,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG;QAC1CjE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK,CAACX,MAAM,CAAC,CAAC,CAAC,KAAKc,IAAI,GAAGA,IAAI,GAAGH,KAAK,GAAGA,KAAK;MAC7D,CAAC,MAAM;QACLlE,GAAG,CAACiE,IAAI,CAAC,GAAGC,KAAK;MACnB;MACA;IAEF,KAAK,UAAU;IACf,KAAK,UAAU;MACblE,GAAG,CAACiE,IAAI,CAAC,GAAGJ,kBAAkB,CAACK,KAAK,CAAC;MACrC;IAEF,KAAK,MAAM;MACT,IAAIf,KAAK,GAAGe,KAAK,CAACZ,OAAO,CAAC,GAAG,CAAC;MAE9B,IAAI,CAACH,KAAK,EAAE;QACVnD,GAAG,CAAC0D,QAAQ,GAAGQ,KAAK,CAACjC,KAAK,CAAC,CAAC,EAAEkB,KAAK,CAAC;QACpCnD,GAAG,CAAC0D,QAAQ,GAAGG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC0D,QAAQ,CAAC,CAAC;QAEnE1D,GAAG,CAAC2D,QAAQ,GAAGO,KAAK,CAACjC,KAAK,CAACkB,KAAK,GAAG,CAAC,CAAC;QACrCnD,GAAG,CAAC2D,QAAQ,GAAGE,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC2D,QAAQ,CAAC,CAAC;MACrE,CAAC,MAAM;QACL3D,GAAG,CAAC0D,QAAQ,GAAGG,kBAAkB,CAACC,kBAAkB,CAACI,KAAK,CAAC,CAAC;MAC9D;EACJ;EAEA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,CAACmC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACrC,IAAI8B,GAAG,GAAGzE,KAAK,CAAC2C,CAAC,CAAC;IAElB,IAAI8B,GAAG,CAAC,CAAC,CAAC,EAAEtE,GAAG,CAACsE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGtE,GAAG,CAACsE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC,CAAC;EACrD;EAEA3B,GAAG,CAAC4D,IAAI,GAAG5D,GAAG,CAAC2D,QAAQ,GAAG3D,GAAG,CAAC0D,QAAQ,GAAE,GAAG,GAAE1D,GAAG,CAAC2D,QAAQ,GAAG3D,GAAG,CAAC0D,QAAQ;EAExE1D,GAAG,CAAC+D,MAAM,GAAG/D,GAAG,CAACE,QAAQ,KAAK,OAAO,IAAID,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,IAAIF,GAAG,CAACwD,IAAI,GACxExD,GAAG,CAACE,QAAQ,GAAE,IAAI,GAAEF,GAAG,CAACwD,IAAI,GAC5B,MAAM;EAEVxD,GAAG,CAACsB,IAAI,GAAGtB,GAAG,CAACL,QAAQ,CAAC,CAAC;EAEzB,OAAOK,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,QAAQA,CAAC4E,SAAS,EAAE;EAC3B,IAAI,CAACA,SAAS,IAAI,UAAU,KAAK,OAAOA,SAAS,EAAEA,SAAS,GAAGrF,EAAE,CAACqF,SAAS;EAE3E,IAAIhE,KAAK;IACLP,GAAG,GAAG,IAAI;IACVwD,IAAI,GAAGxD,GAAG,CAACwD,IAAI;IACftD,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAE3B,IAAIA,QAAQ,IAAIA,QAAQ,CAACqD,MAAM,CAACrD,QAAQ,CAAC8B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE9B,QAAQ,IAAI,GAAG;EAE7E,IAAIsE,MAAM,GACRtE,QAAQ,IACNF,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACX,OAAO,IAAKY,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;EAExE,IAAIF,GAAG,CAAC0D,QAAQ,EAAE;IAChBc,MAAM,IAAIxE,GAAG,CAAC0D,QAAQ;IACtB,IAAI1D,GAAG,CAAC2D,QAAQ,EAAEa,MAAM,IAAI,GAAG,GAAExE,GAAG,CAAC2D,QAAQ;IAC7Ca,MAAM,IAAI,GAAG;EACf,CAAC,MAAM,IAAIxE,GAAG,CAAC2D,QAAQ,EAAE;IACvBa,MAAM,IAAI,GAAG,GAAExE,GAAG,CAAC2D,QAAQ;IAC3Ba,MAAM,IAAI,GAAG;EACf,CAAC,MAAM,IACLxE,GAAG,CAACE,QAAQ,KAAK,OAAO,IACxBD,SAAS,CAACD,GAAG,CAACE,QAAQ,CAAC,IACvB,CAACsD,IAAI,IACLxD,GAAG,CAACoB,QAAQ,KAAK,GAAG,EACpB;IACA;IACA;IACA;IACA;IACAoD,MAAM,IAAI,GAAG;EACf;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAIhB,IAAI,CAACA,IAAI,CAACxB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAK1C,IAAI,CAAC+B,IAAI,CAACrB,GAAG,CAACyD,QAAQ,CAAC,IAAI,CAACzD,GAAG,CAACV,IAAK,EAAE;IAC3EkE,IAAI,IAAI,GAAG;EACb;EAEAgB,MAAM,IAAIhB,IAAI,GAAGxD,GAAG,CAACoB,QAAQ;EAE7Bb,KAAK,GAAG,QAAQ,KAAK,OAAOP,GAAG,CAACO,KAAK,GAAGgE,SAAS,CAACvE,GAAG,CAACO,KAAK,CAAC,GAAGP,GAAG,CAACO,KAAK;EACxE,IAAIA,KAAK,EAAEiE,MAAM,IAAI,GAAG,KAAKjE,KAAK,CAACgD,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAEhD,KAAK,GAAGA,KAAK;EAEjE,IAAIP,GAAG,CAACM,IAAI,EAAEkE,MAAM,IAAIxE,GAAG,CAACM,IAAI;EAEhC,OAAOkE,MAAM;AACf;AAEAtD,GAAG,CAACuD,SAAS,GAAG;EAAET,GAAG,EAAEA,GAAG;EAAErE,QAAQ,EAAEA;AAAS,CAAC;;AAEhD;AACA;AACA;AACA;AACAuB,GAAG,CAACM,eAAe,GAAGA,eAAe;AACrCN,GAAG,CAACJ,QAAQ,GAAGN,SAAS;AACxBU,GAAG,CAACzB,QAAQ,GAAGA,QAAQ;AACvByB,GAAG,CAAChC,EAAE,GAAGA,EAAE;AAEXwF,MAAM,CAACC,OAAO,GAAGzD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}