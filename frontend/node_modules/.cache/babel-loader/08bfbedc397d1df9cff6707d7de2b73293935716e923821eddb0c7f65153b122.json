{"ast": null, "code": "import * as React from 'react';\nimport { useContext, forwardRef } from 'react';\nimport createCache from '@emotion/cache';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport weakMemoize from '@emotion/weak-memoize';\nimport hoistNonReactStatics from '../_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nvar isDevelopment = false;\nvar EmotionCacheContext = /* #__PURE__ */React.createContext(\n// we're doing this to avoid preconstruct's dead code elimination in this one case\n// because this module is primarily intended for the browser and node\n// but it's also required in react native and similar environments sometimes\n// and we could have a special build just for that\n// but this is much easier and the native packages\n// might use a different theme context in the future anyway\ntypeof HTMLElement !== 'undefined' ? /* #__PURE__ */createCache({\n  key: 'css'\n}) : null);\nvar CacheProvider = EmotionCacheContext.Provider;\nvar __unsafe_useEmotionCache = function useEmotionCache() {\n  return useContext(EmotionCacheContext);\n};\nvar withEmotionCache = function withEmotionCache(func) {\n  return /*#__PURE__*/forwardRef(function (props, ref) {\n    // the cache will never be null in the browser\n    var cache = useContext(EmotionCacheContext);\n    return func(props, cache, ref);\n  });\n};\nvar ThemeContext = /* #__PURE__ */React.createContext({});\nvar useTheme = function useTheme() {\n  return React.useContext(ThemeContext);\n};\nvar getTheme = function getTheme(outerTheme, theme) {\n  if (typeof theme === 'function') {\n    var mergedTheme = theme(outerTheme);\n    return mergedTheme;\n  }\n  return _extends({}, outerTheme, theme);\n};\nvar createCacheWithTheme = /* #__PURE__ */weakMemoize(function (outerTheme) {\n  return weakMemoize(function (theme) {\n    return getTheme(outerTheme, theme);\n  });\n});\nvar ThemeProvider = function ThemeProvider(props) {\n  var theme = React.useContext(ThemeContext);\n  if (props.theme !== theme) {\n    theme = createCacheWithTheme(theme)(props.theme);\n  }\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, {\n    value: theme\n  }, props.children);\n};\nfunction withTheme(Component) {\n  var componentName = Component.displayName || Component.name || 'Component';\n  var WithTheme = /*#__PURE__*/React.forwardRef(function render(props, ref) {\n    var theme = React.useContext(ThemeContext);\n    return /*#__PURE__*/React.createElement(Component, _extends({\n      theme: theme,\n      ref: ref\n    }, props));\n  });\n  WithTheme.displayName = \"WithTheme(\" + componentName + \")\";\n  return hoistNonReactStatics(WithTheme, Component);\n}\nvar hasOwn = {}.hasOwnProperty;\nvar typePropName = '__EMOTION_TYPE_PLEASE_DO_NOT_USE__';\nvar createEmotionProps = function createEmotionProps(type, props) {\n  var newProps = {};\n  for (var _key in props) {\n    if (hasOwn.call(props, _key)) {\n      newProps[_key] = props[_key];\n    }\n  }\n  newProps[typePropName] = type; // Runtime labeling is an opt-in feature because:\n\n  return newProps;\n};\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n    serialized = _ref.serialized,\n    isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n  return null;\n};\nvar Emotion = /* #__PURE__ */withEmotionCache(function (props, cache, ref) {\n  var cssProp = props.css; // so that using `css` from `emotion` and passing the result to the css prop works\n  // not passing the registered cache to serializeStyles because it would\n  // make certain babel optimisations not possible\n\n  if (typeof cssProp === 'string' && cache.registered[cssProp] !== undefined) {\n    cssProp = cache.registered[cssProp];\n  }\n  var WrappedComponent = props[typePropName];\n  var registeredStyles = [cssProp];\n  var className = '';\n  if (typeof props.className === 'string') {\n    className = getRegisteredStyles(cache.registered, registeredStyles, props.className);\n  } else if (props.className != null) {\n    className = props.className + \" \";\n  }\n  var serialized = serializeStyles(registeredStyles, undefined, React.useContext(ThemeContext));\n  className += cache.key + \"-\" + serialized.name;\n  var newProps = {};\n  for (var _key2 in props) {\n    if (hasOwn.call(props, _key2) && _key2 !== 'css' && _key2 !== typePropName && !isDevelopment) {\n      newProps[_key2] = props[_key2];\n    }\n  }\n  newProps.className = className;\n  if (ref) {\n    newProps.ref = ref;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n    cache: cache,\n    serialized: serialized,\n    isStringTag: typeof WrappedComponent === 'string'\n  }), /*#__PURE__*/React.createElement(WrappedComponent, newProps));\n});\nvar Emotion$1 = Emotion;\nexport { CacheProvider as C, Emotion$1 as E, ThemeContext as T, __unsafe_useEmotionCache as _, ThemeProvider as a, withTheme as b, createEmotionProps as c, hasOwn as h, isDevelopment as i, useTheme as u, withEmotionCache as w };", "map": {"version": 3, "names": ["React", "useContext", "forwardRef", "createCache", "_extends", "weakMemoize", "hoistNonReactStatics", "getRegisteredStyles", "registerStyles", "insertStyles", "serializeStyles", "useInsertionEffectAlwaysWithSyncFallback", "isDevelopment", "EmotionCacheContext", "createContext", "HTMLElement", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Provider", "__unsafe_useEmotionCache", "useEmotionCache", "withEmotionCache", "func", "props", "ref", "cache", "ThemeContext", "useTheme", "getTheme", "outerTheme", "theme", "mergedTheme", "createCacheWithTheme", "ThemeProvider", "createElement", "value", "children", "withTheme", "Component", "componentName", "displayName", "name", "WithTheme", "render", "hasOwn", "hasOwnProperty", "typePropName", "createEmotionProps", "type", "newProps", "_key", "call", "Insertion", "_ref", "serialized", "isStringTag", "Emotion", "cssProp", "css", "registered", "undefined", "WrappedComponent", "registeredStyles", "className", "_key2", "Fragment", "Emotion$1", "C", "E", "T", "_", "a", "b", "c", "h", "i", "u", "w"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@emotion/react/dist/emotion-element-f0de968e.browser.esm.js"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, forwardRef } from 'react';\nimport createCache from '@emotion/cache';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport weakMemoize from '@emotion/weak-memoize';\nimport hoistNonReactStatics from '../_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\n\nvar isDevelopment = false;\n\nvar EmotionCacheContext = /* #__PURE__ */React.createContext( // we're doing this to avoid preconstruct's dead code elimination in this one case\n// because this module is primarily intended for the browser and node\n// but it's also required in react native and similar environments sometimes\n// and we could have a special build just for that\n// but this is much easier and the native packages\n// might use a different theme context in the future anyway\ntypeof HTMLElement !== 'undefined' ? /* #__PURE__ */createCache({\n  key: 'css'\n}) : null);\n\nvar CacheProvider = EmotionCacheContext.Provider;\nvar __unsafe_useEmotionCache = function useEmotionCache() {\n  return useContext(EmotionCacheContext);\n};\n\nvar withEmotionCache = function withEmotionCache(func) {\n  return /*#__PURE__*/forwardRef(function (props, ref) {\n    // the cache will never be null in the browser\n    var cache = useContext(EmotionCacheContext);\n    return func(props, cache, ref);\n  });\n};\n\nvar ThemeContext = /* #__PURE__ */React.createContext({});\n\nvar useTheme = function useTheme() {\n  return React.useContext(ThemeContext);\n};\n\nvar getTheme = function getTheme(outerTheme, theme) {\n  if (typeof theme === 'function') {\n    var mergedTheme = theme(outerTheme);\n\n    return mergedTheme;\n  }\n\n  return _extends({}, outerTheme, theme);\n};\n\nvar createCacheWithTheme = /* #__PURE__ */weakMemoize(function (outerTheme) {\n  return weakMemoize(function (theme) {\n    return getTheme(outerTheme, theme);\n  });\n});\nvar ThemeProvider = function ThemeProvider(props) {\n  var theme = React.useContext(ThemeContext);\n\n  if (props.theme !== theme) {\n    theme = createCacheWithTheme(theme)(props.theme);\n  }\n\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, {\n    value: theme\n  }, props.children);\n};\nfunction withTheme(Component) {\n  var componentName = Component.displayName || Component.name || 'Component';\n  var WithTheme = /*#__PURE__*/React.forwardRef(function render(props, ref) {\n    var theme = React.useContext(ThemeContext);\n    return /*#__PURE__*/React.createElement(Component, _extends({\n      theme: theme,\n      ref: ref\n    }, props));\n  });\n  WithTheme.displayName = \"WithTheme(\" + componentName + \")\";\n  return hoistNonReactStatics(WithTheme, Component);\n}\n\nvar hasOwn = {}.hasOwnProperty;\n\nvar typePropName = '__EMOTION_TYPE_PLEASE_DO_NOT_USE__';\nvar createEmotionProps = function createEmotionProps(type, props) {\n\n  var newProps = {};\n\n  for (var _key in props) {\n    if (hasOwn.call(props, _key)) {\n      newProps[_key] = props[_key];\n    }\n  }\n\n  newProps[typePropName] = type; // Runtime labeling is an opt-in feature because:\n\n  return newProps;\n};\n\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n      serialized = _ref.serialized,\n      isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n\n  return null;\n};\n\nvar Emotion = /* #__PURE__ */withEmotionCache(function (props, cache, ref) {\n  var cssProp = props.css; // so that using `css` from `emotion` and passing the result to the css prop works\n  // not passing the registered cache to serializeStyles because it would\n  // make certain babel optimisations not possible\n\n  if (typeof cssProp === 'string' && cache.registered[cssProp] !== undefined) {\n    cssProp = cache.registered[cssProp];\n  }\n\n  var WrappedComponent = props[typePropName];\n  var registeredStyles = [cssProp];\n  var className = '';\n\n  if (typeof props.className === 'string') {\n    className = getRegisteredStyles(cache.registered, registeredStyles, props.className);\n  } else if (props.className != null) {\n    className = props.className + \" \";\n  }\n\n  var serialized = serializeStyles(registeredStyles, undefined, React.useContext(ThemeContext));\n\n  className += cache.key + \"-\" + serialized.name;\n  var newProps = {};\n\n  for (var _key2 in props) {\n    if (hasOwn.call(props, _key2) && _key2 !== 'css' && _key2 !== typePropName && (!isDevelopment )) {\n      newProps[_key2] = props[_key2];\n    }\n  }\n\n  newProps.className = className;\n\n  if (ref) {\n    newProps.ref = ref;\n  }\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n    cache: cache,\n    serialized: serialized,\n    isStringTag: typeof WrappedComponent === 'string'\n  }), /*#__PURE__*/React.createElement(WrappedComponent, newProps));\n});\n\nvar Emotion$1 = Emotion;\n\nexport { CacheProvider as C, Emotion$1 as E, ThemeContext as T, __unsafe_useEmotionCache as _, ThemeProvider as a, withTheme as b, createEmotionProps as c, hasOwn as h, isDevelopment as i, useTheme as u, withEmotionCache as w };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,oBAAoB,MAAM,oEAAoE;AACrG,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAClF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,wCAAwC,QAAQ,8CAA8C;AAEvG,IAAIC,aAAa,GAAG,KAAK;AAEzB,IAAIC,mBAAmB,GAAG,eAAeb,KAAK,CAACc,aAAa;AAAE;AAC9D;AACA;AACA;AACA;AACA;AACA,OAAOC,WAAW,KAAK,WAAW,GAAG,eAAeZ,WAAW,CAAC;EAC9Da,GAAG,EAAE;AACP,CAAC,CAAC,GAAG,IAAI,CAAC;AAEV,IAAIC,aAAa,GAAGJ,mBAAmB,CAACK,QAAQ;AAChD,IAAIC,wBAAwB,GAAG,SAASC,eAAeA,CAAA,EAAG;EACxD,OAAOnB,UAAU,CAACY,mBAAmB,CAAC;AACxC,CAAC;AAED,IAAIQ,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EACrD,OAAO,aAAapB,UAAU,CAAC,UAAUqB,KAAK,EAAEC,GAAG,EAAE;IACnD;IACA,IAAIC,KAAK,GAAGxB,UAAU,CAACY,mBAAmB,CAAC;IAC3C,OAAOS,IAAI,CAACC,KAAK,EAAEE,KAAK,EAAED,GAAG,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AAED,IAAIE,YAAY,GAAG,eAAe1B,KAAK,CAACc,aAAa,CAAC,CAAC,CAAC,CAAC;AAEzD,IAAIa,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO3B,KAAK,CAACC,UAAU,CAACyB,YAAY,CAAC;AACvC,CAAC;AAED,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,UAAU,EAAEC,KAAK,EAAE;EAClD,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,IAAIC,WAAW,GAAGD,KAAK,CAACD,UAAU,CAAC;IAEnC,OAAOE,WAAW;EACpB;EAEA,OAAO3B,QAAQ,CAAC,CAAC,CAAC,EAAEyB,UAAU,EAAEC,KAAK,CAAC;AACxC,CAAC;AAED,IAAIE,oBAAoB,GAAG,eAAe3B,WAAW,CAAC,UAAUwB,UAAU,EAAE;EAC1E,OAAOxB,WAAW,CAAC,UAAUyB,KAAK,EAAE;IAClC,OAAOF,QAAQ,CAACC,UAAU,EAAEC,KAAK,CAAC;EACpC,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACV,KAAK,EAAE;EAChD,IAAIO,KAAK,GAAG9B,KAAK,CAACC,UAAU,CAACyB,YAAY,CAAC;EAE1C,IAAIH,KAAK,CAACO,KAAK,KAAKA,KAAK,EAAE;IACzBA,KAAK,GAAGE,oBAAoB,CAACF,KAAK,CAAC,CAACP,KAAK,CAACO,KAAK,CAAC;EAClD;EAEA,OAAO,aAAa9B,KAAK,CAACkC,aAAa,CAACR,YAAY,CAACR,QAAQ,EAAE;IAC7DiB,KAAK,EAAEL;EACT,CAAC,EAAEP,KAAK,CAACa,QAAQ,CAAC;AACpB,CAAC;AACD,SAASC,SAASA,CAACC,SAAS,EAAE;EAC5B,IAAIC,aAAa,GAAGD,SAAS,CAACE,WAAW,IAAIF,SAAS,CAACG,IAAI,IAAI,WAAW;EAC1E,IAAIC,SAAS,GAAG,aAAa1C,KAAK,CAACE,UAAU,CAAC,SAASyC,MAAMA,CAACpB,KAAK,EAAEC,GAAG,EAAE;IACxE,IAAIM,KAAK,GAAG9B,KAAK,CAACC,UAAU,CAACyB,YAAY,CAAC;IAC1C,OAAO,aAAa1B,KAAK,CAACkC,aAAa,CAACI,SAAS,EAAElC,QAAQ,CAAC;MAC1D0B,KAAK,EAAEA,KAAK;MACZN,GAAG,EAAEA;IACP,CAAC,EAAED,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;EACFmB,SAAS,CAACF,WAAW,GAAG,YAAY,GAAGD,aAAa,GAAG,GAAG;EAC1D,OAAOjC,oBAAoB,CAACoC,SAAS,EAAEJ,SAAS,CAAC;AACnD;AAEA,IAAIM,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc;AAE9B,IAAIC,YAAY,GAAG,oCAAoC;AACvD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,IAAI,EAAEzB,KAAK,EAAE;EAEhE,IAAI0B,QAAQ,GAAG,CAAC,CAAC;EAEjB,KAAK,IAAIC,IAAI,IAAI3B,KAAK,EAAE;IACtB,IAAIqB,MAAM,CAACO,IAAI,CAAC5B,KAAK,EAAE2B,IAAI,CAAC,EAAE;MAC5BD,QAAQ,CAACC,IAAI,CAAC,GAAG3B,KAAK,CAAC2B,IAAI,CAAC;IAC9B;EACF;EAEAD,QAAQ,CAACH,YAAY,CAAC,GAAGE,IAAI,CAAC,CAAC;;EAE/B,OAAOC,QAAQ;AACjB,CAAC;AAED,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EACvC,IAAI5B,KAAK,GAAG4B,IAAI,CAAC5B,KAAK;IAClB6B,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC5BC,WAAW,GAAGF,IAAI,CAACE,WAAW;EAClC/C,cAAc,CAACiB,KAAK,EAAE6B,UAAU,EAAEC,WAAW,CAAC;EAC9C5C,wCAAwC,CAAC,YAAY;IACnD,OAAOF,YAAY,CAACgB,KAAK,EAAE6B,UAAU,EAAEC,WAAW,CAAC;EACrD,CAAC,CAAC;EAEF,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,OAAO,GAAG,eAAenC,gBAAgB,CAAC,UAAUE,KAAK,EAAEE,KAAK,EAAED,GAAG,EAAE;EACzE,IAAIiC,OAAO,GAAGlC,KAAK,CAACmC,GAAG,CAAC,CAAC;EACzB;EACA;;EAEA,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAIhC,KAAK,CAACkC,UAAU,CAACF,OAAO,CAAC,KAAKG,SAAS,EAAE;IAC1EH,OAAO,GAAGhC,KAAK,CAACkC,UAAU,CAACF,OAAO,CAAC;EACrC;EAEA,IAAII,gBAAgB,GAAGtC,KAAK,CAACuB,YAAY,CAAC;EAC1C,IAAIgB,gBAAgB,GAAG,CAACL,OAAO,CAAC;EAChC,IAAIM,SAAS,GAAG,EAAE;EAElB,IAAI,OAAOxC,KAAK,CAACwC,SAAS,KAAK,QAAQ,EAAE;IACvCA,SAAS,GAAGxD,mBAAmB,CAACkB,KAAK,CAACkC,UAAU,EAAEG,gBAAgB,EAAEvC,KAAK,CAACwC,SAAS,CAAC;EACtF,CAAC,MAAM,IAAIxC,KAAK,CAACwC,SAAS,IAAI,IAAI,EAAE;IAClCA,SAAS,GAAGxC,KAAK,CAACwC,SAAS,GAAG,GAAG;EACnC;EAEA,IAAIT,UAAU,GAAG5C,eAAe,CAACoD,gBAAgB,EAAEF,SAAS,EAAE5D,KAAK,CAACC,UAAU,CAACyB,YAAY,CAAC,CAAC;EAE7FqC,SAAS,IAAItC,KAAK,CAACT,GAAG,GAAG,GAAG,GAAGsC,UAAU,CAACb,IAAI;EAC9C,IAAIQ,QAAQ,GAAG,CAAC,CAAC;EAEjB,KAAK,IAAIe,KAAK,IAAIzC,KAAK,EAAE;IACvB,IAAIqB,MAAM,CAACO,IAAI,CAAC5B,KAAK,EAAEyC,KAAK,CAAC,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAKlB,YAAY,IAAK,CAAClC,aAAe,EAAE;MAC/FqC,QAAQ,CAACe,KAAK,CAAC,GAAGzC,KAAK,CAACyC,KAAK,CAAC;IAChC;EACF;EAEAf,QAAQ,CAACc,SAAS,GAAGA,SAAS;EAE9B,IAAIvC,GAAG,EAAE;IACPyB,QAAQ,CAACzB,GAAG,GAAGA,GAAG;EACpB;EAEA,OAAO,aAAaxB,KAAK,CAACkC,aAAa,CAAClC,KAAK,CAACiE,QAAQ,EAAE,IAAI,EAAE,aAAajE,KAAK,CAACkC,aAAa,CAACkB,SAAS,EAAE;IACxG3B,KAAK,EAAEA,KAAK;IACZ6B,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAE,OAAOM,gBAAgB,KAAK;EAC3C,CAAC,CAAC,EAAE,aAAa7D,KAAK,CAACkC,aAAa,CAAC2B,gBAAgB,EAAEZ,QAAQ,CAAC,CAAC;AACnE,CAAC,CAAC;AAEF,IAAIiB,SAAS,GAAGV,OAAO;AAEvB,SAASvC,aAAa,IAAIkD,CAAC,EAAED,SAAS,IAAIE,CAAC,EAAE1C,YAAY,IAAI2C,CAAC,EAAElD,wBAAwB,IAAImD,CAAC,EAAErC,aAAa,IAAIsC,CAAC,EAAElC,SAAS,IAAImC,CAAC,EAAEzB,kBAAkB,IAAI0B,CAAC,EAAE7B,MAAM,IAAI8B,CAAC,EAAE9D,aAAa,IAAI+D,CAAC,EAAEhD,QAAQ,IAAIiD,CAAC,EAAEvD,gBAAgB,IAAIwD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}