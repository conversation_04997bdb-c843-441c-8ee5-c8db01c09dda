{"ast": null, "code": "'use client';\n\nimport useEventCallback from '@mui/utils/useEventCallback';\nexport default useEventCallback;", "map": {"version": 3, "names": ["useEventCallback"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@mui/material/utils/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport useEventCallback from '@mui/utils/useEventCallback';\nexport default useEventCallback;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,gBAAgB,MAAM,6BAA6B;AAC1D,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}