{"ast": null, "code": "import React from'react';import{Box,Typography,Grid,Paper}from'@mui/material';import{Bar}from'react-chartjs-2';import{Chart as ChartJS,CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend}from'chart.js';// Register ChartJS components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend);const RiskFactorsChart=_ref=>{let{riskAssessment}=_ref;const{factors}=riskAssessment;if(!factors||factors.length===0){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"No risk factors to display.\"})});}// Prepare data for the chart\nconst impactValues={'Very High':4,'High':3,'Medium':2,'Low':1};const chartData={labels:factors.map(f=>f.factor),datasets:[{label:'Risk Impact',data:factors.map(f=>impactValues[f.impact]||0),backgroundColor:factors.map(f=>{switch(f.impact){case'Very High':return'rgba(255, 99, 132, 0.8)';case'High':return'rgba(255, 159, 64, 0.8)';case'Medium':return'rgba(255, 205, 86, 0.8)';case'Low':return'rgba(75, 192, 192, 0.8)';default:return'rgba(201, 203, 207, 0.8)';}}),borderColor:factors.map(f=>{switch(f.impact){case'Very High':return'rgb(255, 99, 132)';case'High':return'rgb(255, 159, 64)';case'Medium':return'rgb(255, 205, 86)';case'Low':return'rgb(75, 192, 192)';default:return'rgb(201, 203, 207)';}}),borderWidth:1}]};const options={responsive:true,plugins:{legend:{position:'top'},title:{display:true,text:'Risk Factors Impact Analysis'},tooltip:{callbacks:{label:function(context){const impact=['','Low','Medium','High','Very High'][context.raw];return\"Impact: \".concat(impact);}}}},scales:{y:{beginAtZero:true,max:5,ticks:{callback:function(value){return['','Low','Medium','High','Very High'][value];}}}}};return/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Box,{sx:{height:300},children:/*#__PURE__*/_jsx(Bar,{data:chartData,options:options})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Detailed Risk Factors\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:factors.map((factor,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderLeft:'4px solid',borderColor:factor.impact==='Very High'?'error.main':factor.impact==='High'?'warning.main':factor.impact==='Medium'?'info.main':'success.main'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",children:factor.factor}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:[\"Impact: \",factor.impact]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Value: \",typeof factor.value==='number'?factor.value.toFixed(1):factor.value,factor.threshold&&\" (Threshold: \".concat(factor.threshold,\")\")]})]})},index))})]})]});};export default RiskFactorsChart;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Grid", "Paper", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "jsx", "_jsx", "jsxs", "_jsxs", "register", "RiskFactorsChart", "_ref", "riskAssessment", "factors", "length", "sx", "textAlign", "py", "children", "variant", "color", "impactValues", "chartData", "labels", "map", "f", "factor", "datasets", "label", "data", "impact", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "plugins", "legend", "position", "title", "display", "text", "tooltip", "callbacks", "context", "raw", "concat", "scales", "y", "beginAtZero", "max", "ticks", "callback", "value", "container", "spacing", "item", "xs", "height", "gutterBottom", "index", "sm", "md", "elevation", "p", "borderLeft", "fontWeight", "toFixed", "threshold"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\nimport { Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n} from 'chart.js';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst RiskFactorsChart = ({ riskAssessment }) => {\n  const { factors } = riskAssessment;\n  \n  if (!factors || factors.length === 0) {\n    return (\n      <Box sx={{ textAlign: 'center', py: 3 }}>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          No risk factors to display.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Prepare data for the chart\n  const impactValues = {\n    'Very High': 4,\n    'High': 3,\n    'Medium': 2,\n    'Low': 1\n  };\n\n  const chartData = {\n    labels: factors.map(f => f.factor),\n    datasets: [\n      {\n        label: 'Risk Impact',\n        data: factors.map(f => impactValues[f.impact] || 0),\n        backgroundColor: factors.map(f => {\n          switch (f.impact) {\n            case 'Very High': return 'rgba(255, 99, 132, 0.8)';\n            case 'High': return 'rgba(255, 159, 64, 0.8)';\n            case 'Medium': return 'rgba(255, 205, 86, 0.8)';\n            case 'Low': return 'rgba(75, 192, 192, 0.8)';\n            default: return 'rgba(201, 203, 207, 0.8)';\n          }\n        }),\n        borderColor: factors.map(f => {\n          switch (f.impact) {\n            case 'Very High': return 'rgb(255, 99, 132)';\n            case 'High': return 'rgb(255, 159, 64)';\n            case 'Medium': return 'rgb(255, 205, 86)';\n            case 'Low': return 'rgb(75, 192, 192)';\n            default: return 'rgb(201, 203, 207)';\n          }\n        }),\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n      title: {\n        display: true,\n        text: 'Risk Factors Impact Analysis',\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context) {\n            const impact = ['', 'Low', 'Medium', 'High', 'Very High'][context.raw];\n            return `Impact: ${impact}`;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 5,\n        ticks: {\n          callback: function(value) {\n            return ['', 'Low', 'Medium', 'High', 'Very High'][value];\n          }\n        }\n      }\n    }\n  };\n\n  return (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Box sx={{ height: 300 }}>\n          <Bar data={chartData} options={options} />\n        </Box>\n      </Grid>\n      \n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>\n          Detailed Risk Factors\n        </Typography>\n        <Grid container spacing={2}>\n          {factors.map((factor, index) => (\n            <Grid item xs={12} sm={6} md={4} key={index}>\n              <Paper \n                elevation={2} \n                sx={{ \n                  p: 2, \n                  borderLeft: '4px solid',\n                  borderColor: \n                    factor.impact === 'Very High' ? 'error.main' :\n                    factor.impact === 'High' ? 'warning.main' :\n                    factor.impact === 'Medium' ? 'info.main' : 'success.main'\n                }}\n              >\n                <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                  {factor.factor}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  Impact: {factor.impact}\n                </Typography>\n                <Typography variant=\"body2\">\n                  Value: {typeof factor.value === 'number' ? factor.value.toFixed(1) : factor.value}\n                  {factor.threshold && ` (Threshold: ${factor.threshold})`}\n                </Typography>\n              </Paper>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n};\n\nexport default RiskFactorsChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,KAAQ,eAAe,CAC5D,OAASC,GAAG,KAAQ,iBAAiB,CACrC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,KACD,UAAU,CAEjB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAV,OAAO,CAACW,QAAQ,CACdV,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MACF,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CAC1C,KAAM,CAAEE,OAAQ,CAAC,CAAGD,cAAc,CAElC,GAAI,CAACC,OAAO,EAAIA,OAAO,CAACC,MAAM,GAAK,CAAC,CAAE,CACpC,mBACER,IAAA,CAACd,GAAG,EAACuB,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACtCZ,IAAA,CAACb,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAAC,6BAEnD,CAAY,CAAC,CACV,CAAC,CAEV,CAEA;AACA,KAAM,CAAAG,YAAY,CAAG,CACnB,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,QAAQ,CAAE,CAAC,CACX,KAAK,CAAE,CACT,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,CAChBC,MAAM,CAAEV,OAAO,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,MAAM,CAAC,CAClCC,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,aAAa,CACpBC,IAAI,CAAEhB,OAAO,CAACW,GAAG,CAACC,CAAC,EAAIJ,YAAY,CAACI,CAAC,CAACK,MAAM,CAAC,EAAI,CAAC,CAAC,CACnDC,eAAe,CAAElB,OAAO,CAACW,GAAG,CAACC,CAAC,EAAI,CAChC,OAAQA,CAAC,CAACK,MAAM,EACd,IAAK,WAAW,CAAE,MAAO,yBAAyB,CAClD,IAAK,MAAM,CAAE,MAAO,yBAAyB,CAC7C,IAAK,QAAQ,CAAE,MAAO,yBAAyB,CAC/C,IAAK,KAAK,CAAE,MAAO,yBAAyB,CAC5C,QAAS,MAAO,0BAA0B,CAC5C,CACF,CAAC,CAAC,CACFE,WAAW,CAAEnB,OAAO,CAACW,GAAG,CAACC,CAAC,EAAI,CAC5B,OAAQA,CAAC,CAACK,MAAM,EACd,IAAK,WAAW,CAAE,MAAO,mBAAmB,CAC5C,IAAK,MAAM,CAAE,MAAO,mBAAmB,CACvC,IAAK,QAAQ,CAAE,MAAO,mBAAmB,CACzC,IAAK,KAAK,CAAE,MAAO,mBAAmB,CACtC,QAAS,MAAO,oBAAoB,CACtC,CACF,CAAC,CAAC,CACFG,WAAW,CAAE,CACf,CAAC,CAEL,CAAC,CAED,KAAM,CAAAC,OAAO,CAAG,CACdC,UAAU,CAAE,IAAI,CAChBC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,KACZ,CAAC,CACDC,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,8BACR,CAAC,CACDC,OAAO,CAAE,CACPC,SAAS,CAAE,CACTf,KAAK,CAAE,QAAAA,CAASgB,OAAO,CAAE,CACvB,KAAM,CAAAd,MAAM,CAAG,CAAC,EAAE,CAAE,KAAK,CAAE,QAAQ,CAAE,MAAM,CAAE,WAAW,CAAC,CAACc,OAAO,CAACC,GAAG,CAAC,CACtE,iBAAAC,MAAA,CAAkBhB,MAAM,EAC1B,CACF,CACF,CACF,CAAC,CACDiB,MAAM,CAAE,CACNC,CAAC,CAAE,CACDC,WAAW,CAAE,IAAI,CACjBC,GAAG,CAAE,CAAC,CACNC,KAAK,CAAE,CACLC,QAAQ,CAAE,QAAAA,CAASC,KAAK,CAAE,CACxB,MAAO,CAAC,EAAE,CAAE,KAAK,CAAE,QAAQ,CAAE,MAAM,CAAE,WAAW,CAAC,CAACA,KAAK,CAAC,CAC1D,CACF,CACF,CACF,CACF,CAAC,CAED,mBACE7C,KAAA,CAACd,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArC,QAAA,eACzBZ,IAAA,CAACZ,IAAI,EAAC8D,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAvC,QAAA,cAChBZ,IAAA,CAACd,GAAG,EAACuB,EAAE,CAAE,CAAE2C,MAAM,CAAE,GAAI,CAAE,CAAAxC,QAAA,cACvBZ,IAAA,CAACV,GAAG,EAACiC,IAAI,CAAEP,SAAU,CAACY,OAAO,CAAEA,OAAQ,CAAE,CAAC,CACvC,CAAC,CACF,CAAC,cAEP1B,KAAA,CAACd,IAAI,EAAC8D,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAvC,QAAA,eAChBZ,IAAA,CAACb,UAAU,EAAC0B,OAAO,CAAC,IAAI,CAACwC,YAAY,MAAAzC,QAAA,CAAC,uBAEtC,CAAY,CAAC,cACbZ,IAAA,CAACZ,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArC,QAAA,CACxBL,OAAO,CAACW,GAAG,CAAC,CAACE,MAAM,CAAEkC,KAAK,gBACzBtD,IAAA,CAACZ,IAAI,EAAC8D,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAA5C,QAAA,cAC9BV,KAAA,CAACb,KAAK,EACJoE,SAAS,CAAE,CAAE,CACbhD,EAAE,CAAE,CACFiD,CAAC,CAAE,CAAC,CACJC,UAAU,CAAE,WAAW,CACvBjC,WAAW,CACTN,MAAM,CAACI,MAAM,GAAK,WAAW,CAAG,YAAY,CAC5CJ,MAAM,CAACI,MAAM,GAAK,MAAM,CAAG,cAAc,CACzCJ,MAAM,CAACI,MAAM,GAAK,QAAQ,CAAG,WAAW,CAAG,cAC/C,CAAE,CAAAZ,QAAA,eAEFZ,IAAA,CAACb,UAAU,EAAC0B,OAAO,CAAC,WAAW,CAAC+C,UAAU,CAAC,MAAM,CAAAhD,QAAA,CAC9CQ,MAAM,CAACA,MAAM,CACJ,CAAC,cACblB,KAAA,CAACf,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACuC,YAAY,MAAAzC,QAAA,EAAC,UACtD,CAACQ,MAAM,CAACI,MAAM,EACZ,CAAC,cACbtB,KAAA,CAACf,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,SACnB,CAAC,MAAO,CAAAQ,MAAM,CAAC2B,KAAK,GAAK,QAAQ,CAAG3B,MAAM,CAAC2B,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC,CAAGzC,MAAM,CAAC2B,KAAK,CAChF3B,MAAM,CAAC0C,SAAS,kBAAAtB,MAAA,CAAoBpB,MAAM,CAAC0C,SAAS,KAAG,EAC9C,CAAC,EACR,CAAC,EAtB4BR,KAuBhC,CACP,CAAC,CACE,CAAC,EACH,CAAC,EACH,CAAC,CAEX,CAAC,CAED,cAAe,CAAAlD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}