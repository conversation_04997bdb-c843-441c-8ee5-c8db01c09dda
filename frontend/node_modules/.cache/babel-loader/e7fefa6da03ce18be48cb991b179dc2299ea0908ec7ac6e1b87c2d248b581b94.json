{"ast": null, "code": "export function updateCircle(layer, props, prevProps) {\n  if (props.center !== prevProps.center) {\n    layer.setLatLng(props.center);\n  }\n  if (props.radius != null && props.radius !== prevProps.radius) {\n    layer.setRadius(props.radius);\n  }\n}", "map": {"version": 3, "names": ["updateCircle", "layer", "props", "prevProps", "center", "setLatLng", "radius", "setRadius"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/node_modules/@react-leaflet/core/lib/circle.js"], "sourcesContent": ["export function updateCircle(layer, props, prevProps) {\n    if (props.center !== prevProps.center) {\n        layer.setLatLng(props.center);\n    }\n    if (props.radius != null && props.radius !== prevProps.radius) {\n        layer.setRadius(props.radius);\n    }\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAClD,IAAID,KAAK,CAACE,MAAM,KAAKD,SAAS,CAACC,MAAM,EAAE;IACnCH,KAAK,CAACI,SAAS,CAACH,KAAK,CAACE,MAAM,CAAC;EACjC;EACA,IAAIF,KAAK,CAACI,MAAM,IAAI,IAAI,IAAIJ,KAAK,CAACI,MAAM,KAAKH,SAAS,CAACG,MAAM,EAAE;IAC3DL,KAAK,CAACM,SAAS,CAACL,KAAK,CAACI,MAAM,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}