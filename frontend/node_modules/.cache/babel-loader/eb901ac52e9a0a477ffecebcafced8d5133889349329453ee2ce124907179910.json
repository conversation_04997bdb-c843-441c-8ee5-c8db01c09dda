{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  XHRLocalObject = require('./transport/sender/xhr-local'),\n  InfoAjax = require('./info-ajax');\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function (info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\ninherits(InfoReceiverIframe, EventEmitter);\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\nInfoReceiverIframe.prototype.close = function () {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\nmodule.exports = InfoReceiverIframe;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "XHRLocalObject", "InfoAjax", "InfoReceiverIframe", "transUrl", "self", "call", "ir", "once", "info", "rtt", "emit", "JSON", "stringify", "transportName", "prototype", "close", "removeAllListeners", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/info-iframe-receiver.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , XHRLocalObject = require('./transport/sender/xhr-local')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function(info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\n\ninherits(InfoReceiverIframe, EventEmitter);\n\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\n\nInfoReceiverIframe.prototype.close = function() {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\n\nmodule.exports = InfoReceiverIframe;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;EAC7CC,cAAc,GAAGF,OAAO,CAAC,8BAA8B,CAAC;EACxDG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;AAGrC,SAASI,kBAAkBA,CAACC,QAAQ,EAAE;EACpC,IAAIC,IAAI,GAAG,IAAI;EACfL,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAI,CAACC,EAAE,GAAG,IAAIL,QAAQ,CAACE,QAAQ,EAAEH,cAAc,CAAC;EAChD,IAAI,CAACM,EAAE,CAACC,IAAI,CAAC,QAAQ,EAAE,UAASC,IAAI,EAAEC,GAAG,EAAE;IACzCL,IAAI,CAACE,EAAE,GAAG,IAAI;IACdF,IAAI,CAACM,IAAI,CAAC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAAC,CAACJ,IAAI,EAAEC,GAAG,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC;AACJ;AAEAZ,QAAQ,CAACK,kBAAkB,EAAEH,YAAY,CAAC;AAE1CG,kBAAkB,CAACW,aAAa,GAAG,sBAAsB;AAEzDX,kBAAkB,CAACY,SAAS,CAACC,KAAK,GAAG,YAAW;EAC9C,IAAI,IAAI,CAACT,EAAE,EAAE;IACX,IAAI,CAACA,EAAE,CAACS,KAAK,CAAC,CAAC;IACf,IAAI,CAACT,EAAE,GAAG,IAAI;EAChB;EACA,IAAI,CAACU,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGhB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}