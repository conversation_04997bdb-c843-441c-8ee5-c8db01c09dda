{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function useLayerLifecycle(element, context) {\n  useEffect(function addLayer() {\n    var _context$layerContain;\n    const container = (_context$layerContain = context.layerContainer) !== null && _context$layerContain !== void 0 ? _context$layerContain : context.map;\n    container.addLayer(element.instance);\n    return function removeLayer() {\n      var _context$layerContain2;\n      (_context$layerContain2 = context.layerContainer) === null || _context$layerContain2 === void 0 || _context$layerContain2.removeLayer(element.instance);\n      context.map.removeLayer(element.instance);\n    };\n  }, [context, element]);\n}\nexport function createLayerHook(useElement) {\n  return function useLayer(props) {\n    const context = useLeafletContext();\n    const elementRef = useElement(withPane(props, context), context);\n    useAttribution(context.map, props.attribution);\n    useEventHandlers(elementRef.current, props.eventHandlers);\n    useLayerLifecycle(elementRef.current, context);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useAttribution", "useLeafletContext", "useEventHandlers", "with<PERSON>ane", "useLayerLifecycle", "element", "context", "add<PERSON><PERSON>er", "_context$layerContain", "container", "layerContainer", "map", "instance", "<PERSON><PERSON><PERSON>er", "_context$layerContain2", "createLayerHook", "useElement", "useLayer", "props", "elementRef", "attribution", "current", "eventHandlers"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/layer.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAttribution } from './attribution.js';\nimport { useLeafletContext } from './context.js';\nimport { useEventHandlers } from './events.js';\nimport { withPane } from './pane.js';\nexport function useLayerLifecycle(element, context) {\n    useEffect(function addLayer() {\n        const container = context.layerContainer ?? context.map;\n        container.addLayer(element.instance);\n        return function removeLayer() {\n            context.layerContainer?.removeLayer(element.instance);\n            context.map.removeLayer(element.instance);\n        };\n    }, [\n        context,\n        element\n    ]);\n}\nexport function createLayerHook(useElement) {\n    return function useLayer(props) {\n        const context = useLeafletContext();\n        const elementRef = useElement(withPane(props, context), context);\n        useAttribution(context.map, props.attribution);\n        useEventHandlers(elementRef.current, props.eventHandlers);\n        useLayerLifecycle(elementRef.current, context);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAChDP,SAAS,CAAC,SAASQ,QAAQA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IAC1B,MAAMC,SAAS,IAAAD,qBAAA,GAAGF,OAAO,CAACI,cAAc,cAAAF,qBAAA,cAAAA,qBAAA,GAAIF,OAAO,CAACK,GAAG;IACvDF,SAAS,CAACF,QAAQ,CAACF,OAAO,CAACO,QAAQ,CAAC;IACpC,OAAO,SAASC,WAAWA,CAAA,EAAG;MAAA,IAAAC,sBAAA;MAC1B,CAAAA,sBAAA,GAAAR,OAAO,CAACI,cAAc,cAAAI,sBAAA,eAAtBA,sBAAA,CAAwBD,WAAW,CAACR,OAAO,CAACO,QAAQ,CAAC;MACrDN,OAAO,CAACK,GAAG,CAACE,WAAW,CAACR,OAAO,CAACO,QAAQ,CAAC;IAC7C,CAAC;EACL,CAAC,EAAE,CACCN,OAAO,EACPD,OAAO,CACV,CAAC;AACN;AACA,OAAO,SAASU,eAAeA,CAACC,UAAU,EAAE;EACxC,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;IAC5B,MAAMZ,OAAO,GAAGL,iBAAiB,CAAC,CAAC;IACnC,MAAMkB,UAAU,GAAGH,UAAU,CAACb,QAAQ,CAACe,KAAK,EAAEZ,OAAO,CAAC,EAAEA,OAAO,CAAC;IAChEN,cAAc,CAACM,OAAO,CAACK,GAAG,EAAEO,KAAK,CAACE,WAAW,CAAC;IAC9ClB,gBAAgB,CAACiB,UAAU,CAACE,OAAO,EAAEH,KAAK,CAACI,aAAa,CAAC;IACzDlB,iBAAiB,CAACe,UAAU,CAACE,OAAO,EAAEf,OAAO,CAAC;IAC9C,OAAOa,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}