{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  iframeUtils = require('../../utils/iframe'),\n  urlUtils = require('../../utils/url'),\n  EventEmitter = require('events').EventEmitter,\n  random = require('../../utils/random');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ? iframeUtils.createHtmlfile : iframeUtils.createIframe;\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function () {\n      debug('start');\n      self.iframeObj.loaded();\n    },\n    message: function (data) {\n      debug('message', data);\n      self.emit('message', data);\n    },\n    stop: function () {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function () {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\ninherits(HtmlfileReceiver, EventEmitter);\nHtmlfileReceiver.prototype.abort = function () {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\nHtmlfileReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\nHtmlfileReceiver.prototype._close = function (reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\nmodule.exports = HtmlfileReceiver;", "map": {"version": 3, "names": ["inherits", "require", "iframe<PERSON><PERSON>s", "urlUtils", "EventEmitter", "random", "debug", "process", "env", "NODE_ENV", "HtmlfileReceiver", "url", "call", "self", "polluteGlobalNamespace", "id", "string", "<PERSON><PERSON><PERSON><PERSON>", "decodeURIComponent", "WPrefix", "htmlfileEnabled", "constructFunc", "createHtmlfile", "createIframe", "global", "start", "iframeObj", "loaded", "message", "data", "emit", "stop", "_cleanup", "_close", "prototype", "abort", "cleanup", "reason", "removeAllListeners", "axo", "concat", "join", "x", "enabled", "iframeEnabled", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/receiver/htmlfile.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , iframeUtils = require('../../utils/iframe')\n  , urlUtils = require('../../utils/url')\n  , EventEmitter = require('events').EventEmitter\n  , random = require('../../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\n\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ?\n      iframeUtils.createHtmlfile : iframeUtils.createIframe;\n\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function() {\n      debug('start');\n      self.iframeObj.loaded();\n    }\n  , message: function(data) {\n      debug('message', data);\n      self.emit('message', data);\n    }\n  , stop: function() {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function() {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\n\ninherits(HtmlfileReceiver, EventEmitter);\n\nHtmlfileReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nHtmlfileReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\n\nHtmlfileReceiver.prototype._close = function(reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\n\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\n\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\n\nmodule.exports = HtmlfileReceiver;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,WAAW,GAAGD,OAAO,CAAC,oBAAoB,CAAC;EAC3CE,QAAQ,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EACrCG,YAAY,GAAGH,OAAO,CAAC,QAAQ,CAAC,CAACG,YAAY;EAC7CC,MAAM,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAG1C,IAAIK,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC;AAC7D;AAEA,SAASS,gBAAgBA,CAACC,GAAG,EAAE;EAC7BL,KAAK,CAACK,GAAG,CAAC;EACVP,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC;EACvB,IAAIC,IAAI,GAAG,IAAI;EACfX,WAAW,CAACY,sBAAsB,CAAC,CAAC;EAEpC,IAAI,CAACC,EAAE,GAAG,GAAG,GAAGV,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC;EAChCL,GAAG,GAAGR,QAAQ,CAACc,QAAQ,CAACN,GAAG,EAAE,IAAI,GAAGO,kBAAkB,CAAChB,WAAW,CAACiB,OAAO,GAAG,GAAG,GAAG,IAAI,CAACJ,EAAE,CAAC,CAAC;EAE5FT,KAAK,CAAC,gBAAgB,EAAEI,gBAAgB,CAACU,eAAe,CAAC;EACzD,IAAIC,aAAa,GAAGX,gBAAgB,CAACU,eAAe,GAChDlB,WAAW,CAACoB,cAAc,GAAGpB,WAAW,CAACqB,YAAY;EAEzDC,MAAM,CAACtB,WAAW,CAACiB,OAAO,CAAC,CAAC,IAAI,CAACJ,EAAE,CAAC,GAAG;IACrCU,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChBnB,KAAK,CAAC,OAAO,CAAC;MACdO,IAAI,CAACa,SAAS,CAACC,MAAM,CAAC,CAAC;IACzB,CAAC;IACDC,OAAO,EAAE,SAAAA,CAASC,IAAI,EAAE;MACtBvB,KAAK,CAAC,SAAS,EAAEuB,IAAI,CAAC;MACtBhB,IAAI,CAACiB,IAAI,CAAC,SAAS,EAAED,IAAI,CAAC;IAC5B,CAAC;IACDE,IAAI,EAAE,SAAAA,CAAA,EAAW;MACfzB,KAAK,CAAC,MAAM,CAAC;MACbO,IAAI,CAACmB,QAAQ,CAAC,CAAC;MACfnB,IAAI,CAACoB,MAAM,CAAC,SAAS,CAAC;IACxB;EACF,CAAC;EACD,IAAI,CAACP,SAAS,GAAGL,aAAa,CAACV,GAAG,EAAE,YAAW;IAC7CL,KAAK,CAAC,UAAU,CAAC;IACjBO,IAAI,CAACmB,QAAQ,CAAC,CAAC;IACfnB,IAAI,CAACoB,MAAM,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC;AACJ;AAEAjC,QAAQ,CAACU,gBAAgB,EAAEN,YAAY,CAAC;AAExCM,gBAAgB,CAACwB,SAAS,CAACC,KAAK,GAAG,YAAW;EAC5C7B,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAAC0B,QAAQ,CAAC,CAAC;EACf,IAAI,CAACC,MAAM,CAAC,MAAM,CAAC;AACrB,CAAC;AAEDvB,gBAAgB,CAACwB,SAAS,CAACF,QAAQ,GAAG,YAAW;EAC/C1B,KAAK,CAAC,UAAU,CAAC;EACjB,IAAI,IAAI,CAACoB,SAAS,EAAE;IAClB,IAAI,CAACA,SAAS,CAACU,OAAO,CAAC,CAAC;IACxB,IAAI,CAACV,SAAS,GAAG,IAAI;EACvB;EACA,OAAOF,MAAM,CAACtB,WAAW,CAACiB,OAAO,CAAC,CAAC,IAAI,CAACJ,EAAE,CAAC;AAC7C,CAAC;AAEDL,gBAAgB,CAACwB,SAAS,CAACD,MAAM,GAAG,UAASI,MAAM,EAAE;EACnD/B,KAAK,CAAC,QAAQ,EAAE+B,MAAM,CAAC;EACvB,IAAI,CAACP,IAAI,CAAC,OAAO,EAAE,IAAI,EAAEO,MAAM,CAAC;EAChC,IAAI,CAACC,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAED5B,gBAAgB,CAACU,eAAe,GAAG,KAAK;;AAExC;AACA,IAAImB,GAAG,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC/C,IAAIF,GAAG,IAAIf,MAAM,EAAE;EACjB,IAAI;IACFd,gBAAgB,CAACU,eAAe,GAAG,CAAC,CAAC,IAAII,MAAM,CAACe,GAAG,CAAC,CAAC,UAAU,CAAC;EAClE,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV;EAAA;AAEJ;AAEAhC,gBAAgB,CAACiC,OAAO,GAAGjC,gBAAgB,CAACU,eAAe,IAAIlB,WAAW,CAAC0C,aAAa;AAExFC,MAAM,CAACC,OAAO,GAAGpC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}