{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  utils = require('../../utils/event'),\n  urlUtils = require('../../utils/url'),\n  XHR = global.XMLHttpRequest;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\ninherits(AbstractXHRObject, EventEmitter);\nAbstractXHRObject.prototype._start = function (method, url, payload, opts) {\n  var self = this;\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + +new Date());\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function () {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n  this.xhr.onreadystatechange = function () {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n        case 3:\n          // IE doesn't like peeking into responseText or status\n          // on Microsoft.XMLHTTP and readystate=3\n          try {\n            status = x.status;\n            text = x.responseText;\n          } catch (e) {\n            // intentionally empty\n          }\n          debug('status', status);\n          // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n          if (status === 1223) {\n            status = 204;\n          }\n\n          // IE does return readystate == 3 for 404 answers.\n          if (status === 200 && text && text.length > 0) {\n            debug('chunk');\n            self.emit('chunk', status, text);\n          }\n          break;\n        case 4:\n          status = x.status;\n          debug('status', status);\n          // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n          if (status === 1223) {\n            status = 204;\n          }\n          // IE returns this for a bad port\n          // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n          if (status === 12005 || status === 12029) {\n            status = 0;\n          }\n          debug('finish', status, x.responseText);\n          self.emit('finish', status, x.responseText);\n          self._cleanup(false);\n          break;\n      }\n    }\n  };\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\nAbstractXHRObject.prototype._cleanup = function (abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function () {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\nAbstractXHRObject.prototype.close = function () {\n  debug('close');\n  this._cleanup(true);\n};\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && axo in global) {\n  debug('overriding xmlhttprequest');\n  XHR = function () {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\nAbstractXHRObject.supportsCORS = cors;\nmodule.exports = AbstractXHRObject;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "utils", "urlUtils", "XHR", "global", "XMLHttpRequest", "debug", "process", "env", "NODE_ENV", "AbstractXHRObject", "method", "url", "payload", "opts", "self", "call", "setTimeout", "_start", "prototype", "xhr", "x", "emit", "_cleanup", "<PERSON><PERSON><PERSON><PERSON>", "Date", "unloadRef", "unloadAdd", "open", "timeout", "ontimeout", "e", "noCredentials", "supportsCORS", "withCredentials", "headers", "key", "setRequestHeader", "onreadystatechange", "text", "status", "readyState", "responseText", "length", "send", "abort", "removeAllListeners", "unloadDel", "close", "enabled", "axo", "concat", "join", "cors", "ignored", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/browser/abstract-xhr.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('../../utils/event')\n  , urlUtils = require('../../utils/url')\n  , XHR = global.XMLHttpRequest\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\n\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\n\ninherits(AbstractXHRObject, EventEmitter);\n\nAbstractXHRObject.prototype._start = function(method, url, payload, opts) {\n  var self = this;\n\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function() {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n\n  this.xhr.onreadystatechange = function() {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n      case 3:\n        // IE doesn't like peeking into responseText or status\n        // on Microsoft.XMLHTTP and readystate=3\n        try {\n          status = x.status;\n          text = x.responseText;\n        } catch (e) {\n          // intentionally empty\n        }\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n\n        // IE does return readystate == 3 for 404 answers.\n        if (status === 200 && text && text.length > 0) {\n          debug('chunk');\n          self.emit('chunk', status, text);\n        }\n        break;\n      case 4:\n        status = x.status;\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n        // IE returns this for a bad port\n        // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n        if (status === 12005 || status === 12029) {\n          status = 0;\n        }\n\n        debug('finish', status, x.responseText);\n        self.emit('finish', status, x.responseText);\n        self._cleanup(false);\n        break;\n      }\n    }\n  };\n\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\n\nAbstractXHRObject.prototype._cleanup = function(abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function() {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\n\nAbstractXHRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && (axo in global)) {\n  debug('overriding xmlhttprequest');\n  XHR = function() {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\n\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\n\nAbstractXHRObject.supportsCORS = cors;\n\nmodule.exports = AbstractXHRObject;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC9BE,KAAK,GAAGF,OAAO,CAAC,mBAAmB,CAAC;EACpCG,QAAQ,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACrCI,GAAG,GAAGC,MAAM,CAACC,cAAc;AAG/B,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGP,OAAO,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC;AACvD;AAEA,SAASW,iBAAiBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACrDR,KAAK,CAACK,MAAM,EAAEC,GAAG,CAAC;EAClB,IAAIG,IAAI,GAAG,IAAI;EACfjB,YAAY,CAACkB,IAAI,CAAC,IAAI,CAAC;EAEvBC,UAAU,CAAC,YAAY;IACrBF,IAAI,CAACG,MAAM,CAACP,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,CAAC;EACzC,CAAC,EAAE,CAAC,CAAC;AACP;AAEAd,QAAQ,CAACU,iBAAiB,EAAEZ,YAAY,CAAC;AAEzCY,iBAAiB,CAACS,SAAS,CAACD,MAAM,GAAG,UAASP,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACxE,IAAIC,IAAI,GAAG,IAAI;EAEf,IAAI;IACF,IAAI,CAACK,GAAG,GAAG,IAAIjB,GAAG,CAAC,CAAC;EACtB,CAAC,CAAC,OAAOkB,CAAC,EAAE;IACV;EAAA;EAGF,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE;IACbd,KAAK,CAAC,QAAQ,CAAC;IACf,IAAI,CAACgB,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,gBAAgB,CAAC;IACxC,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf;EACF;;EAEA;EACAX,GAAG,GAAGV,QAAQ,CAACsB,QAAQ,CAACZ,GAAG,EAAE,IAAI,GAAI,CAAC,IAAIa,IAAI,CAAC,CAAE,CAAC;;EAElD;EACA;EACA,IAAI,CAACC,SAAS,GAAGzB,KAAK,CAAC0B,SAAS,CAAC,YAAW;IAC1CrB,KAAK,CAAC,gBAAgB,CAAC;IACvBS,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC;EACrB,CAAC,CAAC;EACF,IAAI;IACF,IAAI,CAACH,GAAG,CAACQ,IAAI,CAACjB,MAAM,EAAEC,GAAG,EAAE,IAAI,CAAC;IAChC,IAAI,IAAI,CAACiB,OAAO,IAAI,SAAS,IAAI,IAAI,CAACT,GAAG,EAAE;MACzC,IAAI,CAACA,GAAG,CAACS,OAAO,GAAG,IAAI,CAACA,OAAO;MAC/B,IAAI,CAACT,GAAG,CAACU,SAAS,GAAG,YAAW;QAC9BxB,KAAK,CAAC,aAAa,CAAC;QACpBS,IAAI,CAACO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;QAC1BP,IAAI,CAACQ,QAAQ,CAAC,KAAK,CAAC;MACtB,CAAC;IACH;EACF,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACVzB,KAAK,CAAC,WAAW,EAAEyB,CAAC,CAAC;IACrB;IACA,IAAI,CAACT,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;IAC1B,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC;IACpB;EACF;EAEA,IAAI,CAAC,CAACT,IAAI,IAAI,CAACA,IAAI,CAACkB,aAAa,KAAKtB,iBAAiB,CAACuB,YAAY,EAAE;IACpE3B,KAAK,CAAC,iBAAiB,CAAC;IACxB;IACA;;IAEA,IAAI,CAACc,GAAG,CAACc,eAAe,GAAG,IAAI;EACjC;EACA,IAAIpB,IAAI,IAAIA,IAAI,CAACqB,OAAO,EAAE;IACxB,KAAK,IAAIC,GAAG,IAAItB,IAAI,CAACqB,OAAO,EAAE;MAC5B,IAAI,CAACf,GAAG,CAACiB,gBAAgB,CAACD,GAAG,EAAEtB,IAAI,CAACqB,OAAO,CAACC,GAAG,CAAC,CAAC;IACnD;EACF;EAEA,IAAI,CAAChB,GAAG,CAACkB,kBAAkB,GAAG,YAAW;IACvC,IAAIvB,IAAI,CAACK,GAAG,EAAE;MACZ,IAAIC,CAAC,GAAGN,IAAI,CAACK,GAAG;MAChB,IAAImB,IAAI,EAAEC,MAAM;MAChBlC,KAAK,CAAC,YAAY,EAAEe,CAAC,CAACoB,UAAU,CAAC;MACjC,QAAQpB,CAAC,CAACoB,UAAU;QACpB,KAAK,CAAC;UACJ;UACA;UACA,IAAI;YACFD,MAAM,GAAGnB,CAAC,CAACmB,MAAM;YACjBD,IAAI,GAAGlB,CAAC,CAACqB,YAAY;UACvB,CAAC,CAAC,OAAOX,CAAC,EAAE;YACV;UAAA;UAEFzB,KAAK,CAAC,QAAQ,EAAEkC,MAAM,CAAC;UACvB;UACA,IAAIA,MAAM,KAAK,IAAI,EAAE;YACnBA,MAAM,GAAG,GAAG;UACd;;UAEA;UACA,IAAIA,MAAM,KAAK,GAAG,IAAID,IAAI,IAAIA,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;YAC7CrC,KAAK,CAAC,OAAO,CAAC;YACdS,IAAI,CAACO,IAAI,CAAC,OAAO,EAAEkB,MAAM,EAAED,IAAI,CAAC;UAClC;UACA;QACF,KAAK,CAAC;UACJC,MAAM,GAAGnB,CAAC,CAACmB,MAAM;UACjBlC,KAAK,CAAC,QAAQ,EAAEkC,MAAM,CAAC;UACvB;UACA,IAAIA,MAAM,KAAK,IAAI,EAAE;YACnBA,MAAM,GAAG,GAAG;UACd;UACA;UACA;UACA,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,KAAK,EAAE;YACxCA,MAAM,GAAG,CAAC;UACZ;UAEAlC,KAAK,CAAC,QAAQ,EAAEkC,MAAM,EAAEnB,CAAC,CAACqB,YAAY,CAAC;UACvC3B,IAAI,CAACO,IAAI,CAAC,QAAQ,EAAEkB,MAAM,EAAEnB,CAAC,CAACqB,YAAY,CAAC;UAC3C3B,IAAI,CAACQ,QAAQ,CAAC,KAAK,CAAC;UACpB;MACF;IACF;EACF,CAAC;EAED,IAAI;IACFR,IAAI,CAACK,GAAG,CAACwB,IAAI,CAAC/B,OAAO,CAAC;EACxB,CAAC,CAAC,OAAOkB,CAAC,EAAE;IACVhB,IAAI,CAACO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;IAC1BP,IAAI,CAACQ,QAAQ,CAAC,KAAK,CAAC;EACtB;AACF,CAAC;AAEDb,iBAAiB,CAACS,SAAS,CAACI,QAAQ,GAAG,UAASsB,KAAK,EAAE;EACrDvC,KAAK,CAAC,SAAS,CAAC;EAChB,IAAI,CAAC,IAAI,CAACc,GAAG,EAAE;IACb;EACF;EACA,IAAI,CAAC0B,kBAAkB,CAAC,CAAC;EACzB7C,KAAK,CAAC8C,SAAS,CAAC,IAAI,CAACrB,SAAS,CAAC;;EAE/B;EACA,IAAI,CAACN,GAAG,CAACkB,kBAAkB,GAAG,YAAW,CAAC,CAAC;EAC3C,IAAI,IAAI,CAAClB,GAAG,CAACU,SAAS,EAAE;IACtB,IAAI,CAACV,GAAG,CAACU,SAAS,GAAG,IAAI;EAC3B;EAEA,IAAIe,KAAK,EAAE;IACT,IAAI;MACF,IAAI,CAACzB,GAAG,CAACyB,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOxB,CAAC,EAAE;MACV;IAAA;EAEJ;EACA,IAAI,CAACK,SAAS,GAAG,IAAI,CAACN,GAAG,GAAG,IAAI;AAClC,CAAC;AAEDV,iBAAiB,CAACS,SAAS,CAAC6B,KAAK,GAAG,YAAW;EAC7C1C,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAACiB,QAAQ,CAAC,IAAI,CAAC;AACrB,CAAC;AAEDb,iBAAiB,CAACuC,OAAO,GAAG,CAAC,CAAC9C,GAAG;AACjC;AACA;AACA,IAAI+C,GAAG,GAAG,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC/C,IAAI,CAAC1C,iBAAiB,CAACuC,OAAO,IAAKC,GAAG,IAAI9C,MAAO,EAAE;EACjDE,KAAK,CAAC,2BAA2B,CAAC;EAClCH,GAAG,GAAG,SAAAA,CAAA,EAAW;IACf,IAAI;MACF,OAAO,IAAIC,MAAM,CAAC8C,GAAG,CAAC,CAAC,mBAAmB,CAAC;IAC7C,CAAC,CAAC,OAAOnB,CAAC,EAAE;MACV,OAAO,IAAI;IACb;EACF,CAAC;EACDrB,iBAAiB,CAACuC,OAAO,GAAG,CAAC,CAAC,IAAI9C,GAAG,CAAC,CAAC;AACzC;AAEA,IAAIkD,IAAI,GAAG,KAAK;AAChB,IAAI;EACFA,IAAI,GAAG,iBAAiB,IAAI,IAAIlD,GAAG,CAAC,CAAC;AACvC,CAAC,CAAC,OAAOmD,OAAO,EAAE;EAChB;AAAA;AAGF5C,iBAAiB,CAACuB,YAAY,GAAGoB,IAAI;AAErCE,MAAM,CAACC,OAAO,GAAG9C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}