{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  AjaxBasedTransport = require('./lib/ajax-based'),\n  EventSourceReceiver = require('./receiver/eventsource'),\n  XHRCorsObject = require('./sender/xhr-cors'),\n  EventSourceDriver = require('eventsource');\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\ninherits(EventSourceTransport, AjaxBasedTransport);\nEventSourceTransport.enabled = function () {\n  return !!EventSourceDriver;\n};\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\nmodule.exports = EventSourceTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "EventSourceReceiver", "XHRCorsObject", "EventSourceDriver", "EventSourceTransport", "transUrl", "enabled", "Error", "call", "transportName", "roundTrips", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/eventsource.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , EventSourceReceiver = require('./receiver/eventsource')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , EventSourceDriver = require('eventsource')\n  ;\n\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\n\ninherits(EventSourceTransport, AjaxBasedTransport);\n\nEventSourceTransport.enabled = function() {\n  return !!EventSourceDriver;\n};\n\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\n\nmodule.exports = EventSourceTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,kBAAkB,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAChDE,mBAAmB,GAAGF,OAAO,CAAC,wBAAwB,CAAC;EACvDG,aAAa,GAAGH,OAAO,CAAC,mBAAmB,CAAC;EAC5CI,iBAAiB,GAAGJ,OAAO,CAAC,aAAa,CAAC;AAG9C,SAASK,oBAAoBA,CAACC,QAAQ,EAAE;EACtC,IAAI,CAACD,oBAAoB,CAACE,OAAO,CAAC,CAAC,EAAE;IACnC,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EAEAP,kBAAkB,CAACQ,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,cAAc,EAAEJ,mBAAmB,EAAEC,aAAa,CAAC;AAC7F;AAEAJ,QAAQ,CAACM,oBAAoB,EAAEJ,kBAAkB,CAAC;AAElDI,oBAAoB,CAACE,OAAO,GAAG,YAAW;EACxC,OAAO,CAAC,CAACH,iBAAiB;AAC5B,CAAC;AAEDC,oBAAoB,CAACK,aAAa,GAAG,aAAa;AAClDL,oBAAoB,CAACM,UAAU,GAAG,CAAC;AAEnCC,MAAM,CAACC,OAAO,GAAGR,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}