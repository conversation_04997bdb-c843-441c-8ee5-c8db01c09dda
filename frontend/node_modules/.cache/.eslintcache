[{"/Users/<USER>/Downloads/Flood/frontend/src/index.js": "1", "/Users/<USER>/Downloads/Flood/frontend/src/App.js": "2", "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js": "3", "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js": "4", "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js": "5", "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js": "6", "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js": "7", "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js": "8", "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js": "9", "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js": "10", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js": "11", "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js": "12", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js": "13", "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js": "14", "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js": "15", "/Users/<USER>/Downloads/Flood copy/frontend/src/index.js": "16", "/Users/<USER>/Downloads/Flood copy/frontend/src/App.js": "17", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/Header.js": "18", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/PredictionForm.js": "19", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/ResultDisplay.js": "20", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/RiskFactorsChart.js": "21", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/TimelineRiskPredictor.js": "22", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/FloodMap.js": "23", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/CommunityReports.js": "24", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/VoiceEmergencyAssistant.js": "25", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/AnimatedComponents.js": "26", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/LoadingAnimation.js": "27", "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/neuromorphicUtils.js": "28", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/LocationSelector.js": "29", "/Users/<USER>/Downloads/Flood copy/frontend/src/hooks/useScrollAnimation.js": "30"}, {"size": 254, "mtime": 1747753433573, "results": "31", "hashOfConfig": "32"}, {"size": 49395, "mtime": 1747801561833, "results": "33", "hashOfConfig": "32"}, {"size": 17971, "mtime": 1747764370243, "results": "34", "hashOfConfig": "32"}, {"size": 7400, "mtime": 1747754619314, "results": "35", "hashOfConfig": "32"}, {"size": 52763, "mtime": 1747764850190, "results": "36", "hashOfConfig": "32"}, {"size": 4057, "mtime": 1747753554779, "results": "37", "hashOfConfig": "32"}, {"size": 13816, "mtime": 1747764231471, "results": "38", "hashOfConfig": "32"}, {"size": 33887, "mtime": 1747800864886, "results": "39", "hashOfConfig": "32"}, {"size": 11970, "mtime": 1747757312237, "results": "40", "hashOfConfig": "32"}, {"size": 28486, "mtime": 1747799193533, "results": "41", "hashOfConfig": "32"}, {"size": 5615, "mtime": 1747761188824, "results": "42", "hashOfConfig": "32"}, {"size": 1564, "mtime": 1747761161281, "results": "43", "hashOfConfig": "32"}, {"size": 4145, "mtime": 1747761615192, "results": "44", "hashOfConfig": "32"}, {"size": 8969, "mtime": 1747799643617, "results": "45", "hashOfConfig": "32"}, {"size": 3672, "mtime": 1747801467631, "results": "46", "hashOfConfig": "32"}, {"size": 254, "mtime": 1747753433573, "results": "47", "hashOfConfig": "48"}, {"size": 49395, "mtime": 1747801561833, "results": "49", "hashOfConfig": "48"}, {"size": 13277, "mtime": 1747903365036, "results": "50", "hashOfConfig": "48"}, {"size": 13816, "mtime": 1747764231471, "results": "51", "hashOfConfig": "48"}, {"size": 17971, "mtime": 1747764370243, "results": "52", "hashOfConfig": "48"}, {"size": 4057, "mtime": 1747753554779, "results": "53", "hashOfConfig": "48"}, {"size": 33887, "mtime": 1747800864886, "results": "54", "hashOfConfig": "48"}, {"size": 62798, "mtime": 1747901159903, "results": "55", "hashOfConfig": "48"}, {"size": 28486, "mtime": 1747799193533, "results": "56", "hashOfConfig": "48"}, {"size": 11970, "mtime": 1747757312237, "results": "57", "hashOfConfig": "48"}, {"size": 5615, "mtime": 1747761188824, "results": "58", "hashOfConfig": "48"}, {"size": 4145, "mtime": 1747761615192, "results": "59", "hashOfConfig": "48"}, {"size": 3672, "mtime": 1747801467631, "results": "60", "hashOfConfig": "48"}, {"size": 8969, "mtime": 1747799643617, "results": "61", "hashOfConfig": "48"}, {"size": 1564, "mtime": 1747761161281, "results": "62", "hashOfConfig": "48"}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xqa9ev", {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1x8qpam", {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/Flood/frontend/src/index.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/App.js", ["153", "154", "155"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js", ["156", "157", "158", "159", "160", "161", "162"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js", ["163", "164"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js", ["165", "166", "167", "168"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js", ["169"], [], "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js", ["170", "171", "172"], [], "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/index.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/App.js", ["173", "174", "175"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/Header.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/PredictionForm.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/ResultDisplay.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/RiskFactorsChart.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/TimelineRiskPredictor.js", ["176", "177"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/FloodMap.js", ["178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/CommunityReports.js", ["189", "190", "191", "192"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/VoiceEmergencyAssistant.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/AnimatedComponents.js", ["193"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/LoadingAnimation.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/neuromorphicUtils.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/LocationSelector.js", ["194", "195", "196"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/hooks/useScrollAnimation.js", [], [], {"ruleId": "197", "severity": 1, "message": "198", "line": 16, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "201", "line": 16, "column": 54, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 75}, {"ruleId": "197", "severity": 1, "message": "202", "line": 16, "column": 77, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 93}, {"ruleId": "197", "severity": 1, "message": "203", "line": 9, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 9, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "204", "line": 10, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 10, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "205", "line": 20, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 20, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "206", "line": 22, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 22, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "207", "line": 23, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "208", "line": 44, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 44, "endColumn": 18}, {"ruleId": "197", "severity": 1, "message": "209", "line": 445, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 445, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "210", "line": 14, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 14, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "211", "line": 35, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 35, "endColumn": 22}, {"ruleId": "197", "severity": 1, "message": "212", "line": 26, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 26, "endColumn": 9}, {"ruleId": "213", "severity": 1, "message": "214", "line": 176, "column": 6, "nodeType": "215", "endLine": 176, "endColumn": 8, "suggestions": "216"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 199, "column": 6, "nodeType": "215", "endLine": 199, "endColumn": 8, "suggestions": "217"}, {"ruleId": "197", "severity": 1, "message": "218", "line": 252, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 252, "endColumn": 27}, {"ruleId": "219", "severity": 1, "message": "220", "line": 225, "column": 1, "nodeType": "221", "endLine": 234, "endColumn": 3}, {"ruleId": "197", "severity": 1, "message": "222", "line": 49, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 49, "endColumn": 21}, {"ruleId": "197", "severity": 1, "message": "223", "line": 49, "column": 23, "nodeType": "199", "messageId": "200", "endLine": 49, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "224", "line": 112, "column": 6, "nodeType": "215", "endLine": 112, "endColumn": 24, "suggestions": "225"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 16, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 28}, {"ruleId": "197", "severity": 1, "message": "201", "line": 16, "column": 54, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 75}, {"ruleId": "197", "severity": 1, "message": "202", "line": 16, "column": 77, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 93}, {"ruleId": "197", "severity": 1, "message": "210", "line": 14, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 14, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "211", "line": 35, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 35, "endColumn": 22}, {"ruleId": "197", "severity": 1, "message": "203", "line": 9, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 9, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "204", "line": 10, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 10, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "226", "line": 12, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 12, "endColumn": 10}, {"ruleId": "197", "severity": 1, "message": "205", "line": 21, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 21, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "206", "line": 23, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 23, "endColumn": 7}, {"ruleId": "197", "severity": 1, "message": "207", "line": 24, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 24, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "227", "line": 49, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 49, "endColumn": 15}, {"ruleId": "197", "severity": 1, "message": "208", "line": 50, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 50, "endColumn": 18}, {"ruleId": "197", "severity": 1, "message": "228", "line": 552, "column": 26, "nodeType": "199", "messageId": "200", "endLine": 552, "endColumn": 43}, {"ruleId": "197", "severity": 1, "message": "229", "line": 568, "column": 13, "nodeType": "199", "messageId": "200", "endLine": 568, "endColumn": 23}, {"ruleId": "197", "severity": 1, "message": "209", "line": 653, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 653, "endColumn": 14}, {"ruleId": "197", "severity": 1, "message": "212", "line": 26, "column": 3, "nodeType": "199", "messageId": "200", "endLine": 26, "endColumn": 9}, {"ruleId": "213", "severity": 1, "message": "214", "line": 176, "column": 6, "nodeType": "215", "endLine": 176, "endColumn": 8, "suggestions": "230"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 199, "column": 6, "nodeType": "215", "endLine": 199, "endColumn": 8, "suggestions": "231"}, {"ruleId": "197", "severity": 1, "message": "218", "line": 252, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 252, "endColumn": 27}, {"ruleId": "219", "severity": 1, "message": "220", "line": 225, "column": 1, "nodeType": "221", "endLine": 234, "endColumn": 3}, {"ruleId": "197", "severity": 1, "message": "222", "line": 49, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 49, "endColumn": 21}, {"ruleId": "197", "severity": 1, "message": "223", "line": 49, "column": 23, "nodeType": "199", "messageId": "200", "endLine": 49, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "224", "line": 112, "column": 6, "nodeType": "215", "endLine": 112, "endColumn": 24, "suggestions": "232"}, "no-unused-vars", "'neuromorphicStyles' is defined but never used.", "Identifier", "unusedVar", "'getNeuromorphicShadow' is defined but never used.", "'getPressedEffect' is defined but never used.", "'ZoomControl' is defined but never used.", "'Rectangle' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'LayersIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'Divider' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'Rating' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mapAlertToReport'. Either include it or remove the dependency array.", "ArrayExpression", ["233"], ["234"], "'handleSliderChange' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'searchQuery' is assigned a value but never used.", "'setSearchQuery' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'findNearestCity'. Either include it or remove the dependency array.", ["235"], "'Polygon' is defined but never used.", "'MapIcon' is defined but never used.", "'setHeatMapVisible' is assigned a value but never used.", "'zoneCenter' is assigned a value but never used.", ["236"], ["237"], ["238"], {"desc": "239", "fix": "240"}, {"desc": "239", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "239", "fix": "244"}, {"desc": "239", "fix": "245"}, {"desc": "242", "fix": "246"}, "Update the dependencies array to be: [mapAlertToReport]", {"range": "247", "text": "248"}, {"range": "249", "text": "248"}, "Update the dependencies array to be: [findNearestCity, onLocationSelect]", {"range": "250", "text": "251"}, {"range": "252", "text": "248"}, {"range": "253", "text": "248"}, {"range": "254", "text": "251"}, [4769, 4771], "[mapAlertToReport]", [5425, 5427], [4524, 4542], "[findNearestCity, onLocationSelect]", [4769, 4771], [5425, 5427], [4524, 4542]]