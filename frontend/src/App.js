import { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';
import Header from './components/Header';
import FloodMap from './components/FloodMap';
import PredictionForm from './components/PredictionForm';
import ResultDisplay from './components/ResultDisplay';
import RiskFactorsChart from './components/RiskFactorsChart';
import TimelineRiskPredictor from './components/TimelineRiskPredictor';
import VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';
import CommunityReports from './components/CommunityReports';
import { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';
import LoadingAnimation from './components/animations/LoadingAnimation';
import axios from 'axios';
import { neuromorphicStyles, getNeuromorphicPalette, getNeuromorphicShadow, getPressedEffect } from './theme/neuromorphicUtils';
import './components/ResponsiveLayout.css';

// Create a theme with neuromorphic design
const theme = createTheme({
  palette: {
    mode: 'light',
    ...getNeuromorphicPalette('#3A86FF'),
    info: {
      main: '#4CC9F0', // Light blue
      light: '#e6f7fc',
      dark: '#3AA1C0',
      contrastText: '#ffffff',
    },
    divider: 'rgba(0, 0, 0, 0.08)',
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',
      fontWeight: 800,
      letterSpacing: '-0.01em',
      lineHeight: 1.2,
    },
    h2: {
      fontSize: 'clamp(2rem, 4vw, 2.75rem)',
      fontWeight: 700,
      letterSpacing: '-0.01em',
      lineHeight: 1.2,
    },
    h3: {
      fontSize: 'clamp(1.5rem, 3vw, 2rem)',
      fontWeight: 600,
      letterSpacing: '-0.01em',
      lineHeight: 1.3,
    },
    h4: {
      fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',
      fontWeight: 600,
      letterSpacing: '-0.01em',
      lineHeight: 1.4,
    },
    h5: {
      fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',
      fontWeight: 500,
      letterSpacing: '-0.01em',
      lineHeight: 1.4,
    },
    h6: {
      fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',
      fontWeight: 500,
      letterSpacing: '-0.01em',
      lineHeight: 1.5,
    },
    body1: {
      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',
      lineHeight: 1.6,
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '0.02em',
      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',
    },
    subtitle1: {
      fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    subtitle2: {
      fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',
      fontWeight: 500,
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12,
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.03), 0px 1px 2px rgba(0, 0, 0, 0.06)',
    '0px 4px 6px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06)',
    '0px 6px 8px rgba(0, 0, 0, 0.04), 0px 3px 6px rgba(0, 0, 0, 0.06)',
    '0px 8px 12px rgba(0, 0, 0, 0.05), 0px 4px 8px rgba(0, 0, 0, 0.06)',
    '0px 10px 15px rgba(0, 0, 0, 0.05), 0px 6px 10px rgba(0, 0, 0, 0.06)',
    '0px 12px 18px rgba(0, 0, 0, 0.05), 0px 7px 12px rgba(0, 0, 0, 0.06)',
    '0px 14px 21px rgba(0, 0, 0, 0.05), 0px 8px 14px rgba(0, 0, 0, 0.06)',
    '0px 16px 24px rgba(0, 0, 0, 0.05), 0px 9px 16px rgba(0, 0, 0, 0.06)',
    '0px 18px 27px rgba(0, 0, 0, 0.05), 0px 10px 18px rgba(0, 0, 0, 0.06)',
    '0px 20px 30px rgba(0, 0, 0, 0.05), 0px 11px 20px rgba(0, 0, 0, 0.06)',
    '0px 22px 33px rgba(0, 0, 0, 0.05), 0px 12px 22px rgba(0, 0, 0, 0.06)',
    '0px 24px 36px rgba(0, 0, 0, 0.05), 0px 13px 24px rgba(0, 0, 0, 0.06)',
    '0px 26px 39px rgba(0, 0, 0, 0.05), 0px 14px 26px rgba(0, 0, 0, 0.06)',
    '0px 28px 42px rgba(0, 0, 0, 0.05), 0px 15px 28px rgba(0, 0, 0, 0.06)',
    '0px 30px 45px rgba(0, 0, 0, 0.05), 0px 16px 30px rgba(0, 0, 0, 0.06)',
    '0px 32px 48px rgba(0, 0, 0, 0.05), 0px 17px 32px rgba(0, 0, 0, 0.06)',
    '0px 34px 51px rgba(0, 0, 0, 0.05), 0px 18px 34px rgba(0, 0, 0, 0.06)',
    '0px 36px 54px rgba(0, 0, 0, 0.05), 0px 19px 36px rgba(0, 0, 0, 0.06)',
    '0px 38px 57px rgba(0, 0, 0, 0.05), 0px 20px 38px rgba(0, 0, 0, 0.06)',
    '0px 40px 60px rgba(0, 0, 0, 0.05), 0px 21px 40px rgba(0, 0, 0, 0.06)',
    '0px 42px 63px rgba(0, 0, 0, 0.05), 0px 22px 42px rgba(0, 0, 0, 0.06)',
    '0px 44px 66px rgba(0, 0, 0, 0.05), 0px 23px 44px rgba(0, 0, 0, 0.06)',
    '0px 46px 69px rgba(0, 0, 0, 0.05), 0px 24px 46px rgba(0, 0, 0, 0.06)',
  ],
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
  components: {
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: 16,
          paddingRight: 16,
          '@media (min-width:600px)': {
            paddingLeft: 24,
            paddingRight: 24,
          },
          '@media (min-width:960px)': {
            paddingLeft: 32,
            paddingRight: 32,
          },
          '@media (min-width:1200px)': {
            paddingLeft: 48,
            paddingRight: 48,
          },
          maxWidth: '100%',
          '@media (min-width:1280px)': {
            maxWidth: '1280px',
          },
          '@media (min-width:1920px)': {
            maxWidth: '1920px',
          },
        },
      },
    },
    MuiGrid: {
      styleOverrides: {
        container: {
          marginTop: 0,
          marginLeft: 0,
          width: '100%',
        },
        item: {
          paddingTop: 12,
          paddingLeft: 12,
          '@media (min-width:600px)': {
            paddingTop: 16,
            paddingLeft: 16,
          },
          '@media (min-width:960px)': {
            paddingTop: 20,
            paddingLeft: 20,
          },
          '@media (min-width:1200px)': {
            paddingTop: 24,
            paddingLeft: 24,
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: '#f0f4f8',
          borderRadius: 16,
          boxShadow: `
            6px 6px 12px rgba(174, 174, 192, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          transition: 'all 0.3s ease-in-out',
          padding: {
            xs: 2,
            sm: 3,
            md: 4,
          },
          border: 'none',
          '&:hover': {
            boxShadow: `
              8px 8px 16px rgba(174, 174, 192, 0.35),
              -8px -8px 16px rgba(255, 255, 255, 0.6)
            `,
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          backgroundColor: '#f0f4f8',
          borderRadius: 12,
          boxShadow: `
            6px 6px 12px rgba(174, 174, 192, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          transition: 'all 0.2s ease',
          border: 'none',
          padding: {
            xs: '10px 20px',
            sm: '12px 24px',
            md: '14px 28px',
          },
          fontSize: {
            xs: '0.875rem',
            sm: '0.9375rem',
            md: '1rem',
          },
          '&:hover': {
            boxShadow: `
              8px 8px 16px rgba(174, 174, 192, 0.35),
              -8px -8px 16px rgba(255, 255, 255, 0.6)
            `,
            backgroundColor: '#f0f4f8',
          },
          '&:active': {
            boxShadow: `
              inset 4px 4px 8px rgba(174, 174, 192, 0.3),
              inset -4px -4px 8px rgba(255, 255, 255, 0.5)
            `,
            backgroundColor: '#f0f4f8',
          },
        },
        containedPrimary: {
          color: '#ffffff',
          backgroundColor: '#3A86FF',
          boxShadow: `
            6px 6px 12px rgba(58, 134, 255, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          '&:hover': {
            backgroundColor: '#2a6bc9',
          },
          '&:active': {
            boxShadow: `
              inset 4px 4px 8px rgba(0, 0, 0, 0.2),
              inset -4px -4px 8px rgba(255, 255, 255, 0.1)
            `,
          }
        },
        containedSecondary: {
          color: '#ffffff',
          backgroundColor: '#FF595E',
          boxShadow: `
            6px 6px 12px rgba(255, 89, 94, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          '&:hover': {
            backgroundColor: '#d04649',
          },
          '&:active': {
            boxShadow: `
              inset 4px 4px 8px rgba(0, 0, 0, 0.2),
              inset -4px -4px 8px rgba(255, 255, 255, 0.1)
            `,
          }
        },
        outlined: {
          backgroundColor: 'transparent',
          boxShadow: 'none',
          border: '2px solid #f0f4f8',
          '&:hover': {
            boxShadow: `
              4px 4px 8px rgba(174, 174, 192, 0.2),
              -4px -4px 8px rgba(255, 255, 255, 0.3)
            `,
          },
          '&:active': {
            boxShadow: `
              inset 2px 2px 4px rgba(174, 174, 192, 0.2),
              inset -2px -2px 4px rgba(255, 255, 255, 0.3)
            `,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          transition: 'all 0.3s ease',
          marginBottom: {
            xs: 2,
            sm: 2.5,
            md: 3,
          },
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#f0f4f8',
            borderRadius: 12,
            boxShadow: `
              inset 2px 2px 5px rgba(174, 174, 192, 0.2),
              inset -2px -2px 5px rgba(255, 255, 255, 0.7)
            `,
            border: 'none',
            '& fieldset': {
              border: 'none',
            },
            '&:hover fieldset': {
              border: 'none',
            },
            '&.Mui-focused': {
              boxShadow: `
                inset 4px 4px 8px rgba(174, 174, 192, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.5)
              `,
              '& fieldset': {
                border: 'none',
              },
            },
          },
          '& .MuiInputLabel-root': {
            fontSize: {
              xs: '0.875rem',
              sm: '0.9375rem',
              md: '1rem',
            },
            color: '#1a202c',
            fontWeight: 500,
            '&.Mui-focused': {
              color: '#3A86FF',
              fontWeight: 600,
            }
          },
          '& .MuiInputBase-input': {
            padding: '16px 14px',
            color: '#1a202c',
            fontWeight: 500,
          },
        },
      },
    },
    MuiSlider: {
      styleOverrides: {
        root: {
          height: 10,
          '& .MuiSlider-track': {
            border: 'none',
            boxShadow: 'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',
            backgroundColor: '#3A86FF',
          },
          '& .MuiSlider-rail': {
            boxShadow: `
              inset 2px 2px 4px rgba(174, 174, 192, 0.3),
              inset -2px -2px 4px rgba(255, 255, 255, 0.5)
            `,
            backgroundColor: '#f0f4f8',
            opacity: 1,
          },
          '& .MuiSlider-thumb': {
            height: 24,
            width: 24,
            backgroundColor: '#f0f4f8',
            boxShadow: `
              6px 6px 12px rgba(174, 174, 192, 0.3),
              -6px -6px 12px rgba(255, 255, 255, 0.5)
            `,
            '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
              boxShadow: `
                8px 8px 16px rgba(174, 174, 192, 0.35),
                -8px -8px 16px rgba(255, 255, 255, 0.6)
              `,
            },
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: '#f0f4f8',
          borderRadius: 16,
          boxShadow: `
            6px 6px 12px rgba(174, 174, 192, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          transition: 'all 0.3s ease-in-out',
          border: 'none',
          overflow: 'hidden',
          '&:hover': {
            boxShadow: `
              8px 8px 16px rgba(174, 174, 192, 0.35),
              -8px -8px 16px rgba(255, 255, 255, 0.6)
            `,
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: {
            xs: '20px',
            sm: '24px',
            md: '28px',
          },
          '&:last-child': {
            paddingBottom: {
              xs: '20px',
              sm: '24px',
              md: '28px',
            },
          },
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: `
            6px 6px 12px rgba(174, 174, 192, 0.3),
            -6px -6px 12px rgba(255, 255, 255, 0.5)
          `,
          fontSize: {
            xs: '0.875rem',
            sm: '0.9375rem',
            md: '1rem',
          },
          fontWeight: 500,
        },
        standardSuccess: {
          backgroundColor: 'rgba(6, 214, 160, 0.1)',
          color: '#05ab80',
          '& .MuiAlert-icon': {
            color: '#06d6a0',
          },
        },
        standardError: {
          backgroundColor: 'rgba(255, 89, 94, 0.1)',
          color: '#d04649',
          '& .MuiAlert-icon': {
            color: '#ff595e',
          },
        },
        standardWarning: {
          backgroundColor: 'rgba(255, 159, 28, 0.1)',
          color: '#d18016',
          '& .MuiAlert-icon': {
            color: '#ff9f1c',
          },
        },
        standardInfo: {
          backgroundColor: 'rgba(76, 201, 240, 0.1)',
          color: '#3aa8cc',
          '& .MuiAlert-icon': {
            color: '#4cc9f0',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: '#f0f4f8',
          boxShadow: `
            2px 2px 4px rgba(174, 174, 192, 0.3),
            -2px -2px 4px rgba(255, 255, 255, 0.5)
          `,
          fontSize: {
            xs: '0.75rem',
            sm: '0.8125rem',
            md: '0.875rem',
          },
          color: '#1a202c',
          fontWeight: 500,
          '&:hover': {
            boxShadow: `
              3px 3px 6px rgba(174, 174, 192, 0.35),
              -3px -3px 6px rgba(255, 255, 255, 0.6)
            `,
          },
        },
        colorPrimary: {
          backgroundColor: '#3A86FF',
          color: '#ffffff',
        },
        colorSecondary: {
          backgroundColor: '#FF595E',
          color: '#ffffff',
        },
        colorSuccess: {
          backgroundColor: '#06d6a0',
          color: '#ffffff',
        },
        colorError: {
          backgroundColor: '#ff595e',
          color: '#ffffff',
        },
        colorWarning: {
          backgroundColor: '#ff9f1c',
          color: '#ffffff',
        },
        colorInfo: {
          backgroundColor: '#4cc9f0',
          color: '#ffffff',
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          marginBottom: {
            xs: 1,
            sm: 1.5,
            md: 2,
          },
          color: '#1a202c',
        },
        h1: {
          color: '#1a202c',
          fontWeight: 800,
        },
        h2: {
          color: '#1a202c',
          fontWeight: 700,
        },
        h3: {
          color: '#1a202c',
          fontWeight: 700,
        },
        h4: {
          color: '#1a202c',
          fontWeight: 600,
        },
        h5: {
          color: '#1a202c',
          fontWeight: 600,
        },
        h6: {
          color: '#1a202c',
          fontWeight: 600,
        },
        subtitle1: {
          color: '#2d3748',
          fontWeight: 500,
        },
        subtitle2: {
          color: '#2d3748',
          fontWeight: 500,
        },
        body1: {
          color: '#2d3748',
        },
        body2: {
          color: '#4a5568',
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 56,
          height: 32,
          padding: 0,
        },
        switchBase: {
          padding: 4,
          '&.Mui-checked': {
            transform: 'translateX(24px)',
            '& + .MuiSwitch-track': {
              opacity: 1,
              backgroundColor: '#e6eef8',
            },
          },
        },
        thumb: {
          width: 24,
          height: 24,
          backgroundColor: '#f0f4f8',
          boxShadow: `
            2px 2px 4px rgba(174, 174, 192, 0.3),
            -2px -2px 4px rgba(255, 255, 255, 0.5)
          `,
        },
        track: {
          opacity: 1,
          borderRadius: 16,
          backgroundColor: '#e6eef8',
          boxShadow: `
            inset 2px 2px 4px rgba(174, 174, 192, 0.3),
            inset -2px -2px 4px rgba(255, 255, 255, 0.5)
          `,
        },
      },
    },
  },
});

function App() {
  const [mapData, setMapData] = useState([]);
  const [options, setOptions] = useState({ land_cover: [], soil_type: [] });
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [showPredictionResult, setShowPredictionResult] = useState(false);
  const [forecastSummary, setForecastSummary] = useState(null);
  const [showForecastAlert, setShowForecastAlert] = useState(false);

  useEffect(() => {
    // Fetch map data and options when component mounts
    const fetchData = async () => {
      setInitialLoading(true);
      let loadingTimer;

      try {
        const [mapResponse, optionsResponse] = await Promise.all([
          axios.get('/api/map-data'),
          axios.get('/api/options')
        ]);
        setMapData(mapResponse.data);
        setOptions(optionsResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        // Add a slight delay to make the loading animation visible
        // but ensure it gets cleared if component unmounts
        loadingTimer = setTimeout(() => {
          setInitialLoading(false);
        }, 1500);
      }

      // Cleanup function to ensure loading state is reset if component unmounts
      return () => {
        if (loadingTimer) clearTimeout(loadingTimer);
        setInitialLoading(false);
      };
    };

    fetchData();
  }, []);

  const handleSubmit = async (formData) => {
    setLoading(true);
    setShowPredictionResult(false);
    setShowForecastAlert(false);

    try {
      // Add a slight delay to make the loading animation visible
      await new Promise(resolve => setTimeout(resolve, 1200));

      const response = await axios.post('/api/predict', formData);
      setPrediction(response.data);

      // Add a slight delay before showing the result for better animation
      setTimeout(() => {
        setShowPredictionResult(true);
      }, 300);
    } catch (error) {
      console.error('Error making prediction:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleForecastGenerated = (summary) => {
    setForecastSummary(summary);
    setShowForecastAlert(true);

    // Hide the alert after 10 seconds
    setTimeout(() => {
      setShowForecastAlert(false);
    }, 10000);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Header />
      <VoiceEmergencyAssistant />

      {initialLoading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '80vh',
            background: '#e6eef8'
          }}
        >
          <Box sx={{ position: 'relative', width: 200, height: 200 }}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.primary.main,
                animation: 'spin 1.5s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 15,
                left: 15,
                width: 'calc(100% - 30px)',
                height: 'calc(100% - 30px)',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.secondary.main,
                animation: 'spin 2s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 30,
                left: 30,
                width: 'calc(100% - 60px)',
                height: 'calc(100% - 60px)',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.info.main,
                animation: 'spin 2.5s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
          </Box>
          <Typography
            variant="h4"
            sx={{
              mt: 4,
              fontWeight: 600,
              background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',
              backgroundClip: 'text',
              textFillColor: 'transparent',
              animation: 'pulse 2s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 0.6 },
                '50%': { opacity: 1 },
                '100%': { opacity: 0.6 }
              }
            }}
          >
            Loading Flood Prediction System...
          </Typography>
        </Box>
      ) : (
        <Container
          maxWidth="xl"
          sx={{
            mt: { xs: 3, sm: 4, md: 5 },
            mb: { xs: 5, sm: 7, md: 10 },
            px: { xs: 2, sm: 3, md: 4 },
            py: { xs: 4, sm: 5, md: 6 },
            overflow: 'hidden',
            backgroundColor: '#e6eef8',
            borderRadius: '24px',
            boxShadow: `
              inset 1px 1px 2px rgba(255, 255, 255, 0.5),
              inset -1px -1px 2px rgba(174, 174, 192, 0.3)
            `,
            position: 'relative',
            zIndex: 1
          }}
        >
          <Grid
            container
            spacing={{ xs: 3, sm: 4, md: 5 }}
            alignItems="stretch"
            sx={{
              '& .MuiGrid-item': {
                display: 'flex',
                flexDirection: 'column'
              }
            }}
          >
            <Grid item xs={12}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 4, sm: 5, md: 6 },
                  mb: { xs: 3, sm: 4, md: 5 },
                  backgroundColor: '#f0f4f8',
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  boxShadow: `
                    6px 6px 12px rgba(174, 174, 192, 0.3),
                    -6px -6px 12px rgba(255, 255, 255, 0.5)
                  `,
                  '&:hover': {
                    boxShadow: `
                      8px 8px 16px rgba(174, 174, 192, 0.35),
                      -8px -8px 16px rgba(255, 255, 255, 0.6)
                    `
                  },
                  minHeight: { xs: 'auto', sm: '200px' }
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '180px',
                    height: '180px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 0 100%',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '120px',
                    height: '120px',
                    background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 100% 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    flexDirection: { xs: 'column', sm: 'row' },
                    mb: { xs: 3, sm: 4 },
                    gap: { xs: 2, sm: 0 }
                  }}>
                    <Box
                      sx={{
                        mr: { xs: 0, sm: 2 },
                        p: { xs: 1.5, sm: 2 },
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)',
                        alignSelf: { xs: 'center', sm: 'flex-start' }
                      }}
                    >
                      <Typography variant="h4" component="span">🌊</Typography>
                    </Box>
                    <Typography
                      variant="h2"
                      component="h1"
                      sx={{
                        background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',
                        backgroundClip: 'text',
                        textFillColor: 'transparent',
                        fontWeight: 800,
                        letterSpacing: '-0.5px',
                        fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                        textAlign: { xs: 'center', sm: 'left' },
                        lineHeight: 1.2
                      }}
                    >
                      Flood Risk Prediction System
                    </Typography>
                  </Box>
                  <Typography
                    variant="body1"
                    paragraph
                    sx={{
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      maxWidth: { xs: '100%', sm: '90%' },
                      color: 'text.secondary',
                      lineHeight: 1.6,
                      mb: { xs: 3, sm: 4 },
                      textAlign: { xs: 'center', sm: 'left' }
                    }}
                  >
                    This interactive tool helps predict flood risk based on various environmental and geographical factors.
                    For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: { xs: 1.5, sm: 2 },
                      flexWrap: 'wrap',
                      mt: { xs: 3, sm: 4 },
                      justifyContent: { xs: 'center', sm: 'flex-start' }
                    }}
                  >
                    <Chip
                      label="Real-time Weather Data"
                      color="primary"
                      size="medium"
                      icon={<span>⛈️</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                    <Chip
                      label="Historical Flood Analysis"
                      color="info"
                      size="medium"
                      icon={<span>📊</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                    <Chip
                      label="Indian Cities Database"
                      color="success"
                      size="medium"
                      icon={<span>🏙️</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                  </Box>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} lg={6}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 3, sm: 4, md: 5 },
                  height: '100%',
                  minHeight: { xs: 'auto', lg: '600px' },
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                  },
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100px',
                    height: '100px',
                    background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 100% 0',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    width: '80px',
                    height: '80px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '100% 0 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    flexDirection: { xs: 'column', sm: 'row' },
                    mb: { xs: 3, sm: 4 },
                    gap: { xs: 2, sm: 0 }
                  }}>
                    <Box
                      sx={{
                        mr: { xs: 0, sm: 2 },
                        p: { xs: 1, sm: 1.5 },
                        borderRadius: '12px',
                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        alignSelf: { xs: 'center', sm: 'flex-start' }
                      }}
                    >
                      <Typography variant="h5" component="span">📝</Typography>
                    </Box>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 700,
                        color: theme.palette.primary.main,
                        letterSpacing: '-0.5px',
                        fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
                        textAlign: { xs: 'center', sm: 'left' }
                      }}
                    >
                      Predict Flood Risk
                    </Typography>
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{
                      mb: { xs: 3, sm: 4 },
                      color: 'text.secondary',
                      maxWidth: '100%',
                      textAlign: { xs: 'center', sm: 'left' }
                    }}
                  >
                    Enter location and environmental factors to get a precise flood risk assessment.
                    For Indian cities, we provide enhanced accuracy using historical data.
                  </Typography>
                  <PredictionForm
                    options={options}
                    onSubmit={handleSubmit}
                    loading={loading}
                  />
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} lg={6}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 3, sm: 4, md: 5 },
                  height: '100%',
                  minHeight: { xs: 'auto', lg: '600px' },
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                  },
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '100px',
                    height: '100px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 0 100%',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '80px',
                    height: '80px',
                    background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 100% 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  {loading ? (
                    <LoadingAnimation theme={theme} />
                  ) : prediction && showPredictionResult ? (
                    <Box sx={{
                      opacity: showPredictionResult ? 1 : 0,
                      transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',
                      transition: 'all 0.5s ease-in-out'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          sx={{
                            mr: 2,
                            p: 1,
                            borderRadius: '12px',
                            background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Typography variant="h5" component="span">📊</Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 700,
                            color: theme.palette.primary.main,
                            letterSpacing: '-0.5px'
                          }}
                        >
                          Risk Assessment
                        </Typography>
                      </Box>
                      <ResultDisplay prediction={prediction} />
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      py: 8,
                      opacity: loading ? 0 : 1,
                      transition: 'opacity 0.3s ease-in-out'
                    }}>
                      <Box
                        sx={{
                          width: 100,
                          height: 100,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                          boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',
                          mb: 3,
                          animation: 'float 3s ease-in-out infinite',
                          '@keyframes float': {
                            '0%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                            '100%': { transform: 'translateY(0px)' }
                          }
                        }}
                      >
                        <Typography variant="h2" component="span">📊</Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 600, color: theme.palette.primary.main, textAlign: 'center', mb: 1 }}>
                        Results will appear here
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', maxWidth: '80%', mx: 'auto' }}>
                        Fill out the form on the left to generate a detailed flood risk assessment
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>

            {prediction && prediction.risk_assessment && showPredictionResult && (
              <SlideUp delay={200}>
                <Grid item xs={12}>
                  <Paper
                    elevation={3}
                    sx={{
                      p: { xs: 3, md: 4 },
                      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                      borderRadius: 2,
                      position: 'relative',
                      overflow: 'hidden',
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: '150px',
                        height: '150px',
                        background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                        borderRadius: '0 0 0 100%',
                        zIndex: 0
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        width: '120px',
                        height: '120px',
                        background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',
                        borderRadius: '0 100% 0 0',
                        zIndex: 0
                      }}
                    />
                    <Box sx={{ position: 'relative', zIndex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          sx={{
                            mr: 2,
                            p: 1,
                            borderRadius: '12px',
                            background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Typography variant="h5" component="span">📈</Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 700,
                            color: theme.palette.primary.dark,
                            letterSpacing: '-0.5px'
                          }}
                        >
                          Risk Factor Analysis
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{
                          mb: 3,
                          color: 'text.secondary',
                          maxWidth: '800px'
                        }}
                      >
                        This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.
                        {prediction.accurate_data && " For Indian cities, this includes historical flood data and real-time weather conditions."}
                      </Typography>
                      <RiskFactorsChart riskAssessment={prediction.risk_assessment} />
                    </Box>
                  </Paper>
                </Grid>
              </SlideUp>
            )}

            {/* Forecast Alert */}
            {showForecastAlert && forecastSummary && (
              <ScaleIn delay={100}>
                <Grid item xs={12}>
                  <Alert
                    severity={forecastSummary.maxRiskScore > 70 ? "error" : forecastSummary.maxRiskScore > 40 ? "warning" : "info"}
                    variant="filled"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                      '& .MuiAlert-icon': { fontSize: '1.8rem' },
                      p: 2,
                      animation: 'pulse 2s infinite',
                      '@keyframes pulse': {
                        '0%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' },
                        '50%': { boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)' },
                        '100%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' }
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                          {forecastSummary.maxRiskScore > 70
                            ? "⚠️ High flood risk detected in the forecast!"
                            : forecastSummary.maxRiskScore > 40
                              ? "⚠️ Medium flood risk detected in the forecast"
                              : "ℹ️ Flood risk forecast generated"}
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          Location: <strong>{forecastSummary.location || 'Selected area'}</strong>
                        </Typography>
                        <Typography variant="body1">
                          Peak risk score of <strong>{forecastSummary.maxRiskScore.toFixed(1)}</strong> expected around <strong>{forecastSummary.maxRiskTime}</strong>.
                          Risk trend is <strong>{forecastSummary.riskTrend}</strong>.
                        </Typography>
                        {forecastSummary.maxRiskScore > 60 && (
                          <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                            Please monitor local weather updates and follow emergency guidelines.
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Alert>
                </Grid>
              </ScaleIn>
            )}

            {/* Temporal Prediction Component */}
            {prediction && showPredictionResult && (
              <SlideUp delay={300}>
                <Grid item xs={12}>
                  <Paper
                    elevation={3}
                    sx={{
                      borderRadius: 2,
                      overflow: 'hidden',
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                      }
                    }}
                  >
                    <TimelineRiskPredictor
                      formData={prediction.input_data}
                      onForecastGenerated={handleForecastGenerated}
                    />
                  </Paper>
                </Grid>
              </SlideUp>
            )}

            <SlideUp delay={200}>
              <Grid item xs={12}>
                <Paper
                  elevation={3}
                  sx={{
                    p: { xs: 3, sm: 4, md: 5 },
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                    borderRadius: 3,
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                    }
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '150px',
                      height: '150px',
                      background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                      borderRadius: '0 0 0 100%',
                      zIndex: 0
                    }}
                  />
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          mr: 2,
                          p: 1,
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography variant="h5" component="span">🗺️</Typography>
                      </Box>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          color: theme.palette.primary.dark,
                          letterSpacing: '-0.5px'
                        }}
                      >
                        Flood Risk Map
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      paragraph
                      sx={{
                        mb: 3,
                        color: 'text.secondary',
                        maxWidth: '800px'
                      }}
                    >
                      This interactive map shows areas with predicted flood risk based on our analysis.
                      Green markers indicate low risk areas, while red markers indicate high risk zones.
                      Click on markers to see detailed information.
                    </Typography>
                    <Box sx={{
                      borderRadius: 3,
                      overflow: 'hidden',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      height: { xs: '400px', sm: '500px', md: '600px' },
                      mt: { xs: 2, sm: 3 }
                    }}>
                      <FloodMap mapData={mapData} />
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            </SlideUp>

            {/* Community Reports Section */}
            <SlideUp delay={300}>
              <Grid item xs={12}>
                <Paper
                  elevation={3}
                  sx={{
                    p: { xs: 3, sm: 4, md: 5 },
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                    borderRadius: 3,
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                    }
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '150px',
                      height: '150px',
                      background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',
                      borderRadius: '0 0 0 100%',
                      zIndex: 0
                    }}
                  />
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          mr: 2,
                          p: 1,
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography variant="h5" component="span">👥</Typography>
                      </Box>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          color: theme.palette.primary.dark,
                          letterSpacing: '-0.5px'
                        }}
                      >
                        Community Reports
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      paragraph
                      sx={{
                        mb: 3,
                        color: 'text.secondary',
                        maxWidth: '800px'
                      }}
                    >
                      View and submit real-time flood reports from community members. These reports help validate our predictions
                      and provide valuable on-the-ground information during flood events.
                    </Typography>
                    <CommunityReports />
                  </Box>
                </Paper>
              </Grid>
            </SlideUp>
          </Grid>
        </Container>
      )}
    </ThemeProvider>
  );
}

export default App;
