import React, { useState } from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Button,
  Grid,
  Slider,
  Typography,
  FormControl,
  InputLabel,
  Select,
  CircularProgress
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';

const PredictionForm = ({ options, onSubmit, loading }) => {
  const [formData, setFormData] = useState({
    rainfall: 150,
    temperature: 30,
    humidity: 85,
    discharge: 800,
    water_level: 6.5,
    elevation: 150,
    land_cover: '',
    soil_type: '',
    population_density: 1200,
    infrastructure: 'Yes',
    historical_floods: 'No'
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSliderChange = (name) => (e, newValue) => {
    setFormData(prev => ({ ...prev, [name]: newValue }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        mt: { xs: 2, sm: 3 },
        position: 'relative',
        zIndex: 1,
        flex: 1,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Grid
        container
        spacing={{ xs: 2.5, sm: 3, md: 3.5 }}
        alignItems="stretch"
        sx={{ flex: 1 }}
      >
        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(58, 134, 255, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'primary.light',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                💧
              </Box>
              Rainfall (mm)
            </Typography>
            <Slider
              value={formData.rainfall}
              onChange={handleSliderChange('rainfall')}
              aria-labelledby="rainfall-slider"
              valueLabelDisplay="auto"
              step={10}
              marks
              min={0}
              max={500}
              sx={{ mt: 1, mb: 1 }}
            />
            <TextField
              margin="dense"
              name="rainfall"
              value={formData.rainfall}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(255, 89, 94, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'secondary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'secondary.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🌡️
              </Box>
              Temperature (°C)
            </Typography>
            <Slider
              value={formData.temperature}
              onChange={handleSliderChange('temperature')}
              aria-labelledby="temperature-slider"
              valueLabelDisplay="auto"
              step={1}
              marks
              min={0}
              max={50}
              sx={{
                mt: 1,
                mb: 1,
                color: 'secondary.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'secondary.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #FF5A5F 0%, #FF9F1C 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="temperature"
              value={formData.temperature}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(76, 201, 240, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'info.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'info.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                💦
              </Box>
              Humidity (%)
            </Typography>
            <Slider
              value={formData.humidity}
              onChange={handleSliderChange('humidity')}
              aria-labelledby="humidity-slider"
              valueLabelDisplay="auto"
              step={5}
              marks
              min={0}
              max={100}
              sx={{
                mt: 1,
                mb: 1,
                color: 'info.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'info.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #4CC9F0 0%, #3A86FF 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="humidity"
              value={formData.humidity}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(76, 175, 80, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'success.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'success.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🌊
              </Box>
              River Discharge (m³/s)
            </Typography>
            <Slider
              value={formData.discharge}
              onChange={handleSliderChange('discharge')}
              aria-labelledby="discharge-slider"
              valueLabelDisplay="auto"
              step={50}
              marks
              min={0}
              max={2000}
              sx={{ mt: 1, mb: 1 }}
            />
            <TextField
              margin="dense"
              name="discharge"
              value={formData.discharge}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(33, 150, 243, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'primary.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                📏
              </Box>
              Water Level (m)
            </Typography>
            <Slider
              value={formData.water_level}
              onChange={handleSliderChange('water_level')}
              aria-labelledby="water-level-slider"
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={15}
              sx={{ mt: 1, mb: 1 }}
            />
            <TextField
              margin="dense"
              name="water_level"
              value={formData.water_level}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{ step: 0.1 }}
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 2.5, md: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(156, 39, 176, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            minHeight: { xs: '140px', sm: '160px' }
          }}>
            <Typography
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'secondary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'secondary.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                ⛰️
              </Box>
              Elevation (m)
            </Typography>
            <Slider
              value={formData.elevation}
              onChange={handleSliderChange('elevation')}
              aria-labelledby="elevation-slider"
              valueLabelDisplay="auto"
              step={10}
              marks
              min={0}
              max={500}
              sx={{ mt: 1, mb: 1 }}
            />
            <TextField
              margin="dense"
              name="elevation"
              value={formData.elevation}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth margin="normal" sx={{ mt: 0 }}>
            <InputLabel id="land-cover-label">Land Cover</InputLabel>
            <Select
              labelId="land-cover-label"
              name="land_cover"
              value={formData.land_cover}
              onChange={handleChange}
              label="Land Cover"
              required
              size="medium"
            >
              {options.land_cover.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth margin="normal" sx={{ mt: 0 }}>
            <InputLabel id="soil-type-label">Soil Type</InputLabel>
            <Select
              labelId="soil-type-label"
              name="soil_type"
              value={formData.soil_type}
              onChange={handleChange}
              label="Soil Type"
              required
              size="medium"
            >
              {options.soil_type.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            margin="normal"
            label="Population Density"
            name="population_density"
            value={formData.population_density}
            onChange={handleChange}
            type="number"
            required
            size="medium"
            sx={{ mt: 0 }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal" sx={{ mt: 0 }}>
            <InputLabel id="infrastructure-label">Infrastructure Present</InputLabel>
            <Select
              labelId="infrastructure-label"
              name="infrastructure"
              value={formData.infrastructure}
              onChange={handleChange}
              label="Infrastructure Present"
              size="medium"
            >
              <MenuItem value="Yes">Yes</MenuItem>
              <MenuItem value="No">No</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal" sx={{ mt: 0 }}>
            <InputLabel id="historical-floods-label">Historical Floods</InputLabel>
            <Select
              labelId="historical-floods-label"
              name="historical_floods"
              value={formData.historical_floods}
              onChange={handleChange}
              label="Historical Floods"
              size="medium"
            >
              <MenuItem value="Yes">Yes</MenuItem>
              <MenuItem value="No">No</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Box sx={{
            mt: { xs: 4, sm: 5 },
            pt: { xs: 2, sm: 3 },
            borderTop: '1px solid rgba(0, 0, 0, 0.08)',
            position: 'relative',
            display: 'flex',
            justifyContent: 'center'
          }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              endIcon={loading ? <CircularProgress size={24} color="inherit" /> : <SendIcon />}
              disabled={loading || !formData.land_cover || !formData.soil_type}
              sx={{
                py: { xs: 2, sm: 2.5 },
                px: { xs: 5, sm: 7 },
                borderRadius: 4,
                fontSize: { xs: '1.1rem', sm: '1.2rem' },
                fontWeight: 700,
                boxShadow: '0 8px 20px rgba(58, 134, 255, 0.3)',
                position: 'relative',
                overflow: 'hidden',
                background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',
                minWidth: { xs: '200px', sm: '250px' },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                },
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 28px rgba(58, 134, 255, 0.4)',
                  '&::before': {
                    opacity: 1,
                  }
                },
                '&:active': {
                  transform: 'translateY(1px)',
                  boxShadow: '0 5px 15px rgba(58, 134, 255, 0.4)',
                },
                transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
              }}
            >
              {loading ? 'Analyzing Data...' : 'Predict Flood Risk'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PredictionForm;
