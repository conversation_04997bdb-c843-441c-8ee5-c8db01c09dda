/* ========================================
   RESPONSIVE LAYOUT FIXES
   Professional Spacing & Grid Improvements
   ======================================== */

/* ===== GLOBAL RESPONSIVE IMPROVEMENTS ===== */
.MuiContainer-root {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

@media (min-width: 600px) {
  .MuiContainer-root {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }
}

@media (min-width: 960px) {
  .MuiContainer-root {
    padding-left: 32px !important;
    padding-right: 32px !important;
  }
}

/* ===== GRID SPACING IMPROVEMENTS ===== */
.MuiGrid-container {
  margin-top: 0 !important;
  margin-left: 0 !important;
  width: 100% !important;
}

.MuiGrid-item {
  padding-top: 12px !important;
  padding-left: 12px !important;
}

@media (min-width: 600px) {
  .MuiGrid-item {
    padding-top: 16px !important;
    padding-left: 16px !important;
  }
}

@media (min-width: 960px) {
  .MuiGrid-item {
    padding-top: 20px !important;
    padding-left: 20px !important;
  }
}

/* ===== PAPER COMPONENT IMPROVEMENTS ===== */
.MuiPaper-root {
  margin-bottom: 16px !important;
}

@media (min-width: 600px) {
  .MuiPaper-root {
    margin-bottom: 24px !important;
  }
}

@media (min-width: 960px) {
  .MuiPaper-root {
    margin-bottom: 32px !important;
  }
}

/* ===== FORM FIELD SPACING ===== */
.MuiFormControl-root {
  margin-bottom: 16px !important;
}

.MuiTextField-root {
  margin-bottom: 16px !important;
}

/* ===== TYPOGRAPHY RESPONSIVE IMPROVEMENTS ===== */
.MuiTypography-h1 {
  font-size: 2rem !important;
  line-height: 1.2 !important;
}

@media (min-width: 600px) {
  .MuiTypography-h1 {
    font-size: 2.5rem !important;
  }
}

@media (min-width: 960px) {
  .MuiTypography-h1 {
    font-size: 3rem !important;
  }
}

.MuiTypography-h2 {
  font-size: 1.75rem !important;
  line-height: 1.2 !important;
}

@media (min-width: 600px) {
  .MuiTypography-h2 {
    font-size: 2rem !important;
  }
}

@media (min-width: 960px) {
  .MuiTypography-h2 {
    font-size: 2.5rem !important;
  }
}

.MuiTypography-h4 {
  font-size: 1.25rem !important;
  line-height: 1.3 !important;
}

@media (min-width: 600px) {
  .MuiTypography-h4 {
    font-size: 1.5rem !important;
  }
}

@media (min-width: 960px) {
  .MuiTypography-h4 {
    font-size: 2rem !important;
  }
}

/* ===== BUTTON RESPONSIVE IMPROVEMENTS ===== */
.MuiButton-sizeLarge {
  padding: 12px 24px !important;
  font-size: 1rem !important;
}

@media (min-width: 600px) {
  .MuiButton-sizeLarge {
    padding: 16px 32px !important;
    font-size: 1.1rem !important;
  }
}

@media (min-width: 960px) {
  .MuiButton-sizeLarge {
    padding: 20px 40px !important;
    font-size: 1.2rem !important;
  }
}

/* ===== CHIP RESPONSIVE IMPROVEMENTS ===== */
.MuiChip-root {
  margin: 4px !important;
  font-size: 0.875rem !important;
}

@media (min-width: 600px) {
  .MuiChip-root {
    margin: 6px !important;
    font-size: 0.9rem !important;
  }
}

/* ===== CARD/PAPER CONTENT SPACING ===== */
.card-content {
  padding: 16px !important;
}

@media (min-width: 600px) {
  .card-content {
    padding: 24px !important;
  }
}

@media (min-width: 960px) {
  .card-content {
    padding: 32px !important;
  }
}

/* ===== SECTION SPACING ===== */
.section-spacing {
  margin-bottom: 24px !important;
}

@media (min-width: 600px) {
  .section-spacing {
    margin-bottom: 32px !important;
  }
}

@media (min-width: 960px) {
  .section-spacing {
    margin-bottom: 48px !important;
  }
}

/* ===== MOBILE OPTIMIZATIONS ===== */
@media (max-width: 599px) {
  /* Reduce padding on mobile */
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  
  /* Stack elements vertically on mobile */
  .mobile-stack {
    flex-direction: column !important;
    align-items: center !important;
  }
  
  /* Reduce font sizes on mobile */
  .MuiTypography-h2 {
    font-size: 1.5rem !important;
  }
  
  .MuiTypography-h4 {
    font-size: 1.125rem !important;
  }
  
  /* Full width buttons on mobile */
  .MuiButton-root {
    width: 100% !important;
    max-width: 300px !important;
  }
}

/* ===== TABLET OPTIMIZATIONS ===== */
@media (min-width: 600px) and (max-width: 959px) {
  /* Optimize for tablet layouts */
  .tablet-optimize {
    max-width: 100% !important;
  }
  
  /* Adjust grid spacing for tablets */
  .MuiGrid-item {
    padding-top: 14px !important;
    padding-left: 14px !important;
  }
}

/* ===== DESKTOP OPTIMIZATIONS ===== */
@media (min-width: 1200px) {
  /* Increase spacing on large screens */
  .MuiContainer-root {
    padding-left: 48px !important;
    padding-right: 48px !important;
  }
  
  .MuiGrid-item {
    padding-top: 24px !important;
    padding-left: 24px !important;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .MuiContainer-root {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* ===== FOCUS IMPROVEMENTS ===== */
.MuiButton-root:focus,
.MuiTextField-root:focus-within,
.MuiSelect-root:focus-within {
  outline: 2px solid #3A86FF !important;
  outline-offset: 2px !important;
}

/* ===== LOADING STATE IMPROVEMENTS ===== */
.loading-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
}
